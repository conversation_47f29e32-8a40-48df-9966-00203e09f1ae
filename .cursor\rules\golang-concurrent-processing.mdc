---
description: 
globs: 
alwaysApply: false
---
---
description: Concurrent Processing Patterns
globs: **/worker.go, **/pool.go
alwaysApply: false
---

When implementing concurrency:

1. Worker Pool Implementation
- Control number of workers
- Implement graceful shutdown
- Handle worker errors
- Monitor pool health

Required pool structure:
```go
type Pool struct {
    workers    int
    queue      chan Task
    shutdown   chan struct{}
    wg         sync.WaitGroup
    errorChan  chan error
    metrics    MetricsCollector
}

func NewPool(workers int, queueSize int) *Pool {
    return &Pool{
        workers:   workers,
        queue:     make(chan Task, queueSize),
        shutdown:  make(chan struct{}),
        errorChan: make(chan error, workers),
    }
}

func (p *Pool) Start() {
    for i := 0; i < p.workers; i++ {
        p.wg.Add(1)
        go p.worker()
    }
}

func (p *Pool) worker() {
    defer p.wg.Done()
    for {
        select {
        case task := <-p.queue:
            if err := p.processTask(task); err != nil {
                p.error<PERSON>han <- err
            }
        case <-p.shutdown:
            return
        }
    }
}
```

2. Rate Limiting
- Implement token bucket
- Support burst handling
- Configure limits dynamically
- Monitor rate limit metrics

Required rate limiter:
```go
type RateLimiter struct {
    rate      rate.Limit
    burst     int
    limiter   *rate.Limiter
    metrics   MetricsCollector
}

func (r *RateLimiter) Allow() bool {
    allowed := r.limiter.Allow()
    if !allowed {
        r.metrics.IncCounter("rate_limit_exceeded")
    }
    return allowed
}
```

3. Context Management
- Use context for cancellation
- Implement timeouts
- Handle parent context
- Propagate deadlines

4. Error Handling
- Collect worker errors
- Implement retry logic
- Handle panic recovery
- Log error details

5. Resource Management
- Monitor resource usage
- Implement backpressure
- Handle cleanup
- Track metrics
