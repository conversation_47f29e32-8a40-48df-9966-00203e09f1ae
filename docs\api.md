# TopAI Chat Server API 文档

## 概述

TopAI Chat Server 提供了丰富的API接口，支持多种AI模型的交互，包括文本对话、语音处理、图像生成等功能。

## 认证方式

### 1. JWT Token 认证
用于前端用户认证，需要在请求头中携带：
```
Authorization: Bearer <JWT_TOKEN>
```

### 2. API Key 认证
用于第三方应用认证，需要在请求头中携带：
```
Authorization: Bearer <API_KEY>
```

## API 接口分类

### 1. 认证接口 (Authentication)

#### 1.1 用户登录
```http
POST /api/v1/auths/signin
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

响应：
```json
{
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "expires_at": 1703721600,
  "user": {
    "id": "user-uuid",
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

#### 1.2 用户注册
```http
POST /api/v1/auths/signup
Content-Type: application/json

{
  "name": "新用户",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 1.3 获取用户信息
```http
GET /api/v1/auths
Authorization: Bearer <JWT_TOKEN>
```

#### 1.4 用户登出
```http
GET /api/v1/auths/signout
Authorization: Bearer <JWT_TOKEN>
```

#### 1.5 更新用户资料
```http
POST /api/v1/auths/update/profile
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "name": "新用户名",
  "email": "<EMAIL>"
}
```

#### 1.6 更新密码
```http
POST /api/v1/auths/update/password
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "password": "旧密码",
  "new_password": "新密码"
}
```

### 2. 对话接口 (Conversations)

#### 2.1 获取对话列表
```http
GET /api/v1/chats?page=1&limit=10
Authorization: Bearer <JWT_TOKEN>
```

响应：
```json
[
  {
    "id": "conversation-uuid",
    "title": "对话标题",
    "created_at": 1703721600,
    "updated_at": 1703721600,
    "chat": {
      "title": "对话标题",
      "messages": [...],
      "model_type": "llm",
      "models": ["gpt-3.5-turbo"]
    }
  }
]
```

#### 2.2 创建新对话
```http
POST /api/v1/chats/new
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "chat": {
    "title": "新对话",
    "model_type": "llm",
    "models": ["gpt-3.5-turbo"],
    "messages": [
      {
        "id": "msg-uuid",
        "role": "user",
        "content": "你好",
        "timestamp": 1703721600000
      }
    ],
    "history": {
      "currentId": "msg-uuid"
    },
    "timestamp": 1703721600000
  }
}
```

#### 2.3 获取对话详情
```http
GET /api/v1/chats/{chat_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 2.4 更新对话
```http
POST /api/v1/chats/{chat_id}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "title": "更新的标题"
}
```

#### 2.5 删除对话
```http
DELETE /api/v1/chats/{chat_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 2.6 非LLM对话
```http
POST /api/v1/chats/nonllm
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "model_type": "tts",
  "model": "tts-1",
  "content": "要转换为语音的文本",
  "chat_id": "conversation-uuid"
}
```

### 3. 模型接口 (Models)

#### 3.1 获取基础模型列表
```http
GET /api/models/base
```

响应：
```json
{
  "data": [
    {
      "id": "gpt-3.5-turbo",
      "name": "GPT-3.5 Turbo",
      "object": "model",
      "active": true,
      "context_window": 4096,
      "max_completion_tokens": 4096,
      "owned_by": "openai",
      "model_type": "llm"
    }
  ]
}
```

#### 3.2 获取可用模型列表
```http
GET /api/models
```

#### 3.3 聊天完成
```http
POST /api/chat/completions
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "stream": true,
  "chat_id": "conversation-uuid",
  "session_id": "session-uuid"
}
```

### 4. OpenAI 兼容接口

#### 4.1 聊天完成
```http
POST /api/openai/chat/completions
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 150
}
```

响应（非流式）：
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1703721600,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 12,
    "total_tokens": 22
  }
}
```

#### 4.2 语音识别
```http
POST /api/openai/audio/transcriptions
Authorization: Bearer <API_KEY>
Content-Type: multipart/form-data

file: <audio_file>
model: whisper-1
language: zh
```

响应：
```json
{
  "text": "识别出的文本内容"
}
```

#### 4.3 语音合成
```http
POST /api/openai/audio/speech
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "model": "tts-1",
  "input": "要转换为语音的文本",
  "voice": "alloy",
  "response_format": "mp3",
  "speed": 1.0
}
```

#### 4.4 图像生成
```http
POST /api/openai/images/generations
Authorization: Bearer <API_KEY>
Content-Type: application/json

{
  "model": "dall-e-3",
  "prompt": "一只可爱的小猫在花园里玩耍",
  "size": "1024x1024",
  "response_format": "url"
}
```

### 5. TopAI 模型市场接口

#### 5.1 获取TopAI模型列表
```http
POST /api/v1/topai/models
Content-Type: application/json

{
  "model_ids": [],
  "category_ids": [],
  "series_ids": [],
  "provider_ids": [],
  "model_types": ["llm"],
  "support_params": [],
  "page": 1,
  "page_size": 10
}
```

#### 5.2 获取模型分类
```http
GET /api/v1/topai/models/categories
```

#### 5.3 获取模型系列
```http
GET /api/v1/topai/models/series
```

#### 5.4 获取供应商列表
```http
GET /api/v1/topai/models/providers
```

#### 5.5 获取模型详情
```http
GET /api/v1/topai/models/{model_id}
```

#### 5.6 按Token排序获取模型
```http
POST /api/v1/topai/models/ranking
Content-Type: application/json

{
  "model_types": ["llm"],
  "page": 1,
  "page_size": 10
}
```

### 6. 用户应用接口

#### 6.1 获取应用列表
```http
GET /api/v1/user/app/list
Authorization: Bearer <JWT_TOKEN>
```

#### 6.2 创建应用
```http
POST /api/v1/user/app/create
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "name": "我的应用",
  "description": "应用描述",
  "website": "https://example.com",
  "logo": "logo-file-uuid"
}
```

#### 6.3 获取应用详情
```http
GET /api/v1/user/app/info/{app_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 6.4 更新应用
```http
POST /api/v1/user/app/update/{app_id}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "name": "更新的应用名",
  "description": "更新的描述"
}
```

#### 6.5 删除应用
```http
POST /api/v1/user/app/delete/{app_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 6.6 获取API密钥列表
```http
GET /api/v1/user/app/key/list
Authorization: Bearer <JWT_TOKEN>
```

#### 6.7 创建API密钥
```http
POST /api/v1/user/app/key/{app_id}/create
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "key_name": "密钥名称"
}
```

#### 6.8 删除API密钥
```http
POST /api/v1/user/app/key/{app_id}/delete/{key_name}
Authorization: Bearer <JWT_TOKEN>
```

### 7. 文件接口

#### 7.1 上传文件
```http
POST /api/v1/files
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data

file: <file>
```

响应：
```json
{
  "id": "file-uuid",
  "filename": "example.txt",
  "size": 1024,
  "content_type": "text/plain",
  "url": "/api/v1/files/content/file-uuid"
}
```

#### 7.2 上传图片
```http
POST /api/v1/files/image
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data

file: <image_file>
```

#### 7.3 删除文件
```http
DELETE /api/v1/files/{file_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 7.4 获取图片文件
```http
GET /api/v1/files/image/{file_id}/content
Authorization: Bearer <JWT_TOKEN>
```

#### 7.5 获取音频文件
```http
GET /api/v1/files/audio/{file_id}/content
Authorization: Bearer <JWT_TOKEN>
```

#### 7.6 获取公共图片文件
```http
GET /api/v1/public/files/image/{file_id}/content
```

### 8. 音频处理接口

#### 8.1 创建语音
```http
POST /api/v1/audio/speech/{chat_id}/{msg_id}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice": "alloy",
  "model": "tts-1"
}
```

### 9. 用户余额接口

#### 9.1 获取用户余额
```http
GET /api/v1/user/balance
Authorization: Bearer <JWT_TOKEN>
```

响应：
```json
{
  "balance": "100.50",
  "currency": "USD"
}
```

#### 9.2 获取余额记录
```http
GET /api/v1/user/balance/record?page=1&page_size=10
Authorization: Bearer <JWT_TOKEN>
```

响应：
```json
{
  "total": 50,
  "records": [
    {
      "id": 1,
      "type": "consumption",
      "is_in": false,
      "amount": "0.10",
      "description": "API调用费用",
      "created_at": 1703721600
    }
  ]
}
```

### 10. 管理员接口

#### 10.1 获取所有用户
```http
GET /api/v1/users?page=1&page_size=10&order=created_at&direction=desc
Authorization: Bearer <ADMIN_JWT_TOKEN>
```

#### 10.2 创建用户
```http
POST /api/v1/auths/add
Authorization: Bearer <ADMIN_JWT_TOKEN>
Content-Type: application/json

{
  "name": "新用户",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

#### 10.3 更新用户
```http
POST /api/v1/users/{user_id}/update
Authorization: Bearer <ADMIN_JWT_TOKEN>
Content-Type: application/json

{
  "name": "更新的用户名",
  "email": "<EMAIL>"
}
```

#### 10.4 删除用户
```http
DELETE /api/v1/users/{user_id}
Authorization: Bearer <ADMIN_JWT_TOKEN>
```

#### 10.5 更新用户角色
```http
POST /api/v1/users/update/role
Authorization: Bearer <ADMIN_JWT_TOKEN>
Content-Type: application/json

{
  "id": "user-uuid",
  "role": "admin"
}
```

#### 10.6 赠送余额
```http
POST /api/v1/users/gift/balance
Authorization: Bearer <ADMIN_JWT_TOKEN>
Content-Type: application/json

{
  "to_user_id": "user-uuid",
  "amount": 100
}
```

#### 10.7 获取系统配置
```http
GET /api/v1/auths/admin/config
Authorization: Bearer <ADMIN_JWT_TOKEN>
```

#### 10.8 更新系统配置
```http
POST /api/v1/auths/admin/config
Authorization: Bearer <ADMIN_JWT_TOKEN>
Content-Type: application/json

{
  "ENABLE_SIGNUP": true,
  "DEFAULT_USER_ROLE": "user",
  "JWT_EXPIRES_IN": "24h"
}
```

### 11. 系统接口

#### 11.1 获取系统配置
```http
GET /api/config
```

#### 11.2 获取版本更新
```http
GET /api/v1/version/updates
Authorization: Bearer <JWT_TOKEN>
```

#### 11.3 获取并发统计
```http
GET /api/v1/system/concurrency/stats
Authorization: Bearer <ADMIN_JWT_TOKEN>
```

响应：
```json
{
  "global": {
    "limit": 100,
    "current": 15
  },
  "user": {
    "limit": 10,
    "active_users": 8
  },
  "user_model": {
    "limit": 3,
    "active_user_models": 12
  }
}
```

## WebSocket 接口

### 1. 连接认证
```javascript
const socket = io('/ws/socket.io/');

// 用户认证
socket.emit('user-join', {
  auth: { token: 'Bearer_JWT_TOKEN' }
});
```

### 2. 聊天事件
```javascript
// 发送聊天消息
socket.emit('chat-message', {
  chat_id: 'conversation-uuid',
  message: {
    role: 'user',
    content: '你好'
  }
});

// 接收聊天响应
socket.on('chat-completion', (data) => {
  console.log('收到响应:', data);
});
```

### 3. 任务管理
```javascript
// 获取聊天任务
socket.emit('get-chat-task', {
  chat_id: 'conversation-uuid'
});

// 停止任务
socket.emit('stop-task', {
  task_id: 'task-uuid'
});

// 聊天完成
socket.emit('chat-completed', {
  chat_id: 'conversation-uuid',
  session_id: 'session-uuid'
});
```

### 4. 系统事件
```javascript
// 接收用户列表
socket.on('user-list', (users) => {
  console.log('在线用户:', users);
});

// 接收使用情况
socket.on('usage', (data) => {
  console.log('使用情况:', data);
});

// 错误处理
socket.on('error', (error) => {
  console.error('WebSocket错误:', error);
});

// 断开连接
socket.on('disconnect', (reason) => {
  console.log('连接断开:', reason);
});
```

## 错误响应格式

所有API错误响应都遵循统一格式：

```json
{
  "code": 400,
  "msg": "用户友好的错误信息",
  "error": "详细的错误信息（仅开发环境）"
}
```

### 常见错误码

- `400`: 客户端请求错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源未找到
- `429`: 请求频率过高
- `500`: 服务器内部错误

## 请求限制

### 1. 并发限制
- 全局并发限制：100个请求
- 单用户并发限制：10个请求
- 单用户单模型并发限制：3个请求

### 2. 速率限制
- IP级别：500次/分钟
- 用户级别：200次/分钟
- API Key级别：根据配置

### 3. 文件大小限制
- 上传文件：最大10MB
- 图片文件：最大10MB
- 音频文件：最大10MB

## SDK 支持

目前提供以下语言的SDK：
- JavaScript/TypeScript
- Python
- Go
- Java

SDK使用示例：

### JavaScript
```javascript
import { TopAIClient } from '@topai/client';

const client = new TopAIClient({
  apiKey: 'your-api-key',
  baseURL: 'https://api.topai.com'
});

// 聊天
const response = await client.chat.completions.create({
  model: 'gpt-3.5-turbo',
  messages: [{ role: 'user', content: 'Hello!' }]
});
```

### Python
```python
from topai import TopAI

client = TopAI(api_key="your-api-key")

response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

---

**文档版本**: v2.0  
**更新时间**: 2024-12-28  
**维护人员**: TopAI开发团队 