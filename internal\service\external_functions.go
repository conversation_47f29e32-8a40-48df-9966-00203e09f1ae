package service

import (
	"context"
	"crypto/ecdsa"
	"math/big"

	"topnetwork.ai/topai/chat-webserver/utils/chain"
)

// 外部函数变量，可以在测试时替换为mock函数
var DefaultUtilChainFunc = UtilChainFunc{
	GetLatestBlockNumberFunc:    chain.GetLatestBlockNumber,
	GetErc20ContractBalanceFunc: chain.GetErc20ContractBalance,
	TransferErc20Func:           chain.TransferErc20,
	ApproveFunc:                 chain.Approve,
	DepositBalanceFunc:          chain.DepositBalance,
	GetTransactionByHashFunc:    chain.GetTransactionByHash,
	CheckRegisteNodeFunc:        chain.CheckRegisteNode,
	RegisterNodeFunc:            chain.RegisterNode,
	NodeVoteFunc:                chain.NodeVote,
	ReportModelCostFunc:         chain.ReportModelCost,
	GetAiModelsListFunc:         chain.GetAiModelsList,
	GetDepositBalanceFunc:       chain.GetDepositBalance,
}

// 函数类型定义
type UtilChainFunc struct {
	GetLatestBlockNumberFunc    func(ctx context.Context, chainUrl string) (uint64, error)
	GetErc20ContractBalanceFunc func(ctx context.Context, chainUrl, contractAddress, publicAddress string, blockNumber uint64) (*big.Int, error)
	TransferErc20Func           func(ctx context.Context, chainUrl, contractAddress string, privateKey *ecdsa.PrivateKey, toAddress string, amount *big.Int) (string, error)
	ApproveFunc                 func(ctx context.Context, chainUrl, contractAddress string, privateKey *ecdsa.PrivateKey, spenderAddress string, amount *big.Int) (string, error)
	DepositBalanceFunc          func(ctx context.Context, chainUrl, depositAddress string, privateKey *ecdsa.PrivateKey, amount *big.Int) (string, error)
	GetTransactionByHashFunc    func(ctx context.Context, chainUrl, txHash string) (*chain.TransactionStatus, error)
	CheckRegisteNodeFunc        func(ctx context.Context, chainUrl, nodeRegisterAddress, address string) (bool, error)
	RegisterNodeFunc            func(ctx context.Context, chainUrl, nodeRegisterAddress string, privateKey *ecdsa.PrivateKey, name string) (string, error)
	NodeVoteFunc                func(ctx context.Context, chainUrl, nodeRegisterAddress string, vote bool) (string, error)
	ReportModelCostFunc         func(ctx context.Context, chainUrl, aiWorkerAddress, publicAddress string, chainModelId uint, sessionId, epochId uint, inputTokens, outputTokens *big.Int) (string, error)
	GetAiModelsListFunc         func(ctx context.Context, chainUrl, aiModelsAddress string) ([]*chain.AIModelInfo, error)
	GetDepositBalanceFunc       func(ctx context.Context, chainUrl, depositContractAddress, userAddress string, blockNumber uint64) (*chain.UserDepositBalance, error)
}
