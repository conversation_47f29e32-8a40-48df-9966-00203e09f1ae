---
description: 
globs: 
alwaysApply: false
---
---
description: <PERSON><PERSON><PERSON>ling and Recovery Patterns
globs: **/*.go
alwaysApply: false
---

When implementing error handling:

1. Error Types
- Create domain-specific error types
- Include context and metadata
- Support error wrapping
- Implement error hierarchies

Required error structure:
```go
type Error struct {
    Code    ErrorCode
    Message string
    Op      string
    Err     error
    Meta    map[string]interface{}
}

func (e *Error) Error() string {
    return fmt.Sprintf("%s: %s", e.Op, e.Message)
}

func (e *Error) Unwrap() error {
    return e.Err
}

func IsNotFound(err error) bool {
    var e *Error
    return errors.As(err, &e) && e.Code == NotFound
}
```

2. Recovery Mechanisms
- Implement panic recovery middleware
- Log recovery details
- Clean up resources on panic
- Restore system state

Required recovery pattern:
```go
func RecoverMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        defer func() {
            if err := recover(); err != nil {
                stack := debug.Stack()
                log.Printf("panic: %v\n%s", err, stack)
                http.Error(w, "Internal Server Error", http.StatusInternalServerError)
            }
        }()
        next.ServeHTTP(w, r)
    })
}
```

3. Retry Logic
- Implement exponential backoff
- Handle different error types differently
- Set proper timeout limits
- Include circuit breaker pattern

4. Error Reporting
- Include stack traces
- Add context information
- Support structured logging
- Implement error aggregation

5. Client Error Handling
- Provide clear error messages
- Include error codes
- Support internationalization
- Handle validation errors

6. Transaction Management
- Implement proper rollback
- Handle distributed transactions
- Maintain data consistency
- Support compensation actions
