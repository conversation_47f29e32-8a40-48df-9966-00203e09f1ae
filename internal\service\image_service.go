package service

import (
	"context"
	"strconv"

	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type ImageService struct {
	db  *repository.DB
	log *logger.ModuleLogger
}

func NewImageService(ctx context.Context, db *repository.DB) *ImageService {
	return &ImageService{
		db:  db,
		log: logger.GetLogger("image-service"),
	}
}

func (s *ImageService) GetImageConfig(ctx context.Context) (*ImageConfig, error) {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, imageConfigCategory)
	if err != nil {
		return nil, err
	}
	config := &ImageConfig{}
	for _, conf := range configList {
		switch conf.Key {
		case ImageConfigModel:
			config.Model = conf.Value
		case ImageConfigEngine:
			config.Engine = conf.Value
		// case ImageConfigConfig:
		// 	config.Config = conf.Value
		case ImageConfigMaxTextLength:
			config.MaxTextLength, err = strconv.Atoi(conf.Value)
			if err != nil {
				return nil, err
			}
		case ImageConfigMaxImageSize:
			config.MaxImageSize, err = strconv.Atoi(conf.Value)
			if err != nil {
				return nil, err
			}
		case ImageConfigEnable:
			config.ENABLE, err = strconv.ParseBool(conf.Value)
			if err != nil {
				return nil, err
			}
		}
	}
	return config, nil
}

func (s *ImageService) GetImageBaseModels(ctx context.Context) ([]*ImageModel, error) {
	// 先写死
	modelExts, err := s.db.TopaiModelExt.GetOnlineAll(ctx)
	if err != nil {
		return nil, err
	}
	modelIds := make([]uint, 0, len(modelExts))
	for _, model := range modelExts {
		modelIds = append(modelIds, model.TopaiModelId)
	}
	models, err := s.db.TopaiModel.GetByIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}
	modelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range models {
		modelMap[model.Id] = model
	}

	imageModels := []*ImageModel{}
	for _, ext := range modelExts {
		model, ok := modelMap[ext.TopaiModelId]
		if !ok {
			continue
		}
		if model.ModelType == repository.RemoterModelTypeTTI.String() {
			imageModels = append(imageModels, &ImageModel{
				ID:      ext.Id,
				ModelID: ext.ModelId,
				Engine:  "",
				Params:  nil,
			})
		}
	}
	return imageModels, nil
}

func (s *ImageService) UpdateImageConfig(ctx context.Context, config *ImageConfig) error {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, imageConfigCategory)
	if err != nil {
		return err
	}

	configMap := map[string]*repository.SystemConfig{}
	for _, conf := range configList {
		s.log.Info("conf:", zap.Any("conf", *conf))
		configMap[conf.Key] = conf
	}

	updateList := []*repository.SystemConfig{}
	if conf, ok := configMap[ImageConfigModel]; ok {
		if config.Model != conf.Value {
			conf.Value = config.Model
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: imageConfigCategory,
			Key:      ImageConfigModel,
			Value:    config.Model,
		})
	}

	if conf, ok := configMap[ImageConfigEngine]; ok {
		if config.Engine != conf.Value {
			conf.Value = config.Engine
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: imageConfigCategory,
			Key:      ImageConfigEngine,
			Value:    config.Engine,
		})
	}

	// if conf, ok := configMap[ImageConfigConfig]; ok {
	// 	if config.Config != conf.Value {
	// 		conf.Value = config.Config
	// 		updateList = append(updateList, conf)
	// 	}
	// } else {
	// 	updateList = append(updateList, &repository.SystemConfig{
	// 		Category: imageConfigCategory,
	// 		Key:      ImageConfigConfig,
	// 		Value:    config.Config,
	// 	})
	// }

	if conf, ok := configMap[ImageConfigMaxTextLength]; ok {
		if strconv.Itoa(config.MaxTextLength) != conf.Value {
			conf.Value = strconv.Itoa(config.MaxTextLength)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: imageConfigCategory,
			Key:      ImageConfigMaxTextLength,
			Value:    strconv.Itoa(config.MaxTextLength),
		})
	}

	if conf, ok := configMap[ImageConfigMaxImageSize]; ok {
		if strconv.Itoa(config.MaxImageSize) != conf.Value {
			conf.Value = strconv.Itoa(config.MaxImageSize)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: imageConfigCategory,
			Key:      ImageConfigMaxImageSize,
			Value:    strconv.Itoa(config.MaxImageSize),
		})
	}

	s.log.Info("config:", zap.Any("config", configMap[ImageConfigEnable]))
	if conf, ok := configMap[ImageConfigEnable]; ok {
		if config.ENABLE != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: imageConfigCategory,
			Key:      ImageConfigEnable,
			Value:    strconv.FormatBool(config.ENABLE),
		})
	}

	if len(updateList) == 0 {
		return nil
	}

	return s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		for _, conf := range updateList {
			s.log.Info("conf:", zap.Any("conf", *conf))
			if conf.ID == 0 {
				err = db.SystemConfig.Create(ctx, conf)
			} else {
				err = db.SystemConfig.UpdateByCategoryAndKey(ctx, conf.Category, conf.Key, conf.Value)
			}
			if err != nil {
				return err
			}
		}
		return nil
	})
}
