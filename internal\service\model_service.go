package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"math/big"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sashabaranov/go-openai"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

type chatInterface interface {
	ChatLLMStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (<-chan *openai.ChatCompletionStreamResponse, <-chan error, error)
	ChatLLMNonStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (*openai.ChatCompletionResponse, error)
	ChatASR(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.AudioRequest) (string, error)
	ChatTTI(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ImageRequest) ([]byte, error)
	ChatTTS(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.CreateSpeechRequest) ([]byte, error)
}

type giftBalance struct {
	FromUserID uint
	ToUserID   uint
	Amount     *big.Int
	errCh      chan error
}

type ModelService struct {
	log                  *logger.ModuleLogger
	db                   *repository.DB
	chatProvider         map[repository.RemoterModelProvider]chatInterface
	openaiProviderConfig map[repository.RemoterModelProvider]*config.Openai
	stop                 chan struct{}
	usageIdCh            chan<- uint
	companyUserID        uint
	fileOP               FileCommon
}

var _ ModelApi = &ModelService{}
var defaultProvider = repository.RemoterModelProviderTopAi

func NewModelService(ctx context.Context, db *repository.DB, serviceConfig *config.ServiceConfig, usageIdCh chan<- uint, fileOP FileCommon) *ModelService {
	chatProvider := make(map[repository.RemoterModelProvider]chatInterface)
	openaiProviderConfig := make(map[repository.RemoterModelProvider]*config.Openai)
	chatProvider[repository.RemoterModelProviderTopAi] = newTopaiManage(ctx, serviceConfig.TopAi, false, serviceConfig.TopChain)
	for provider, conf := range serviceConfig.Openai {
		chatProvider[repository.RemoterModelProvider(provider)] = newOpenaiManager(ctx, conf)
		openaiProviderConfig[repository.RemoterModelProvider(provider)] = &conf
	}

	return &ModelService{
		log:                  logger.GetLogger("model-service"),
		db:                   db,
		chatProvider:         chatProvider,
		stop:                 make(chan struct{}),
		usageIdCh:            usageIdCh,
		openaiProviderConfig: openaiProviderConfig,
		companyUserID:        serviceConfig.CompanyUserID,
		fileOP:               fileOP,
	}
}

func (s *ModelService) start(ctx context.Context) error {
	// if s.chatProvider[repository.RemoterModelProviderTopAi] != nil {
	// 	s.chatProvider[repository.RemoterModelProviderTopAi].(*topaiManage).start()
	// }

	// 启动之前，拉取已确认未结算的usage
	usageList, err := s.db.UserChatUsage.GetUnconfirmed(ctx)
	if err != nil {
		s.log.Fatal("get confirmed usage failed", zap.Error(err))
	}
	for _, usage := range usageList {
		go func(usage *repository.UserChatUsage) {
			err := s.calculateUsage(ctx, usage)
			if err != nil {
				s.log.Error("calculate usage failed", zap.Error(err))
			}
		}(usage)
	}
	return nil
}

func (s *ModelService) GetAllBaseLLMModels(ctx context.Context) ([]*BaseModel, error) {
	modelExts, err := s.db.TopaiModelExt.GetOnlineAll(ctx)
	if err != nil {
		return nil, err
	}
	modelIds := make([]uint, 0, len(modelExts))
	for _, model := range modelExts {
		modelIds = append(modelIds, model.TopaiModelId)
	}
	models, err := s.db.TopaiModel.GetByIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}

	modelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range models {
		modelMap[model.Id] = model
	}

	baseModels := make([]*BaseModel, 0, len(modelExts))
	for _, ext := range modelExts {
		model, ok := modelMap[ext.TopaiModelId]
		if !ok {
			continue
		}

		if model.ModelType != string(repository.RemoterModelTypeLLM) {
			continue
		}

		baseModels = append(baseModels, &BaseModel{
			ModelID:   ext.ModelId,
			Name:      ext.ModelId,
			ModelType: model.ModelType,
			Remote: &RemoteModel{
				ModelID:             model.ModelName,
				Active:              true,
				ContextWindow:       1000,
				Created:             1715145600,
				MaxCompletionTokens: 1000,
				Object:              "chat.completions",
				OwnedBy:             "topai",
				PublicApps:          nil,
			},
			OwnedBy:             "topai",
			PublicApps:          nil,
			URLIdx:              0,
			Active:              true,
			ContextWindow:       1000,
			CreatedAt:           1715145600,
			MaxCompletionTokens: 1000,
			Object:              "chat.completions",
		})
	}

	return baseModels, nil
}

func (s *ModelService) GetAllBaseLLMModelsConfig(ctx context.Context) ([]*ModelConfigIO, error) {
	models, err := s.db.LLMModelConfig.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	modelsIO := make([]*ModelConfigIO, len(models))
	for i, model := range models {
		modelsIO[i] = &ModelConfigIO{
			ID:            model.ID,
			ModelID:       model.ModelID,
			BaseModelID:   model.BaseModelID,
			Name:          model.Name,
			Params:        nil,
			Meta:          nil,
			AccessControl: nil,
			IsActive:      model.IsActive,
			CreatedAt:     model.CreatedAt.Unix(),
			UpdatedAt:     model.UpdatedAt.Unix(),
		}
	}

	return modelsIO, nil
}

func (s *ModelService) CreateLLMModelConfig(ctx context.Context, req *ModelConfigIO) (*ModelConfigIO, error) {
	model, err := s.db.LLMModelConfig.GetByModelID(ctx, req.ModelID)
	if err != nil {
		return nil, err
	}
	if model != nil {
		return nil, errors.New("model already exists")
	}

	model = &repository.LLMModelConfig{
		ModelID:     req.ModelID,
		BaseModelID: req.BaseModelID,
		Name:        req.Name,
		IsActive:    req.IsActive,
	}

	err = s.db.LLMModelConfig.Create(ctx, model)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (s *ModelService) GetLLMModelConfig(ctx context.Context, modelID string) (*ModelConfigIO, error) {
	model, err := s.db.LLMModelConfig.GetByModelID(ctx, modelID)
	if err != nil {
		return nil, err
	}
	if model == nil {
		return nil, errors.New("model not found")
	}

	return &ModelConfigIO{
		ID:            model.ID,
		ModelID:       model.ModelID,
		BaseModelID:   model.BaseModelID,
		Name:          model.Name,
		Params:        nil,
		Meta:          nil,
		AccessControl: nil,
		IsActive:      model.IsActive,
		CreatedAt:     model.CreatedAt.Unix(),
		UpdatedAt:     model.UpdatedAt.Unix(),
	}, nil
}

func (s *ModelService) UpdateLLMModelConfig(ctx context.Context, req *ModelConfigIO) (*ModelConfigIO, error) {
	model, err := s.db.LLMModelConfig.GetByModelID(ctx, req.ModelID)
	if err != nil {
		return nil, err
	}
	if model == nil {
		return nil, errors.New("model not found")
	}

	model.Name = req.Name
	model.IsActive = req.IsActive

	err = s.db.LLMModelConfig.Update(ctx, model)
	if err != nil {
		return nil, err
	}
	return req, nil
}

func (s *ModelService) GetAvailableLLMModels(ctx context.Context) ([]*AvailableModel, error) {
	// 先获取模型列表
	BaseModels, err := s.GetAllBaseLLMModels(ctx)
	if err != nil {
		return nil, err
	}

	if len(BaseModels) == 0 {
		return nil, errors.New("no base models found")
	}

	// 获取模型配置
	ModelConfigs, err := s.GetAllBaseLLMModelsConfig(ctx)
	if err != nil {
		return nil, err
	}
	modelConfigsMap := make(map[string]*ModelConfigIO)
	for _, modelConfig := range ModelConfigs {
		modelConfigsMap[modelConfig.ModelID] = modelConfig
	}
	modelIds := make([]string, 0, len(BaseModels))
	for _, model := range BaseModels {
		modelIds = append(modelIds, model.ModelID)
	}
	modelCosts, err := s.db.TopaiModelCost.GetByModelIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}
	modelCostsMap := make(map[string]*repository.TopaiModelCost)
	for _, modelCost := range modelCosts {
		modelCostsMap[modelCost.ModelId] = modelCost
	}

	availableModels := make([]*AvailableModel, 0, len(BaseModels))
	for _, baseModel := range BaseModels {
		conf, ok := modelConfigsMap[baseModel.ModelID]
		if ok && !conf.IsActive {
			continue
		}
		totalTokens := "0"
		if modelCost, ok := modelCostsMap[baseModel.ModelID]; ok {
			totalTokens = modelCost.TotalTokens
		}
		availableModels = append(availableModels, &AvailableModel{
			ModelID:             baseModel.ModelID,
			ModelType:           baseModel.ModelType,
			Name:                baseModel.Name,
			Remote:              baseModel.Remote,
			Actions:             []interface{}{},
			Created:             baseModel.CreatedAt,
			OwnedBy:             baseModel.OwnedBy,
			Object:              baseModel.Object,
			ContextWindow:       baseModel.ContextWindow,
			MaxCompletionTokens: baseModel.MaxCompletionTokens,
			PublicApps:          baseModel.PublicApps,
			Active:              true,
			Info:                conf,
			TotalTokens:         totalTokens,
		})

	}
	sort.Slice(availableModels, func(i, j int) bool {
		if len(availableModels[i].TotalTokens) == len(availableModels[j].TotalTokens) {
			return availableModels[i].TotalTokens > availableModels[j].TotalTokens
		}
		return len(availableModels[i].TotalTokens) > len(availableModels[j].TotalTokens)
	})

	return availableModels, nil
}

func (s *ModelService) ToggleLLMModelConfig(ctx context.Context, modelID string) (*ModelConfigIO, error) {
	model, err := s.db.LLMModelConfig.GetByModelID(ctx, modelID)
	if err != nil {
		return nil, err
	}
	if model == nil {
		return nil, errors.New("model not found")
	}

	model.IsActive = !model.IsActive
	err = s.db.LLMModelConfig.Update(ctx, model)
	if err != nil {
		return nil, err
	}

	return &ModelConfigIO{
		ID:            model.ID,
		ModelID:       model.ModelID,
		BaseModelID:   model.BaseModelID,
		Name:          model.Name,
		IsActive:      model.IsActive,
		CreatedAt:     model.CreatedAt.Unix(),
		UpdatedAt:     model.UpdatedAt.Unix(),
		Params:        nil,
		Meta:          nil,
		AccessControl: nil,
	}, nil
}

var DEFAULT_RAG_TEMPLATE = `### Task:
Respond to the user query using the provided context, incorporating inline citations in the format [id] **only when the <source> tag includes an explicit id attribute** (e.g., <source id="1">).

### Guidelines:
- If you don't know the answer, clearly state that.
- If uncertain, ask the user for clarification.
- Respond in the same language as the user's query.
- If the context is unreadable or of poor quality, inform the user and provide the best possible answer.
- If the answer isn't present in the context but you possess the knowledge, explain this to the user and provide the answer using your own understanding.
- **Only include inline citations using [id] (e.g., [1], [2]) when the <source> tag includes an id attribute.**
- Do not cite if the <source> tag does not contain an id attribute.
- Do not use XML tags in your response.
- Ensure citations are concise and directly related to the information provided.

### Example of Citation:
If the user asks about a specific topic and the information is found in a source with a provided id attribute, the response should include the citation like in the following example:
* "According to the study, the proposed method increases efficiency by 20% [1]."

### Output:
Provide a clear and direct response to the user's query, including inline citations in the format [id] only when the <source> tag with id attribute is present in the context.

<context>
{{CONTEXT}}
</context>

<user_query>
{{QUERY}}
</user_query>
`

func (s *ModelService) StartChatLLMStream(ctx context.Context, req *CreateChatCompletionRequest) (<-chan string, <-chan error, error) {
	err := s.checkCompanyLimit(ctx, req.ModelItem.ModelID)
	if err != nil {
		s.log.Error("check company limit failed", zap.Error(err))
		return nil, nil, api.NewClientError("limit exceeded, please contact support or try again later")
	}
	msg, err := s.generateOpenaiChatMsg(ctx, req)
	if err != nil {
		return nil, nil, err
	}
	chatUsage := &repository.UserChatUsage{
		UserID:                  req.User.ID,
		ConversationUUID:        req.ChatID,
		ConversationMessageUUID: req.MsgID,
		ModelID:                 req.ModelItem.ModelID,
		ModelType:               "llm",
		UsedType:                "chat",
	}
	respCh, errCh, err := s.chatLLMStream(ctx, chatUsage, &ChatLLMByApiRequest{
		Messages: msg,
		Stream:   true,
	})
	if err != nil {
		return nil, nil, err
	}

	contentCh := make(chan string, 1)
	go func() {
		defer close(contentCh)
		for resp := range respCh {
			if len(resp.Choices) > 0 {
				contentCh <- resp.Choices[0].Delta.Content
			}
		}
	}()

	return contentCh, errCh, nil
}

// 检查文件资源，生成引入
func (s *ModelService) generateOpenaiChatMsg(ctx context.Context, req *CreateChatCompletionRequest) ([]ChatLLMByApiMessage, error) {
	msg := make([]ChatLLMByApiMessage, 0, len(req.Messages))
	userContext := ""
	for _, message := range req.Messages {
		msg = append(msg, ChatLLMByApiMessage{
			Role:    message.Role,
			Content: message.Content,
		})
		if message.Role == openai.ChatMessageRoleUser {
			userContext = message.Content
		}
	}

	if len(req.Files) > 0 {
		sourceStr := ""
		for idx, file := range req.Files {
			sourceStr += fmt.Sprintf("<source id=\"%d\">%s</source>\n", idx, file.File.Data.Content)
		}

		sourceStr = strings.ReplaceAll(DEFAULT_RAG_TEMPLATE, "{{CONTEXT}}", sourceStr)
		sourceStr = strings.ReplaceAll(sourceStr, "{{QUERY}}", userContext)

		if msg[0].Role == openai.ChatMessageRoleSystem {
			msg[0].Content = fmt.Sprintf("%s\n%s", sourceStr, msg[0].Content)
		} else {
			msg = append([]ChatLLMByApiMessage{
				{Role: openai.ChatMessageRoleSystem, Content: sourceStr},
			}, msg...)
		}
	}

	return msg, nil
}

func (s *ModelService) GenerateTitle(ctx context.Context, req *CreateChatCompletionRequest) (string, error) {
	if len(req.Messages) > 2 {
		return "", nil
	}

	if req.Messages[0].Role != openai.ChatMessageRoleSystem && len(req.Messages) != 1 {
		return "", nil
	}

	userMsg := req.Messages[len(req.Messages)-1]
	systemPrompt := []string{
		"You are a helpful assistant that generates titles for conversations.",
		"The title should be a single sentence that captures the essence of the conversation.",
		"Keep under 20 characters, no punctuation. Directly return the title only.",
	}

	chatMsgs := []ChatLLMByApiMessage{
		{Role: openai.ChatMessageRoleSystem, Content: strings.Join(systemPrompt, "\n")},
		{Role: openai.ChatMessageRoleUser, Content: userMsg.Content},
	}
	chatUsage := &repository.UserChatUsage{
		UserID:                  req.User.ID,
		ConversationUUID:        req.ChatID,
		ConversationMessageUUID: req.MsgID,
		ModelID:                 req.ModelItem.ModelID,
		ModelType:               "llm",
		UsedType:                "chat_title",
	}
	openaiReq := &ChatLLMByApiRequest{
		Model:    req.ModelItem.ModelID,
		Messages: chatMsgs,
		Stream:   false,
	}
	resp, err := s.chatLLMNonStream(ctx, chatUsage, openaiReq)
	if err != nil {
		return "", err
	}
	content := ""
	if len(resp.Choices) > 0 {
		content = resp.Choices[0].Message.Content
	}

	if req.ChatID != "" && content != "" {
		err = s.db.Conversation.UpdateTitleByUUID(ctx, req.ChatID, content)
		if err != nil {
			return "", err
		}
	}
	return content, nil
}

func (s *ModelService) chatLLMStream(ctx context.Context, chatUsage *repository.UserChatUsage, req *ChatLLMByApiRequest) (<-chan ChatLLMStreamByApiResponse, <-chan error, error) {
	conf, err := s.db.LLMModelConfig.GetByModelID(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, nil, err
	}
	if conf != nil && !conf.IsActive {
		return nil, nil, api.NewClientError("model not found")
	}

	provider := defaultProvider
	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, nil, err
	}
	if ext == nil {
		return nil, nil, api.NewClientError("model not found")
	}
	if req.MaxTokens > 0 {
		maxLength := ext.ContextLength
		if maxLength == 0 {
			maxLength = 32000
		}
		if req.MaxTokens > uint64(maxLength) {
			return nil, nil, api.NewClientError("max_tokens must be less than context_length")
		}
	}
	topaiModel, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, nil, err
	}
	if topaiModel == nil {
		return nil, nil, api.NewClientError("model not found")
	}
	if topaiModel.ModelType != repository.RemoterModelTypeLLM.String() {
		return nil, nil, api.NewClientError("model type not supported")
	}

	remoterModel, err := s.db.RemoterModelInfo.GetByModelName(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, nil, err
	}
	if remoterModel != nil && remoterModel.Id != 0 {
		provider = remoterModel.Provider
	} else {
		remoterModel = &repository.RemoterModelInfo{
			Id:       topaiModel.ChainModelId,
			Provider: provider,
		}
	}

	chatProvider, ok := s.chatProvider[provider]
	if !ok {
		return nil, nil, errors.New("chat provider not found")
	}
	openaiMsgs := make([]openai.ChatCompletionMessage, 0, len(req.Messages))
	for _, msg := range req.Messages {
		openaiMsgs = append(openaiMsgs, openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}
	openaiReq := &openai.ChatCompletionRequest{
		Model:            remoterModel.ProviderModelName,
		Messages:         openaiMsgs,
		Stream:           true,
		Temperature:      req.Temperature,
		MaxTokens:        int(req.MaxTokens),
		TopP:             req.TopP,
		Stop:             req.Stop,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		Seed:             req.Seed,
		User:             req.User,
	}
	respCh, errCh, err := chatProvider.ChatLLMStream(ctx, remoterModel, openaiReq)
	if err != nil {
		return nil, nil, err
	}
	contentCh := make(chan ChatLLMStreamByApiResponse, 1)
	newErrCh := make(chan error, 1)
	chatUsage.UUID = uuid.New().String()
	now := time.Now()
	chatUsage.CreatedAt = &now
	go func() {
		defer close(contentCh)
		defer close(newErrCh)
		var lastUsage *openai.Usage
		var lastId string
		openaiResp := ChatLLMStreamByApiResponse{
			ID:      chatUsage.UUID,
			Object:  "chat.completion.chunk",
			Created: now.Unix(),
			Model:   chatUsage.ModelID,
			Choices: make([]*ChatLLMStreamByApiResponseChoice, 0),
		}
		for resp := range respCh {
			openaiResp.Choices = []*ChatLLMStreamByApiResponseChoice{
				{
					Index: 0,
					Delta: ChatLLMByApiMessage{
						Role:    resp.Choices[0].Delta.Role,
						Content: resp.Choices[0].Delta.Content,
					},
					FinishReason: string(resp.Choices[0].FinishReason),
				},
			}
			contentCh <- openaiResp
			if resp.Usage != nil {
				lastUsage = resp.Usage
			}
			if resp.ID != "" {
				lastId = resp.ID
			}
		}
		select {
		case err := <-errCh:
			s.log.Error("chat llm error", zap.Error(err))
			newErrCh <- err
		default:
		}

		chatUsage.InputPrice = topaiModel.InputPrice
		chatUsage.OutputPrice = topaiModel.OutputPrice
		chatUsage.Platform = provider.String()
		chatUsage.PlatformId = lastId

		if lastUsage != nil {
			s.log.Info("usage", zap.Any("usage", lastUsage))
			chatUsage.InputTokens = strconv.Itoa(lastUsage.PromptTokens)
			chatUsage.OutputTokens = strconv.Itoa(lastUsage.CompletionTokens)
		}
		// 保存到数据库
		err = s.db.UserChatUsage.Create(ctx, chatUsage)
		if err != nil {
			s.log.Error("save usage failed", zap.Error(err))
		}

		err = s.calculateUsage(ctx, chatUsage)
		if err != nil {
			s.log.Error("calculate usage failed", zap.Error(err))
		}

	}()
	return contentCh, newErrCh, nil
}

func (s *ModelService) chatLLMNonStream(ctx context.Context, chatUsage *repository.UserChatUsage, req *ChatLLMByApiRequest) (*ChatLLMNonStreamByApiResponse, error) {
	conf, err := s.db.LLMModelConfig.GetByModelID(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if conf != nil && !conf.IsActive {
		return nil, api.NewClientError("model not found")
	}

	provider := defaultProvider
	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if ext == nil {
		return nil, api.NewClientError("model not found")
	}
	topaiModel, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if topaiModel == nil {
		return nil, api.NewClientError("model not found")
	}
	if topaiModel.ModelType != repository.RemoterModelTypeLLM.String() {
		return nil, api.NewClientError("model type not supported")
	}

	remoterModel, err := s.db.RemoterModelInfo.GetByModelName(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if remoterModel != nil && remoterModel.Id != 0 {
		provider = remoterModel.Provider
	} else {
		remoterModel = &repository.RemoterModelInfo{
			Id:       topaiModel.ChainModelId,
			Provider: provider,
		}
	}

	chatProvider, ok := s.chatProvider[provider]
	if !ok {
		return nil, errors.New("chat provider not found")
	}
	openaiMsgs := make([]openai.ChatCompletionMessage, 0, len(req.Messages))
	for _, msg := range req.Messages {
		openaiMsgs = append(openaiMsgs, openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}
	openaiReq := openai.ChatCompletionRequest{
		Model:            remoterModel.ProviderModelName,
		Messages:         openaiMsgs,
		Stream:           false,
		Temperature:      req.Temperature,
		MaxTokens:        int(req.MaxTokens),
		TopP:             req.TopP,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		Seed:             req.Seed,
		User:             req.User,
		Stop:             req.Stop,
	}

	resp, err := chatProvider.ChatLLMNonStream(ctx, remoterModel, &openaiReq)
	if err != nil {
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, errors.New("no choices")
	}

	now := time.Now()
	chatUsage.CreatedAt = &now
	chatUsage.UUID = uuid.New().String()
	openaiResp := ChatLLMNonStreamByApiResponse{
		ID:      chatUsage.UUID,
		Object:  "chat.completion",
		Created: now.Unix(),
		Model:   chatUsage.ModelID,
		Choices: []*ChatLLMNonStreamByApiResponseChoice{
			{
				Index: 0,
				Message: ChatLLMByApiMessage{
					Role:    resp.Choices[0].Message.Role,
					Content: resp.Choices[0].Message.Content,
				},
			},
		},
	}

	chatUsage.InputPrice = topaiModel.InputPrice
	chatUsage.OutputPrice = topaiModel.OutputPrice
	chatUsage.Platform = provider.String()
	chatUsage.PlatformId = resp.ID

	s.log.Info("usage", zap.Any("usage", resp.Usage))
	chatUsage.InputTokens = strconv.Itoa(resp.Usage.PromptTokens)
	chatUsage.OutputTokens = strconv.Itoa(resp.Usage.CompletionTokens)

	// 保存到数据库
	err = s.db.UserChatUsage.Create(ctx, chatUsage)
	if err != nil {
		s.log.Error("save usage failed", zap.Error(err))
	}

	err = s.calculateUsage(ctx, chatUsage)
	if err != nil {
		s.log.Error("calculate usage failed", zap.Error(err))
	}

	return &openaiResp, nil
}

func (s *ModelService) ProcessAudioFile(ctx context.Context, user *UserBaseOP, fileInfo *FileInfoMeta) (*FileInfoData, error) {
	enable, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, audioASRConfigCategory, AudioConfigASR_ENABLE)
	if err != nil {
		return nil, err
	}
	if enable == nil || enable.Value != "true" {
		return nil, errors.New("not enabled asr")
	}
	// 第一版本只有管理员设置的全局语音转文本模型
	// 获取系统配置的语音转文本模型
	asrModel, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, audioASRConfigCategory, AudioConfigASR_MODEL)
	if err != nil {
		return nil, err
	}

	if asrModel == nil || asrModel.Value == "" {
		return nil, errors.New("not set asr model")
	}

	chatUsage := &repository.UserChatUsage{
		UserID:    user.ID,
		FileUUID:  utils.GetFileUUIDByUrl(fileInfo.SavePath),
		ModelID:   asrModel.Value,
		ModelType: "asr",
		UsedType:  "speech",
	}

	resp, err := s.chatASR(ctx, chatUsage, &ChatASRByApiRequest{
		Model:    asrModel.Value,
		FilePath: fileInfo.SavePath,
	})
	if err != nil {
		return nil, err
	}

	return &FileInfoData{
		Content: resp.Text,
	}, nil
}

func (s *ModelService) chatASR(ctx context.Context, chatUsage *repository.UserChatUsage, req *ChatASRByApiRequest) (*ChatASRByApiResponse, error) {
	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if ext == nil {
		return nil, errors.New("model not found")
	}

	topaiModel, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if topaiModel == nil {
		return nil, errors.New("model not found")
	}
	if topaiModel.ModelType != repository.RemoterModelTypeASR.String() {
		return nil, api.NewClientError("model type not supported")
	}

	provider := defaultProvider
	remoterModel, err := s.db.RemoterModelInfo.GetByModelName(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if remoterModel != nil && remoterModel.Id != 0 {
		provider = remoterModel.Provider
	} else {
		remoterModel = &repository.RemoterModelInfo{
			Id:       topaiModel.ChainModelId,
			Provider: provider,
		}
	}

	// 使用模型处理语音文件
	chatProvider, ok := s.chatProvider[provider]
	if !ok {
		return nil, errors.New("chat provider not found")
	}

	openaiReq := &openai.AudioRequest{
		Model: remoterModel.ProviderModelName,
	}

	var audioBytes []byte
	if req.FilePath != "" {
		audioBytes, err = os.ReadFile(req.FilePath)
		if err != nil {
			return nil, err
		}
		openaiReq.FilePath = req.FilePath
	} else if req.File != nil {
		audioBytes, err = io.ReadAll(req.File)
		if err != nil {
			return nil, err
		}
		openaiReq.Reader = bytes.NewReader(audioBytes)
	} else {
		return nil, errors.New("file or file path is required")
	}

	content, err := chatProvider.ChatASR(ctx, remoterModel, openaiReq)
	if err != nil {
		return nil, err
	}

	chatUsage.UUID = uuid.New().String()
	chatUsage.InputPrice = topaiModel.InputPrice
	chatUsage.OutputPrice = topaiModel.OutputPrice
	chatUsage.Platform = remoterModel.Provider.String()
	chatUsage.PlatformId = ""
	chatUsage.InputTokens = strconv.Itoa(utils.GetASRInputTokens(remoterModel.Provider.String(), audioBytes))
	chatUsage.OutputTokens = strconv.Itoa(utils.GetASROutputTokens(remoterModel.Provider.String(), content))

	err = s.db.UserChatUsage.Create(ctx, chatUsage)
	if err != nil {
		return nil, err
	}

	err = s.calculateUsage(ctx, chatUsage)
	if err != nil {
		s.log.Error("calculate usage failed", zap.Error(err))
	}

	return &ChatASRByApiResponse{
		Text: content,
	}, nil
}

var DEFAULT_IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE = `### Task:
Generate a detailed prompt for am image generation task based on the given language and context. Describe the image as if you were explaining it to someone who cannot see it. Include relevant details, colors, shapes, and any other important elements.

### Guidelines:
- Be descriptive and detailed, focusing on the most important aspects of the image.
- Avoid making assumptions or adding information not present in the image.
- Use the chat's primary language; default to English if multilingual.
- If the image is too complex, focus on the most prominent elements.

### Output:
Strictly return in JSON format:
{
    "prompt": "Your detailed description here."
}

### Chat History:
<chat_history>
{{CHAT_HISTORY}}
</chat_history>`

func (s *ModelService) GenerateImage(ctx context.Context, req *CreateChatCompletionRequest) ([]byte, error) {
	// 查看系统是否启用图片生成
	imageConfig, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, imageConfigCategory, ImageConfigEnable)
	if err != nil {
		return nil, err
	}
	if imageConfig == nil || imageConfig.Value != "true" {
		return nil, errors.New("not enabled image generation")
	}

	// 获取配置的默认模型
	imageModel, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, imageConfigCategory, ImageConfigModel)
	if err != nil {
		return nil, err
	}
	if imageModel == nil || imageModel.Value == "" {
		return nil, errors.New("not set default image model")
	}

	// 获取最后一个用户输入
	var userMsg string
	// 生成图片提示词
	history := ""
	for _, message := range req.Messages {
		if message.Role == openai.ChatMessageRoleUser {
			userMsg = message.Content
		}
		history += fmt.Sprintf("%s: %s\n", strings.ToUpper(message.Role), message.Content)
	}

	history = strings.ReplaceAll(DEFAULT_IMAGE_PROMPT_GENERATION_PROMPT_TEMPLATE, "{{CHAT_HISTORY}}", history)
	resp, err := s.chatLLMNonStream(ctx, &repository.UserChatUsage{
		UserID:                  req.User.ID,
		ConversationUUID:        req.ChatID,
		ConversationMessageUUID: req.MsgID,
		ModelID:                 req.ModelItem.ModelID,
		ModelType:               "llm",
		UsedType:                "image_prompt",
	}, &ChatLLMByApiRequest{
		Model:    req.ModelItem.ModelID,
		Messages: []ChatLLMByApiMessage{{Role: openai.ChatMessageRoleSystem, Content: history}},
		Stream:   false,
	})
	if err == nil && len(resp.Choices) > 0 {
		userMsg = resp.Choices[0].Message.Content
	} else if err != nil {
		s.log.Error("generate image prompt failed", zap.Error(err))
	}

	imageBytes, err := s.chatTTI(ctx, &repository.UserChatUsage{
		UserID:                  req.User.ID,
		ConversationUUID:        req.ChatID,
		ConversationMessageUUID: req.MsgID,
		ModelID:                 imageModel.Value,
		ModelType:               "tti",
		UsedType:                "image_generation",
	}, &ChatTTIByApiRequest{
		Prompt:         userMsg,
		ResponseFormat: openai.CreateImageResponseFormatB64JSON,
		Size:           openai.CreateImageSize512x512,
	})
	if err != nil {
		return nil, err
	}

	return imageBytes, nil
}

func (s *ModelService) chatTTI(ctx context.Context, chatUsage *repository.UserChatUsage, req *ChatTTIByApiRequest) ([]byte, error) {
	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if ext == nil {
		return nil, errors.New("model not found")
	}

	topaiModel, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if topaiModel == nil {
		return nil, errors.New("model not found")
	}
	if topaiModel.ModelType != repository.RemoterModelTypeTTI.String() {
		return nil, api.NewClientError("model type not supported")
	}
	provider := defaultProvider
	// 获取模型配置
	imageModelConfig, err := s.db.RemoterModelInfo.GetByModelName(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if imageModelConfig != nil && imageModelConfig.Id != 0 {
		provider = imageModelConfig.Provider
	} else {
		imageModelConfig = &repository.RemoterModelInfo{
			Id:       topaiModel.ChainModelId,
			Provider: provider,
		}
	}

	// 调用模型
	chatProvider, ok := s.chatProvider[provider]
	if !ok {
		return nil, errors.New("chat provider not found")
	}

	openaiReq := &openai.ImageRequest{
		Model:          imageModelConfig.ProviderModelName,
		Prompt:         req.Prompt,
		ResponseFormat: req.ResponseFormat,
		Size:           req.Size,
		N:              1,
		User:           req.User,
	}

	imageBytes, err := chatProvider.ChatTTI(ctx, imageModelConfig, openaiReq)
	if err != nil {
		return nil, err
	}

	chatUsage.UUID = uuid.New().String()
	chatUsage.InputPrice = topaiModel.InputPrice
	chatUsage.OutputPrice = topaiModel.OutputPrice
	chatUsage.Platform = provider.String()
	chatUsage.PlatformId = ""
	chatUsage.InputTokens = strconv.Itoa(utils.GetTTIInputTokens(provider.String(), req.Prompt))
	chatUsage.OutputTokens = strconv.Itoa(utils.GetTTIOutputTokens(provider.String(), req.Size))

	err = s.db.UserChatUsage.Create(ctx, chatUsage)
	if err != nil {
		return nil, err
	}

	err = s.calculateUsage(ctx, chatUsage)
	if err != nil {
		s.log.Error("calculate usage failed", zap.Error(err))
	}

	return imageBytes, nil
}

func (s *ModelService) CreateSpeech(ctx context.Context, user *UserBaseOP, chatId, msgId string) ([]byte, error) {
	conversation, err := s.db.Conversation.GetByUUID(ctx, chatId)
	if err != nil {
		return nil, err
	}
	if conversation == nil || conversation.UserID != user.ID {
		return nil, api.NewClientError("chat not found")
	}

	conversationMsg, err := s.db.ConversationMessage.GetByConversationIDAndUUID(ctx, conversation.ID, msgId)
	if err != nil {
		return nil, err
	}
	if conversationMsg == nil || conversationMsg.Content == "" {
		return nil, api.NewClientError("message not found")
	}

	audioConf, err := s.db.SystemConfig.GetByCategory(ctx, audioTTSConfigCategory)
	if err != nil {
		return nil, err
	}
	enable := false
	ttsModel := ""
	ttsVoice := ""
	for _, conf := range audioConf {
		if conf.Key == AudioConfigTTS_ENABLE {
			enable = conf.Value == "true"
		} else if conf.Key == AudioConfigTTS_MODEL {
			ttsModel = conf.Value
		} else if conf.Key == AudioConfigTTS_VOICE {
			ttsVoice = conf.Value
		}
	}

	if !enable {
		return nil, errors.New("not enabled tts")
	}

	if ttsModel == "" {
		return nil, errors.New("not set tts model")
	}

	if ttsVoice == "" {
		return nil, errors.New("not set tts voice")
	}

	chatUsage := &repository.UserChatUsage{
		UserID:                  user.ID,
		ConversationUUID:        chatId,
		ConversationMessageUUID: msgId,
		ModelID:                 ttsModel,
		ModelType:               "tts",
		UsedType:                "speech",
	}

	resp, err := s.chatTTS(ctx, chatUsage, &ChatTTSByApiRequest{
		Model:          ttsModel,
		Input:          conversationMsg.Content,
		Voice:          ttsVoice,
		ResponseFormat: "wav",
		Speed:          1,
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *ModelService) calculateUsage(ctx context.Context, chatUsage *repository.UserChatUsage) error {
	if chatUsage.Platform == repository.RemoterModelProviderOpenRouter.String() {
		if chatUsage.PlatformId == "" {
			return errors.New("openrouter usage data platform id not found, usage id: " + strconv.Itoa(int(chatUsage.ID)))
		}
		conf := s.openaiProviderConfig[repository.RemoterModelProviderOpenRouter]
		if conf == nil {
			return errors.New("openrouter provider config not found")
		}
		// 从deepinfra获取使用量
		usage, err := utils.GetOpenRouterUsage(ctx, conf.BaseUrl, conf.ApiKey, chatUsage.PlatformId)
		if err != nil {
			return err
		}
		if usage == nil || usage.Data.Id == "" {
			return errors.New("openrouter usage data not found, usage id: " + strconv.Itoa(int(chatUsage.ID)))
		}
		chatUsage.InputTokens = strconv.Itoa(usage.Data.TokensPrompt)
		chatUsage.OutputTokens = strconv.Itoa(usage.Data.TokensCompletion)
	}

	inputPrice, _ := big.NewInt(0).SetString(chatUsage.InputPrice, 10)
	outputPrice, _ := big.NewInt(0).SetString(chatUsage.OutputPrice, 10)

	inputTokens, _ := strconv.Atoi(chatUsage.InputTokens)
	outputTokens, _ := strconv.Atoi(chatUsage.OutputTokens)

	inputCost := big.NewInt(0).Mul(inputPrice, big.NewInt(int64(inputTokens)))
	outputCost := big.NewInt(0).Mul(outputPrice, big.NewInt(int64(outputTokens)))

	totalCost := big.NewInt(0).Add(inputCost, outputCost)

	chatUsage.InputCost = inputCost.String()
	chatUsage.OutputCost = outputCost.String()
	chatUsage.TotalCost = totalCost.String()
	chatUsage.Status = 1

	err := s.db.UserChatUsage.Update(ctx, chatUsage)
	if err != nil {
		return err
	}

	go func() {
		s.usageIdCh <- chatUsage.ID
	}()

	return nil

}

func (s *ModelService) chatTTS(ctx context.Context, chatUsage *repository.UserChatUsage, req *ChatTTSByApiRequest) ([]byte, error) {
	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if ext == nil {
		return nil, errors.New("model not found")
	}
	topaiModel, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if topaiModel == nil {
		return nil, errors.New("model not found")
	}
	if topaiModel.ModelType != repository.RemoterModelTypeTTS.String() {
		return nil, api.NewClientError("model type not supported")
	}
	remoterModel, err := s.db.RemoterModelInfo.GetByModelName(ctx, chatUsage.ModelID)
	if err != nil {
		return nil, err
	}
	if remoterModel == nil || remoterModel.Id == 0 || remoterModel.ModelType != repository.RemoterModelTypeTTS {
		return nil, errors.New("remoter model not found")
	}

	// 获取音色
	providerVoice := ""
	if req.Voice != "" {
		ttsVoiceRepo, err := s.db.TTSModelVoice.GetByModelNameAndName(ctx, chatUsage.ModelID, req.Voice)
		if err != nil {
			return nil, err
		}
		if ttsVoiceRepo == nil {
			return nil, errors.New("tts voice not found")
		}
		providerVoice = ttsVoiceRepo.ProviderName
	}

	chatProvider, ok := s.chatProvider[remoterModel.Provider]
	if !ok {
		return nil, errors.New("chat provider not found")
	}

	audioBytes, err := chatProvider.ChatTTS(ctx, remoterModel, &openai.CreateSpeechRequest{
		Model:          openai.SpeechModel(remoterModel.ProviderModelName),
		Input:          req.Input,
		Voice:          openai.SpeechVoice(providerVoice),
		ResponseFormat: openai.SpeechResponseFormat(req.ResponseFormat),
		Speed:          req.Speed,
	})
	if err != nil {
		return nil, err
	}
	chatUsage.UUID = uuid.New().String()
	chatUsage.InputPrice = topaiModel.InputPrice
	chatUsage.OutputPrice = topaiModel.OutputPrice
	chatUsage.Platform = remoterModel.Provider.String()
	chatUsage.PlatformId = ""
	chatUsage.InputTokens = strconv.Itoa(utils.GetTTSInputTokens(remoterModel.Provider.String(), req.Input))
	chatUsage.OutputTokens = strconv.Itoa(utils.GetTTSOutputTokens(remoterModel.Provider.String(), audioBytes))

	err = s.db.UserChatUsage.Create(ctx, chatUsage)
	if err != nil {
		return nil, err
	}

	err = s.calculateUsage(ctx, chatUsage)
	if err != nil {
		s.log.Error("calculate usage failed", zap.Error(err))
	}

	return audioBytes, nil
}

func (s *ModelService) ChatLLMStreamByApi(ctx context.Context, userID uint, appKey string, req *ChatLLMByApiRequest) (<-chan ChatLLMStreamByApiResponse, <-chan error, error) {
	// todo 此处是否需要确认用户最小余额
	balance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, nil, err
	}
	if balance == nil || balance.ID == 0 {
		return nil, nil, api.NewClientError("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(balance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return nil, nil, api.NewClientError("balance not enough")
	}
	// 是否要预扣款
	chatUsage := &repository.UserChatUsage{
		UserID:    userID,
		AppKey:    appKey,
		ModelID:   req.ModelId,
		ModelType: "llm",
		UsedType:  "api-stream-chat",
	}

	return s.chatLLMStream(ctx, chatUsage, req)
}

func (s *ModelService) ChatLLMNonStreamByApi(ctx context.Context, userID uint, appKey string, req *ChatLLMByApiRequest) (*ChatLLMNonStreamByApiResponse, error) {
	// todo 此处是否需要确认用户最小余额
	balance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, err
	}
	if balance == nil || balance.ID == 0 {
		return nil, api.NewClientError("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(balance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return nil, api.NewClientError("balance not enough")
	}
	// todo 是否要预扣款
	chatUsage := &repository.UserChatUsage{
		UserID:    userID,
		AppKey:    appKey,
		ModelID:   req.ModelId,
		ModelType: "llm",
		UsedType:  "api-nonstream-chat",
	}

	return s.chatLLMNonStream(ctx, chatUsage, req)
}

func (s *ModelService) ChatASRByApi(ctx context.Context, userID uint, appKey string, req *ChatASRByApiRequest) (*ChatASRByApiResponse, error) {
	// todo 此处是否需要确认用户最小余额
	balance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, err
	}
	if balance == nil || balance.ID == 0 {
		return nil, api.NewClientError("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(balance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return nil, api.NewClientError("balance not enough")
	}
	// todo 是否要预扣款
	chatUsage := &repository.UserChatUsage{
		UserID:    userID,
		AppKey:    appKey,
		ModelID:   req.ModelId,
		ModelType: "asr",
		UsedType:  "api-asr",
	}
	return s.chatASR(ctx, chatUsage, req)
}

func (s *ModelService) ChatTTSByApi(ctx context.Context, userID uint, appKey string, req *ChatTTSByApiRequest) ([]byte, error) {
	// todo 此处是否需要确认用户最小余额
	balance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, err
	}
	if balance == nil || balance.ID == 0 {
		return nil, api.NewClientError("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(balance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return nil, api.NewClientError("balance not enough")
	}
	// todo 是否要预扣款
	chatUsage := &repository.UserChatUsage{
		UserID:    userID,
		AppKey:    appKey,
		ModelID:   req.ModelId,
		ModelType: "tts",
		UsedType:  "api-tts",
	}

	return s.chatTTS(ctx, chatUsage, req)
}

func (s *ModelService) ChatTTIByApi(ctx context.Context, userID uint, appKey string, req *ChatTTIByApiRequest) (*ChatTTIByApiResponse, error) {
	// todo 此处是否需要确认用户最小余额
	balance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, err
	}
	if balance == nil || balance.ID == 0 {
		return nil, api.NewClientError("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(balance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return nil, api.NewClientError("balance not enough")
	}
	// todo 是否要预扣款
	chatUsage := &repository.UserChatUsage{
		UserID:    userID,
		AppKey:    appKey,
		ModelID:   req.ModelId,
		ModelType: "image",
		UsedType:  "api-image",
	}

	imageBytes, err := s.chatTTI(ctx, chatUsage, req)
	if err != nil {
		return nil, err
	}
	return &ChatTTIByApiResponse{
		Created: time.Now().Unix(),
		Data: struct {
			RevisedPrompt string `json:"revised_prompt"`
			B64Json       string `json:"b64_json"`
		}{
			RevisedPrompt: req.Prompt,
			B64Json:       base64.StdEncoding.EncodeToString(imageBytes),
		},
	}, nil
}

func (s *ModelService) StartChatASR(ctx context.Context, user *UserBaseOP, req *StartChatASRRequest) (*StartChatASRResponse, error) {
	err := s.checkCompanyLimit(ctx, req.ModelID)
	if err != nil {
		s.log.Error("check company limit failed", zap.Error(err))
		return nil, api.NewClientError("limit exceeded, please contact support or try again later")
	}

	// 上传文件
	fileInfo, err := s.fileOP.UploadFile(ctx, user, &FileUploadIP{
		Name:     req.FileName,
		Size:     req.FileSize,
		MimeType: req.MimeType,
	}, req.File, "audio", true)
	if err != nil {
		return nil, err
	}
	usage := &repository.UserChatUsage{
		UserID:    user.ID,
		ModelID:   req.ModelID,
		ModelType: "asr",
	}
	resp, err := s.chatASR(ctx, usage, &ChatASRByApiRequest{
		ModelId:  req.ModelID,
		Model:    req.ModelID,
		File:     nil,
		FilePath: fileInfo.Meta.SavePath,
	})
	if err != nil {
		return nil, err
	}
	uuid := uuid.New().String()
	// 保存记录
	err = s.db.UserChatASRRecord.Create(ctx, &repository.UserChatASRRecord{
		UUID:            uuid,
		UserID:          user.ID,
		UserChatUsageID: usage.ID,
		ModelID:         req.ModelID,
		FileUUIDs:       fileInfo.FileID,
		Content:         resp.Text,
	})
	if err != nil {
		return nil, err
	}

	return &StartChatASRResponse{
		UUID:    uuid,
		Content: resp.Text,
	}, nil
}

func (s *ModelService) StartChatTTS(ctx context.Context, user *UserBaseOP, req *StartChatTTSRequest) (*StartChatTTSResponse, error) {
	err := s.checkCompanyLimit(ctx, req.ModelID)
	if err != nil {
		s.log.Error("check company limit failed", zap.Error(err))
		return nil, api.NewClientError("limit exceeded, please contact support or try again later")
	}

	content := req.Content

	usage := &repository.UserChatUsage{
		UserID:    user.ID,
		ModelID:   req.ModelID,
		ModelType: "tts",
	}

	resp, err := s.chatTTS(ctx, usage, &ChatTTSByApiRequest{
		ModelId:        req.ModelID,
		Model:          req.ModelID,
		Input:          content,
		Voice:          req.Voice,
		Speed:          req.Speed,
		ResponseFormat: string(openai.SpeechResponseFormatWav),
	})
	if err != nil {
		return nil, err
	}
	// 上传文件
	fileInfo, err := s.fileOP.UploadFile(ctx, user, &FileUploadIP{
		Size:     int64(len(resp)),
		MimeType: "audio/wav",
	}, bytes.NewReader(resp), "audio", true)
	if err != nil {
		return nil, err
	}
	uuid := uuid.New().String()
	// 保存记录
	err = s.db.UserChatTTSRecord.Create(ctx, &repository.UserChatTTSRecord{
		UUID:            uuid,
		UserID:          user.ID,
		UserChatUsageID: usage.ID,
		ModelID:         req.ModelID,
		Voice:           req.Voice,
		Format:          string(openai.SpeechResponseFormatWav),
		Content:         content,
		FileUUIDs:       fileInfo.FileID,
	})
	if err != nil {
		return nil, err
	}

	return &StartChatTTSResponse{
		UUID: uuid,
		URL:  fileInfo.URL,
	}, nil
}

func (s *ModelService) StartChatTTI(ctx context.Context, user *UserBaseOP, req *StartChatTTIRequest) (*StartChatTTIResponse, error) {
	err := s.checkCompanyLimit(ctx, req.ModelID)
	if err != nil {
		s.log.Error("check company limit failed", zap.Error(err))
		return nil, api.NewClientError("limit exceeded, please contact support or try again later")
	}

	content := req.Content

	usage := &repository.UserChatUsage{
		UserID:    user.ID,
		ModelID:   req.ModelID,
		ModelType: "tti",
	}

	imageBytes, err := s.chatTTI(ctx, usage, &ChatTTIByApiRequest{
		ModelId:        req.ModelID,
		Model:          req.ModelID,
		Prompt:         content,
		Size:           req.Size,
		ResponseFormat: openai.CreateImageResponseFormatB64JSON,
	})
	if err != nil {
		return nil, err
	}
	// 上传文件
	fileInfo, err := s.fileOP.UploadFile(ctx, user, &FileUploadIP{
		Size:     int64(len(imageBytes)),
		MimeType: "image/png",
	}, bytes.NewReader(imageBytes), "image", true)
	if err != nil {
		return nil, err
	}
	uuid := uuid.New().String()
	// 保存记录
	err = s.db.UserChatTTIRecord.Create(ctx, &repository.UserChatTTIRecord{
		UUID:            uuid,
		UserID:          user.ID,
		UserChatUsageID: usage.ID,
		ModelID:         req.ModelID,
		Size:            req.Size,
		Format:          string(openai.CreateImageResponseFormatB64JSON),
		Content:         content,
		FileUUIDs:       fileInfo.FileID,
	})
	if err != nil {
		return nil, err
	}

	return &StartChatTTIResponse{
		UUID: uuid,
		URL:  fileInfo.URL,
	}, nil
}

func (s *ModelService) UpdateConversationContent(ctx context.Context, user *UserBaseOP, conversationUUID, msgID, content, imageUrl string) error {
	conversation, err := s.db.Conversation.GetByUUID(ctx, conversationUUID)
	if err != nil {
		return err
	}
	if conversation == nil || conversation.UserID != user.ID {
		return errors.New("conversation not found")
	}

	message, err := s.db.ConversationMessage.GetByConversationIDAndUUID(ctx, conversation.ID, msgID)
	if err != nil {
		return err
	}
	if message == nil {
		return errors.New("message not found")
	}
	fileUUID := ""
	if imageUrl != "" {
		fileUUID = utils.GetFileUUIDByUrl(imageUrl)
	}

	return s.db.ConversationMessage.UpdateContentAndDoneById(ctx, message.ID, content, fileUUID)
}

// 用户随机生成usage，并不经过真实的聊天请求，不提供给用户调用
func (s *ModelService) MockUserChatUsage(ctx context.Context, key *AppKeyOP, model *TopaiModelsInfoOP, inputTokens, outputTokens, ttft, duration *big.Int) error {
	appInfo, err := s.db.DevAppInfo.GetByUuid(ctx, key.AppUuid)
	if err != nil {
		return err
	}
	if appInfo == nil || appInfo.Id == 0 {
		return errors.New("app info not found")
	}

	inputPrice, _ := big.NewInt(0).SetString(model.InputPrice, 10)
	outputPrice, _ := big.NewInt(0).SetString(model.OutputPrice, 10)

	inputCost := big.NewInt(0).Mul(inputPrice, inputTokens)
	outputCost := big.NewInt(0).Mul(outputPrice, outputTokens)
	totalCost := big.NewInt(0).Add(inputCost, outputCost)

	usage := &repository.UserChatUsage{
		UUID:         uuid.New().String(),
		UserID:       appInfo.UserId,
		ModelID:      model.ModelId,
		ModelType:    model.ModelType,
		TotalCost:    totalCost.String(),
		InputTokens:  inputTokens.String(),
		OutputTokens: outputTokens.String(),
		Status:       1,
		AppKey:       key.Key,
		UsedType:     "chat",
		Platform:     "mock",
		PlatformId:   "mock",
		InputPrice:   inputPrice.String(),
		OutputPrice:  outputPrice.String(),
		InputCost:    inputCost.String(),
		OutputCost:   outputCost.String(),
	}
	err = s.db.UserChatUsage.Create(ctx, usage)
	if err != nil {
		return err
	}
	s.usageIdCh <- usage.ID
	return nil
}

// 检查公司账户的一些限制，主要用于聊天，比如限制公司账户的余额
func (s *ModelService) checkCompanyLimit(ctx context.Context, modelID string) error {
	userBalance, err := s.db.UserBalance.GetByUserIDAndCurrency(ctx, s.companyUserID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return err
	}
	if userBalance == nil || userBalance.ID == 0 {
		return errors.New("balance not enough")
	}
	balanceInt, _ := big.NewInt(0).SetString(userBalance.Balance, 10)
	if balanceInt.Cmp(big.NewInt(0)) < 0 {
		return errors.New("balance not enough")
	}
	return nil
}

func (s *ModelService) Close() {
	close(s.stop)
}
