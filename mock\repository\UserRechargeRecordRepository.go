// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserRechargeRecordRepository is an autogenerated mock type for the UserRechargeRecordRepository type
type UserRechargeRecordRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserRechargeRecordRepository) Create(ctx context.Context, record *repository.UserRechargeRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserRechargeRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetUnconfirmed provides a mock function with given fields: ctx
func (_m *UserRechargeRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserRechargeRecord, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUnconfirmed")
	}

	var r0 []*repository.UserRechargeRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.UserRechargeRecord, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.UserRechargeRecord); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserRechargeRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateConfirmedByID provides a mock function with given fields: ctx, id, userBalanceRecordId
func (_m *UserRechargeRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, userBalanceRecordId uint) error {
	ret := _m.Called(ctx, id, userBalanceRecordId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateConfirmedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, uint) error); ok {
		r0 = rf(ctx, id, userBalanceRecordId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateFailedByID provides a mock function with given fields: ctx, id, blockNumber
func (_m *UserRechargeRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	ret := _m.Called(ctx, id, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFailedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, uint64) error); ok {
		r0 = rf(ctx, id, blockNumber)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserRechargeRecordRepository creates a new instance of UserRechargeRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserRechargeRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserRechargeRecordRepository {
	mock := &UserRechargeRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
