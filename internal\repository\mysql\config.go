package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// 系统配置相关仓库

// systemConfigRepository 实现系统配置相关的数据库操作
type systemConfigRepository struct {
	db *gorm.DB
}

// NewSystemConfigRepository 创建系统配置仓库实例
func NewSystemConfigRepository(db *gorm.DB) repository.SystemConfigRepository {
	return &systemConfigRepository{
		db: db,
	}
}

// Get 获取配置
func (r *systemConfigRepository) Get(ctx context.Context, category, key string) (*repository.SystemConfig, error) {
	var config repository.SystemConfig
	if err := r.db.WithContext(ctx).Where("category = ? AND `key` = ?", category, key).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// GetByCategory 获取分类的所有配置
func (r *systemConfigRepository) GetByCategory(ctx context.Context, category string) ([]*repository.SystemConfig, error) {
	var configs []*repository.SystemConfig
	err := r.db.WithContext(ctx).Where("category = ?", category).Find(&configs).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return configs, nil
}

// GetByCategoryAndKey 获取分类和key的配置
func (r *systemConfigRepository) GetByCategoryAndKey(ctx context.Context, category, key string) (*repository.SystemConfig, error) {
	var config repository.SystemConfig
	if err := r.db.WithContext(ctx).Where("category = ? AND `key` = ?", category, key).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// UpdateByCategoryAndKey 更新分类和key的配置
func (r *systemConfigRepository) UpdateByCategoryAndKey(ctx context.Context, category, key string, value string) error {
	return r.db.WithContext(ctx).Table(repository.SystemConfig{}.TableName()).Where("category = ? AND `key` = ?", category, key).Update("value", value).Error
}

// Delete 删除配置
func (r *systemConfigRepository) Delete(ctx context.Context, category, key string) error {
	return r.db.WithContext(ctx).
		Where("category = ? AND `key` = ?", category, key).
		Delete(&repository.SystemConfig{}).Error
}

// Create 创建配置
func (r *systemConfigRepository) Create(ctx context.Context, config *repository.SystemConfig) error {
	return r.db.WithContext(ctx).Create(config).Error
}
