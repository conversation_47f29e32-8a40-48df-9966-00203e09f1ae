package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

// ProviderAuth 模型提供者认证中间件
func ProviderAuth(svc *service.Api) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Authorization头获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			api.Fail(c, api.CodeUnauthorized, "Authorization header required", api.NewClientError("Authorization header required"))
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			api.Fail(c, api.CodeUnauthorized, "Invalid authorization header format", api.NewClientError("Invalid authorization header format"))
			c.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")

		// 验证token并获取提供者信息
		providerInfo, err := svc.Provider.GetProviderInfoByToken(c.Request.Context(), token)
		if err != nil {
			api.Fail(c, api.CodeUnauthorized, "Invalid or expired token", api.NewClientError("Invalid or expired token"))
			c.Abort()
			return
		}

		// 将提供者信息存储到上下文中
		c.Set("provider_info", providerInfo)

		c.Next()
	}
}
