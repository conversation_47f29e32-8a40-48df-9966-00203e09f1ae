// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// LLMModelConfigRepository is an autogenerated mock type for the LLMModelConfigRepository type
type LLMModelConfigRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, model
func (_m *LLMModelConfigRepository) Create(ctx context.Context, model *repository.LLMModelConfig) error {
	ret := _m.Called(ctx, model)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.LLMModelConfig) error); ok {
		r0 = rf(ctx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAll provides a mock function with given fields: ctx
func (_m *LLMModelConfigRepository) GetAll(ctx context.Context) ([]*repository.LLMModelConfig, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.LLMModelConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.LLMModelConfig, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.LLMModelConfig); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.LLMModelConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *LLMModelConfigRepository) GetByID(ctx context.Context, id uint) (*repository.LLMModelConfig, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.LLMModelConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.LLMModelConfig, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.LLMModelConfig); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.LLMModelConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelID provides a mock function with given fields: ctx, modelID
func (_m *LLMModelConfigRepository) GetByModelID(ctx context.Context, modelID string) (*repository.LLMModelConfig, error) {
	ret := _m.Called(ctx, modelID)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelID")
	}

	var r0 *repository.LLMModelConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.LLMModelConfig, error)); ok {
		return rf(ctx, modelID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.LLMModelConfig); ok {
		r0 = rf(ctx, modelID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.LLMModelConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, model
func (_m *LLMModelConfigRepository) Update(ctx context.Context, model *repository.LLMModelConfig) error {
	ret := _m.Called(ctx, model)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.LLMModelConfig) error); ok {
		r0 = rf(ctx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewLLMModelConfigRepository creates a new instance of LLMModelConfigRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLLMModelConfigRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *LLMModelConfigRepository {
	mock := &LLMModelConfigRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
