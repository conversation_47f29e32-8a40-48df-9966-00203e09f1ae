package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// userRepository 实现用户相关的数据库操作
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓库实例
func NewUserRepository(db *gorm.DB) repository.UserRepository {
	return &userRepository{
		db: db,
	}
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(ctx context.Context, id uint) (*repository.User, error) {
	var user repository.User
	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = 0", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*repository.User, error) {
	var user repository.User
	if err := r.db.WithContext(ctx).Where("email = ? AND is_deleted = 0", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByIdentityID 根据IdentityID获取用户
func (r *userRepository) GetByIdentityID(ctx context.Context, identityID string) (*repository.User, error) {
	var user repository.User
	if err := r.db.WithContext(ctx).Where("identity_id = ? AND is_deleted = 0", identityID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// Create 创建用户
func (r *userRepository) Create(ctx context.Context, user *repository.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// Update 更新用户
func (r *userRepository) Update(ctx context.Context, user *repository.User) error {
	return r.db.WithContext(ctx).Omit("created_at", "updated_at").Save(user).Error
}

// Delete 删除用户（软删除）
func (r *userRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.User{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// Count 获取用户总数
func (r *userRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	return count, r.db.WithContext(ctx).Model(&repository.User{}).Where("is_deleted = 0").Count(&count).Error
}

// List 获取用户列表
func (r *userRepository) List(ctx context.Context, offset, limit int, order, direction string) ([]*repository.User, error) {
	var users []*repository.User
	if order != "" {
		order = order + " " + direction
	}
	err := r.db.WithContext(ctx).Where("is_deleted = 0").Offset(offset).Limit(limit).Order(order).Find(&users).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return users, nil
}

// GetAll 获取所有用户
func (r *userRepository) GetAll(ctx context.Context) ([]*repository.User, error) {
	var users []*repository.User
	err := r.db.WithContext(ctx).Where("is_deleted = 0").Find(&users).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return users, nil
}

type userChatRequestRepository struct {
	db *gorm.DB
}

func NewUserChatRequestRepository(db *gorm.DB) repository.UserChatRequestRepository {
	return &userChatRequestRepository{
		db: db,
	}
}

func (r *userChatRequestRepository) Create(ctx context.Context, request *repository.UserChatRequest) error {
	return r.db.WithContext(ctx).Create(request).Error
}

func (r *userChatRequestRepository) GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*repository.UserChatRequest, error) {
	var requests []*repository.UserChatRequest
	err := r.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", start, end).Find(&requests).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return requests, nil
}
