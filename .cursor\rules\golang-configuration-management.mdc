---
description: 
globs: 
alwaysApply: false
---
---
description: Configuration Management
globs: config/**/*.go
alwaysApply: false
---

When handling configuration:

1. Configuration Loading
- Support multiple sources
- Handle environment variables
- Support config files
- Set Default config

Required config structure:
```go

var conf *config

type config struct {
    Server Server`yaml:"server"`
    
    Database Database `yaml:"database"`
    
    Logger Logger `yaml:"logger"`
}

type Server struct {
    Port     int           `yaml:"port"`
    Timeout  time.Duration `yaml:"timeout"`
    TLS      TLSConfig    `yaml:"tls"`
}

type Database struct {
    Host     string `yaml:"host"`
    Port     int    `yaml:"port"`
    Username string `yaml:"username"`
    Password string `yaml:"password"`
    Database string `yaml:"database"`
} 

type Logger struct {
        Filename:   string `yaml:"filename"`
        MaxSize:    int `yaml:"max-size"`
        MaxBackups: int `yaml:"max-backups"`
        MaxAge:     int `yaml:"max-age"`
        Compress:   bool `yaml:"compress"`,
    }

func defaultConfig() *config {
	conf = &config{
        Server: {
            Port : 8080,
            Timeout : 30 * time.Second,
            TLS.Enabled : false,
        },
        Database: {
            Host : "localhost",
            Port : 3306,
            Username : "root",
            Password : "",
            Database : "test",
        },
        Logger : {
        Filename:   "/var/log/service.log",
        MaxSize:    100,   // Rotate after 100MB
        MaxBackups: 5,     // Keep 5 historical files
        MaxAge:     30,    // Retain for 30 days
        Compress:   true,
        },
    }

	return conf
}

func LoadConfig(path string) error {
    conf = defaultConfig()
    
    // Read file
    data, err := os.ReadFile(path)
    if err != nil {
        return fmt.Errorf("read config file: %w", err)
    }
    
    // Parse YAML
    if err := yaml.Unmarshal(data, conf); err != nil {
        return fmt.Errorf("parse config: %w", err)
    }
    
    // Validate
    if err := validate.Struct(conf); err != nil {
        return fmt.Errorf("validate config: %w", err)
    }
    
    return nil
}

func GetConfig() config {
    if conf == nil {
        panic("config not load")
    }
    return *conf
}
```

2. Secret Management
- Use secure storage
- Rotate secrets
- Handle encryption
- Implement access control

3. Dynamic Configuration
- Support hot reload
- Handle config updates
- Validate changes
- Notify components

4. Configuration Versioning
- Track config versions
- Support rollback
- Validate compatibility
- Document changes
