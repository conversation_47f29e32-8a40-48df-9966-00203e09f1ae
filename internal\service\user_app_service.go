package service

import (
	"context"

	"github.com/google/uuid"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

type UserAppService struct {
	repo *repository.DB
	log  *logger.ModuleLogger
}

func NewUserAppService(repo *repository.DB) UserAppApi {
	return &UserAppService{repo: repo,
		log: logger.GetLogger("user_app_service"),
	}
}

func (s *UserAppService) GetUserAppList(ctx context.Context, userID uint) ([]*UserAppInfoOP, error) {
	appList, err := s.repo.DevAppInfo.GetAllByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	appListOP := make([]*UserAppInfoOP, 0)
	for _, app := range appList {
		logo := ""
		if app.Logo != "" {
			file, err := s.repo.File.GetByUUID(ctx, app.Logo)
			if err != nil {
				return nil, err
			}
			logo = utils.GetFileRouter(file)
		}
		appListOP = append(appListOP, &UserAppInfoOP{
			AppId:          app.Id,
			AppUuid:        app.Uuid,
			AppName:        app.Name,
			AppAccountId:   app.AccountId,
			AppLogo:        logo,
			AppDescription: app.Description,
			AppWebsite:     app.Website,
			AppStatus:      app.Status,
			AppCreatedAt:   app.CreatedAt.Unix(),
		})
	}
	return appListOP, nil
}

func (s *UserAppService) GetUserAppDetail(ctx context.Context, userID uint, appUuid string) (*UserAppInfoOP, error) {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appUuid)
	if err != nil {
		return nil, err
	}
	if app.UserId != userID {
		return nil, api.NewClientError("app not found")
	}
	logo := ""
	if app.Logo != "" {
		file, err := s.repo.File.GetByUUID(ctx, app.Logo)
		if err != nil {
			return nil, err
		}
		logo = utils.GetFileRouter(file)
	}
	return &UserAppInfoOP{
		AppId:          app.Id,
		AppUuid:        app.Uuid,
		AppName:        app.Name,
		AppAccountId:   app.AccountId,
		AppLogo:        logo,
		AppDescription: app.Description,
		AppWebsite:     app.Website,
		AppStatus:      app.Status,
		AppCreatedAt:   app.CreatedAt.Unix(),
	}, nil
}

func (s *UserAppService) CreateUserApp(ctx context.Context, userID uint, appInfo *UserAppInfoOP) error {
	logo := utils.GetFileUUIDByUrl(appInfo.AppLogo)
	if logo != "" {
		file, err := s.repo.File.GetByUUID(ctx, logo)
		if err != nil {
			return err
		}
		if file == nil || file.UserID != userID {
			return api.NewClientError("app logo not found")
		}
	}

	app := &repository.DevAppInfo{
		UserId:      userID,
		Uuid:        uuid.New().String(),
		Name:        appInfo.AppName,
		AccountId:   appInfo.AppAccountId,
		Logo:        logo,
		Description: appInfo.AppDescription,
		Website:     appInfo.AppWebsite,
		Status:      repository.DevAppStatusNormal,
	}
	err := s.repo.DevAppInfo.Create(ctx, app)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserAppService) UpdateUserApp(ctx context.Context, userID uint, appInfo *UserAppInfoOP) error {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appInfo.AppUuid)
	if err != nil {
		return err
	}
	if app.UserId != userID {
		return api.NewClientError("app not found")
	}
	logo := utils.GetFileUUIDByUrl(appInfo.AppLogo)
	if logo != "" && logo != app.Logo {
		file, err := s.repo.File.GetByUUID(ctx, logo)
		if err != nil {
			return err
		}
		if file == nil || file.UserID != userID {
			return api.NewClientError("app logo not found")
		}
	}
	app.Name = appInfo.AppName
	app.AccountId = appInfo.AppAccountId
	app.Description = appInfo.AppDescription
	app.Website = appInfo.AppWebsite
	app.Logo = logo
	err = s.repo.DevAppInfo.Update(ctx, app)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserAppService) DeleteUserApp(ctx context.Context, userID uint, appUuid string) error {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appUuid)
	if err != nil {
		return err
	}
	if app.UserId != userID {
		return api.NewClientError("app not found")
	}

	return s.repo.DevAppInfo.Delete(ctx, app.Id)
}

func (s *UserAppService) GetUserAllAppKeyList(ctx context.Context, userID uint) ([]*AppKeyOP, error) {
	appList, err := s.repo.DevAppInfo.GetAllByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	appIds := make([]uint, 0)
	appMap := make(map[uint]*repository.DevAppInfo)
	for _, app := range appList {
		appIds = append(appIds, app.Id)
		appMap[app.Id] = app
	}
	appKeyList, err := s.repo.DevAppKey.GetByDevAppIds(ctx, appIds)
	if err != nil {
		return nil, err
	}
	appKeyListOP := make([]*AppKeyOP, 0)
	for _, appKey := range appKeyList {
		appKeyListOP = append(appKeyListOP, &AppKeyOP{
			AppId:     appMap[appKey.DevAppId].Id,
			AppUuid:   appMap[appKey.DevAppId].Uuid,
			AppName:   appMap[appKey.DevAppId].Name,
			Key:       appKey.Key[:5] + "****", //只展示前5位
			KeyName:   appKey.Name,
			CreatedAt: appKey.CreatedAt.Unix(),
		})
	}
	return appKeyListOP, nil
}

func (s *UserAppService) GetUserAppKeyList(ctx context.Context, userID uint, appUuid string) ([]*AppKeyOP, error) {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appUuid)
	if err != nil {
		return nil, err
	}
	if app.UserId != userID {
		return nil, api.NewClientError("app not found")
	}
	appKeyList, err := s.repo.DevAppKey.GetByDevAppId(ctx, app.Id)
	if err != nil {
		return nil, err
	}
	appKeyListOP := make([]*AppKeyOP, 0)
	for _, appKey := range appKeyList {
		appKeyListOP = append(appKeyListOP, &AppKeyOP{
			AppId:     app.Id,
			AppUuid:   app.Uuid,
			AppName:   app.Name,
			Key:       appKey.Key[:5] + "****", //只展示前5位
			KeyName:   appKey.Name,
			CreatedAt: appKey.CreatedAt.Unix(),
		})
	}
	return appKeyListOP, nil
}

func (s *UserAppService) CreateUserAppKey(ctx context.Context, userID uint, appUuid string, keyName string) (*AppKeyOP, error) {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appUuid)
	if err != nil {
		return nil, err
	}
	if app.UserId != userID {
		return nil, api.NewClientError("app not found")
	}

	// 检查keyname是否已经存在
	appKey, err := s.repo.DevAppKey.GetByDevAppIdAndKey(ctx, app.Id, keyName)
	if err != nil {
		return nil, err
	}
	if appKey != nil && appKey.Id > 0 {
		return nil, api.NewClientError("key name already exists")
	}

	key := uuid.New().String()

	appKey = &repository.DevAppKey{
		DevAppId: app.Id,
		UserId:   userID,
		Key:      key,
		Name:     keyName,
	}
	err = s.repo.DevAppKey.Create(ctx, appKey)
	if err != nil {
		return nil, err
	}
	return &AppKeyOP{
		UserId:    userID,
		AppId:     app.Id,
		AppUuid:   app.Uuid,
		AppName:   app.Name,
		Key:       key,
		KeyName:   keyName,
		CreatedAt: appKey.CreatedAt.Unix(),
	}, nil
}

func (s *UserAppService) DeleteUserAppKey(ctx context.Context, userID uint, appUuid string, keyName string) error {
	app, err := s.repo.DevAppInfo.GetByUuid(ctx, appUuid)
	if err != nil {
		return err
	}
	if app.UserId != userID {
		return api.NewClientError("app not found")
	}
	appKey, err := s.repo.DevAppKey.GetByDevAppIdAndName(ctx, app.Id, keyName)
	if err != nil {
		return err
	}
	if appKey == nil {
		return api.NewClientError("app key not found")
	}
	if appKey.UserId != userID {
		return api.NewClientError("app key not found")
	}
	return s.repo.DevAppKey.Delete(ctx, appKey.Id)
}

func (s *UserAppService) GetAppByApiKey(ctx context.Context, apiKey string) (*AppKeyOP, error) {
	appKey, err := s.repo.DevAppKey.GetByKey(ctx, apiKey)
	if err != nil {
		return nil, err
	}
	if appKey == nil || appKey.Id == 0 || appKey.IsDeleted == 1 {
		return nil, api.NewClientError("app key not found")
	}
	app, err := s.repo.DevAppInfo.GetByID(ctx, appKey.DevAppId)
	if err != nil {
		return nil, err
	}
	if app == nil || app.Id == 0 {
		return nil, api.NewClientError("app not found")
	}
	if app.Status != repository.DevAppStatusNormal {
		return nil, api.NewClientError("app is not normal")
	}
	return &AppKeyOP{
		UserId:    app.UserId,
		AppId:     app.Id,
		AppUuid:   app.Uuid,
		AppName:   app.Name,
		Key:       appKey.Key,
		KeyName:   appKey.Name,
		CreatedAt: appKey.CreatedAt.Unix(),
	}, nil
}
