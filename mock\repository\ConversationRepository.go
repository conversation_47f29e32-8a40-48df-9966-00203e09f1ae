// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// ConversationRepository is an autogenerated mock type for the ConversationRepository type
type ConversationRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, conversation
func (_m *ConversationRepository) Create(ctx context.Context, conversation *repository.Conversation) error {
	ret := _m.Called(ctx, conversation)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.Conversation) error); ok {
		r0 = rf(ctx, conversation)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, id
func (_m *ConversationRepository) Delete(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByUserIDOrderByUpdatedAtDesc provides a mock function with given fields: ctx, userID, limit, offset
func (_m *ConversationRepository) FindByUserIDOrderByUpdatedAtDesc(ctx context.Context, userID uint, limit int, offset int) ([]*repository.Conversation, error) {
	ret := _m.Called(ctx, userID, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDOrderByUpdatedAtDesc")
	}

	var r0 []*repository.Conversation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, int, int) ([]*repository.Conversation, error)); ok {
		return rf(ctx, userID, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, int, int) []*repository.Conversation); ok {
		r0 = rf(ctx, userID, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Conversation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, int, int) error); ok {
		r1 = rf(ctx, userID, limit, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *ConversationRepository) GetByID(ctx context.Context, id uint) (*repository.Conversation, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.Conversation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.Conversation, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.Conversation); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Conversation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUUID provides a mock function with given fields: ctx, uuid
func (_m *ConversationRepository) GetByUUID(ctx context.Context, uuid string) (*repository.Conversation, error) {
	ret := _m.Called(ctx, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByUUID")
	}

	var r0 *repository.Conversation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.Conversation, error)); ok {
		return rf(ctx, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.Conversation); ok {
		r0 = rf(ctx, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Conversation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCurrentMsgUUIDByUUID provides a mock function with given fields: ctx, uuid, currentMsgUUID
func (_m *ConversationRepository) UpdateCurrentMsgUUIDByUUID(ctx context.Context, uuid string, currentMsgUUID string) error {
	ret := _m.Called(ctx, uuid, currentMsgUUID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCurrentMsgUUIDByUUID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, uuid, currentMsgUUID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTitleByUUID provides a mock function with given fields: ctx, uuid, title
func (_m *ConversationRepository) UpdateTitleByUUID(ctx context.Context, uuid string, title string) error {
	ret := _m.Called(ctx, uuid, title)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTitleByUUID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, uuid, title)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewConversationRepository creates a new instance of ConversationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewConversationRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ConversationRepository {
	mock := &ConversationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
