#!/usr/bin/env python3
"""
OpenRouter Claude 性能测试套件
包含流式和非流式调用的性能测试用例
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    success: bool
    response_time: float
    tokens_generated: int
    error: Optional[str] = None
    first_token_time: Optional[float] = None  # 流式请求的首token时间
    completion_time: Optional[float] = None

@dataclass
class PerformanceMetrics:
    """性能指标统计"""
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    success_rate: float
    total_tokens: int
    tokens_per_second: float
    avg_first_token_time: Optional[float] = None

class OpenRouterClaudeClient:
    """OpenRouter Claude客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
    
    async def create_completion_stream(self, 
                                     messages: List[Dict[str, str]], 
                                     model: str = "anthropic/claude-3.5-sonnet",
                                     max_tokens: int = 1000) -> TestResult:
        """流式调用Claude"""
        test_name = f"stream_{model.split('/')[-1]}"
        start_time = time.time()
        first_token_time = None
        tokens_generated = 0
        full_response = ""
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": model,
                    "messages": messages,
                    "max_tokens": max_tokens,
                    "stream": True,
                    "temperature": 0.7
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        return TestResult(
                            test_name=test_name,
                            success=False,
                            response_time=time.time() - start_time,
                            tokens_generated=0,
                            error=f"HTTP {response.status}: {error_text}"
                        )
                    
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str == '[DONE]':
                                    break
                                    
                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and data['choices']:
                                        delta = data['choices'][0].get('delta', {})
                                        if 'content' in delta:
                                            if first_token_time is None:
                                                first_token_time = time.time() - start_time
                                            content = delta['content']
                                            full_response += content
                                            tokens_generated += len(content.split())
                                except json.JSONDecodeError:
                                    continue
                
                end_time = time.time()
                return TestResult(
                    test_name=test_name,
                    success=True,
                    response_time=end_time - start_time,
                    tokens_generated=tokens_generated,
                    first_token_time=first_token_time,
                    completion_time=end_time - start_time
                )
                
        except Exception as e:
            return TestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                tokens_generated=0,
                error=str(e)
            )
    
    async def create_completion_non_stream(self, 
                                          messages: List[Dict[str, str]], 
                                          model: str = "anthropic/claude-3.5-sonnet",
                                          max_tokens: int = 1000) -> TestResult:
        """非流式调用Claude"""
        test_name = f"non_stream_{model.split('/')[-1]}"
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": model,
                    "messages": messages,
                    "max_tokens": max_tokens,
                    "stream": False,
                    "temperature": 0.7
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                ) as response:
                    
                    response_data = await response.json()
                    end_time = time.time()
                    
                    if response.status != 200:
                        return TestResult(
                            test_name=test_name,
                            success=False,
                            response_time=end_time - start_time,
                            tokens_generated=0,
                            error=response_data.get('error', {}).get('message', 'Unknown error')
                        )
                    
                    # 计算生成的token数量
                    content = response_data['choices'][0]['message']['content']
                    tokens_generated = len(content.split())
                    
                    return TestResult(
                        test_name=test_name,
                        success=True,
                        response_time=end_time - start_time,
                        tokens_generated=tokens_generated
                    )
                    
        except Exception as e:
            return TestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                tokens_generated=0,
                error=str(e)
            )

class OpenRouterPerformanceTester:
    """OpenRouter性能测试器"""
    
    def __init__(self, api_key: str):
        self.client = OpenRouterClaudeClient(api_key)
        self.test_cases = [
            {
                "name": "简单问答",
                "messages": [{"role": "user", "content": "请简要介绍人工智能的发展历史。"}],
                "max_tokens": 500
            },
            {
                "name": "代码生成",
                "messages": [{"role": "user", "content": "请用Python写一个快速排序算法，并添加详细注释。"}],
                "max_tokens": 800
            },
            {
                "name": "长文本分析",
                "messages": [{"role": "user", "content": "请分析以下文本的情感倾向和主要观点：机器学习正在改变我们的世界，从医疗诊断到自动驾驶汽车，AI技术的应用越来越广泛。然而，我们也需要关注AI发展可能带来的伦理问题和就业影响。"}],
                "max_tokens": 600
            },
            {
                "name": "创意写作",
                "messages": [{"role": "user", "content": "请写一个关于未来城市的科幻短故事，大约300字。"}],
                "max_tokens": 400
            },
            {
                "name": "数据分析",
                "messages": [{"role": "user", "content": "假设我有一个电商网站的销售数据：1月销售额100万，2月销售额120万，3月销售额95万，4月销售额140万。请分析这些数据的趋势并给出建议。"}],
                "max_tokens": 500
            }
        ]
        self.models = [
            "anthropic/claude-3.5-sonnet",
            "anthropic/claude-3-haiku",
            "anthropic/claude-3-opus"
        ]
    
    async def run_single_test(self, test_case: Dict[str, Any], model: str, stream: bool) -> TestResult:
        """运行单个测试"""
        if stream:
            return await self.client.create_completion_stream(
                messages=test_case["messages"],
                model=model,
                max_tokens=test_case["max_tokens"]
            )
        else:
            return await self.client.create_completion_non_stream(
                messages=test_case["messages"],
                model=model,
                max_tokens=test_case["max_tokens"]
            )
    
    async def run_batch_test(self, test_case: Dict[str, Any], model: str, stream: bool, batch_size: int = 5) -> List[TestResult]:
        """运行批量测试"""
        logger.info(f"运行批量测试: {test_case['name']} - {model} ({'流式' if stream else '非流式'}) - 批量大小: {batch_size}")
        
        tasks = []
        for i in range(batch_size):
            task = self.run_single_test(test_case, model, stream)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(TestResult(
                    test_name=f"{'stream' if stream else 'non_stream'}_{model.split('/')[-1]}",
                    success=False,
                    response_time=0,
                    tokens_generated=0,
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def calculate_metrics(self, results: List[TestResult]) -> PerformanceMetrics:
        """计算性能指标"""
        if not results:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0)
        
        successful_results = [r for r in results if r.success]
        
        if not successful_results:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0)
        
        response_times = [r.response_time for r in successful_results]
        total_tokens = sum(r.tokens_generated for r in successful_results)
        total_time = sum(response_times)
        
        # 计算首token时间平均值（仅适用于流式请求）
        first_token_times = [r.first_token_time for r in successful_results if r.first_token_time is not None]
        avg_first_token_time = statistics.mean(first_token_times) if first_token_times else None
        
        return PerformanceMetrics(
            avg_response_time=statistics.mean(response_times),
            min_response_time=min(response_times),
            max_response_time=max(response_times),
            success_rate=len(successful_results) / len(results),
            total_tokens=total_tokens,
            tokens_per_second=total_tokens / total_time if total_time > 0 else 0,
            avg_first_token_time=avg_first_token_time
        )
    
    async def run_comprehensive_test(self):
        """运行综合性能测试"""
        logger.info("开始OpenRouter Claude性能测试")
        all_results = {}
        
        for model in self.models:
            logger.info(f"测试模型: {model}")
            model_results = {}
            
            for test_case in self.test_cases:
                logger.info(f"  测试用例: {test_case['name']}")
                
                # 流式测试
                stream_results = await self.run_batch_test(test_case, model, stream=True)
                stream_metrics = self.calculate_metrics(stream_results)
                
                # 添加延迟避免过快请求
                await asyncio.sleep(1)
                
                # 非流式测试
                non_stream_results = await self.run_batch_test(test_case, model, stream=False)
                non_stream_metrics = self.calculate_metrics(non_stream_results)
                
                model_results[test_case['name']] = {
                    'stream': {
                        'results': stream_results,
                        'metrics': stream_metrics
                    },
                    'non_stream': {
                        'results': non_stream_results,
                        'metrics': non_stream_metrics
                    }
                }
                
                # 添加延迟避免请求过于频繁
                await asyncio.sleep(2)
            
            all_results[model] = model_results
        
        return all_results
    
    def print_results(self, results: Dict[str, Any]):
        """打印测试结果"""
        print("\n" + "="*80)
        print("OpenRouter Claude 性能测试报告")
        print("="*80)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        for model, model_results in results.items():
            print(f"\n{'='*60}")
            print(f"模型: {model}")
            print(f"{'='*60}")
            
            for test_case, test_results in model_results.items():
                print(f"\n测试用例: {test_case}")
                print("-" * 40)
                
                # 流式结果
                stream_metrics = test_results['stream']['metrics']
                print(f"流式调用:")
                print(f"  平均响应时间: {stream_metrics.avg_response_time:.2f}s")
                if stream_metrics.avg_first_token_time:
                    print(f"  平均首token时间: {stream_metrics.avg_first_token_time:.2f}s")
                print(f"  成功率: {stream_metrics.success_rate:.1%}")
                print(f"  Tokens/秒: {stream_metrics.tokens_per_second:.1f}")
                
                # 非流式结果
                non_stream_metrics = test_results['non_stream']['metrics']
                print(f"非流式调用:")
                print(f"  平均响应时间: {non_stream_metrics.avg_response_time:.2f}s")
                print(f"  成功率: {non_stream_metrics.success_rate:.1%}")
                print(f"  Tokens/秒: {non_stream_metrics.tokens_per_second:.1f}")
                
                # 性能对比
                if stream_metrics.success_rate > 0 and non_stream_metrics.success_rate > 0:
                    if stream_metrics.avg_first_token_time:
                        ttft_advantage = non_stream_metrics.avg_response_time - stream_metrics.avg_first_token_time
                        print(f"  首token优势: {ttft_advantage:.2f}s")
                    
                    throughput_ratio = stream_metrics.tokens_per_second / non_stream_metrics.tokens_per_second
                    print(f"  流式吞吐量优势: {throughput_ratio:.2f}x")

async def main():
    """主函数"""
    # 请替换为您的OpenRouter API密钥
    API_KEY = "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"
    
    if API_KEY == "your-openrouter-api-key-here":
        print("请先设置您的OpenRouter API密钥")
        return
    
    tester = OpenRouterPerformanceTester(API_KEY)
    
    try:
        results = await tester.run_comprehensive_test()
        tester.print_results(results)
        
        # 保存结果到文件
        output_file = f"openrouter_performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            # 将结果转换为可序列化的格式
            serializable_results = {}
            for model, model_results in results.items():
                serializable_results[model] = {}
                for test_case, test_results in model_results.items():
                    serializable_results[model][test_case] = {
                        'stream': {
                            'metrics': test_results['stream']['metrics'].__dict__
                        },
                        'non_stream': {
                            'metrics': test_results['non_stream']['metrics'].__dict__
                        }
                    }
            
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n测试结果已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())