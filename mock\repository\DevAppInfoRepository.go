// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// DevAppInfoRepository is an autogenerated mock type for the DevAppInfoRepository type
type DevAppInfoRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, app
func (_m *DevAppInfoRepository) Create(ctx context.Context, app *repository.DevAppInfo) error {
	ret := _m.Called(ctx, app)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.DevAppInfo) error); ok {
		r0 = rf(ctx, app)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, id
func (_m *DevAppInfoRepository) Delete(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAll provides a mock function with given fields: ctx
func (_m *DevAppInfoRepository) GetAll(ctx context.Context) ([]*repository.DevAppInfo, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.DevAppInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.DevAppInfo, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.DevAppInfo); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DevAppInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllByUserID provides a mock function with given fields: ctx, userID
func (_m *DevAppInfoRepository) GetAllByUserID(ctx context.Context, userID uint) ([]*repository.DevAppInfo, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllByUserID")
	}

	var r0 []*repository.DevAppInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.DevAppInfo, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.DevAppInfo); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DevAppInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *DevAppInfoRepository) GetByID(ctx context.Context, id uint) (*repository.DevAppInfo, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.DevAppInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.DevAppInfo, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.DevAppInfo); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DevAppInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *DevAppInfoRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.DevAppInfo, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.DevAppInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.DevAppInfo, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.DevAppInfo); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DevAppInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUuid provides a mock function with given fields: ctx, uuid
func (_m *DevAppInfoRepository) GetByUuid(ctx context.Context, uuid string) (*repository.DevAppInfo, error) {
	ret := _m.Called(ctx, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByUuid")
	}

	var r0 *repository.DevAppInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.DevAppInfo, error)); ok {
		return rf(ctx, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.DevAppInfo); ok {
		r0 = rf(ctx, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DevAppInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, app
func (_m *DevAppInfoRepository) Update(ctx context.Context, app *repository.DevAppInfo) error {
	ret := _m.Called(ctx, app)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.DevAppInfo) error); ok {
		r0 = rf(ctx, app)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewDevAppInfoRepository creates a new instance of DevAppInfoRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDevAppInfoRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *DevAppInfoRepository {
	mock := &DevAppInfoRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
