---
description: 
globs: 
alwaysApply: false
---
---
description: the top-level directory structure for the project
globs: 
alwaysApply: false
---     
# Directory Structure
```mermaid
flowchart TD
    Root[Project Root]
    Root --> Docs[docs/]
    Root --> Tasks[tasks/]
    Root --> Cursor[.cursor/rules/]
    Root --> CLINE[.clinerules]
    Root --> CmdCode[cmd/]
    Root --> Test[test/]
    Root --> Utils[utils/]
    Root --> Config[config/]
    Root --> Logger[logger/]
    Root --> Internal[internal/]
    Root --> Data[data/]
    Root --> Mock[mock/]
    Root --> Other[Other Directories]
```