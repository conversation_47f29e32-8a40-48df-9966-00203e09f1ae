package config

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
)

// MockConfigNotifier 模拟配置变更通知器
type MockConfigNotifier struct {
	mock.Mock
}

func (m *MockConfigNotifier) NotifyChange(old, new *Config) {
	m.Called(old, new)
}

// MockNotifier 用于测试配置变更通知
type MockNotifier struct {
	notified bool
	old      *Config
	new      *Config
}

func (m *MockNotifier) NotifyChange(old, new *Config) {
	m.notified = true
	m.old = old
	m.new = new
}

// TestEnv 测试环境结构
type TestEnv struct {
	t       *testing.T
	tempDir string
	config  *Config
}

// NewTestEnv 创建新的测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	tempDir := t.TempDir()
	return &TestEnv{
		t:       t,
		tempDir: tempDir,
		config:  &Config{}, // 使用空配置，因为 DefaultConfig 可能不存在
	}
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	os.RemoveAll(e.tempDir)
}

func TestConfigLoading(t *testing.T) {
	tests := []struct {
		name       string
		setup      func(*testing.T) *TestEnv
		configPath string
		wantErr    bool
		validate   func(*testing.T, *Config)
	}{
		{
			name: "加载有效配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				configPath := filepath.Join(env.tempDir, "valid_config.yaml")
				validConfig := &Config{
					Version: "1.0.0",
					Server: ServerConfig{
						Port:    8080,
						Timeout: 30 * time.Second,
					},
					Database: DatabaseConfig{
						Host:     "localhost",
						Port:     3306,
						Username: "test",
						Password: "test",
						Database: "test_db",
					},
				}
				data, err := yaml.Marshal(validConfig)
				require.NoError(t, err)
				err = os.WriteFile(configPath, data, 0644)
				require.NoError(t, err)
				return env
			},
			configPath: "valid_config.yaml",
			wantErr:    false,
			validate: func(t *testing.T, cfg *Config) {
				assert.Equal(t, "1.0.0", cfg.Version)
				assert.Equal(t, 8080, cfg.Server.Port)
				assert.Equal(t, "localhost", cfg.Database.Host)
			},
		},
		{
			name: "加载不存在的配置文件",
			setup: func(t *testing.T) *TestEnv {
				return NewTestEnv(t)
			},
			configPath: "nonexistent.yaml",
			wantErr:    true,
		},
		{
			name: "加载无效的配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				configPath := filepath.Join(env.tempDir, "invalid_config.yaml")
				err := os.WriteFile(configPath, []byte("invalid: yaml: content"), 0644)
				require.NoError(t, err)
				return env
			},
			configPath: "invalid_config.yaml",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := tt.setup(t)
			defer env.Cleanup()

			configPath := filepath.Join(env.tempDir, tt.configPath)
			err := LoadConfig(configPath)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.validate != nil {
					tt.validate(t, GetConfig())
				}
			}
		})
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		wantErr     bool
		errContains string
	}{
		{
			name: "有效配置",
			config: &Config{
				Version: "1.0.0",
				Server: ServerConfig{
					Port:    8080,
					Timeout: 30 * time.Second,
				},
				Database: DatabaseConfig{
					Host:     "localhost",
					Port:     3306,
					Username: "test",
					Password: "test",
					Database: "test_db",
				},
				Auth: AuthConfig{
					Secret:   "test-secret",
					TokenTTL: 24 * time.Hour,
				},
			},
			wantErr: false,
		},
		{
			name: "无效的服务器端口",
			config: &Config{
				Server: ServerConfig{
					Port: -1,
				},
			},
			wantErr:     true,
			errContains: "invalid server port",
		},
		{
			name: "无效的超时时间",
			config: &Config{
				Server: ServerConfig{
					Timeout: -1 * time.Second,
				},
			},
			wantErr:     true,
			errContains: "invalid server timeout",
		},
		{
			name: "无效的数据库端口",
			config: &Config{
				Database: DatabaseConfig{
					Port: -1,
				},
			},
			wantErr:     true,
			errContains: "invalid database port",
		},
		{
			name: "无效的Token TTL",
			config: &Config{
				Auth: AuthConfig{
					TokenTTL: -1 * time.Hour,
				},
			},
			wantErr:     true,
			errContains: "invalid auth token TTL",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConfigHotReload(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 创建初始配置文件
	configPath := filepath.Join(env.tempDir, "hot_reload_config.yaml")
	initialConfig := &Config{
		Version: "1.0.0",
		Server: ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
	}
	data, err := yaml.Marshal(initialConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	// 加载初始配置
	err = LoadConfig(configPath)
	require.NoError(t, err)
	assert.Equal(t, 8080, GetConfig().Server.Port)

	// 修改配置文件
	updatedConfig := &Config{
		Version: "1.0.1",
		Server: ServerConfig{
			Port:    9090,
			Timeout: 30 * time.Second,
		},
	}
	data, err = yaml.Marshal(updatedConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	// 重新加载配置
	err = LoadConfig(configPath)
	require.NoError(t, err)
	assert.Equal(t, 9090, GetConfig().Server.Port)
}

func TestConfigDefaultValues(t *testing.T) {
	// 由于 DefaultConfig 可能不存在，我们直接测试默认值
	cfg := &Config{
		Version: "1.0.0",
		Server: ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
		Database: DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "",
			Database: "chat_server",
		},
		Auth: AuthConfig{
			Secret:   "default-secret-key-for-jwt-token-generation",
			TokenTTL: 24 * time.Hour,
		},
	}

	assert.NotNil(t, cfg)
	assert.Equal(t, "1.0.0", cfg.Version)
	assert.Equal(t, 8080, cfg.Server.Port)
	assert.Equal(t, 30*time.Second, cfg.Server.Timeout)
	assert.Equal(t, "localhost", cfg.Database.Host)
	assert.Equal(t, 3306, cfg.Database.Port)
	assert.Equal(t, 24*time.Hour, cfg.Auth.TokenTTL)
}

func TestConfigChangeNotification(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 创建测试通知器
	notified := false
	notifier := &testConfigNotifier{
		onNotify: func(old, new *Config) {
			notified = true
		},
	}

	// 注册通知器
	RegisterNotifier(notifier)

	// 加载新配置
	configPath := filepath.Join(env.tempDir, "notification_config.yaml")
	newConfig := &Config{
		Version: "1.0.0",
		Server: ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
	}
	data, err := yaml.Marshal(newConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	err = LoadConfig(configPath)
	require.NoError(t, err)
	assert.True(t, notified)
}

// testConfigNotifier 用于测试配置变更通知
type testConfigNotifier struct {
	onNotify func(old, new *Config)
}

func (n *testConfigNotifier) NotifyChange(old, new *Config) {
	if n.onNotify != nil {
		n.onNotify(old, new)
	}
}

// BenchmarkLoadConfig 测试配置加载性能
func BenchmarkLoadConfig(b *testing.B) {
	// 创建临时配置文件
	tempDir := b.TempDir()
	configPath := filepath.Join(tempDir, "config.yaml")

	// 写入测试配置
	configContent := `
version: "1.0.0"
server:
  port: 8080
  timeout: 30s
`
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	assert.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		err := LoadConfig(configPath)
		assert.NoError(b, err)
	}
}

// ExampleLoadConfig 配置加载示例
func ExampleLoadConfig() {
	// 加载配置
	err := LoadConfig("config.yaml")
	if err != nil {
		fmt.Println("Error loading config:", err)
		return
	}

	// 使用配置
	config := GetConfig()
	fmt.Println("Server port:", config.Server.Port)
	fmt.Println("Server timeout:", config.Server.Timeout)
}

func TestConfig(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建配置文件
	configPath := filepath.Join(tempDir, "config.yaml")
	configContent := `
version: "1.0.0"
server:
  port: 8080
  timeout: 30s
  tls:
    enabled: false
database:
  host: localhost
  port: 3306
  username: root
  password: ""
  database: chat_server
auth:
  secret: test-secret
  tokenTTL: 24h
log:
  level: info
  path: logs/app.log
  maxSize: 100
  maxBackups: 3
  maxAge: 7
  compress: true
  console: true
`
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// 测试加载配置
	if err := LoadConfig(configPath); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 验证配置
	conf := GetConfig()
	if conf.Version != "1.0.0" {
		t.Errorf("Expected version 1.0.0, got %s", conf.Version)
	}
	if conf.Server.Port != 8080 {
		t.Errorf("Expected server port 8080, got %d", conf.Server.Port)
	}
	if conf.Server.Timeout != 30*time.Second {
		t.Errorf("Expected server timeout 30s, got %v", conf.Server.Timeout)
	}
	if conf.Database.Host != "localhost" {
		t.Errorf("Expected database host localhost, got %s", conf.Database.Host)
	}
	if conf.Auth.Secret != "test-secret" {
		t.Errorf("Expected auth secret test-secret, got %s", conf.Auth.Secret)
	}
	if conf.Log.Level != "info" {
		t.Errorf("Expected log level info, got %s", conf.Log.Level)
	}

	// 测试配置变更通知
	notifier := &MockNotifier{}
	RegisterNotifier(notifier)

	// 保存当前配置作为旧配置
	oldConfig := *conf

	// 修改配置文件
	newConfigContent := `
version: "1.0.1"
server:
  port: 8081
  timeout: 60s
  tls:
    enabled: false
database:
  host: localhost
  port: 3306
  username: root
  password: ""
  database: chat_server
auth:
  secret: new-secret
  tokenTTL: 24h
log:
  level: debug
  path: logs/app.log
  maxSize: 100
  maxBackups: 3
  maxAge: 7
  compress: true
  console: true
`
	if err := os.WriteFile(configPath, []byte(newConfigContent), 0644); err != nil {
		t.Fatalf("Failed to write new config file: %v", err)
	}

	// 重新加载配置
	if err := LoadConfig(configPath); err != nil {
		t.Fatalf("Failed to reload config: %v", err)
	}

	// 验证配置变更通知
	if !notifier.notified {
		t.Error("Config change notification was not sent")
	}
	if notifier.old == nil || notifier.new == nil {
		t.Error("Config change notification did not include old and new config")
	}
	if notifier.old.Version != oldConfig.Version {
		t.Errorf("Expected old version %s, got %s", oldConfig.Version, notifier.old.Version)
	}
	if notifier.new.Version != "1.0.1" {
		t.Errorf("Expected new version 1.0.1, got %s", notifier.new.Version)
	}

	// 验证新配置
	conf = GetConfig()
	if conf.Version != "1.0.1" {
		t.Errorf("Expected version 1.0.1, got %s", conf.Version)
	}
	if conf.Server.Port != 8081 {
		t.Errorf("Expected server port 8081, got %d", conf.Server.Port)
	}
	if conf.Server.Timeout != 60*time.Second {
		t.Errorf("Expected server timeout 60s, got %v", conf.Server.Timeout)
	}
	if conf.Auth.Secret != "new-secret" {
		t.Errorf("Expected auth secret new-secret, got %s", conf.Auth.Secret)
	}
	if conf.Log.Level != "debug" {
		t.Errorf("Expected log level debug, got %s", conf.Log.Level)
	}
}

// TestMain 设置测试环境
func TestMain(m *testing.M) {
	// 保存原始配置状态
	originalConf := conf
	defer func() {
		conf = originalConf
	}()

	m.Run()
}

// TestConfig_LoadConfig 测试配置加载
func TestConfig_LoadConfig(t *testing.T) {
	tests := []struct {
		name        string
		setup       func(*testing.T) *TestEnv
		want        *Config
		wantErr     error
		description string
	}{
		{
			name: "TC1-加载默认配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)

				// 创建配置文件
				configPath := filepath.Join(env.tempDir, "config.yaml")
				configContent := `
version: "1.0.0"
server:
  port: 8080
  timeout: 30s
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "test_db"
  parse_time: true
  collation: "utf8mb4_unicode_ci"
  pool:
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 1h
    conn_max_idletime: 30m
  timeouts:
    connect: 5s
    query: 30s
    transaction: 30s
auth:
  secret: "test-secret"
  tokenTTL: 24h
log:
  level: "info"
  path: "logs/test.log"
  maxSize: 100
  maxBackups: 3
  maxAge: 7
  compress: true
  console: true
service:
  file:
    max_size: 10485760
    path: "uploads"
  concurrency_limit:
    global_limit: 100
    user_limit: 10
    user_model_limit: 3
`

				err := os.WriteFile(configPath, []byte(configContent), 0644)
				require.NoError(t, err)

				// 加载配置
				err = LoadConfig(configPath)
				require.NoError(t, err)

				return env
			},
			want: &Config{
				Version: "1.0.0",
				Server: ServerConfig{
					Port:    8080,
					Timeout: 30 * time.Second,
				},
				Database: DatabaseConfig{
					Host:      "localhost",
					Port:      3306,
					Username:  "root",
					Password:  "password",
					Database:  "test_db",
					ParseTime: true,
					Collation: "utf8mb4_unicode_ci",
				},
			},
			wantErr:     nil,
			description: "成功加载有效的配置文件",
		},
		{
			name: "TC2-配置文件不存在时创建默认配置",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)

				// 配置文件路径（不存在）
				configPath := filepath.Join(env.tempDir, "nonexistent.yaml")

				// 加载配置
				err := LoadConfig(configPath)
				require.NoError(t, err)

				// 验证文件已创建
				_, err = os.Stat(configPath)
				require.NoError(t, err, "Config file should be created")

				return env
			},
			want:        defaultConfig(),
			wantErr:     nil,
			description: "配置文件不存在时应创建默认配置",
		},
		{
			name: "TC3-无效的配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)

				configPath := filepath.Join(env.tempDir, "invalid.yaml")
				invalidContent := `
server:
  port: -1  # 无效端口
  timeout: 0  # 无效超时
database:
  port: 0  # 无效端口
auth:
  tokenTTL: 0  # 无效TTL
`

				err := os.WriteFile(configPath, []byte(invalidContent), 0644)
				require.NoError(t, err)

				return env
			},
			want:        nil,
			wantErr:     assert.AnError,
			description: "无效配置应返回错误",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := tt.setup(t)
			defer env.Cleanup()

			config := GetConfig()

			if tt.wantErr != nil {
				// 对于期望错误的情况，我们只验证配置验证逻辑
				err := config.Validate()
				assert.Error(t, err, tt.description)
			} else {
				assert.NotNil(t, config, tt.description)
				if tt.want != nil {
					assert.Equal(t, tt.want.Version, config.Version)
					assert.Equal(t, tt.want.Server.Port, config.Server.Port)
					assert.Equal(t, tt.want.Database.Host, config.Database.Host)
				}
			}
		})
	}
}

// TestConfig_Validate 测试配置验证
func TestConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
		errMsg  string
	}{
		{
			name:    "TC1-有效配置",
			config:  defaultConfig(),
			wantErr: false,
		},
		{
			name: "TC2-无效服务器端口",
			config: &Config{
				Server: ServerConfig{
					Port:    -1,
					Timeout: 30 * time.Second,
				},
				Database: DatabaseConfig{Port: 3306},
				Auth:     AuthConfig{TokenTTL: time.Hour},
			},
			wantErr: true,
			errMsg:  "invalid server port",
		},
		{
			name: "TC3-无效服务器超时",
			config: &Config{
				Server: ServerConfig{
					Port:    8080,
					Timeout: 0,
				},
				Database: DatabaseConfig{Port: 3306},
				Auth:     AuthConfig{TokenTTL: time.Hour},
			},
			wantErr: true,
			errMsg:  "invalid server timeout",
		},
		{
			name: "TC4-无效数据库端口",
			config: &Config{
				Server: ServerConfig{
					Port:    8080,
					Timeout: 30 * time.Second,
				},
				Database: DatabaseConfig{Port: 0},
				Auth:     AuthConfig{TokenTTL: time.Hour},
			},
			wantErr: true,
			errMsg:  "invalid database port",
		},
		{
			name: "TC5-无效认证Token TTL",
			config: &Config{
				Server: ServerConfig{
					Port:    8080,
					Timeout: 30 * time.Second,
				},
				Database: DatabaseConfig{Port: 3306},
				Auth:     AuthConfig{TokenTTL: 0},
			},
			wantErr: true,
			errMsg:  "invalid auth token TTL",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := tt.config.Validate()

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestConfig_GetConfig 测试获取配置
func TestConfig_GetConfig(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 设置测试配置
	testConfig := &Config{
		Version: "test-version",
		Server:  ServerConfig{Port: 9999},
	}

	// 保存原始配置
	originalConf := conf
	defer func() {
		conf = originalConf
	}()

	// 设置测试配置
	conf = testConfig

	result := GetConfig()
	assert.Equal(t, testConfig, result)
	assert.Equal(t, "test-version", result.Version)
	assert.Equal(t, 9999, result.Server.Port)
}

// TestConfig_GetLogConfig 测试获取日志配置
func TestConfig_GetLogConfig(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	testConfig := &Config{
		Log: LogConfig{
			Level:   "debug",
			Path:    "test.log",
			Console: false,
		},
	}

	// 保存原始配置
	originalConf := conf
	defer func() {
		conf = originalConf
	}()

	// 设置测试配置
	conf = testConfig

	logConfig := GetLogConfig()
	assert.Equal(t, "debug", logConfig.Level)
	assert.Equal(t, "test.log", logConfig.Path)
	assert.False(t, logConfig.Console)
}

// TestConfig_ConfigChangeNotifier 测试配置变更通知
func TestConfig_ConfigChangeNotifier(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// Mock配置变更通知器
	type MockNotifier struct {
		called  bool
		oldConf *Config
		newConf *Config
	}

	mockNotifier := &MockNotifier{}

	// 实现接口
	notifyChange := func(old, new *Config) {
		mockNotifier.called = true
		mockNotifier.oldConf = old
		mockNotifier.newConf = new
	}

	// 这里简化测试，实际需要根据具体实现调整
	// 注册通知器
	// RegisterNotifier(&mockNotifier)

	// 模拟配置变更
	oldConfig := &Config{Version: "1.0.0"}
	newConfig := &Config{Version: "2.0.0"}

	// 手动调用通知函数进行测试
	notifyChange(oldConfig, newConfig)

	assert.True(t, mockNotifier.called)
	assert.Equal(t, "1.0.0", mockNotifier.oldConf.Version)
	assert.Equal(t, "2.0.0", mockNotifier.newConf.Version)
}

// BenchmarkConfig_GetConfig 性能基准测试
func BenchmarkConfig_GetConfig(b *testing.B) {
	// 设置测试配置
	testConfig := defaultConfig()
	conf = testConfig

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		config := GetConfig()
		if config == nil {
			b.Fatal("Config should not be nil")
		}
	}
}

// BenchmarkConfig_Validate 验证性能基准测试
func BenchmarkConfig_Validate(b *testing.B) {
	config := defaultConfig()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		err := config.Validate()
		if err != nil {
			b.Fatal(err)
		}
	}
}
