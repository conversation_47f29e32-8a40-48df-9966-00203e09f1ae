# TopAI Chat Server

[![Go Version](https://img.shields.io/badge/go-1.23+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://hub.docker.com/r/topai/chat-server)
[![Kubernetes](https://img.shields.io/badge/kubernetes-ready-green.svg)](docs/deployment.md)

TopAI Chat Server 是一个企业级的AI聊天服务器，支持多种AI模型的统一接入，包括大语言模型(LLM)、文本转语音(TTS)、语音识别(ASR)、文本生成图像(TTI)等功能。

## ✨ 核心特性

### 🤖 多模型AI支持
- **LLM对话**: GPT、Claude、TopAI等大语言模型
- **语音处理**: TTS文本转语音、ASR语音识别
- **图像生成**: DALL-E、Midjourney、Stable Diffusion
- **多供应商**: OpenAI、TopAI、自定义模型提供商

### 🚀 企业级架构
- **高性能**: Go语言开发，支持高并发
- **微服务**: 模块化设计，易于扩展
- **容器化**: Docker + Kubernetes部署支持
- **负载均衡**: 支持水平扩展和负载均衡

### 🔒 安全可靠
- **JWT认证**: 无状态用户认证
- **API Key**: 第三方应用认证
- **权限控制**: 角色权限管理(RBAC)
- **数据安全**: 敏感数据加密存储

### ⚡ 实时通信
- **WebSocket**: 基于Socket.IO的实时通信
- **流式响应**: 支持流式和非流式AI响应
- **并发控制**: 精细化并发限制策略

### 🔗 区块链集成
- **TopAI网络**: 集成TopAI去中心化模型网络
- **影子钱包**: 自动钱包管理和交易处理
- **链上结算**: 透明的模型使用费用结算

### 📊 监控运维
- **结构化日志**: 模块化日志记录
- **性能监控**: Prometheus + Grafana
- **健康检查**: 全面的健康检查机制
- **自动备份**: 数据库和文件自动备份

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "客户端层"
        A[Web前端]
        B[移动应用]
        C[第三方应用]
    end
    
    subgraph "API网关层"
        D[HTTP API Gateway]
        E[WebSocket Gateway]
        F[OpenAI兼容API]
    end
    
    subgraph "服务层"
        G[用户服务]
        H[模型服务]
        I[对话服务]
        J[文件服务]
        K[余额服务]
    end
    
    subgraph "数据层"
        L[(MySQL)]
        M[文件存储]
        N[区块链网络]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> H
    F --> I
    
    G --> L
    H --> M
    I --> N
```

## 🚀 快速开始

### 环境要求
- **Go**: 1.23+
- **MySQL**: 8.0+
- **Git**: 最新版本

### 1. 克隆项目
```bash
git clone https://github.com/topnetwork/chat-server.git
cd chat-server
```

### 2. 安装依赖
```bash
go mod download
```

### 3. 配置数据库
```sql
CREATE DATABASE chat_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

```bash
# 运行数据库迁移
mysql -u root -p chat_server < internal/repository/migrations/0001_initial_schema.sql
mysql -u root -p chat_server < internal/repository/migrations/0002_schema.sql
```

### 4. 配置文件
```bash
# 创建配置目录
mkdir -p .chat-webserver

# 复制配置模板
cp config.example.yaml .chat-webserver/config.yaml

# 编辑配置文件
nano .chat-webserver/config.yaml
```

### 5. 启动服务
```bash
go run cmd/server/main.go -config .chat-webserver/config.yaml
```

### 6. 验证部署
```bash
curl http://localhost:8080/health
```

## 📚 文档导航

### 📖 核心文档
- [系统架构文档](docs/architecture.md) - 详细的系统架构设计
- [API接口文档](docs/api.md) - 完整的API接口说明
- [开发指南](docs/development.md) - 开发环境配置和开发流程
- [部署指南](docs/deployment.md) - 生产环境部署指南

### 🔧 技术文档
- [产品需求文档](docs/product_requirement_docs.md) - 产品功能需求
- [技术规范文档](docs/technical.md) - 技术实现规范
- [Swagger API](docs/swagger.yaml) - OpenAPI规范文档

### 📝 其他文档
- [项目问题报告](PROJECT_ISSUES_REPORT.md) - 代码质量检查报告
- [Web API说明](web-api.md) - API使用说明

## 🛠️ API接口

### 认证接口
```bash
# 用户登录
POST /api/v1/auths/signin

# 用户注册
POST /api/v1/auths/signup

# 获取用户信息
GET /api/v1/auths
```

### 对话接口
```bash
# 创建对话
POST /api/v1/chats/new

# 聊天完成
POST /api/chat/completions

# 获取对话列表
GET /api/v1/chats
```

### OpenAI兼容接口
```bash
# 聊天完成
POST /api/openai/chat/completions

# 语音识别
POST /api/openai/audio/transcriptions

# 语音合成
POST /api/openai/audio/speech

# 图像生成
POST /api/openai/images/generations
```

### TopAI模型市场
```bash
# 获取模型列表
POST /api/v1/topai/models

# 获取模型详情
GET /api/v1/topai/models/{model_id}
```

完整的API文档请参考：[API接口文档](docs/api.md)

## 🐳 Docker部署

### 使用Docker Compose
```bash
# 下载docker-compose.yml
wget https://raw.githubusercontent.com/topnetwork/chat-server/main/docker-compose.yml

# 配置环境变量
cp .env.example .env
nano .env

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps
```

### 自定义构建
```bash
# 构建镜像
docker build -t topai/chat-server .

# 运行容器
docker run -d \
  --name chat-server \
  -p 8080:8080 \
  -v $(pwd)/config:/app/config \
  topai/chat-server
```

详细的部署文档请参考：[部署指南](docs/deployment.md)

## ☸️ Kubernetes部署

```bash
# 部署到Kubernetes
kubectl apply -f k8s/

# 检查状态
kubectl get pods -n chat-server
kubectl get svc -n chat-server
```

## 🧪 开发环境

### 安装开发工具
```bash
# 安装开发依赖
go install github.com/cosmtrek/air@latest
go install github.com/swaggo/swag/cmd/swag@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

### 热重载开发
```bash
# 使用air进行热重载
air
```

### 代码检查
```bash
# 运行代码检查
make lint

# 运行测试
make test

# 生成覆盖率报告
make test-coverage
```

详细的开发文档请参考：[开发指南](docs/development.md)

## 🧪 测试

### 单元测试
```bash
go test ./...
```

### 集成测试
```bash
go test ./test/...
```

### API测试
```bash
# 使用Postman或curl测试API
curl -X POST http://localhost:8080/api/v1/auths/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## 📊 监控

### Prometheus指标
访问 `http://localhost:8080/metrics` 查看Prometheus指标

### 健康检查
```bash
curl http://localhost:8080/health
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看systemd日志
sudo journalctl -u chat-server -f
```

## 🔧 配置

### 基础配置
```yaml
server:
  port: 8080
  timeout: 30s

database:
  host: localhost
  port: 3306
  username: chat_user
  password: "your_password"
  database: chat_server

auth:
  secret: "your-jwt-secret"
  tokenTTL: 24h
```

### AI服务配置
```yaml
service:
  openai:
    openai:
      base_url: "https://api.openai.com/v1"
      api_key: "sk-your-openai-key"
  
  topai:
    wallet_addr: "0x..."
    ip_map_path: "ip_map.json"
```

完整的配置选项请参考：[配置文档](docs/development.md#配置管理)

## 🔐 安全

### 认证方式
- **JWT Token**: 用户认证
- **API Key**: 第三方应用认证
- **Admin Role**: 管理员权限

### 安全特性
- 密码bcrypt加密
- 敏感数据AES加密
- SQL注入防护
- CORS跨域控制
- 请求限流

### 权限管理
```go
// 角色定义
const (
    RoleUser  = "user"
    RoleAdmin = "admin"
)
```

## 🌍 多语言支持

### Go SDK
```go
import "github.com/topnetwork/chat-server-go-sdk"

client := sdk.NewClient("your-api-key")
response, err := client.Chat.Create(ctx, &sdk.ChatRequest{
    Model: "gpt-3.5-turbo",
    Messages: []sdk.Message{
        {Role: "user", Content: "Hello"},
    },
})
```

### JavaScript SDK
```javascript
import { TopAIClient } from '@topai/client';

const client = new TopAIClient({
  apiKey: 'your-api-key'
});

const response = await client.chat.completions.create({
  model: 'gpt-3.5-turbo',
  messages: [{ role: 'user', content: 'Hello' }]
});
```

## 📈 性能

### 基准测试
- **并发连接**: 1000+
- **请求吞吐**: 5000+ RPS
- **响应延迟**: P99 < 200ms
- **内存使用**: < 500MB

### 优化特性
- 连接池管理
- 数据库查询优化
- 内存缓存
- 并发控制

## 🤝 贡献

我们欢迎社区贡献！请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与贡献。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交变更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
- 遵循 Go 官方代码规范
- 使用 `golangci-lint` 进行代码检查
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

### 获取帮助
- [GitHub Issues](https://github.com/topnetwork/chat-server/issues) - 问题报告和功能请求
- [GitHub Discussions](https://github.com/topnetwork/chat-server/discussions) - 社区讨论
- [文档](docs/) - 详细的技术文档

### 联系我们
- **邮箱**: <EMAIL>
- **网站**: https://topai.com
- **社区**: https://discord.gg/topai

### 问题报告
在提交问题前，请检查：
1. 是否已有相似的问题
2. 提供详细的错误信息
3. 包含重现步骤
4. 说明系统环境

## 🎯 路线图

### v2.1 计划 (Q1 2025)
- [ ] Redis缓存集成
- [ ] 微服务架构重构
- [ ] GraphQL API支持
- [ ] 实时协作功能

### v2.2 计划 (Q2 2025)
- [ ] 多租户支持
- [ ] 高级分析面板
- [ ] 自定义插件系统
- [ ] 企业级SSO集成

### 长期目标
- [ ] 边缘计算支持
- [ ] AI模型本地部署
- [ ] 联邦学习集成
- [ ] 零信任安全架构

## 📊 项目统计

![GitHub stars](https://img.shields.io/github/stars/topnetwork/chat-server?style=social)
![GitHub forks](https://img.shields.io/github/forks/topnetwork/chat-server?style=social)
![GitHub issues](https://img.shields.io/github/issues/topnetwork/chat-server)
![GitHub pull requests](https://img.shields.io/github/issues-pr/topnetwork/chat-server)

---

## ⭐ Star History

[![Star History Chart](https://api.star-history.com/svg?repos=topnetwork/chat-server&type=Date)](https://star-history.com/#topnetwork/chat-server&Date)

---

**感谢使用 TopAI Chat Server！** 🚀

如果这个项目对您有帮助，请给我们一个 ⭐️

**文档版本**: v2.0  
**更新时间**: 2024-12-28  
**维护团队**: TopAI开发团队 