package mysql_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/repository/mysql"
)

// TestEnv 测试环境结构
type TestEnv struct {
	t       *testing.T
	db      *repository.DB
	cleanup func()
}

// NewTestEnv 创建新的测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	// 创建测试数据库配置
	dbConfig := &config.DatabaseConfig{
		Host:     "localhost",
		Port:     3306,
		Username: "root",
		Password: "",
		Database: "chat_server_test",
		Pool: struct {
			MaxOpenConns    int           `yaml:"max_open_conns"`
			MaxIdleConns    int           `yaml:"max_idle_conns"`
			ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
			ConnMaxIdleTime time.Duration `yaml:"conn_max_idletime"`
		}{
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: time.Hour,
			ConnMaxIdleTime: 30 * time.Minute,
		},
		Timeouts: struct {
			Connect     time.Duration `yaml:"connect"`
			Query       time.Duration `yaml:"query"`
			Transaction time.Duration `yaml:"transaction"`
		}{
			Connect:     5 * time.Second,
			Query:       30 * time.Second,
			Transaction: 30 * time.Second,
		},
		ParseTime: true,
		Collation: "utf8mb4_unicode_ci",
	}

	// 创建数据库连接
	db, err := mysql.NewClient(dbConfig)
	require.NoError(t, err)

	// 清理函数
	cleanup := func() {
		if db != nil {
			db.Close()
		}
	}

	return &TestEnv{
		t:       t,
		db:      db,
		cleanup: cleanup,
	}
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	if e.cleanup != nil {
		e.cleanup()
	}
}

func TestUserRepository(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	ctx := context.Background()

	tests := []struct {
		name   string
		setup  func(*testing.T, *repository.DB)
		action func(*testing.T, *repository.DB)
		verify func(*testing.T, *repository.DB)
	}{
		{
			name: "创建用户",
			setup: func(t *testing.T, db *repository.DB) {
				// 清理测试数据
				db.User.Delete(ctx, 1) // 使用 Delete 方法替代 Exec
			},
			action: func(t *testing.T, db *repository.DB) {
				user := &repository.User{
					IdentityID:   "test123",
					Username:     "testuser",
					Email:        "<EMAIL>",
					Role:         repository.UserRoleUser,
					PasswordHash: "hashed_password",
				}
				err := db.User.Create(ctx, user)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				user, err := db.User.GetByEmail(ctx, "<EMAIL>")
				require.NoError(t, err)
				assert.Equal(t, "testuser", user.Username)
				assert.Equal(t, "hashed_password", user.PasswordHash)
			},
		},
		{
			name: "更新用户",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试用户
				user := &repository.User{
					IdentityID:   "update123",
					Username:     "olduser",
					Email:        "<EMAIL>",
					Role:         repository.UserRoleUser,
					PasswordHash: "old_password",
				}
				err := db.User.Create(ctx, user)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				user, err := db.User.GetByEmail(ctx, "<EMAIL>")
				require.NoError(t, err)
				user.Username = "newuser"
				user.PasswordHash = "new_password"
				err = db.User.Update(ctx, user)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				user, err := db.User.GetByEmail(ctx, "<EMAIL>")
				require.NoError(t, err)
				assert.Equal(t, "newuser", user.Username)
				assert.Equal(t, "new_password", user.PasswordHash)
			},
		},
		{
			name: "删除用户",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试用户
				user := &repository.User{
					IdentityID:   "delete123",
					Username:     "deleteuser",
					Email:        "<EMAIL>",
					Role:         repository.UserRoleUser,
					PasswordHash: "password",
				}
				err := db.User.Create(ctx, user)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				user, err := db.User.GetByEmail(ctx, "<EMAIL>")
				require.NoError(t, err)
				err = db.User.Delete(ctx, user.ID)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				_, err := db.User.GetByEmail(ctx, "<EMAIL>")
				assert.Error(t, err)
				assert.Equal(t, gorm.ErrRecordNotFound, err)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t, env.db)
			}

			if tt.action != nil {
				tt.action(t, env.db)
			}

			if tt.verify != nil {
				tt.verify(t, env.db)
			}
		})
	}
}

func TestConversationRepository(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	ctx := context.Background()

	tests := []struct {
		name   string
		setup  func(*testing.T, *repository.DB)
		action func(*testing.T, *repository.DB)
		verify func(*testing.T, *repository.DB)
	}{
		{
			name: "创建会话",
			setup: func(t *testing.T, db *repository.DB) {
				// 清理测试数据
				db.Conversation.Delete(ctx, 1) // 使用 Delete 方法替代 Exec
			},
			action: func(t *testing.T, db *repository.DB) {
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "conv123",
					UUID:       "test-uuid",
					Title:      "Test Conversation",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				convs, err := db.Conversation.FindByUserIDOrderByUpdatedAtDesc(ctx, 1, 10, 0)
				require.NoError(t, err)
				assert.Len(t, convs, 1)
				assert.Equal(t, "Test Conversation", convs[0].Title)
				assert.Equal(t, "gpt-3.5-turbo", convs[0].ModelID)
			},
		},
		{
			name: "更新会话",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试会话
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "update123",
					UUID:       "update-uuid",
					Title:      "Old Title",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				err := db.Conversation.UpdateTitleByUUID(ctx, "update-uuid", "New Title")
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				convs, err := db.Conversation.FindByUserIDOrderByUpdatedAtDesc(ctx, 1, 10, 0)
				require.NoError(t, err)
				assert.Len(t, convs, 1)
				assert.Equal(t, "New Title", convs[0].Title)
			},
		},
		{
			name: "删除会话",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试会话
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "delete123",
					UUID:       "delete-uuid",
					Title:      "Delete Me",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "delete-uuid")
				require.NoError(t, err)
				err = db.Conversation.Delete(ctx, conv.ID)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				convs, err := db.Conversation.FindByUserIDOrderByUpdatedAtDesc(ctx, 1, 10, 0)
				require.NoError(t, err)
				assert.Len(t, convs, 0)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t, env.db)
			}

			if tt.action != nil {
				tt.action(t, env.db)
			}

			if tt.verify != nil {
				tt.verify(t, env.db)
			}
		})
	}
}

func TestConversationMessageRepository(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	ctx := context.Background()

	tests := []struct {
		name   string
		setup  func(*testing.T, *repository.DB)
		action func(*testing.T, *repository.DB)
		verify func(*testing.T, *repository.DB)
	}{
		{
			name: "创建消息",
			setup: func(t *testing.T, db *repository.DB) {
				// 清理测试数据
				// 创建测试会话
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "msg123",
					UUID:       "msg-uuid",
					Title:      "Test Conversation",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "msg-uuid")
				require.NoError(t, err)

				msg := &repository.ConversationMessage{
					UUID:             "msg1-uuid",
					ConversationID:   conv.ID,
					ConversationUUID: conv.UUID,
					Role:             "user",
					Content:          "Hello, AI!",
				}
				err = db.ConversationMessage.Create(ctx, msg)
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "msg-uuid")
				require.NoError(t, err)

				msgs, err := db.ConversationMessage.GetByConversationID(ctx, conv.ID)
				require.NoError(t, err)
				assert.Len(t, msgs, 1)
				assert.Equal(t, "user", msgs[0].Role)
				assert.Equal(t, "Hello, AI!", msgs[0].Content)
			},
		},
		{
			name: "更新消息",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试会话和消息
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "update123",
					UUID:       "update-uuid",
					Title:      "Test Conversation",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)

				msg := &repository.ConversationMessage{
					UUID:             "msg2-uuid",
					ConversationID:   conv.ID,
					ConversationUUID: conv.UUID,
					Role:             "user",
					Content:          "Old Content",
				}
				err = db.ConversationMessage.Create(ctx, msg)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "update-uuid")
				require.NoError(t, err)

				msgs, err := db.ConversationMessage.GetByConversationID(ctx, conv.ID)
				require.NoError(t, err)
				require.Len(t, msgs, 1)

				msgs[0].Content = "Updated Content"
				err = db.ConversationMessage.Update(ctx, msgs[0])
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "update-uuid")
				require.NoError(t, err)

				msgs, err := db.ConversationMessage.GetByConversationID(ctx, conv.ID)
				require.NoError(t, err)
				assert.Len(t, msgs, 1)
				assert.Equal(t, "Updated Content", msgs[0].Content)
			},
		},
		{
			name: "删除消息",
			setup: func(t *testing.T, db *repository.DB) {
				// 创建测试会话和消息
				conv := &repository.Conversation{
					UserID:     1,
					IdentityID: "delete123",
					UUID:       "delete-uuid",
					Title:      "Test Conversation",
					ModelID:    "gpt-3.5-turbo",
					System:     "{}",
				}
				err := db.Conversation.Create(ctx, conv)
				require.NoError(t, err)

				msg := &repository.ConversationMessage{
					UUID:             "msg3-uuid",
					ConversationID:   conv.ID,
					ConversationUUID: conv.UUID,
					Role:             "user",
					Content:          "Delete Me",
				}
				err = db.ConversationMessage.Create(ctx, msg)
				require.NoError(t, err)
			},
			action: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "delete-uuid")
				require.NoError(t, err)

				msgs, err := db.ConversationMessage.GetByConversationID(ctx, conv.ID)
				require.NoError(t, err)
				require.Len(t, msgs, 1)

				// 由于没有 Delete 方法，我们通过更新消息内容来模拟删除
				msgs[0].Content = ""
				err = db.ConversationMessage.Update(ctx, msgs[0])
				require.NoError(t, err)
			},
			verify: func(t *testing.T, db *repository.DB) {
				conv, err := db.Conversation.GetByUUID(ctx, "delete-uuid")
				require.NoError(t, err)

				msgs, err := db.ConversationMessage.GetByConversationID(ctx, conv.ID)
				require.NoError(t, err)
				assert.Len(t, msgs, 1)
				assert.Equal(t, "", msgs[0].Content)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t, env.db)
			}

			if tt.action != nil {
				tt.action(t, env.db)
			}

			if tt.verify != nil {
				tt.verify(t, env.db)
			}
		})
	}
}
