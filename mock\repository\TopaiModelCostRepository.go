// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelCostRepository is an autogenerated mock type for the TopaiModelCostRepository type
type TopaiModelCostRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, cost
func (_m *TopaiModelCostRepository) Create(ctx context.Context, cost *repository.TopaiModelCost) error {
	ret := _m.Called(ctx, cost)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.TopaiModelCost) error); ok {
		r0 = rf(ctx, cost)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAll provides a mock function with given fields: ctx, offset, limit, sort
func (_m *TopaiModelCostRepository) GetAll(ctx context.Context, offset int, limit int, sort string) ([]*repository.TopaiModelCost, error) {
	ret := _m.Called(ctx, offset, limit, sort)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelCost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string) ([]*repository.TopaiModelCost, error)); ok {
		return rf(ctx, offset, limit, sort)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string) []*repository.TopaiModelCost); ok {
		r0 = rf(ctx, offset, limit, sort)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int, string) error); ok {
		r1 = rf(ctx, offset, limit, sort)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelId provides a mock function with given fields: ctx, modelId
func (_m *TopaiModelCostRepository) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelCost, error) {
	ret := _m.Called(ctx, modelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelId")
	}

	var r0 *repository.TopaiModelCost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.TopaiModelCost, error)); ok {
		return rf(ctx, modelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.TopaiModelCost); ok {
		r0 = rf(ctx, modelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelCost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelIds provides a mock function with given fields: ctx, modelIds
func (_m *TopaiModelCostRepository) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelCost, error) {
	ret := _m.Called(ctx, modelIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelIds")
	}

	var r0 []*repository.TopaiModelCost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModelCost, error)); ok {
		return rf(ctx, modelIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModelCost); ok {
		r0 = rf(ctx, modelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, modelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelId provides a mock function with given fields: ctx, topaiModelId
func (_m *TopaiModelCostRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelCost, error) {
	ret := _m.Called(ctx, topaiModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelId")
	}

	var r0 *repository.TopaiModelCost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModelCost, error)); ok {
		return rf(ctx, topaiModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModelCost); ok {
		r0 = rf(ctx, topaiModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelCost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, topaiModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelIds provides a mock function with given fields: ctx, topaiModelIds
func (_m *TopaiModelCostRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCost, error) {
	ret := _m.Called(ctx, topaiModelIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelIds")
	}

	var r0 []*repository.TopaiModelCost
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelCost, error)); ok {
		return rf(ctx, topaiModelIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelCost); ok {
		r0 = rf(ctx, topaiModelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCost)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, topaiModelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, cost
func (_m *TopaiModelCostRepository) Update(ctx context.Context, cost *repository.TopaiModelCost) error {
	ret := _m.Called(ctx, cost)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.TopaiModelCost) error); ok {
		r0 = rf(ctx, cost)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewTopaiModelCostRepository creates a new instance of TopaiModelCostRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelCostRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelCostRepository {
	mock := &TopaiModelCostRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
