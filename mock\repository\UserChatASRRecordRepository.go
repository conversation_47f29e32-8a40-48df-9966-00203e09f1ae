// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserChatASRRecordRepository is an autogenerated mock type for the UserChatASRRecordRepository type
type UserChatASRRecordRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserChatASRRecordRepository) Create(ctx context.Context, record *repository.UserChatASRRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatASRRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserChatASRRecordRepository creates a new instance of UserChatASRRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserChatASRRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserChatASRRecordRepository {
	mock := &UserChatASRRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
