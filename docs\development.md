# TopAI Chat Server 开发指南

## 1. 环境准备

### 1.1 系统要求
- **操作系统**: Linux、macOS、Windows
- **Go版本**: 1.23+
- **MySQL**: 8.0+
- **Git**: 最新版本

### 1.2 开发工具推荐
- **IDE**: GoLand、VSCode
- **API测试**: Postman、Insomnia
- **数据库管理**: MySQL Workbench、DBeaver
- **版本控制**: Git

### 1.3 依赖安装

#### 安装Go
```bash
# macOS
brew install go

# Linux (Ubuntu/Debian)
sudo apt-get install golang-go

# 或者从官网下载: https://golang.org/dl/
```

#### 安装MySQL
```bash
# macOS
brew install mysql

# Linux (Ubuntu/Debian)
sudo apt-get install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
```

## 2. 项目设置

### 2.1 克隆项目
```bash
git clone https://github.com/topnetwork/chat-server.git
cd chat-server
```

### 2.2 依赖管理
```bash
# 下载依赖
go mod download

# 验证依赖
go mod verify

# 清理依赖
go mod tidy
```

### 2.3 配置环境变量
```bash
# 设置Go代理（中国用户推荐）
export GOPROXY=https://goproxy.cn,direct

# 设置Go模块
export GO111MODULE=on
```

### 2.4 创建配置文件
```bash
# 创建配置目录
mkdir -p .chat-webserver

# 复制配置模板
cp config.example.yaml .chat-webserver/config.yaml
```

编辑配置文件：
```yaml
# .chat-webserver/config.yaml
version: "1.0.0"

server:
  port: 8080
  timeout: 30s

database:
  host: localhost
  port: 3306
  username: root
  password: "your_password"
  database: chat_server_dev
  parse_time: true
  collation: utf8mb4_unicode_ci

auth:
  secret: "your-jwt-secret-key"
  tokenTTL: 24h

log:
  level: debug
  path: logs/app.log
  console: true

service:
  file:
    max_size: 10485760
    path: uploads
  
  openai:
    openai:
      base_url: "https://api.openai.com/v1"
      api_key: "sk-your-openai-key"
  
  concurrency_limit:
    global_limit: 100
    user_limit: 10
    user_model_limit: 3
```

### 2.5 数据库初始化
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE chat_server_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 运行迁移脚本
mysql -u root -p chat_server_dev < internal/repository/migrations/0001_initial_schema.sql
mysql -u root -p chat_server_dev < internal/repository/migrations/0002_schema.sql
```

## 3. 项目结构

### 3.1 目录结构说明
```
chat-server/
├── cmd/                    # 应用程序入口
│   └── server/
│       ├── main.go        # 主程序入口
│       └── main_test.go   # 主程序测试
├── config/                # 配置管理
│   ├── config.go
│   └── config_test.go
├── internal/              # 内部包（不对外暴露）
│   ├── api/               # API层
│   │   ├── handlers/      # HTTP处理器
│   │   ├── middleware/    # 中间件
│   │   └── response.go    # 响应格式
│   ├── repository/        # 数据访问层
│   │   ├── mysql/         # MySQL实现
│   │   ├── migrations/    # 数据库迁移
│   │   └── repositories.go
│   ├── service/           # 业务逻辑层
│   └── testutil/          # 测试工具
├── logger/                # 日志模块
├── utils/                 # 工具函数
│   └── chain/            # 区块链工具
├── docs/                  # 文档
├── test/                  # 测试文件
└── web-api.md            # Web API说明
```

### 3.2 架构分层

#### 表现层 (Presentation Layer)
- **路径**: `internal/api/handlers/`
- **职责**: HTTP请求处理、参数验证、响应格式化
- **示例**: `auth_handler.go`, `user_handler.go`

#### 业务逻辑层 (Business Logic Layer)
- **路径**: `internal/service/`
- **职责**: 业务规则实现、数据处理、外部服务集成
- **示例**: `user_service.go`, `model_service.go`

#### 数据访问层 (Data Access Layer)
- **路径**: `internal/repository/`
- **职责**: 数据库操作、数据模型定义
- **示例**: `mysql/user.go`, `mysql/conversation.go`

## 4. 开发工作流

### 4.1 分支管理
```bash
# 主要分支
main           # 生产环境分支
develop        # 开发分支
feature/*      # 功能分支
hotfix/*       # 热修复分支
release/*      # 发布分支
```

### 4.2 功能开发流程
```bash
# 1. 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发功能
# ... 编写代码

# 3. 提交代码
git add .
git commit -m "feat: add new feature"

# 4. 推送到远程
git push origin feature/new-feature

# 5. 创建Pull Request
# 在GitHub/GitLab上创建PR，目标分支为develop
```

### 4.3 代码规范

#### 4.3.1 命名规范
```go
// 包名：小写，简短
package user

// 常量：大写，下划线分隔
const MAX_RETRY_COUNT = 3

// 变量：驼峰命名
var userName string
var isActive bool

// 函数：驼峰命名，导出函数首字母大写
func GetUser() {}
func validateInput() {}

// 结构体：驼峰命名，导出结构体首字母大写
type User struct {
    ID   uint   `json:"id"`
    Name string `json:"name"`
}

// 接口：以er结尾
type UserRepository interface {
    Create(user *User) error
    GetByID(id uint) (*User, error)
}
```

#### 4.3.2 注释规范
```go
// Package user 提供用户管理相关功能
package user

// User 表示系统用户
type User struct {
    ID   uint   `json:"id"`   // 用户ID
    Name string `json:"name"` // 用户名
}

// CreateUser 创建新用户
// 参数:
//   - name: 用户名，不能为空
//   - email: 邮箱地址，必须有效
// 返回值:
//   - *User: 创建的用户对象
//   - error: 错误信息，nil表示成功
func CreateUser(name, email string) (*User, error) {
    // 实现逻辑...
}
```

#### 4.3.3 错误处理
```go
// 定义错误类型
var (
    ErrUserNotFound = errors.New("用户未找到")
    ErrInvalidEmail = errors.New("邮箱格式无效")
)

// 错误包装
func GetUser(id uint) (*User, error) {
    user, err := repo.GetByID(id)
    if err != nil {
        return nil, fmt.Errorf("获取用户失败: %w", err)
    }
    return user, nil
}

// 错误检查
if errors.Is(err, ErrUserNotFound) {
    // 处理用户未找到的情况
}
```

### 4.4 测试开发

#### 4.4.1 单元测试
```go
// user_test.go
package user

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

func TestCreateUser(t *testing.T) {
    tests := []struct {
        name     string
        input    CreateUserRequest
        want     *User
        wantErr  bool
    }{
        {
            name: "有效用户创建",
            input: CreateUserRequest{
                Name:  "张三",
                Email: "<EMAIL>",
            },
            want: &User{
                Name:  "张三",
                Email: "<EMAIL>",
            },
            wantErr: false,
        },
        {
            name: "无效邮箱",
            input: CreateUserRequest{
                Name:  "张三",
                Email: "invalid-email",
            },
            want:    nil,
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := CreateUser(tt.input.Name, tt.input.Email)
            
            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, got)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tt.want.Name, got.Name)
                assert.Equal(t, tt.want.Email, got.Email)
            }
        })
    }
}
```

#### 4.4.2 集成测试
```go
// integration_test.go
package integration

func TestUserAPIIntegration(t *testing.T) {
    // 创建测试环境
    env := testutil.NewTestEnv(t)
    defer env.Cleanup()

    // 创建测试用户
    user := &repository.User{
        Name:  "测试用户",
        Email: "<EMAIL>",
    }
    err := env.DB.User.Create(context.Background(), user)
    assert.NoError(t, err)

    // 测试API
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("GET", "/api/v1/users/"+user.IdentityID, nil)
    req.Header.Set("Authorization", "Bearer "+env.Token)
    
    env.Router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
}
```

#### 4.4.3 Mock测试
```go
// mocks/user_repository.go
type MockUserRepository struct {
    mock.Mock
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint) (*repository.User, error) {
    args := m.Called(ctx, id)
    return args.Get(0).(*repository.User), args.Error(1)
}

// 使用Mock
func TestUserService(t *testing.T) {
    mockRepo := new(MockUserRepository)
    service := NewUserService(mockRepo)

    // 设置Mock期望
    mockRepo.On("GetByID", mock.Anything, uint(1)).Return(&repository.User{
        ID:   1,
        Name: "测试用户",
    }, nil)

    // 执行测试
    user, err := service.GetUser(1)
    
    assert.NoError(t, err)
    assert.Equal(t, "测试用户", user.Name)
    mockRepo.AssertExpectations(t)
}
```

### 4.5 运行和调试

#### 4.5.1 本地运行
```bash
# 运行开发服务器
go run cmd/server/main.go -config .chat-webserver/config.yaml

# 热重载运行（需要安装air）
go install github.com/cosmtrek/air@latest
air

# 指定环境变量运行
APP_ENV=development go run cmd/server/main.go
```

#### 4.5.2 调试配置

**VSCode调试配置** (`.vscode/launch.json`):
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Server",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/server",
            "args": ["-config", ".chat-webserver/config.yaml"],
            "env": {
                "APP_ENV": "development"
            }
        }
    ]
}
```

**GoLand调试配置**:
- Run/Debug Configurations -> Go Build
- Program arguments: `-config .chat-webserver/config.yaml`
- Environment: `APP_ENV=development`

#### 4.5.3 日志调试
```go
// 使用结构化日志
log := logger.GetLogger("module_name")

log.Debug("调试信息", zap.String("key", "value"))
log.Info("信息", zap.Int("count", 10))
log.Warn("警告", zap.Error(err))
log.Error("错误", zap.String("user_id", userID), zap.Error(err))

// 带上下文的日志
contextLogger := log.WithContext(
    zap.String("request_id", requestID),
    zap.String("user_id", userID),
)
contextLogger.Info("处理请求")
```

## 5. 数据库开发

### 5.1 数据库迁移

#### 5.1.1 创建迁移文件
```sql
-- internal/repository/migrations/0003_add_new_table.sql
-- 创建新表
CREATE TABLE new_table (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_new_table_name ON new_table(name);
```

#### 5.1.2 运行迁移
```bash
# 手动运行迁移
mysql -u root -p chat_server_dev < internal/repository/migrations/0003_add_new_table.sql

# 或者使用迁移工具（推荐）
go install -tags 'mysql' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
migrate -path internal/repository/migrations -database "mysql://user:password@tcp(localhost:3306)/chat_server_dev" up
```

### 5.2 Repository模式

#### 5.2.1 定义接口
```go
// internal/repository/repositories.go
type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByID(ctx context.Context, id uint) (*User, error)
    GetByEmail(ctx context.Context, email string) (*User, error)
    Update(ctx context.Context, user *User) error
    Delete(ctx context.Context, id uint) error
}
```

#### 5.2.2 实现Repository
```go
// internal/repository/mysql/user.go
type userRepository struct {
    db *gorm.DB
}

func NewUserRepository(db *gorm.DB) repository.UserRepository {
    return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user *repository.User) error {
    return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) GetByID(ctx context.Context, id uint) (*repository.User, error) {
    var user repository.User
    err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = 0", id).First(&user).Error
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, repository.ErrUserNotFound
        }
        return nil, err
    }
    return &user, nil
}
```

#### 5.2.3 事务处理
```go
// 使用事务
func (s *UserService) CreateUserWithProfile(ctx context.Context, userData UserData) error {
    return s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
        // 创建用户
        user := &repository.User{
            Name:  userData.Name,
            Email: userData.Email,
        }
        if err := db.User.Create(ctx, user); err != nil {
            return err
        }

        // 创建用户配置
        profile := &repository.UserProfile{
            UserID: user.ID,
            Avatar: userData.Avatar,
        }
        if err := db.UserProfile.Create(ctx, profile); err != nil {
            return err
        }

        return nil
    })
}
```

## 6. API开发

### 6.1 Handler开发

#### 6.1.1 Handler结构
```go
// internal/api/handlers/user_handler.go
type UserHandler struct {
    service *service.Api
    log     *logger.ModuleLogger
}

func NewUserHandler(service *service.Api) *UserHandler {
    return &UserHandler{
        service: service,
        log:     logger.GetLogger("user_handler"),
    }
}
```

#### 6.1.2 实现Handler方法
```go
// CreateUser 创建用户
// @Summary 创建用户
// @Description 管理员创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body CreateUserRequest true "用户信息"
// @Success 200 {object} service.UserAdminOP
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        api.Fail(c, api.CodeClientError, "请求参数无效", err)
        return
    }

    // 参数验证
    if !utils.IsEmailValid(req.Email) {
        api.Fail(c, api.CodeClientError, "邮箱格式无效", errors.New("invalid email"))
        return
    }

    // 调用服务层
    user, err := h.service.User.AdminCreateUser(
        c.Request.Context(),
        req.Email,
        req.Password,
        req.Name,
        req.Role,
    )
    if err != nil {
        h.log.Error("创建用户失败", zap.Error(err))
        api.Fail(c, api.CodeServerError, "创建用户失败", err)
        return
    }

    api.Success(c, user)
}
```

### 6.2 中间件开发

#### 6.2.1 认证中间件
```go
// internal/api/middleware/auth.go
func Auth(svc *service.Api) gin.HandlerFunc {
    return func(c *gin.Context) {
        token := extractToken(c)
        if token == "" {
            api.Fail(c, api.CodeUnauthorized, "未授权访问", errors.New("missing token"))
            c.Abort()
            return
        }

        user, err := svc.User.GetUserByToken(c.Request.Context(), token)
        if err != nil {
            api.Fail(c, api.CodeUnauthorized, "令牌无效", err)
            c.Abort()
            return
        }

        c.Set("user", user)
        c.Next()
    }
}
```

#### 6.2.2 自定义中间件
```go
// 请求ID中间件
func RequestID() gin.HandlerFunc {
    return func(c *gin.Context) {
        requestID := c.GetHeader("X-Request-ID")
        if requestID == "" {
            requestID = uuid.New().String()
        }
        c.Set("request_id", requestID)
        c.Header("X-Request-ID", requestID)
        c.Next()
    }
}

// 性能监控中间件
func PerformanceMonitor() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        log := logger.GetLogger("performance")
        log.Info("请求性能",
            zap.String("method", c.Request.Method),
            zap.String("path", c.Request.URL.Path),
            zap.Duration("duration", duration),
            zap.Int("status", c.Writer.Status()),
        )
    }
}
```

### 6.3 路由注册
```go
// internal/api/handlers/handler.go
func NewHandler(ctx context.Context, router *gin.Engine, service *service.Api) Stop {
    // 注册中间件
    router.Use(middleware.RequestID())
    router.Use(middleware.PerformanceMonitor())
    router.Use(middleware.Logger())
    router.Use(middleware.CORS())

    // API路由组
    apiV1 := router.Group("/api/v1")
    
    // 公共路由（无需认证）
    apiV1.POST("/auths/signin", authHandler.Signin)
    apiV1.POST("/auths/signup", authHandler.Signup)

    // 需要认证的路由
    authRequired := apiV1.Group("")
    authRequired.Use(middleware.Auth(service))
    authRequired.GET("/users", userHandler.GetUsers)
    
    // 需要管理员权限的路由
    adminRequired := apiV1.Group("")
    adminRequired.Use(middleware.AdminAuth(service))
    adminRequired.POST("/users", userHandler.CreateUser)

    return func() {
        // 清理资源
    }
}
```

## 7. 服务层开发

### 7.1 服务接口定义
```go
// internal/service/api.go
type UserApi interface {
    CreateUser(ctx context.Context, req CreateUserRequest) (*User, error)
    GetUser(ctx context.Context, id uint) (*User, error)
    UpdateUser(ctx context.Context, id uint, req UpdateUserRequest) (*User, error)
    DeleteUser(ctx context.Context, id uint) error
}
```

### 7.2 服务实现
```go
// internal/service/user_service.go
type userService struct {
    log  *logger.ModuleLogger
    repo *repository.DB
}

func NewUserService(ctx context.Context, repo *repository.DB) UserApi {
    return &userService{
        log:  logger.GetLogger("user_service"),
        repo: repo,
    }
}

func (s *userService) CreateUser(ctx context.Context, req CreateUserRequest) (*User, error) {
    // 业务验证
    if !utils.IsEmailValid(req.Email) {
        return nil, api.NewClientError("邮箱格式无效")
    }

    // 检查邮箱是否已存在
    existingUser, err := s.repo.User.GetByEmail(ctx, req.Email)
    if err != nil && !errors.Is(err, repository.ErrUserNotFound) {
        return nil, fmt.Errorf("检查用户邮箱失败: %w", err)
    }
    if existingUser != nil {
        return nil, api.NewClientError("邮箱已被使用")
    }

    // 密码加密
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
    if err != nil {
        return nil, fmt.Errorf("密码加密失败: %w", err)
    }

    // 创建用户对象
    user := &repository.User{
        Name:         req.Name,
        Email:        req.Email,
        PasswordHash: string(hashedPassword),
        Role:         repository.UserRole(req.Role),
        IdentityID:   uuid.New().String(),
    }

    // 保存到数据库
    if err := s.repo.User.Create(ctx, user); err != nil {
        return nil, fmt.Errorf("创建用户失败: %w", err)
    }

    s.log.Info("用户创建成功", zap.String("email", user.Email))

    return &User{
        ID:    user.IdentityID,
        Name:  user.Name,
        Email: user.Email,
        Role:  string(user.Role),
    }, nil
}
```

### 7.3 外部服务集成
```go
// 外部AI服务客户端
type OpenAIClient struct {
    apiKey  string
    baseURL string
    client  *http.Client
    log     *logger.ModuleLogger
}

func NewOpenAIClient(apiKey, baseURL string) *OpenAIClient {
    return &OpenAIClient{
        apiKey:  apiKey,
        baseURL: baseURL,
        client:  &http.Client{Timeout: 30 * time.Second},
        log:     logger.GetLogger("openai_client"),
    }
}

func (c *OpenAIClient) ChatCompletion(ctx context.Context, req ChatRequest) (*ChatResponse, error) {
    // 构建请求
    payload, err := json.Marshal(req)
    if err != nil {
        return nil, fmt.Errorf("序列化请求失败: %w", err)
    }

    httpReq, err := http.NewRequestWithContext(ctx, "POST", c.baseURL+"/chat/completions", bytes.NewBuffer(payload))
    if err != nil {
        return nil, fmt.Errorf("创建请求失败: %w", err)
    }

    httpReq.Header.Set("Content-Type", "application/json")
    httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

    // 发送请求
    resp, err := c.client.Do(httpReq)
    if err != nil {
        return nil, fmt.Errorf("发送请求失败: %w", err)
    }
    defer resp.Body.Close()

    // 处理响应
    if resp.StatusCode != http.StatusOK {
        body, _ := io.ReadAll(resp.Body)
        return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
    }

    var chatResp ChatResponse
    if err := json.NewDecoder(resp.Body).Decode(&chatResp); err != nil {
        return nil, fmt.Errorf("解析响应失败: %w", err)
    }

    return &chatResp, nil
}
```

## 8. 工具和脚本

### 8.1 Makefile
```makefile
# Makefile
.PHONY: build test lint clean run docker

# 变量
APP_NAME=chat-server
VERSION=1.0.0
BUILD_DIR=build
DOCKER_IMAGE=topai/chat-server

# 构建
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(APP_NAME) cmd/server/main.go

# 运行
run:
	@echo "Running $(APP_NAME)..."
	@go run cmd/server/main.go -config .chat-webserver/config.yaml

# 测试
test:
	@echo "Running tests..."
	@go test -v ./...

# 覆盖率测试
test-coverage:
	@echo "Running tests with coverage..."
	@go test -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html

# 代码检查
lint:
	@echo "Running linter..."
	@golangci-lint run

# 格式化代码
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# 清理
clean:
	@echo "Cleaning..."
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html

# Docker构建
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(DOCKER_IMAGE):$(VERSION) .
	@docker tag $(DOCKER_IMAGE):$(VERSION) $(DOCKER_IMAGE):latest

# Docker运行
docker-run:
	@echo "Running Docker container..."
	@docker run -p 8080:8080 -v $(PWD)/.chat-webserver:/etc/config $(DOCKER_IMAGE):latest

# 依赖管理
deps:
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy

# 生成Swagger文档
swagger:
	@echo "Generating Swagger docs..."
	@swag init -g cmd/server/main.go -o docs/

# 数据库迁移
migrate-up:
	@echo "Running database migrations..."
	@migrate -path internal/repository/migrations -database "mysql://$(DB_USER):$(DB_PASS)@tcp($(DB_HOST):$(DB_PORT))/$(DB_NAME)" up

migrate-down:
	@echo "Rolling back database migrations..."
	@migrate -path internal/repository/migrations -database "mysql://$(DB_USER):$(DB_PASS)@tcp($(DB_HOST):$(DB_PORT))/$(DB_NAME)" down 1
```

### 8.2 开发脚本
```bash
#!/bin/bash
# scripts/dev-setup.sh

echo "Setting up development environment..."

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
MIN_VERSION="1.23"

if [ "$(printf '%s\n' "$MIN_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$MIN_VERSION" ]; then
    echo "Error: Go version $GO_VERSION is less than required $MIN_VERSION"
    exit 1
fi

# 安装开发工具
echo "Installing development tools..."
go install github.com/cosmtrek/air@latest
go install github.com/swaggo/swag/cmd/swag@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 创建必要目录
mkdir -p logs
mkdir -p uploads
mkdir -p .chat-webserver

# 检查MySQL连接
echo "Checking MySQL connection..."
mysql -h localhost -u root -p -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Warning: Cannot connect to MySQL. Please check your configuration."
fi

echo "Development environment setup completed!"
```

### 8.3 Git Hooks
```bash
#!/bin/bash
# .git/hooks/pre-commit

echo "Running pre-commit hooks..."

# 代码格式化
echo "Formatting code..."
go fmt ./...

# 代码检查
echo "Running linter..."
golangci-lint run
if [ $? -ne 0 ]; then
    echo "Linting failed. Please fix the issues."
    exit 1
fi

# 运行测试
echo "Running tests..."
go test ./...
if [ $? -ne 0 ]; then
    echo "Tests failed. Please fix the issues."
    exit 1
fi

echo "All checks passed!"
```

## 9. 性能优化

### 9.1 数据库优化
```go
// 批量操作
func (r *userRepository) BatchCreate(ctx context.Context, users []*User) error {
    return r.db.WithContext(ctx).CreateInBatches(users, 100).Error
}

// 预加载关联数据
func (r *userRepository) GetUsersWithProfiles(ctx context.Context) ([]*User, error) {
    var users []*User
    err := r.db.WithContext(ctx).Preload("Profile").Find(&users).Error
    return users, err
}

// 索引优化查询
func (r *userRepository) GetUsersByStatus(ctx context.Context, status string) ([]*User, error) {
    var users []*User
    err := r.db.WithContext(ctx).
        Where("status = ?", status).
        Order("created_at DESC").
        Find(&users).Error
    return users, err
}
```

### 9.2 缓存策略
```go
// 内存缓存
type MemoryCache struct {
    cache sync.Map
    ttl   time.Duration
}

func (c *MemoryCache) Get(key string) (interface{}, bool) {
    value, ok := c.cache.Load(key)
    if !ok {
        return nil, false
    }
    
    item := value.(*cacheItem)
    if time.Since(item.createdAt) > c.ttl {
        c.cache.Delete(key)
        return nil, false
    }
    
    return item.value, true
}

func (c *MemoryCache) Set(key string, value interface{}) {
    c.cache.Store(key, &cacheItem{
        value:     value,
        createdAt: time.Now(),
    })
}
```

### 9.3 连接池优化
```go
// 数据库连接池配置
func configureDatabasePool(db *sql.DB) {
    // 最大连接数
    db.SetMaxOpenConns(100)
    
    // 最大空闲连接数
    db.SetMaxIdleConns(10)
    
    // 连接最大存活时间
    db.SetConnMaxLifetime(time.Hour)
    
    // 连接最大空闲时间
    db.SetConnMaxIdleTime(30 * time.Minute)
}
```

## 10. 监控和调试

### 10.1 健康检查
```go
// 健康检查端点
func (h *SystemHandler) HealthCheck(c *gin.Context) {
    health := map[string]interface{}{
        "status":    "ok",
        "timestamp": time.Now().Unix(),
        "version":   "1.0.0",
    }

    // 检查数据库连接
    if err := h.service.HealthCheck(); err != nil {
        health["status"] = "error"
        health["error"] = err.Error()
        c.JSON(http.StatusServiceUnavailable, health)
        return
    }

    c.JSON(http.StatusOK, health)
}
```

### 10.2 性能监控
```go
// 性能指标收集
type Metrics struct {
    requestCount   prometheus.CounterVec
    requestLatency prometheus.HistogramVec
    activeUsers    prometheus.Gauge
}

func NewMetrics() *Metrics {
    return &Metrics{
        requestCount: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "http_requests_total",
                Help: "Total number of HTTP requests",
            },
            []string{"method", "path", "status"},
        ),
        requestLatency: prometheus.NewHistogramVec(
            prometheus.HistogramOpts{
                Name: "http_request_duration_seconds",
                Help: "HTTP request latency",
            },
            []string{"method", "path"},
        ),
    }
}
```

### 10.3 分布式追踪
```go
// OpenTelemetry集成
func setupTracing() {
    exp, err := jaeger.New(jaeger.WithCollectorEndpoint())
    if err != nil {
        log.Fatal("Failed to create exporter", err)
    }

    tp := trace.NewTracerProvider(
        trace.WithBatcher(exp),
        trace.WithResource(resource.NewWithAttributes(
            semconv.SchemaURL,
            semconv.ServiceNameKey.String("chat-server"),
        )),
    )

    otel.SetTracerProvider(tp)
}
```

---

**文档版本**: v2.0  
**更新时间**: 2024-12-28  
**维护人员**: TopAI开发团队 