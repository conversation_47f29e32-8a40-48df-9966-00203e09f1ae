package service

import (
	"context"
	"strconv"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type AudioService struct {
	db  *repository.DB
	log *logger.ModuleLogger
}

var _ AudioApi = &AudioService{}

func NewAudioService(ctx context.Context, db *repository.DB) *AudioService {
	return &AudioService{
		db:  db,
		log: logger.GetLogger("audio-service"),
	}
}

func (s *AudioService) GetAudioConfig(ctx context.Context) (*AudioConfig, error) {
	ttsConfig, err := s.GetAudioTTSConfig(ctx)
	if err != nil {
		return nil, err
	}
	asrConfig, err := s.GetAudioASRConfig(ctx)
	if err != nil {
		return nil, err
	}
	return &AudioConfig{
		TTS: ttsConfig,
		ASR: asrConfig,
	}, nil
}

func (s *AudioService) UpdateAudioConfig(ctx context.Context, config *AudioConfig) error {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, audioTTSConfigCategory)
	if err != nil {
		return err
	}
	configMap := map[string]*repository.SystemConfig{}
	for _, c := range configList {
		configMap[c.Key] = c
	}

	updateList := []*repository.SystemConfig{}

	if conf, ok := configMap[AudioConfigTTS_MODEL]; ok {
		if config.TTS.MODEL != conf.Value {
			conf.Value = config.TTS.MODEL
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: audioTTSConfigCategory,
			Key:      AudioConfigTTS_MODEL,
			Value:    config.TTS.MODEL,
		})
	}

	if conf, ok := configMap[AudioConfigTTS_VOICE]; ok {
		if config.TTS.VOICE != conf.Value {
			conf.Value = config.TTS.VOICE
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: audioTTSConfigCategory,
			Key:      AudioConfigTTS_VOICE,
			Value:    config.TTS.VOICE,
		})
	}

	if conf, ok := configMap[AudioConfigTTS_ENABLE]; ok {
		if config.TTS.ENABLE != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.TTS.ENABLE)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: audioTTSConfigCategory,
			Key:      AudioConfigTTS_ENABLE,
			Value:    strconv.FormatBool(config.TTS.ENABLE),
		})
	}

	configList, err = s.db.SystemConfig.GetByCategory(ctx, audioASRConfigCategory)
	if err != nil {
		return err
	}

	configMap = map[string]*repository.SystemConfig{}
	for _, c := range configList {
		configMap[c.Key] = c
	}
	if conf, ok := configMap[AudioConfigASR_MODEL]; ok {
		if config.ASR.MODEL != conf.Value {
			conf.Value = config.ASR.MODEL
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: audioASRConfigCategory,
			Key:      AudioConfigASR_MODEL,
			Value:    config.ASR.MODEL,
		})
	}

	if conf, ok := configMap[AudioConfigASR_ENABLE]; ok {
		if config.ASR.ENABLE != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ASR.ENABLE)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: audioASRConfigCategory,
			Key:      AudioConfigASR_ENABLE,
			Value:    strconv.FormatBool(config.ASR.ENABLE),
		})
	}

	if len(updateList) == 0 {
		return nil
	}

	return s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		for _, c := range updateList {
			if c.ID > 0 {
				err = db.SystemConfig.UpdateByCategoryAndKey(ctx, c.Category, c.Key, c.Value)
			} else {
				err = db.SystemConfig.Create(ctx, c)
			}
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (s *AudioService) GetAudioTTSConfig(ctx context.Context) (*AudioConfigTTS, error) {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, audioTTSConfigCategory)
	if err != nil {
		return nil, err
	}

	config := &AudioConfigTTS{}

	for _, c := range configList {
		switch c.Key {
		case AudioConfigTTS_MODEL:
			config.MODEL = c.Value
		case AudioConfigTTS_VOICE:
			config.VOICE = c.Value
		case AudioConfigTTS_ENABLE:
			config.ENABLE = c.Value == "true"
		}
	}

	return config, nil
}

func (s *AudioService) GetAudioASRConfig(ctx context.Context) (*AudioConfigASR, error) {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, audioASRConfigCategory)
	if err != nil {
		return nil, err
	}

	config := &AudioConfigASR{}

	for _, c := range configList {
		switch c.Key {
		case AudioConfigASR_MODEL:
			config.MODEL = c.Value
		case AudioConfigASR_ENABLE:
			config.ENABLE = c.Value == "true"
		}
	}

	return config, nil
}

func (s *AudioService) GetAudioVoices(ctx context.Context) ([]*AudioVoice, error) {
	// 先写死
	return []*AudioVoice{
		{
			ID:   "alloy",
			Name: "alloy",
		},
		{
			ID:   "echo",
			Name: "echo",
		},
		{
			ID:   "fable",
			Name: "fable",
		},
		{
			ID:   "onyx",
			Name: "onyx",
		},
		{
			ID:   "nova",
			Name: "nova",
		},
		{
			ID:   "shimmer",
			Name: "shimmer",
		},
	}, nil
}

func (s *AudioService) GetAudioBaseModels(ctx context.Context) (*AudioBaseModel, error) {
	modelExts, err := s.db.TopaiModelExt.GetOnlineAll(ctx)
	if err != nil {
		return nil, err
	}
	modelIds := make([]uint, 0, len(modelExts))
	for _, model := range modelExts {
		modelIds = append(modelIds, model.TopaiModelId)
	}
	models, err := s.db.TopaiModel.GetByIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}

	modelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range models {
		modelMap[model.Id] = model
	}

	audioModels := &AudioBaseModel{
		ASR: []*AudioModel{},
		TTS: []*AudioModel{},
	}

	for _, ext := range modelExts {
		model, ok := modelMap[ext.TopaiModelId]
		if !ok {
			continue
		}
		if model.ModelType == repository.RemoterModelTypeTTS.String() {
			voices, err := s.db.TTSModelVoice.GetByModelName(ctx, ext.ModelId)
			if err != nil {
				return nil, err
			}
			if len(voices) == 0 {
				// 没有音色，不展示
				continue
			}
			voicesList := make([]*AudioVoice, len(voices))
			for i, voice := range voices {
				voicesList[i] = &AudioVoice{
					ID:   voice.Name,
					Name: voice.Name,
				}
			}
			audioModels.TTS = append(audioModels.TTS, &AudioModel{
				ID:      ext.Id,
				ModelId: ext.ModelId,
				Voices:  voicesList,
			})
		}
		if model.ModelType == repository.RemoterModelTypeASR.String() {
			audioModels.ASR = append(audioModels.ASR, &AudioModel{
				ID:      ext.Id,
				ModelId: ext.ModelId,
			})
		}
	}
	return audioModels, nil
}
