package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// {"data":{"created_at":"2025-06-24T10:02:59.129327+00:00","model":"mistralai/mistral-small-3.1-24b-instruct-2503:free","app_id":null,"external_user":null,"streamed":true,"cancelled":false,"latency":1438,"moderation_latency":null,"generation_time":278,"tokens_prompt":353,"tokens_completion":11,"native_tokens_prompt":423,"native_tokens_completion":16,"native_tokens_reasoning":0,"native_tokens_cached":0,"num_media_prompt":null,"num_media_completion":null,"num_search_results":null,"origin":"","is_byok":false,"finish_reason":"stop","native_finish_reason":"stop","usage":0,"id":"gen-**********-aJbegP9Ocj7ZrWpMd5if","upstream_id":"chatcmpl-dcacbd0c8b0c473a926658123693cc20","total_cost":0,"cache_discount":null,"provider_name":"Chutes"}}

type openRouterUsage struct {
	Data struct {
		CreatedAt              string  `json:"created_at"`
		Model                  string  `json:"model"`
		Latency                int     `json:"latency"`
		GenerationTime         int     `json:"generation_time"`
		TokensPrompt           int     `json:"tokens_prompt"`
		TokensCompletion       int     `json:"tokens_completion"`
		NativeTokensPrompt     int     `json:"native_tokens_prompt"`
		NativeTokensCompletion int     `json:"native_tokens_completion"`
		NativeTokensReasoning  int     `json:"native_tokens_reasoning"`
		NativeTokensCached     int     `json:"native_tokens_cached"`
		Origin                 string  `json:"origin"`
		IsByok                 bool    `json:"is_byok"`
		FinishReason           string  `json:"finish_reason"`
		NativeFinishReason     string  `json:"native_finish_reason"`
		Usage                  float64 `json:"usage"`
		Id                     string  `json:"id"`
		UpstreamId             string  `json:"upstream_id"`
		TotalCost              float64 `json:"total_cost"`
		ProviderName           string  `json:"provider_name"`
	} `json:"data"`
}

func GetOpenRouterUsage(ctx context.Context, baseUrl string, apiKey string, requestId string) (*openRouterUsage, error) {
	url := fmt.Sprintf("%s/generation?id=%s", baseUrl, requestId)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println(string(body))

	var usage openRouterUsage
	if err := json.Unmarshal(body, &usage); err != nil {
		return nil, err
	}
	return &usage, nil
}
