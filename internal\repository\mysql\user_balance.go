package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type userBalanceRepository struct {
	db *gorm.DB
}

func NewUserBalanceRepository(db *gorm.DB) repository.UserBalanceRepository {
	return &userBalanceRepository{db: db}
}

func (r *userBalanceRepository) Create(ctx context.Context, balance *repository.UserBalance) error {
	return r.db.WithContext(ctx).Create(balance).Error
}

func (r *userBalanceRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserBalance, error) {
	var balance repository.UserBalance
	err := r.db.WithContext(ctx).Table(repository.UserBalance{}.TableName()).Where("user_id = ? and currency = ?", userID, currency).First(&balance).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &balance, nil
}

func (r *userBalanceRepository) UpdateBalanceByID(ctx context.Context, id uint, balance string, accumulated string) error {
	return r.db.WithContext(ctx).Table(repository.UserBalance{}.TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"balance":     balance,
		"accumulated": accumulated,
	}).Error
}

type userBalanceRecordRepository struct {
	db *gorm.DB
}

func NewUserBalanceRecordRepository(db *gorm.DB) repository.UserBalanceRecordRepository {
	return &userBalanceRecordRepository{db: db}
}

func (r *userBalanceRecordRepository) Create(ctx context.Context, record *repository.UserBalanceRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *userBalanceRecordRepository) BatchCreate(ctx context.Context, records []*repository.UserBalanceRecord) error {
	return r.db.WithContext(ctx).Create(records).Error
}

func (r *userBalanceRecordRepository) GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*repository.UserBalanceRecord, error) {
	var records []*repository.UserBalanceRecord
	err := r.db.WithContext(ctx).Table(repository.UserBalanceRecord{}.TableName()).Where("created_at between ? and ?", start, end).Find(&records).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

func (r *userBalanceRecordRepository) CountByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (int64, error) {
	var total int64
	err := r.db.WithContext(ctx).Table(repository.UserBalanceRecord{}.TableName()).Where("user_id = ? and currency = ?", userID, currency).Count(&total).Error
	if err != nil {
		return 0, err
	}
	return total, nil
}

func (r *userBalanceRecordRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string, offset, pageSize int, orderBy, orderDirection string) ([]*repository.UserBalanceRecord, error) {
	var records []*repository.UserBalanceRecord
	if orderBy == "" {
		orderBy = "id"
	}
	if orderDirection == "" {
		orderDirection = "desc"
	}
	err := r.db.WithContext(ctx).Where("user_id = ? and currency = ?", userID, currency).Offset(offset).Limit(pageSize).Order(orderBy + " " + orderDirection).Find(&records).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

type userRechargeRecordRepository struct {
	db *gorm.DB
}

func NewUserRechargeRecordRepository(db *gorm.DB) repository.UserRechargeRecordRepository {
	return &userRechargeRecordRepository{db: db}
}

func (r *userRechargeRecordRepository) Create(ctx context.Context, record *repository.UserRechargeRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *userRechargeRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserRechargeRecord, error) {
	var records []*repository.UserRechargeRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.UserRechargeRecordStatusUnconfirmed).Find(&records).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

func (r *userRechargeRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, userBalanceRecordId uint) error {
	return r.db.WithContext(ctx).Table(repository.UserRechargeRecord{}.TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"status":                 repository.UserRechargeRecordStatusConfirmed,
		"user_balance_record_id": userBalanceRecordId,
	}).Error
}

func (r *userRechargeRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	return r.db.WithContext(ctx).Table(repository.UserRechargeRecord{}.TableName()).Where("id = ?", id).Updates(map[string]interface{}{
		"status": repository.UserRechargeRecordStatusFailed,
	}).Error
}
