{"_format": "hh-sol-artifact-1", "contractName": "AIWorkload", "sourceName": "contracts/AI/AIWorkload.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_nodeRegistry", "type": "address"}, {"internalType": "address", "name": "_modelRegistry", "type": "address"}, {"internalType": "address", "name": "_stakeToken", "type": "address"}, {"internalType": "address", "name": "_settlement", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sessionId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "reporter", "type": "address"}, {"indexed": false, "internalType": "address", "name": "worker", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "epochId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "workload", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "modelId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "inputWorload", "type": "uint256"}], "name": "WorkloadReported", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "sessionId", "type": "uint256"}], "name": "getLastEpoch", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "sessionId", "type": "uint256"}, {"internalType": "uint256", "name": "epochId", "type": "uint256"}], "name": "getNodeWorkload", "outputs": [{"components": [{"internalType": "uint256", "name": "epochId", "type": "uint256"}, {"internalType": "uint256", "name": "workload", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "modelId", "type": "uint256"}, {"internalType": "address", "name": "reporter", "type": "address"}, {"internalType": "address", "name": "worker", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "inputWorload", "type": "uint256"}], "internalType": "struct AIWorkload.Workload", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "modelId", "type": "uint256"}], "name": "getTotalModelWorkload", "outputs": [{"components": [{"internalType": "uint256", "name": "totalWorkload", "type": "uint256"}, {"internalType": "uint256", "name": "settledWorkload", "type": "uint256"}], "internalType": "struct AIWorkload.WorkLoad", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "reporter", "type": "address"}], "name": "getTotalWorkReport", "outputs": [{"components": [{"internalType": "uint256", "name": "totalWorkload", "type": "uint256"}, {"internalType": "uint256", "name": "settledWorkload", "type": "uint256"}], "internalType": "struct AIWorkload.WorkLoad", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "node", "type": "address"}], "name": "getTotalWorkerWorkload", "outputs": [{"components": [{"internalType": "uint256", "name": "totalWorkload", "type": "uint256"}, {"internalType": "uint256", "name": "settledWorkload", "type": "uint256"}], "internalType": "struct AIWorkload.WorkLoad", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastSettlementTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "modelRegistry", "outputs": [{"internalType": "contract AIModels", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nodeRegistry", "outputs": [{"internalType": "contract NodesRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "worker", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "workload", "type": "uint256"}, {"internalType": "uint256", "name": "modelId", "type": "uint256"}, {"internalType": "uint256", "name": "sessionId", "type": "uint256"}, {"internalType": "uint256", "name": "epochId", "type": "uint256"}, {"internalType": "uint256", "name": "inputWorkload", "type": "uint256"}, {"components": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}, {"internalType": "uint8", "name": "v", "type": "uint8"}], "internalType": "struct Signature[]", "name": "signatures", "type": "tuple[]"}], "name": "reportWorkload", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "sessions", "outputs": [{"internalType": "uint256", "name": "lastEpochId", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "settleRewards", "outputs": [{"components": [{"internalType": "address", "name": "node", "type": "address"}, {"internalType": "uint256", "name": "workload", "type": "uint256"}], "internalType": "struct NodeSettleWorkload[]", "name": "settledWorkers", "type": "tuple[]"}, {"components": [{"internalType": "uint256", "name": "modelId", "type": "uint256"}, {"internalType": "uint256", "name": "workload", "type": "uint256"}], "internalType": "struct ModelSettleWorkload[]", "name": "settledModels", "type": "tuple[]"}, {"components": [{"internalType": "address", "name": "node", "type": "address"}, {"internalType": "uint256", "name": "workload", "type": "uint256"}], "internalType": "struct NodeSettleWorkload[]", "name": "settledReporters", "type": "tuple[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "settlement", "outputs": [{"internalType": "contract Settlement", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "settlementInterval", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "contract IStake", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}