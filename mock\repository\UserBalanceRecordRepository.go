// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserBalanceRecordRepository is an autogenerated mock type for the UserBalanceRecordRepository type
type UserBalanceRecordRepository struct {
	mock.Mock
}

// BatchCreate provides a mock function with given fields: ctx, records
func (_m *UserBalanceRecordRepository) BatchCreate(ctx context.Context, records []*repository.UserBalanceRecord) error {
	ret := _m.Called(ctx, records)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*repository.UserBalanceRecord) error); ok {
		r0 = rf(ctx, records)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CountByUserIDAndCurrency provides a mock function with given fields: ctx, userID, currency
func (_m *UserBalanceRecordRepository) CountByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (int64, error) {
	ret := _m.Called(ctx, userID, currency)

	if len(ret) == 0 {
		panic("no return value specified for CountByUserIDAndCurrency")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (int64, error)); ok {
		return rf(ctx, userID, currency)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) int64); ok {
		r0 = rf(ctx, userID, currency)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, userID, currency)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserBalanceRecordRepository) Create(ctx context.Context, record *repository.UserBalanceRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserBalanceRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByUserIDAndCurrency provides a mock function with given fields: ctx, userID, currency, offset, pageSize, orderBy, orderDirection
func (_m *UserBalanceRecordRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string, offset int, pageSize int, orderBy string, orderDirection string) ([]*repository.UserBalanceRecord, error) {
	ret := _m.Called(ctx, userID, currency, offset, pageSize, orderBy, orderDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserIDAndCurrency")
	}

	var r0 []*repository.UserBalanceRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string, int, int, string, string) ([]*repository.UserBalanceRecord, error)); ok {
		return rf(ctx, userID, currency, offset, pageSize, orderBy, orderDirection)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string, int, int, string, string) []*repository.UserBalanceRecord); ok {
		r0 = rf(ctx, userID, currency, offset, pageSize, orderBy, orderDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserBalanceRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string, int, int, string, string) error); ok {
		r1 = rf(ctx, userID, currency, offset, pageSize, orderBy, orderDirection)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserBalanceRecordRepository creates a new instance of UserBalanceRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserBalanceRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserBalanceRecordRepository {
	mock := &UserBalanceRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
