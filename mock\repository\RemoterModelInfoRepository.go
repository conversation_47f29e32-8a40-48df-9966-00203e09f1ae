// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// RemoterModelInfoRepository is an autogenerated mock type for the RemoterModelInfoRepository type
type RemoterModelInfoRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *RemoterModelInfoRepository) GetAll(ctx context.Context) ([]*repository.RemoterModelInfo, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.RemoterModelInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.RemoterModelInfo, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.RemoterModelInfo); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.RemoterModelInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelName provides a mock function with given fields: ctx, modelName
func (_m *RemoterModelInfoRepository) GetByModelName(ctx context.Context, modelName string) (*repository.RemoterModelInfo, error) {
	ret := _m.Called(ctx, modelName)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelName")
	}

	var r0 *repository.RemoterModelInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.RemoterModelInfo, error)); ok {
		return rf(ctx, modelName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.RemoterModelInfo); ok {
		r0 = rf(ctx, modelName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.RemoterModelInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewRemoterModelInfoRepository creates a new instance of RemoterModelInfoRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRemoterModelInfoRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *RemoterModelInfoRepository {
	mock := &RemoterModelInfoRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
