package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"path"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	_ "topnetwork.ai/topai/chat-webserver/docs"
	"topnetwork.ai/topai/chat-webserver/internal/api/handlers"
	"topnetwork.ai/topai/chat-webserver/internal/api/middleware"
	"topnetwork.ai/topai/chat-webserver/internal/repository/mysql"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

var (
	configPath = flag.String("config", ".chat-webserver/config.yaml", "Path to configuration file")
)

// @title topai web服务api文档
// @version 1.0
// @description 用户登陆，聊天系统
// @host 127.0.0.1:8080
// @securityDefinitions.apikey   BearerAuth
// @in header
// @name Authorization
func main() {
	// 解析命令行参数
	flag.Parse()

	baseDir, err := os.Getwd()
	if err != nil {
		fmt.Println("Failed to get base directory", err)
		os.Exit(1)
	}
	fmt.Println("Base directory", baseDir)

	// 如果不熟绝对路径改成绝对路径
	if !strings.HasPrefix(*configPath, "/") {
		*configPath = path.Join(baseDir, *configPath)
	}
	fmt.Println("Config path", *configPath)

	// 加载配置文件
	if err := config.LoadConfig(*configPath); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	conf := config.GetConfig()

	// 初始化日志系统
	logger.InitLogger(&conf.Log)
	log := logger.GetLogger("main")

	// 初始化数据库连接
	db, err := mysql.NewClient(&conf.Database)
	if err != nil {
		log.Error("Failed to initialize database", zap.Error(err))
		os.Exit(1)
	}
	defer db.Close()
	mainCtx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建服务实例
	svc := service.NewService(mainCtx, db)
	svc.Start(mainCtx)

	// 创建 Gin 路由引擎
	if conf.Log.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
		//gin.SetMode(gin.DebugMode)
	}
	router := gin.New()
	router.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerFiles.Handler,
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
	))

	// 添加全局中间件
	middleware.Init()

	// 初始化并发限制器
	middleware.InitConcurrencyLimiter(conf.Service.ConcurrencyLimit)

	router.Use(middleware.Logger())                        // 记录请求日志
	router.Use(middleware.CORS())                          // 支持跨域请求
	router.Use(gin.Recovery())                             // 从任何 panic 恢复
	router.Use(middleware.RateLimitByIP(500, time.Minute)) // 根据ip限制请求频率

	// 创建处理器实例并注册路由
	handlerStop := handlers.NewHandler(mainCtx, router, svc)
	defer handlerStop()

	// 启动服务器
	serverConfig := config.GetConfig().Server
	addr := fmt.Sprintf(":%d", serverConfig.Port)

	log.Info("Starting server", zap.String("addr", addr))

	server := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	// 优雅地关闭服务器
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error("Failed to start server", zap.Error(err))
			os.Exit(1)
		}
	}()

	// 等待信号
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, os.Interrupt, syscall.SIGTERM)
	<-signalChan

	log.Info("Shutdown signal received, exiting...")
	cancel()

	// 创建一个上下文来通知服务器关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Error("Failed to shutdown server", zap.Error(err))
		os.Exit(1)
	}

	log.Info("Server gracefully stopped")
}
