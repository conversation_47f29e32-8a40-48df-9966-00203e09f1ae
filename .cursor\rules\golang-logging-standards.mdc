---
description: 
globs: 
alwaysApply: false
---
---
description: Logging Standards and Implementation
globs: logger/**/*.go
alwaysApply: false
---

When implementing logging:

1. Log Levels
- Define clear log levels
- Include context information
- Support structured logging
- Handle sensitive data
- Add module-specific logging
- Enable dynamic level modification

Required logger implementation:
```go
type ModuleLogger struct {
    name    string
    logger  *zap.Logger
    level   zap.AtomicLevel  // Changed to atomic level for dynamic updates
    context map[string]interface{}
}

// Global registry for module loggers
var (
    registry sync.Map
    defaultConfig *config.Logger
)

func InitLogger(config config.Logger) {
    defaultConfig = &config
}

// GetOrCreate module logger
func GetLogger(module string) *ModuleLogger {
    if defaultConfig == nil {
        panic("Not init config")
    }
    if val, ok := registry.Load(module); ok {
        return val.(*ModuleLogger)
    }
    
    newLogger := &ModuleLogger{
        name:    module,
        level:   zap.NewAtomicLevelAt(defaultConfig.Level),
        context: make(map[string]interface{}),
    }
    newLogger.initLogger()
    registry.Store(module, newLogger)
    return newLogger
}

// Dynamic level update method
func (ml *ModuleLogger) SetLevel(level zapcore.Level) {
    ml.level.SetLevel(level)
}
```

2. Log Format
- Use consistent formatting
- Include timestamp
- Add correlation IDs
- Support log aggregation

3. Log Management
- Implement log rotation
  - Support size-based log rotation (default: 100MB)
  - Configurable number of retained historical log files
  - Automatic cleanup of expired logs
- Handle log storage
- Support log analysis
- Monitor log volume
- Unified log file writer
  - Singleton writer instance
    - Ensure all services/modules share a single writer instance globally
    - Thread-safe initialization (e.g., double-checked locking)
    - Prohibit direct instantiation via private constructor
  - Centralized write control
    - Use unified entry point for all log writes (e.g., LogWriter.write())
    - Synchronized write operations to prevent file conflicts
    - Buffer mechanism for batch writes (configurable buffer size)
  - Failure resilience
    - Retry failed writes with exponential backoff strategy
    - Fallback to temporary cache if persistent failures occur
  - Dynamic configuration binding
    - Auto-reload writer settings (e.g., path/format) without restart
    - Validate configuration changes before hot-swapping
  - Compatibility guarantees
    - Seamless integration with log rotation policies
    - Maintain file handle consistency during log file switching
