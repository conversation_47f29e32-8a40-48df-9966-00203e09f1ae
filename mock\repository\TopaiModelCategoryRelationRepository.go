// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelCategoryRelationRepository is an autogenerated mock type for the TopaiModelCategoryRelationRepository type
type TopaiModelCategoryRelationRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelCategoryRelationRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelCategoryRelation, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelCategoryRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelCategoryRelation, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelCategoryRelation); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategoryRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByCategoryIds provides a mock function with given fields: ctx, categoryIds
func (_m *TopaiModelCategoryRelationRepository) GetByCategoryIds(ctx context.Context, categoryIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	ret := _m.Called(ctx, categoryIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByCategoryIds")
	}

	var r0 []*repository.TopaiModelCategoryRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelCategoryRelation, error)); ok {
		return rf(ctx, categoryIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelCategoryRelation); ok {
		r0 = rf(ctx, categoryIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategoryRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, categoryIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *TopaiModelCategoryRelationRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.TopaiModelCategoryRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelCategoryRelation, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelCategoryRelation); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategoryRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelId provides a mock function with given fields: ctx, topaiModelId
func (_m *TopaiModelCategoryRelationRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelCategoryRelation, error) {
	ret := _m.Called(ctx, topaiModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelId")
	}

	var r0 []*repository.TopaiModelCategoryRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.TopaiModelCategoryRelation, error)); ok {
		return rf(ctx, topaiModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.TopaiModelCategoryRelation); ok {
		r0 = rf(ctx, topaiModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategoryRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, topaiModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelIds provides a mock function with given fields: ctx, topaiModelIds
func (_m *TopaiModelCategoryRelationRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	ret := _m.Called(ctx, topaiModelIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelIds")
	}

	var r0 []*repository.TopaiModelCategoryRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelCategoryRelation, error)); ok {
		return rf(ctx, topaiModelIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelCategoryRelation); ok {
		r0 = rf(ctx, topaiModelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategoryRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, topaiModelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelCategoryRelationRepository creates a new instance of TopaiModelCategoryRelationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelCategoryRelationRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelCategoryRelationRepository {
	mock := &TopaiModelCategoryRelationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
