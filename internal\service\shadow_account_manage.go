package service

import (
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
	"topnetwork.ai/topai/chat-webserver/utils/chain"
)

type shadowAccountManage struct {
	repo              *repository.DB
	log               *logger.ModuleLogger
	shadowConf        *config.ShadowAccountConfig
	topChainConf      *config.TopChain
	minTopBalance     *big.Int // 最低需要作为gas的top数量
	transferTopAmount *big.Int // 每次转账的top数量
	companyUserID     uint     // 公司账户ID，用于扣除非apiKey的usage
	utilChainFunc     *UtilChainFunc
}

func NewShadowAccountManage(repo *repository.DB, serviceConf *config.ServiceConfig, utilChainFunc *UtilChainFunc) *shadowAccountManage {
	return &shadowAccountManage{
		repo:              repo,
		log:               logger.GetLogger("shadow_account_manage"),
		shadowConf:        &serviceConf.ShadowAccount,
		topChainConf:      &serviceConf.TopChain,
		minTopBalance:     big.NewInt(1000e6), // 先设置最低需要1000Top作为gas
		transferTopAmount: big.NewInt(5000e6), // 每次转账5000Top
		companyUserID:     serviceConf.CompanyUserID,
		utilChainFunc:     utilChainFunc,
	}
}

func (s *shadowAccountManage) Start(ctx context.Context) error {
	// 每次启动时，需要保证一下worker的活跃
	//注册节点
	var exist bool
	var err error
	var tx string
	nameArray := []string{
		"jack",
		"tom",
		"jerry",
	}
	for i, privateKey := range chain.PrivateKeys {
		//tx, err := chain.DeregisterNode(ctx, chainUrl, nodesRegistryAddress, privateKey)
		//h.logger.Info("deregister node tx", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.String("tx", tx), zap.Error(err))
		exist, err = chain.CheckRegisteNode(ctx, s.topChainConf.ChainUrl, s.topChainConf.NodeRegisterAddress, crypto.PubkeyToAddress(privateKey.PublicKey).Hex())
		if err != nil {
			s.log.Error("check register node error", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Error(err))
			continue
		}
		s.log.Info("check register node", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Bool("exist", exist), zap.Error(err))
		if exist {
			continue
		}
		tx, err = chain.RegisterNode(ctx, s.topChainConf.ChainUrl, s.topChainConf.NodeRegisterAddress, privateKey, nameArray[i])
		if err != nil {
			s.log.Error("register node error", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Error(err))
			continue
		}
		s.log.Info("register node", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.String("tx", tx), zap.Error(err))
		//tx, err := chain.RegisterNode(ctx, chainUrl, nodesRegistryAddress, privateKey, aliasIdentifiers[i])
		tx, err = chain.NodeVote(ctx, s.topChainConf.ChainUrl, s.topChainConf.NodeRegisterAddress, true)
		if err != nil {
			s.log.Error("node vote error", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Error(err))
			continue
		}
		s.log.Info("register node tx", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.String("tx", tx), zap.Error(err))
		exist, err = chain.CheckRegisteNode(ctx, s.topChainConf.ChainUrl, s.topChainConf.NodeRegisterAddress, crypto.PubkeyToAddress(privateKey.PublicKey).Hex())
		if err != nil {
			s.log.Error("check register node error", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Error(err))
			continue
		}
		s.log.Info("check register node", zap.String("address", crypto.PubkeyToAddress(privateKey.PublicKey).Hex()), zap.Bool("exist", exist), zap.Error(err))
	}

	go s.cron(ctx)
	go s.monitorUsdtAndTopErc20Transfer(ctx)
	go s.monitorDepositContract(ctx)
	return nil
}

func (s *shadowAccountManage) cron(ctx context.Context) error {
	usageTime := time.Now().Add(-time.Hour * 24)
	walletTransferTime := time.Now().Add(-time.Hour * 24)
	shadowWalletBalanceTime := time.Now().Add(-time.Hour * 24)
	reportCostTime := time.Now().Add(-time.Hour * 24)
	depositRecordTime := time.Now().Add(-time.Hour * 24)
	// 按照顺序执行
	for {
		select {
		case <-ctx.Done():
			return nil
		default:
		}
		// 定时上报到链上
		if time.Since(usageTime) > 0 {
			err := s.settleUserChatUsage(ctx)
			if err != nil {
				s.log.Error("settle user chat usage error", zap.Error(err))
			}
			// 每4小时执行一次
			usageTime = time.Now().Add(time.Hour * 2)
		}

		// 定时确认转账记录
		if time.Since(walletTransferTime) > 0 {
			err := s.ConfirmWalletTransfer(ctx)
			if err != nil {
				s.log.Error("confirm wallet transfer error", zap.Error(err))
			}
			walletTransferTime = time.Now().Add(time.Hour * 2)
		}

		// 定时确认钱包记录更新钱包余额
		if time.Since(shadowWalletBalanceTime) > 0 {
			err := s.ConfirmShadowWalletBalanceRecord(ctx)
			if err != nil {
				s.log.Error("confirm shadow wallet balance record error", zap.Error(err))
			}
			shadowWalletBalanceTime = time.Now().Add(time.Second * 30)
		}

		// 定时确认上报结果
		if time.Since(reportCostTime) > 0 {
			err := s.ConfirmReportCostRecord(ctx)
			if err != nil {
				s.log.Error("confirm report cost record error", zap.Error(err))
			}
			reportCostTime = time.Now().Add(time.Hour * 2)
		}

		// 定时确认质押记录
		if time.Since(depositRecordTime) > 0 {
			err := s.ConfirmWalletDepositRecord(ctx)
			if err != nil {
				s.log.Error("confirm wallet deposit record error", zap.Error(err))
			}
			depositRecordTime = time.Now().Add(time.Hour * 2)
		}

		time.Sleep(time.Second * 10)
	}
}

// 监控usdt和top的erc20合约转账记录，只做同步
func (s *shadowAccountManage) monitorUsdtAndTopErc20Transfer(ctx context.Context) {
	lastScanBlockNumber := uint64(12037)
	lastRecord, err := s.repo.UserShadowWalletBalanceRecord.GetLastByCurrencys(ctx, []string{repository.UserBalanceCurrencyUSDT, repository.UserBalanceCurrencyTOP})
	if err != nil {
		s.log.Fatal("get last scan block number error", zap.Error(err))
		return
	}
	if lastRecord != nil && lastRecord.BlockNumber > lastScanBlockNumber {
		lastScanBlockNumber = lastRecord.BlockNumber
	}
	// 每次最多查找100个高度
	const maxBlocksPerScan = 100

	for {
		select {
		case <-ctx.Done():
			return
		default:
		}
		latestBlockNumber, err := chain.GetLatestBlockNumber(ctx, s.topChainConf.ChainUrl)
		if err != nil {
			s.log.Error("get latest block number error", zap.Error(err))
			time.Sleep(time.Second * 10)
			continue
		}
		if latestBlockNumber <= lastScanBlockNumber {
			time.Sleep(time.Second * 10)
			continue
		}

		startBlock := lastScanBlockNumber + 1
		endBlock := latestBlockNumber
		if endBlock-startBlock+1 > maxBlocksPerScan {
			endBlock = startBlock + maxBlocksPerScan - 1
		}

		// 查询区块范围内的USDT转账事件
		events, err := chain.GetErc20TransferEvents(ctx, s.topChainConf.ChainUrl, []string{s.topChainConf.UsdtErc20WrapperAddress, s.topChainConf.TopErc20WrapperAddress}, startBlock, endBlock)
		if err != nil {
			s.log.Error("get erc20 transfer events error", zap.Error(err), zap.Uint64("startBlock", startBlock), zap.Uint64("endBlock", endBlock))
			time.Sleep(time.Second * 10)
			continue
		}

		records := make([]*repository.UserShadowWalletBalanceRecord, 0)
		// 按照区块高度正序
		sort.Slice(events, func(i, j int) bool {
			return events[i].BlockNumber < events[j].BlockNumber
		})
		for _, event := range events {
			// 检查该txHash是否已处理
			// exist, err := s.repo.UserShadowWalletBalanceRecord.ExistsByTxHash(ctx, event.TxHash)
			// if err != nil {
			// 	s.log.Error("check balance record exists error", zap.Error(err), zap.String("txHash", event.TxHash))
			// 	continue
			// }
			// if exist {
			// 	continue
			// }
			currency := repository.UserBalanceCurrencyUSDT
			if event.ContractAddress == s.topChainConf.TopErc20WrapperAddress {
				currency = repository.UserBalanceCurrencyTOP
			}

			// 如果是从 pool 账户转出，则需要记录到账
			if event.From == s.shadowConf.PoolAccountPub {
				records = append(records, &repository.UserShadowWalletBalanceRecord{
					UserID:        0,
					PublicAddress: event.From,
					TxHash:        event.TxHash,
					BlockNumber:   event.BlockNumber,
					Amount:        event.Value.String(),
					Status:        repository.UserShadowWalletBalanceRecordStatusConfirmed,
					Currency:      currency,
					Type:          repository.UserShadowWalletBalanceRecordTypeUnknown,
					Description:   "send",
					IsIn:          0,
				})
			} else {
				// 判断是否是用户转出, 用户转出正常是质押到deposit合约
				userShadowWallet, err := s.repo.UserShadowWallet.GetByPublicAddress(ctx, event.From)
				if err != nil {
					s.log.Error("get user shadow wallet by address error", zap.Error(err), zap.String("from", event.From))
					continue
				}
				recordType := repository.UserShadowWalletBalanceRecordTypeUnknown
				// 如果转入的是bank合约，则认为是质押
				if event.To == s.topChainConf.BankAddress {
					recordType = repository.UserShadowWalletBalanceRecordTypeDeposit
				}
				// 如果用户影子账户存在，则记录到账
				if userShadowWallet != nil && userShadowWallet.UserID > 0 {
					records = append(records, &repository.UserShadowWalletBalanceRecord{
						UserID:        userShadowWallet.UserID,
						PublicAddress: event.From,
						TxHash:        event.TxHash,
						BlockNumber:   event.BlockNumber,
						Amount:        event.Value.String(),
						Status:        repository.UserShadowWalletBalanceRecordStatusUnconfirmed,
						Currency:      currency,
						Type:          recordType,
						Description:   "send",
						IsIn:          0,
					})
				}
			}
			if event.To == s.shadowConf.PoolAccountPub {
				// 如果是 pool 账户收入，则需要记录收入
				records = append(records, &repository.UserShadowWalletBalanceRecord{
					UserID:        0,
					PublicAddress: event.To,
					TxHash:        event.TxHash,
					BlockNumber:   event.BlockNumber,
					Amount:        event.Value.String(),
					Status:        repository.UserShadowWalletBalanceRecordStatusConfirmed,
					Currency:      currency,
					Type:          repository.UserShadowWalletBalanceRecordTypeUnknown,
					Description:   "receive",
					IsIn:          1,
				})
			} else {
				// 如果是用户收入，则需要记录收入
				userShadowWallet, err := s.repo.UserShadowWallet.GetByPublicAddress(ctx, event.To)
				if err != nil {
					s.log.Error("get user shadow wallet by address error", zap.Error(err), zap.String("to", event.To))
					continue
				}
				recordType := repository.UserShadowWalletBalanceRecordTypeRecharge
				if event.From == s.shadowConf.PoolAccountPub {
					recordType = repository.UserShadowWalletBalanceRecordTypeGift
				}
				if userShadowWallet != nil && userShadowWallet.UserID > 0 {
					records = append(records, &repository.UserShadowWalletBalanceRecord{
						UserID:        userShadowWallet.UserID,
						PublicAddress: event.To,
						TxHash:        event.TxHash,
						BlockNumber:   event.BlockNumber,
						Amount:        event.Value.String(),
						Status:        repository.UserShadowWalletBalanceRecordStatusUnconfirmed,
						Currency:      currency,
						Type:          recordType,
						Description:   "receive",
						IsIn:          1,
					})
				}
			}
		}

		if len(records) > 0 {
			err = s.repo.UserShadowWalletBalanceRecord.BatchCreate(ctx, records)
			if err != nil {
				s.log.Error("batch create user shadow wallet balance record error", zap.Error(err))
				time.Sleep(time.Second * 10)
				continue
			}
		}

		lastScanBlockNumber = endBlock
		time.Sleep(time.Second * 10)
	}
}

// 监控deposit合约变动，只同步
func (s *shadowAccountManage) monitorDepositContract(ctx context.Context) {
	lastScanBlockNumber := uint64(0)
	lastRecord, err := s.repo.UserShadowWalletBalanceRecord.GetLastByCurrencys(ctx, []string{repository.UserBalanceCurrencyUSDT_DEPOSIT})
	if err != nil {
		s.log.Fatal("get last scan block number error", zap.Error(err))
		return
	}
	if lastRecord != nil && lastRecord.BlockNumber > lastScanBlockNumber {
		lastScanBlockNumber = lastRecord.BlockNumber
	}
	// 每次最多查找100个高度
	const maxBlocksPerScan = 100

	for {
		select {
		case <-ctx.Done():
			return
		default:
		}
		latestBlockNumber, err := chain.GetLatestBlockNumber(ctx, s.topChainConf.ChainUrl)
		if err != nil {
			s.log.Error("get latest block number error", zap.Error(err))
			time.Sleep(time.Second * 10)
			continue
		}
		if latestBlockNumber <= lastScanBlockNumber {
			time.Sleep(time.Second * 10)
			continue
		}

		startBlock := lastScanBlockNumber + 1
		endBlock := latestBlockNumber
		if endBlock-startBlock+1 > maxBlocksPerScan {
			endBlock = startBlock + maxBlocksPerScan - 1
		}

		events, err := chain.GetUsdtDepositEvents(ctx, s.topChainConf.ChainUrl, s.topChainConf.DepositAddress, startBlock, endBlock)
		if err != nil {
			s.log.Error("get deposit events error", zap.Error(err), zap.Uint64("startBlock", startBlock), zap.Uint64("endBlock", endBlock))
			time.Sleep(time.Second * 10)
			continue
		}
		sort.Slice(events, func(i, j int) bool {
			return events[i].BlockNumber < events[j].BlockNumber
		})
		records := make([]*repository.UserShadowWalletBalanceRecord, 0)
		for _, event := range events {
			userShadowWallet, err := s.repo.UserShadowWallet.GetByPublicAddress(ctx, event.User)
			if err != nil {
				s.log.Error("get user shadow wallet by address error", zap.Error(err), zap.String("user", event.User))
				continue
			}
			if userShadowWallet == nil || userShadowWallet.UserID == 0 {
				continue
			}
			isIn := uint8(0)
			recordType := repository.UserShadowWalletBalanceRecordTypeChatCost
			if event.Directory {
				isIn = 1
				recordType = repository.UserShadowWalletBalanceRecordTypeDeposit
			}
			records = append(records, &repository.UserShadowWalletBalanceRecord{
				UserID:         userShadowWallet.UserID,
				PublicAddress:  event.User,
				TxHash:         event.TxHash,
				BlockNumber:    event.BlockNumber,
				Amount:         event.NewBalance.String(),
				CurrentBalance: event.CurrentBalance.String(),
				Status:         repository.UserShadowWalletBalanceRecordStatusUnconfirmed,
				Currency:       repository.UserBalanceCurrencyUSDT_DEPOSIT,
				Type:           recordType,
				Description:    "deposit",
				IsIn:           isIn,
			})
		}
		if len(records) > 0 {
			err = s.repo.UserShadowWalletBalanceRecord.BatchCreate(ctx, records)
			if err != nil {
				s.log.Error("batch create user shadow wallet balance record error", zap.Error(err))
				continue
			}
		}
		lastScanBlockNumber = endBlock
		time.Sleep(time.Second * 10)
	}
}

// 确认钱包转账记录状态，pending中超时会报错，failed直接修改，success等待确认
func (s *shadowAccountManage) ConfirmWalletTransfer(ctx context.Context) error {
	// 获取所有未完成的转账记录
	records, err := s.repo.WalletTransferRecord.GetUnconfirmed(ctx)
	if err != nil {
		return err
	}

	for _, record := range records {
		if record.TxHash == "" {
			s.log.Error("tx hash is empty", zap.Uint("wallet_transfer_record_id", record.ID))
			continue
		}

		// 确认是否已经上链
		txStatus, err := chain.GetTransactionByHash(ctx, s.topChainConf.ChainUrl, record.TxHash)
		if err != nil {
			s.log.Error("get transaction by hash error", zap.Error(err))
			continue
		}
		if txStatus.Pending {
			// 如果超过20分钟还未上链，需要报错提示
			if time.Since(*record.CreatedAt).Minutes() > 20 {
				s.log.Error("tx pending more than 20 minutes", zap.Uint("wallet_transfer_record_id", record.ID))
			}
			continue
		}
		// 成功上链
		if !txStatus.Success {
			// 失败，更新状态为失败
			s.log.Error("tx failed", zap.Uint("wallet_transfer_record_id", record.ID), zap.String("tx_hash", record.TxHash))
			err = s.repo.WalletTransferRecord.UpdateFailedByID(ctx, record.ID, txStatus.BlockNumber)
			if err != nil {
				s.log.Error("update wallet transfer record error", zap.Error(err))
			}
			continue
		}
		s.log.Info("tx success, waiting for confirm", zap.Uint("wallet_transfer_record_id", record.ID), zap.String("tx_hash", record.TxHash))

		// err = s.repo.WalletTransferRecord.UpdateConfirmedByID(ctx, record.ID, txStatus.BlockNumber)
		// if err != nil {
		// 	s.log.Error("update wallet transfer record error", zap.Error(err))
		// }
	}
	return nil
}

func (s *shadowAccountManage) ConfirmReportCostRecord(ctx context.Context) error {
	records, err := s.repo.WalletReportCostRecord.GetUnconfirmed(ctx)
	if err != nil {
		return err
	}

	for _, record := range records {
		// 确认链上状态
		txStatus, err := chain.GetTransactionByHash(ctx, s.topChainConf.ChainUrl, record.TxHash)
		if err != nil {
			s.log.Error("get transaction by hash error", zap.Error(err))
			continue
		}
		if txStatus.Pending {
			// 如果超过20分钟还未上链，需要报错提示
			if time.Since(*record.CreatedAt).Minutes() > 20 {
				s.log.Error("tx pending more than 20 minutes", zap.Uint("wallet_report_cost_record_id", record.ID))
			}
			continue
		}
		if txStatus.Success {
			s.log.Info("tx success, waiting for confirm", zap.Uint("wallet_report_cost_record_id", record.ID), zap.String("tx_hash", record.TxHash))
			continue
		}

		// 上链失败，更新状态为失败
		err = s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
			err = db.WalletReportCostRecord.UpdateFailedByID(ctx, record.ID, txStatus.BlockNumber)
			if err != nil {
				return err
			}
			// 修改usage状态
			usageIds := strings.Split(record.UserChatUsageIDs, ",")
			usageIdsUint := make([]uint, 0)
			for _, usageId := range usageIds {
				usageIdUint, _ := strconv.ParseUint(usageId, 10, 64)
				usageIdsUint = append(usageIdsUint, uint(usageIdUint))
			}
			return db.UserChatUsage.UpdateSettledByIDs(ctx, usageIdsUint)
		})
		if err != nil {
			s.log.Error("update wallet report cost record error", zap.Error(err))
		}
	}
	return nil
}

func (s *shadowAccountManage) ConfirmWalletDepositRecord(ctx context.Context) error {
	records, err := s.repo.WalletDepositRecord.GetUnconfirmed(ctx)
	if err != nil {
		return err
	}

	for _, record := range records {
		// 确认链上状态
		txStatus, err := chain.GetTransactionByHash(ctx, s.topChainConf.ChainUrl, record.TxHash)
		if err != nil {
			s.log.Error("get transaction by hash error", zap.Error(err))
			continue
		}
		if txStatus.Pending {
			// 如果超过20分钟还未上链，需要报错提示
			if time.Since(*record.CreatedAt).Minutes() > 20 {
				s.log.Error("tx pending more than 20 minutes", zap.Uint("wallet_deposit_record_id", record.ID))
			}
			continue
		}
		if txStatus.Success {
			s.log.Info("tx success, waiting for confirm", zap.Uint("wallet_deposit_record_id", record.ID), zap.String("tx_hash", record.TxHash))
			continue
		}

		// 上链失败，更新状态为失败
		err = s.repo.WalletDepositRecord.UpdateFailedByID(ctx, record.ID, txStatus.BlockNumber)
		if err != nil {
			s.log.Error("update wallet deposit record error", zap.Error(err))
		}
	}
	return nil
}

// 根据同步的钱包数据，更新钱包余额和上报记录状态
func (s *shadowAccountManage) ConfirmShadowWalletBalanceRecord(ctx context.Context) error {
	// 获取所有未完成的结算记录
	records, err := s.repo.UserShadowWalletBalanceRecord.GetUnconfirmed(ctx)
	if err != nil {
		return err
	}

	for _, record := range records {
		if record.TxHash == "" {
			s.log.Error("tx hash is empty", zap.Uint("user_shadow_wallet_balance_record_id", record.ID))
			continue
		}

		if record.UserID == 0 {
			// 如果是pool账户，直接修改为已确认
			err = s.repo.UserShadowWalletBalanceRecord.UpdateConfirmedByID(ctx, record.ID)
			if err != nil {
				s.log.Error("update user shadow wallet balance record error", zap.Error(err))
				return err
			}
			continue
		}

		switch record.Currency {
		case repository.UserBalanceCurrencyUSDT:
			balance, err := s.getShadowBalanceAccount(ctx, record.UserID, repository.UserBalanceCurrencyUSDT)
			if err != nil {
				s.log.Error("get user shadow wallet balance error", zap.Error(err))
				return err
			}
			currentBalance, _ := new(big.Int).SetString(balance.Balance, 10)
			accumulated, _ := new(big.Int).SetString(balance.Accumulated, 10)
			recordAmount, _ := new(big.Int).SetString(record.Amount, 10)
			if record.IsIn == 1 {
				currentBalance.Add(currentBalance, recordAmount)
				accumulated.Add(accumulated, recordAmount)
			} else {
				currentBalance.Sub(currentBalance, recordAmount)
			}
			// 查找一下转账记录，只有系统主动发起的才会被记录
			transferRecord, err := s.repo.WalletTransferRecord.GetByTxHash(ctx, record.TxHash)
			if err != nil {
				s.log.Error("get wallet transfer record by tx hash error", zap.Error(err))
				return err
			}

			err = s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
				err = db.UserShadowWalletBalance.UpdateBalanceByUserIDAndCurrency(ctx, record.UserID, repository.UserBalanceCurrencyUSDT, currentBalance.String(), accumulated.String(), record.BlockNumber)
				if err != nil {
					s.log.Error("update user shadow wallet balance error", zap.Error(err))
					return err
				}
				if transferRecord != nil && transferRecord.ID > 0 && transferRecord.Status == repository.WalletTransferRecordStatusUnconfirmed {
					err = db.WalletTransferRecord.UpdateConfirmedByID(ctx, transferRecord.ID, record.BlockNumber, record.ID)
					if err != nil {
						s.log.Error("update wallet transfer record error", zap.Error(err))
						return err
					}
				}
				if record.IsIn == 1 && record.Type != repository.UserShadowWalletBalanceRecordTypeGift {
					// 如果是转入，插入用户充值记录
					err = db.UserRechargeRecord.Create(ctx, &repository.UserRechargeRecord{
						UserID:                          record.UserID,
						UserShadowWalletBalanceRecordID: record.ID,
						TxHash:                          record.TxHash,
						BlockNumber:                     record.BlockNumber,
						Amount:                          record.Amount,
						Currency:                        repository.UserBalanceCurrencyUSDT,
						RechargeType:                    repository.UserRechargeRecordTypeOnchain,
						Status:                          repository.UserRechargeRecordStatusUnconfirmed,
						Description:                     record.Type,
					})
					if err != nil {
						s.log.Error("create user recharge record error", zap.Error(err))
						return err
					}
				}
				return db.UserShadowWalletBalanceRecord.UpdateConfirmedByID(ctx, record.ID)
			})
			if err != nil {
				s.log.Error("update user shadow wallet balance record error", zap.Error(err))
				return err
			}

		case repository.UserBalanceCurrencyTOP:
			// 如果是TOP，直接更新为已确认，不确认top的具体流动，因为有gas未确认
			err = s.repo.UserShadowWalletBalanceRecord.UpdateConfirmedByID(ctx, record.ID)
			if err != nil {
				s.log.Error("update user shadow wallet balance record error", zap.Error(err))
				return err
			}
		case repository.UserBalanceCurrencyUSDT_DEPOSIT:
			// 如果是USDT_DEPOSIT，需要更新质押余额
			balance, err := s.getShadowBalanceAccount(ctx, record.UserID, repository.UserBalanceCurrencyUSDT_DEPOSIT)
			if err != nil {
				s.log.Error("get user shadow wallet balance error", zap.Error(err))
				return err
			}
			currentBalance, _ := new(big.Int).SetString(balance.Balance, 10)
			accumulated, _ := new(big.Int).SetString(balance.Accumulated, 10)
			recordAmount, _ := new(big.Int).SetString(record.Amount, 10)
			reportRecordId := uint(0)
			depositRecordId := uint(0)
			if record.IsIn == 1 { // 质押
				currentBalance.Add(currentBalance, recordAmount)
				accumulated.Add(accumulated, recordAmount)

				// 查询一下质押记录
				depositRecords, err := s.repo.WalletDepositRecord.GetByTxHash(ctx, record.TxHash)
				if err != nil {
					s.log.Error("get wallet deposit record by tx hash error", zap.Error(err))
					return err
				}
				if depositRecords != nil && depositRecords.ID > 0 && depositRecords.Status == repository.WalletDepositRecordStatusUnconfirmed {
					depositRecordId = depositRecords.ID
				} else {
					s.log.Warn("wallet deposit record not found", zap.String("tx_hash", record.TxHash))
				}
			} else { // 上报
				currentBalance.Sub(currentBalance, recordAmount)
				// 查询一下上报记录
				reportRecords, err := s.repo.WalletReportCostRecord.GetByTxHash(ctx, record.TxHash)
				if err != nil {
					s.log.Error("get wallet report cost record by tx hash error", zap.Error(err))
					return err
				}
				if reportRecords != nil && reportRecords.ID > 0 && reportRecords.Status == repository.WalletReportCostRecordStatusUnconfirmed {
					reportRecordId = reportRecords.ID
				} else {
					s.log.Warn("wallet report cost record not found", zap.String("tx_hash", record.TxHash))
				}
			}
			recordCurrentBalance, _ := new(big.Int).SetString(record.CurrentBalance, 10)
			if recordCurrentBalance != nil && currentBalance.Cmp(recordCurrentBalance) != 0 {
				s.log.Error("user shadow wallet balance record current balance not equal to database balance", zap.Uint("user_id", record.UserID), zap.String("public_address", record.PublicAddress), zap.String("tx_hash", record.TxHash), zap.String("current_balance", record.CurrentBalance), zap.String("balance", currentBalance.String()))
				return errors.New("user shadow wallet balance record current balance not equal to database balance")
			}

			err = s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
				err = db.UserShadowWalletBalance.UpdateBalanceByUserIDAndCurrency(ctx, record.UserID, repository.UserBalanceCurrencyUSDT_DEPOSIT, currentBalance.String(), accumulated.String(), record.BlockNumber)
				if err != nil {
					s.log.Error("update user shadow wallet balance error", zap.Error(err))
					return err
				}
				if reportRecordId > 0 {
					err = db.WalletReportCostRecord.UpdateConfirmedByID(ctx, reportRecordId, record.BlockNumber, record.ID)
					if err != nil {
						s.log.Error("update user report cost record error", zap.Error(err))
						return err
					}
				}
				if depositRecordId > 0 {
					err = db.WalletDepositRecord.UpdateConfirmedByID(ctx, depositRecordId, record.BlockNumber, record.ID)
					if err != nil {
						s.log.Error("update wallet deposit record error", zap.Error(err))
						return err
					}
				}
				return db.UserShadowWalletBalanceRecord.UpdateConfirmedByID(ctx, record.ID)
			})
			if err != nil {
				s.log.Error("update user shadow wallet balance record error", zap.Error(err))
				return err
			}
		}
	}
	return nil
}

// 结算
func (s *shadowAccountManage) settleUserChatUsage(ctx context.Context) error {
	// 获取所有未完成的结算记录
	chatUsages, err := s.repo.UserChatUsage.GetSettled(ctx)
	if err != nil {
		return err
	}

	topaiModelCache := make(map[string]*repository.TopaiModel)

	getTopaiModel := func(modelId string) (*repository.TopaiModel, error) {
		if topaiModel, ok := topaiModelCache[modelId]; ok {
			return topaiModel, nil
		}
		// 获取模型供应商
		modelExt, err := s.repo.TopaiModelExt.GetByModelId(ctx, modelId)
		if err != nil {
			return nil, fmt.Errorf("get modelExt by modelId %s error: %v", modelId, err)
		}
		if modelExt == nil || modelExt.Id == 0 {
			s.log.Error("modelExt is nil or id is 0", zap.String("modelId", modelId))
			return nil, errors.New("modelExt is nil or id is 0")
		}

		topaiModel, err := s.repo.TopaiModel.GetByID(ctx, modelExt.TopaiModelId)
		if err != nil {
			return nil, fmt.Errorf("get provider by id %d error: %v", modelExt.TopaiModelProviderId, err)
		}
		if topaiModel == nil || topaiModel.Id == 0 {
			s.log.Error("topaiModel is nil or id is 0", zap.Uint("topaiModelId", modelExt.TopaiModelId))
			return nil, errors.New("topaiModel is nil or id is 0")
		}
		topaiModelCache[modelId] = topaiModel
		return topaiModel, nil
	}

	type tokens struct {
		InputTokens  *big.Int
		OutputTokens *big.Int
		UsageIds     []uint
	}

	freeUsageIds := []uint{}
	offchainUsageIds := []uint{}
	maybeUserCost := make(map[uint]*big.Int)
	// 按照用户id和供应商分组，统计每个用户每个链上id的结算金额
	groupedChatUsages := make(map[uint]map[uint]*tokens)
	for _, chatUsage := range chatUsages {
		topaiModel, err := getTopaiModel(chatUsage.ModelID)
		if err != nil {
			s.log.Error("get provider by modelId %s error: %v", zap.String("modelId", chatUsage.ModelID), zap.Error(err))
			continue
		}

		if topaiModel.InputPrice == "0" && topaiModel.OutputPrice == "0" {
			freeUsageIds = append(freeUsageIds, chatUsage.ID)
			s.log.Info("free usage", zap.Uint("user_id", chatUsage.UserID), zap.String("model_id", chatUsage.ModelID))
			continue
		}
		if topaiModel.ChainModelId == 0 {
			offchainUsageIds = append(offchainUsageIds, chatUsage.ID)
			s.log.Info("offchain usage", zap.Uint("user_id", chatUsage.UserID), zap.String("model_id", chatUsage.ModelID))
			continue
		}

		// 非appkey的usage， 认为是聊天界面，改成配置的companyUserID
		if chatUsage.AppKey == "" {
			chatUsage.UserID = s.companyUserID
		}

		if _, ok := groupedChatUsages[chatUsage.UserID]; !ok {
			groupedChatUsages[chatUsage.UserID] = make(map[uint]*tokens)
		}

		if _, ok := groupedChatUsages[chatUsage.UserID][topaiModel.ChainModelId]; !ok {
			groupedChatUsages[chatUsage.UserID][topaiModel.ChainModelId] = &tokens{
				InputTokens:  new(big.Int),
				OutputTokens: new(big.Int),
				UsageIds:     []uint{},
			}
		}
		if _, ok := maybeUserCost[chatUsage.UserID]; !ok {
			maybeUserCost[chatUsage.UserID] = new(big.Int)
		}

		_tokens := groupedChatUsages[chatUsage.UserID][topaiModel.ChainModelId]
		inputTokens, _ := new(big.Int).SetString(chatUsage.InputTokens, 10)
		outputTokens, _ := new(big.Int).SetString(chatUsage.OutputTokens, 10)

		modelInputPrice, _ := new(big.Int).SetString(topaiModel.InputPrice, 10)
		modelOutputPrice, _ := new(big.Int).SetString(topaiModel.OutputPrice, 10)
		maybeUserCost[chatUsage.UserID].Add(maybeUserCost[chatUsage.UserID], new(big.Int).Mul(modelInputPrice, inputTokens))
		maybeUserCost[chatUsage.UserID].Add(maybeUserCost[chatUsage.UserID], new(big.Int).Mul(modelOutputPrice, outputTokens))

		_tokens.InputTokens.Add(_tokens.InputTokens, inputTokens)
		_tokens.OutputTokens.Add(_tokens.OutputTokens, outputTokens)
		_tokens.UsageIds = append(_tokens.UsageIds, chatUsage.ID)

		groupedChatUsages[chatUsage.UserID][topaiModel.ChainModelId] = _tokens
	}
	if len(freeUsageIds) > 0 || len(offchainUsageIds) > 0 {
		err = s.repo.UserChatUsage.UpdateCompletedByIDs(ctx, append(freeUsageIds, offchainUsageIds...), 0)
		if err != nil {
			s.log.Error("update user chat usage status error", zap.Error(err))
		}
	}

	sessionId := time.Now().Unix()
	epochId := 0
	// 按照用户id和供应商分组，链上结算
	for userID, onchainModelMap := range groupedChatUsages {
		s.log.Info("settle user chat usage", zap.Uint("userID", userID), zap.Any("onchainModelMap", onchainModelMap))
		// 获取影子余额账户
		shadowBalanceAccount, err := s.getShadowBalanceAccount(ctx, userID, repository.UserBalanceCurrencyUSDT_DEPOSIT)
		if err != nil {
			return err
		}

		// 判断用户当前余额是否与数据库余额一致
		depositBalance, err := s.utilChainFunc.GetDepositBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.DepositAddress, shadowBalanceAccount.PublicAddress, 0)
		if err != nil {
			return err
		}
		userBalance, _ := big.NewInt(0).SetString(shadowBalanceAccount.Balance, 10)
		if depositBalance.Current.Cmp(userBalance) != 0 {
			s.log.Error("user balance not equal to database balance", zap.String("publicAddress", shadowBalanceAccount.PublicAddress), zap.String("balance", shadowBalanceAccount.Balance), zap.String("depositBalance", depositBalance.Current.String()))
			continue
		}

		if depositBalance.Current.Cmp(maybeUserCost[userID]) < 0 {
			// 触发质押
			err = s.depositUsdtToDepositContract(ctx, shadowBalanceAccount, nil)
			if err != nil {
				s.log.Error("deposit usdt to deposit contract error", zap.Error(err))
				continue
			}
			s.log.Info("deposit balance not enough", zap.Uint("userID", userID), zap.String("publicAddress", shadowBalanceAccount.PublicAddress), zap.String("balance", shadowBalanceAccount.Balance))
			continue
		}

		for chainModelId, tokens := range onchainModelMap {
			// 发送结算消息
			epochId++
			tx, err := s.utilChainFunc.ReportModelCostFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.AiWorkerAddress, shadowBalanceAccount.PublicAddress, chainModelId, uint(sessionId), uint(epochId), tokens.InputTokens, tokens.OutputTokens)
			if err != nil {
				s.log.Error("send report model cost tx error", zap.Error(err))
				continue
			}

			usageIds := ""
			for _, usageId := range tokens.UsageIds {
				usageIds += fmt.Sprintf("%d,", usageId)
			}
			usageIds = strings.TrimRight(usageIds, ",")

			// 记录到数据库
			record := &repository.WalletReportCostRecord{
				UserID:           userID,
				PublicAddress:    shadowBalanceAccount.PublicAddress,
				UserChatUsageIDs: usageIds,
				TxHash:           tx,
				Status:           repository.WalletReportCostRecordStatusUnconfirmed, // 待确认
				InputTokens:      tokens.InputTokens.String(),
				OutputTokens:     tokens.OutputTokens.String(),
			}
			err = s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
				err = db.WalletReportCostRecord.Create(ctx, record)
				if err != nil {
					s.log.Error("create wallet report cost record error", zap.Error(err))
					return err
				}
				// 更新结算状态，并记录到数据库
				err = db.UserChatUsage.UpdateCompletedByIDs(ctx, tokens.UsageIds, record.ID)
				if err != nil {
					s.log.Error("update user chat usage status error", zap.Error(err))
					return err
				}

				return nil
			})

			if err != nil {
				s.log.Error("create wallet report cost record error", zap.Error(err))
			}

		}
	}
	return nil
}

// poolSendTopToShadowAccount 从统筹账户转账TOP到影子账户，主要用于gas费
func (s *shadowAccountManage) poolSendTopToShadowAccount(ctx context.Context, userID uint, amount *big.Int) error {
	// 参数验证
	if amount == nil {
		return fmt.Errorf("amount cannot be nil")
	}
	if amount.Cmp(big.NewInt(0)) <= 0 {
		return fmt.Errorf("amount must be positive")
	}

	// 确定poolGas
	poolTopBalance, err := s.getTopBalance(ctx, s.shadowConf.PoolAccountPub, 0)
	if err != nil {
		return fmt.Errorf("get pool balance error: %v", err)
	}
	needAmount := big.NewInt(0).Add(s.minTopBalance, amount)
	if poolTopBalance.Cmp(needAmount) <= 0 {
		return fmt.Errorf("pool top balance is not enough, need %s, have %s", needAmount.String(), poolTopBalance.String())
	}

	userShadowAccount, err := s.getShadowAccount(ctx, userID)
	if err != nil {
		return err
	}
	// 如果有未完成的转账记录，则需要等待
	unconfirmedRecords, err := s.repo.WalletTransferRecord.GetUnconfirmed(ctx)
	if err != nil {
		return err
	}
	if len(unconfirmedRecords) > 0 {
		s.log.Info("there are unconfirmed wallet transfer records", zap.Uint("userID", userID))
		return nil
	}
	// 直接发起转账即可
	poolPriKey, err := utils.MustParsePrivateKey(s.shadowConf.PoolAccountPri)
	if err != nil {
		return err
	}
	poolPubKey := crypto.PubkeyToAddress(poolPriKey.PublicKey).Hex()

	tx, err := s.utilChainFunc.TransferErc20Func(ctx, s.topChainConf.ChainUrl, s.topChainConf.TopErc20WrapperAddress, poolPriKey, userShadowAccount.PublicAddress, amount)
	if err != nil {
		return err
	}

	// 保存转账记录
	record := &repository.WalletTransferRecord{
		UserID:      userID,
		FromAddress: poolPubKey,
		ToAddress:   userShadowAccount.PublicAddress,
		Amount:      amount.String(),
		Currency:    repository.UserBalanceCurrencyTOP,
		TxHash:      tx,
		Status:      repository.WalletTransferRecordStatusUnconfirmed,
		Description: "auto transfer from pool to shadow account",
	}
	err = s.repo.WalletTransferRecord.Create(ctx, record)
	if err != nil {
		return err
	}

	return nil
}

// poolSendUsdtToShadowAccount 从统筹账户转账USDT到影子账户
func (s *shadowAccountManage) poolSendUsdtToShadowAccount(ctx context.Context, userID uint, amount *big.Int) error {
	if amount == nil || amount.Cmp(big.NewInt(0)) <= 0 {
		return fmt.Errorf("amount is not positive")
	}
	// 确定poolGas
	poolTopBalance, err := s.getTopBalance(ctx, s.shadowConf.PoolAccountPub, 0)
	if err != nil {
		return fmt.Errorf("get pool balance error: %v", err)
	}
	if poolTopBalance.Cmp(s.minTopBalance) <= 0 {
		return fmt.Errorf("pool top balance is not enough: %s", poolTopBalance.String())
	}

	userShadowAccount, err := s.getShadowAccount(ctx, userID)
	if err != nil {
		return err
	}

	// 如果有未完成的转账记录，则需要等待
	unconfirmedRecords, err := s.repo.WalletTransferRecord.GetUnconfirmed(ctx)
	if err != nil {
		return fmt.Errorf("get unconfirmed wallet transfer records error: %v", err)
	}
	for _, record := range unconfirmedRecords {
		if record.ToAddress == userShadowAccount.PublicAddress {
			s.log.Info("there are unconfirmed wallet transfer records", zap.Uint("userID", userID), zap.String("to_address", userShadowAccount.PublicAddress))
			return nil
		}
	}

	// 发起从统筹账户转账到影子账户
	poolPriKey, err := utils.MustParsePrivateKey(s.shadowConf.PoolAccountPri)
	if err != nil {
		return fmt.Errorf("parse pool private key error: %v", err)
	}
	poolPubKey := crypto.PubkeyToAddress(poolPriKey.PublicKey).Hex()
	s.log.Info("pool public key", zap.String("pool_public_key", poolPubKey))

	poolBalance, err := s.getUsdtBalance(ctx, poolPubKey, 0)
	if err != nil {
		return fmt.Errorf("get pool balance error: %v", err)
	}
	if poolBalance.Cmp(amount) < 0 {
		return fmt.Errorf("pool balance not enough")
	}

	tx, err := s.utilChainFunc.TransferErc20Func(ctx, s.topChainConf.ChainUrl, s.topChainConf.UsdtErc20WrapperAddress, poolPriKey, userShadowAccount.PublicAddress, amount)
	if err != nil {
		return fmt.Errorf("transfer erc20 error: %v", err)
	}

	// 保存转账记录
	record := &repository.WalletTransferRecord{
		UserID:      userID,
		FromAddress: poolPubKey,
		ToAddress:   userShadowAccount.PublicAddress,
		Amount:      amount.String(),
		Currency:    repository.UserBalanceCurrencyUSDT,
		TxHash:      tx,
		Status:      repository.WalletTransferRecordStatusUnconfirmed,
		Description: "admin transfer usdt to shadow account",
	}
	err = s.repo.WalletTransferRecord.Create(ctx, record)
	if err != nil {
		return fmt.Errorf("create wallet transfer record error: %v", err)
	}

	return nil
}

func (s *shadowAccountManage) getShadowBalanceAccount(ctx context.Context, userID uint, currency string) (*repository.UserShadowWalletBalance, error) {
	shadowBalanceAccount, err := s.repo.UserShadowWalletBalance.GetByUserIDAndCurrency(ctx, userID, currency)
	if err != nil {
		return nil, err
	}
	if shadowBalanceAccount == nil || shadowBalanceAccount.ID == 0 {
		shadowAccount, err := s.getShadowAccount(ctx, userID)
		if err != nil {
			return nil, err
		}
		shadowBalanceAccount = &repository.UserShadowWalletBalance{
			UserID:        userID,
			PublicAddress: shadowAccount.PublicAddress,
			Balance:       "0",
			Accumulated:   "0",
			Currency:      currency,
		}
		err = s.repo.UserShadowWalletBalance.Create(ctx, shadowBalanceAccount)
		if err != nil {
			return nil, err
		}
	}
	return shadowBalanceAccount, nil
}

func (s *shadowAccountManage) depositUsdtToDepositContract(ctx context.Context, userWallet *repository.UserShadowWalletBalance, amount *big.Int) error {
	s.log.Info("deposit usdt to deposit contract", zap.Uint("userID", userWallet.UserID), zap.String("to_address", userWallet.PublicAddress))
	// 确认用户gas费 Top
	topBalance, err := s.utilChainFunc.GetErc20ContractBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.TopErc20WrapperAddress, userWallet.PublicAddress, 0)
	if err != nil {
		return fmt.Errorf("get top balance error: %v", err)
	}
	s.log.Info("top balance", zap.Uint("userID", userWallet.UserID), zap.String("to_address", userWallet.PublicAddress), zap.String("top_balance", topBalance.String()))

	if topBalance.Cmp(s.minTopBalance) < 0 {
		s.log.Info("top balance not enough for gas fee", zap.Uint("userID", userWallet.UserID), zap.String("to_address", userWallet.PublicAddress), zap.String("top_balance", topBalance.String()))
		// 如果余额不足，则需要自动充值
		err = s.poolSendTopToShadowAccount(ctx, userWallet.UserID, s.transferTopAmount)
		if err != nil {
			return fmt.Errorf("pool send top to shadow account error: %v", err)
		}
		return nil
	}

	// 确认用户余额
	usdtBalance, err := s.utilChainFunc.GetErc20ContractBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.UsdtErc20WrapperAddress, userWallet.PublicAddress, 0)
	if err != nil {
		return fmt.Errorf("get usdt balance error: %v", err)
	}
	if amount == nil {
		amount = usdtBalance
	} else if usdtBalance.Cmp(amount) < 0 {
		return fmt.Errorf("user(%d) usdt balance is not enough:need %s, have %s", userWallet.UserID, amount.String(), usdtBalance.String())
	}

	if amount.Cmp(big.NewInt(0)) <= 0 {
		// 判断是否累计的余额是否有盈余
		userBalance, err := s.repo.UserBalance.GetByUserIDAndCurrency(ctx, userWallet.UserID, repository.UserBalanceCurrencyUSDT)
		if err != nil {
			return fmt.Errorf("get user balance error: %v", err)
		}
		if userBalance == nil || userBalance.ID == 0 {
			return fmt.Errorf("user balance not found")
		}
		userAccumulatedInt, _ := big.NewInt(0).SetString(userBalance.Accumulated, 10)
		shadowAccumulatedInt, _ := big.NewInt(0).SetString(userWallet.Accumulated, 10)
		remain := big.NewInt(0).Sub(userAccumulatedInt, shadowAccumulatedInt)
		if remain.Cmp(big.NewInt(0)) > 0 {
			err = s.poolSendUsdtToShadowAccount(ctx, userWallet.UserID, remain)
			if err != nil {
				return fmt.Errorf("pool send usdt to shadow account error: %v", err)
			}
			return nil
		}
		return fmt.Errorf("amount is not positive")
	}

	// 先授权
	priKey, err := s.getPrivateKey(ctx, userWallet.UserID)
	if err != nil {
		return fmt.Errorf("get private key error: %v", err)
	}
	s.log.Info("priKey", zap.Uint("userID", userWallet.UserID), zap.String("priKey", hex.EncodeToString(crypto.FromECDSA(priKey))))

	s.log.Info("approve", zap.Uint("userID", userWallet.UserID), zap.String("to_address", userWallet.PublicAddress), zap.String("amount", amount.String()))
	tx, err := s.utilChainFunc.ApproveFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.UsdtErc20WrapperAddress, priKey, s.topChainConf.DepositAddress, amount)
	if err != nil {
		return fmt.Errorf("approve error: %v", err)
	}
	s.log.Info("approve tx", zap.String("tx", tx))
	// 尝试3次，判断授权结果
	success := false
	for i := 0; i < 3; i++ {
		time.Sleep(10 * time.Second)
		txStatus, err := s.utilChainFunc.GetTransactionByHashFunc(ctx, s.topChainConf.ChainUrl, tx)
		if err != nil {
			return fmt.Errorf("get transaction by hash error: %v", err)
		}
		if txStatus.Pending {
			continue
		}
		if txStatus.Success {
			success = true
			break
		}

	}
	if !success {
		return fmt.Errorf("approve error or timeout")
	}

	s.log.Info("approve success", zap.Uint("userID", userWallet.UserID), zap.String("to_address", userWallet.PublicAddress), zap.String("amount", amount.String()))
	// 发起充值
	tx, err = s.utilChainFunc.DepositBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.DepositAddress, priKey, amount)
	if err != nil {
		return fmt.Errorf("deposit error: %v", err)
	}

	// 更新交易状态，并记录到数据库
	shadowWalletRecord := &repository.WalletDepositRecord{
		UserID:        userWallet.UserID,
		PublicAddress: userWallet.PublicAddress,
		TxHash:        tx,
		Amount:        amount.String(),
		Status:        repository.WalletDepositRecordStatusUnconfirmed,
	}
	err = s.repo.WalletDepositRecord.Create(ctx, shadowWalletRecord)
	if err != nil {
		return fmt.Errorf("create wallet deposit record error: %v", err)
	}
	return nil
}

func (s *shadowAccountManage) getShadowAccount(ctx context.Context, userID uint) (*repository.UserShadowWallet, error) {
	shadowAccount, err := s.repo.UserShadowWallet.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if shadowAccount == nil || shadowAccount.ID == 0 {
		shadowAccount, err = s.generateAccount(ctx, userID)
		if err != nil {
			return nil, err
		}
	}
	return shadowAccount, nil
}

// getPrivateKey 获取影子账户的私钥
func (s *shadowAccountManage) getPrivateKey(ctx context.Context, userID uint) (*ecdsa.PrivateKey, error) {
	// 获取用户信息
	user, err := s.repo.User.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil || user.ID == 0 {
		return nil, fmt.Errorf("user not found")
	}
	shadowAccount, err := s.getShadowAccount(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 解析加密数据
	privateKey, err := utils.DecryptPrivateKey(s.shadowConf.Base64Key, &utils.EncryptedKey{
		EncryptedData: shadowAccount.EncryptedPrivateKey,
		KeySalt:       shadowAccount.KeySalt,
	}, user.IdentityID)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

// generateAccount 生成影子账户
func (s *shadowAccountManage) generateAccount(ctx context.Context, userID uint) (*repository.UserShadowWallet, error) {
	// 获取用户信息
	user, err := s.repo.User.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil || user.ID == 0 {
		return nil, fmt.Errorf("user not found")
	}

	encryptedKey, err := utils.GenerateRandomPrivateKey(s.shadowConf.Base64Key, user.IdentityID)
	if err != nil {
		return nil, err
	}

	account := &repository.UserShadowWallet{
		UserID:              userID,
		PublicAddress:       encryptedKey.PublicKey,
		EncryptedPrivateKey: encryptedKey.EncryptedData,
		KeySalt:             encryptedKey.KeySalt,
		KeyVersion:          s.shadowConf.Version,
		Algorithm:           s.shadowConf.Algorithm,
	}

	err = s.repo.UserShadowWallet.Create(ctx, account)
	if err != nil {
		return nil, err
	}

	return account, nil
}

// 获取TOP余额, 如果blockNumber为0，则获取当前最新区块高度
func (s *shadowAccountManage) getTopBalance(ctx context.Context, publicAddress string, blockNumber uint64) (*big.Int, error) {
	balance, err := s.utilChainFunc.GetErc20ContractBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.TopErc20WrapperAddress, publicAddress, blockNumber)
	if err != nil {
		return nil, err
	}
	return balance, nil
}

// 获取USDT余额, 如果blockNumber为0，则获取当前最新区块高度
func (s *shadowAccountManage) getUsdtBalance(ctx context.Context, publicAddress string, blockNumber uint64) (*big.Int, error) {
	balance, err := s.utilChainFunc.GetErc20ContractBalanceFunc(ctx, s.topChainConf.ChainUrl, s.topChainConf.UsdtErc20WrapperAddress, publicAddress, blockNumber)
	if err != nil {
		return nil, err
	}
	return balance, nil
}

func (s *shadowAccountManage) GetUserShadowAddress(ctx context.Context, userID uint) (string, error) {
	shadowAccount, err := s.getShadowAccount(ctx, userID)
	if err != nil {
		return "", err
	}
	return shadowAccount.PublicAddress, nil
}
