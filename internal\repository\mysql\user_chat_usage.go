package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type UserChatUsageRepository struct {
	db *gorm.DB
}

func NewUserChatUsageRepository(db *gorm.DB) repository.UserChatUsageRepository {
	return &UserChatUsageRepository{
		db: db,
	}
}

func (r *UserChatUsageRepository) Create(ctx context.Context, chatUsage *repository.UserChatUsage) error {
	return r.db.WithContext(ctx).Create(chatUsage).Error
}

func (r *UserChatUsageRepository) GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*repository.UserChatUsage, error) {
	var chatUsages []*repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", start, end).Find(&chatUsages).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return chatUsages, nil
}

func (r *UserChatUsageRepository) Update(ctx context.Context, usage *repository.UserChatUsage) error {
	return r.db.WithContext(ctx).Table(usage.TableName()).Where("id = ?", usage.ID).Updates(usage).Error
}

func (r *UserChatUsageRepository) GetByID(ctx context.Context, id uint) (*repository.UserChatUsage, error) {
	var chatUsage repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&chatUsage).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &chatUsage, nil
}

func (r *UserChatUsageRepository) UpdateSettledByID(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Table(repository.UserChatUsage{}.TableName()).Where("id = ?", id).Update("status", repository.UserChatUsageStatusSettled).Error
}

func (r *UserChatUsageRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	var chatUsages []*repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("status = ?", repository.UserChatUsageStatusUnconfirmed).Find(&chatUsages).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return chatUsages, nil
}

func (r *UserChatUsageRepository) GetConfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	var chatUsages []*repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("status = ?", repository.UserChatUsageStatusConfirmed).Find(&chatUsages).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return chatUsages, nil
}

func (r *UserChatUsageRepository) GetSettled(ctx context.Context) ([]*repository.UserChatUsage, error) {
	var chatUsages []*repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("status = ?", repository.UserChatUsageStatusSettled).Find(&chatUsages).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return chatUsages, nil
}

func (r *UserChatUsageRepository) UpdateCompletedByIDs(ctx context.Context, ids []uint, userReportCostRecordID uint) error {
	return r.db.WithContext(ctx).Table(repository.UserChatUsage{}.TableName()).Where("id IN (?)", ids).
		Updates(map[string]interface{}{
			"user_report_cost_record_id": userReportCostRecordID,
			"status":                     repository.UserChatUsageStatusCompleted,
		}).Error
}

func (r *UserChatUsageRepository) UpdateSettledByIDs(ctx context.Context, ids []uint) error {
	return r.db.WithContext(ctx).Table(repository.UserChatUsage{}.TableName()).Where("id IN (?)", ids).Update("status", repository.UserChatUsageStatusSettled).Error
}

func (r *UserChatUsageRepository) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatUsage, error) {
	var chatUsage repository.UserChatUsage
	err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&chatUsage).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &chatUsage, nil
}

type UserUsageDayRecordRepository struct {
	db *gorm.DB
}

func NewUserUsageDayRecordRepository(db *gorm.DB) repository.UserUsageDayRecordRepository {
	return &UserUsageDayRecordRepository{
		db: db,
	}
}

func (r *UserUsageDayRecordRepository) CreateBatch(ctx context.Context, records []*repository.UserUsageDayRecord) error {
	return r.db.WithContext(ctx).Create(records).Error
}

func (r *UserUsageDayRecordRepository) GetByModelId(ctx context.Context, modelId string) ([]*repository.UserUsageDayRecord, error) {
	var records []*repository.UserUsageDayRecord
	err := r.db.WithContext(ctx).Where("model_id = ?", modelId).Find(&records).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

func (r *UserUsageDayRecordRepository) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.UserUsageDayRecord, error) {
	var records []*repository.UserUsageDayRecord
	err := r.db.WithContext(ctx).Where("model_id IN (?)", modelIds).Find(&records).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

func (r *UserUsageDayRecordRepository) GetLast(ctx context.Context) (*repository.UserUsageDayRecord, error) {
	var record repository.UserUsageDayRecord
	err := r.db.WithContext(ctx).Order("id DESC").First(&record).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}
