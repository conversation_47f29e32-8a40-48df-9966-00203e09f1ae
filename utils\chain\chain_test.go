package chain

import (
	"context"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

func TestReportModelCost(t *testing.T) {
	ctx := context.Background()
	chainUrl := "https://toptest.crosschain.topnetwork.org"
	//aiModelsAddress := "******************************************"
	aiWorkloadAddress := "******************************************"
	// models, err := GetAiModelsList(ctx, chainUrl, aiModelsAddress)
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// for _, model := range models {
	// 	t.Log(model.ModelId, model.ModelName, model.InTokenPrice, model.OutTokenPrice)
	// }

	//tx, err := ReportModelCost(context.Background(), "https://toptest.crosschain.topnetwork.org", "******************************************", "******************************************", 8, 1, 1, big.NewInt(1e14), big.NewInt(1e14))
	tx, err := ReportModelCost(ctx, chainUrl, aiWorkloadAddress, "******************************************", 8, 2, 3, big.NewInt(1000), big.NewInt(2000))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(tx)
}

func TestSendTopAndUsdc(t *testing.T) {
	ctx := context.Background()
	chainUrl := "https://toptest.crosschain.topnetwork.org"

	// 测试批量转账TOP和USDC代币
	usdtTokenAddress := "0xc9B4e5c5CD83EfA16bC89b49283381aD2c74710D" // USDT代币合约地址
	//topTokenAddress := "0x7e5eF930DA3b4F777dA4fAfb958047A5CaAe5D8b" // top代币合约地址

	// 测试接收地址列表
	recipients := []string{
		"0xf7c9345eAa277fb18094eb091cC152458026256E",
		"0xE9b0DfE22d6524B7c5d542F51560DEa421506EA1",
		"0xBeD1fe9Ef9C956d85e75Da9B3a814D2aE9Eb12c6",
		"0x20846E44597ADFe7b04900E6D9A85239394F9469",
		"0x5784aA32459b345Ff0F188faA147D264b48F63ef",
		"0x8122D810aC1965A9d45b9e55F7D7c48F8ed282EA",
		"0xE4616B9fC9410cA8499E6E452A933D2E8fEc53b3",
		"0xf0e5CF4Df4adAdeC93228226090D3f03503df527",
		"0xd0950AA1F21868A872850ef08eEdF765806f25Bd",
		"0x32A4d85B930f9D9423Cb0DC30c5EeE8054505e25",
		"0x35cbfd295f98DBED650BB5C6Bd43167238a7bD8f",
		"0xbf6faC74a41733316faf2e2474343fc5bb1fC9b8",
		"0xBfebfBa80Be33AE9FAE7Fddc1F8d8Bd3433cD274",
		"0x0c02627613a235f5d786C6F24fd28e4facfDE6c7",
		"0xDc3d54565a8ee002B3e95a8EA34f5173f4E7289C",
		"0x4B18566a89F37B29EBb9372F7A0e8653F6145a36",
		"0x13989fd33f91a75D8f7F6b99513cE0E97b5E6cC6",
		"0x591ac00612Ad61677C706FB86A15B42e238f1136",
		"0xE837D1fe5821CA92a3420B4FD8987359490F102D",
		"0x9A6C870ac6a50f6a9E6601e51eD2Ec2F7992683d",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
		"******************************************",
	}

	// 每个地址转账金额
	amount := big.NewInt(0).Mul(big.NewInt(10), big.NewInt(1e18)) // 2000Top/10U
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		t.Fatal("Failed to connect to the Ethereum client:", err)
	}
	fromKey := "4edf73c3fb18df03994640f1f0156103accc327bd4d8a8e497e969733e87e151"
	privateKey, err := crypto.HexToECDSA(fromKey) // 测试私钥
	if err != nil {
		t.Fatal("Failed to parse private key:", err)
	}

	// 转TOP代币
	for _, recipient := range recipients {
		toAddress := common.HexToAddress(recipient)
		auth, err := createLatestAuth(client, privateKey, usdtTokenAddress)
		if err != nil {
			t.Fatal("Failed to create auth:", err)
		}

		contract := bind.NewBoundContract(common.HexToAddress(usdtTokenAddress), erc20Abi, client, client, client)
		if err != nil {
			t.Fatal("Failed to create contract:", err)
		}

		tx, err := contract.Transact(auth, "transfer",
			toAddress,
			amount,
		)
		if err != nil {
			t.Fatal("Failed to send TOP:", err)
		}
		// 等待交易被打包
		receipt, err := bind.WaitMined(ctx, client, tx)
		if err != nil {
			t.Fatal("Failed to wait for tx:", err)
		}

		// 检查交易状态
		if receipt.Status != types.ReceiptStatusSuccessful {
			t.Fatal("tx execution failed, status code:", receipt.Status)
		}
		t.Log("TOP transfer tx:", tx.Hash().Hex())
	}

}

func TestDeposit(t *testing.T) {
	ctx := context.Background()
	chainUrl := "https://toptest.crosschain.topnetwork.org"
	depositAddress := "0x3446FC8cD906d0a55866E64B7a764491beD87ca8"
	usdtTokenAddress := "0xc9B4e5c5CD83EfA16bC89b49283381aD2c74710D"
	privateKeys := []string{
		"28f90adf2941baf17dcd86349cc5b2cd61737303156aa93791f74a56821d21f6",
		"cdf7e38ce4c9b682e1687b38e301604dd3cf16d79c3386e6aa430e165a4c0cc6",
		"055f423de44a655a1b21daee7c5cd34a5f4042d2e7022e83d9048df0de1b70ae",
		"f12bdc6606b4d4dc7aa601ac943d79e56ef7096e17f630197c359446eb22c73d",
		"b660374cab3236a6ed65a4b3d302444e697250ebbab6b8201549faa3291403be",
		"aa4d6e4363cd3baad194c84210ce4ab810d5eff898d554125c7b69749ebd071f",
		"d60edfa09f6bb852300cd67ebff817c339346dc1b4013f6c7191bcb1e2fc60cb",
		"2ef61d7136a496aa42a99d8522e9a0a90f9eec266712ea3191685370766bdef6",
		"aaf363f392d0a3844211a8f84654dc9fd61d1abb2d9ee229cbfb56b83e6ce32e",
		"23d500ce7772d90d22430ea1e9940776831937bf000a11e456a69b9f35b260dd",
		"ccbea360619559aa5de01ffc752d37633a1fc3063cb1a218ad71ad9e7d928962",
		"65ef23d9fa0695e67a4c3d31b0e93153b58aeeb1c2c2d50f0db399976b20a84b",
		"fcf68794d7adc246055f7a6c29b19ef835b64e9ff22bd88a1d71a1a3251436f9",
		"8c86aea81c7806cd6abb066ffe285d0b8594122d3662c2996085d95d5b53ed42",
		"b4e2d0b7d757fd7b95a8ac87cdc97a23b58a60c353fd28fa08c4322617895fc0",
		"bc71fd4c3b018ffdd87b73e880affbfa73d07a231c0e0b39aedffb1c4e6f8385",
		"7a2523af0ff8199f41d51ed7d739b40aba68d2ee5bd83ca2511fb9bd5c07878a",
		"321e881c4124107cf283a98e3f670173d9dec586cf7e31f47a2949fbcb5924b8",
		"207e4ad26714a1d591d2d95dacda1da304805499e3c2f8c331ca832d7b10abcd",
		"8c793be882747665b85ea928f4da670ae16c593aa7feae7f6ac2179c55a03302",
		"618cb0494495f338a7d52961a9111d12d4c9c5de77b422b3ed96aff2c44819a7",
		"360e3b040f60cb199b9d8bd7cfdb662c14f22aabd3ad9272c4da3acf61fc1731",
		"3f2ce604a7311ef9cfbe3665915886a67dcbf0db51a3527a2f279d3ba6256dd0",
		"fa25281da47cc31e437a67779b66bfc89575c60a5984b61914f6e563d75758f3",
		"b6ac669a4caa355355a78747c4f1348423e9ce1d4d4355a3838e049159e8c988",
		"765554e5f2e4ef04c77633d81d73e96477d2328d737b80f90de4027b2b5a1838",
		"d93557a410f52331241e2211732cf86efbd87bf0d0c6700f0ffb2193dfbddd35",
		"72a36010e5525d1ab6f6cd2dcc2c61ec724c39031265e86c8dded32fe7bc38d9",
	}
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		t.Fatal("Failed to connect to the Ethereum client:", err)
	}
	approveAmount := big.NewInt(0).Mul(big.NewInt(10), big.NewInt(1e18)) // 10 USDT

	for _, privateKey := range privateKeys {
		privateKey, err := crypto.HexToECDSA(privateKey)
		if err != nil {
			t.Fatal("Failed to parse private key:", err)
		}

		// 先授权
		auth, err := createLatestAuth(client, privateKey, usdtTokenAddress)
		if err != nil {
			t.Fatal("Failed to create auth:", err)
		}

		contract := bind.NewBoundContract(common.HexToAddress(usdtTokenAddress), erc20Abi, client, client, client)
		if err != nil {
			t.Fatal("Failed to create contract:", err)
		}

		tx, err := contract.Transact(auth, "approve",
			common.HexToAddress(depositAddress),
			approveAmount,
		)
		if err != nil {
			t.Fatal("Failed to approve:", err)
		}
		// 等待交易被打包
		receipt, err := bind.WaitMined(ctx, client, tx)
		if err != nil {
			t.Fatal("Failed to wait for tx:", err)
		}

		// 检查交易状态
		if receipt.Status != types.ReceiptStatusSuccessful {
			t.Fatal("tx execution failed, status code:", receipt.Status)
		}
		t.Log("approve tx:", tx.Hash().Hex())

		// 授权成功之后，调用充值
		auth, err = createLatestAuth(client, privateKey, depositAddress)
		if err != nil {
			t.Fatal("Failed to create auth:", err)
		}

		contract = bind.NewBoundContract(common.HexToAddress(depositAddress), depositAbi, client, client, client)
		if err != nil {
			t.Fatal("Failed to create contract:", err)
		}

		tx, err = contract.Transact(auth, "deposit",
			approveAmount,
		)
		if err != nil {
			t.Fatal("Failed to deposit:", err)
		}
		// 等待交易被打包
		receipt, err = bind.WaitMined(ctx, client, tx)
		if err != nil {
			t.Fatal("Failed to wait for tx:", err)
		}

		// 检查交易状态
		if receipt.Status != types.ReceiptStatusSuccessful {
			t.Fatal("tx execution failed, status code:", receipt.Status)
		}
		t.Log("deposit tx:", tx.Hash().Hex())
	}

}

func TestGetTransactionByHash(t *testing.T) {
	ctx := context.Background()
	chainUrl := "https://toptest.crosschain.topnetwork.org"

	// 测试一个已经确认的交易哈希
	txHash := "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

	// 这个测试主要是为了验证chainId错误处理
	status, err := GetTransactionByHash(ctx, chainUrl, txHash)

	// 如果是missing chainId错误，函数应该能够处理而不是崩溃
	if err != nil {
		// 如果是其他错误（比如交易不存在），那也是正常的
		t.Logf("Expected error for test transaction: %v", err)
	} else {
		t.Logf("Transaction status: pending=%v, included=%v, success=%v",
			status.Pending, status.Included, status.Success)
	}
}

func TestGetTransactionByHashWithRealTx(t *testing.T) {
	ctx := context.Background()
	chainUrl := "https://toptest.crosschain.topnetwork.org"

	balance, err := GetDepositBalance(ctx, chainUrl, "0x3446FC8cD906d0a55866E64B7a764491beD87ca8", "0x1f3B07eDE62E46055D003C8f08D2a20c530c428a", 0)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(balance)

	b, err := GetErc20ContractBalance(ctx, chainUrl, "0xc9B4e5c5CD83EfA16bC89b49283381aD2c74710D", "0x1f3B07eDE62E46055D003C8f08D2a20c530c428a", 0)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(b)
}
