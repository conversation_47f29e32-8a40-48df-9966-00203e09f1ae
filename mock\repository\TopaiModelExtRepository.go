// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelExtRepository is an autogenerated mock type for the TopaiModelExtRepository type
type TopaiModelExtRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, ext
func (_m *TopaiModelExtRepository) Create(ctx context.Context, ext *repository.TopaiModelExt) error {
	ret := _m.Called(ctx, ext)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.TopaiModelExt) error); ok {
		r0 = rf(ctx, ext)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelExtRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelId provides a mock function with given fields: ctx, modelId
func (_m *TopaiModelExtRepository) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, modelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelId")
	}

	var r0 *repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.TopaiModelExt, error)); ok {
		return rf(ctx, modelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.TopaiModelExt); ok {
		r0 = rf(ctx, modelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelIds provides a mock function with given fields: ctx, modelIds
func (_m *TopaiModelExtRepository) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, modelIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelIds")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx, modelIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx, modelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, modelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBySearchOnlineModelId provides a mock function with given fields: ctx, modelId
func (_m *TopaiModelExtRepository) GetBySearchOnlineModelId(ctx context.Context, modelId string) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, modelId)

	if len(ret) == 0 {
		panic("no return value specified for GetBySearchOnlineModelId")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx, modelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx, modelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelId provides a mock function with given fields: ctx, topaiModelId
func (_m *TopaiModelExtRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, topaiModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelId")
	}

	var r0 *repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModelExt, error)); ok {
		return rf(ctx, topaiModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModelExt); ok {
		r0 = rf(ctx, topaiModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, topaiModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelIds provides a mock function with given fields: ctx, topaiModelIds
func (_m *TopaiModelExtRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, topaiModelIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelIds")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx, topaiModelIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx, topaiModelIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, topaiModelIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOnlineAll provides a mock function with given fields: ctx
func (_m *TopaiModelExtRepository) GetOnlineAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetOnlineAll")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOnlineList provides a mock function with given fields: ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId
func (_m *TopaiModelExtRepository) GetOnlineList(ctx context.Context, offset int, pageSize int, sortBy string, sortOrder string, modelIds []uint, providerIds []uint, isfree bool, searchModelId string) ([]*repository.TopaiModelExt, error) {
	ret := _m.Called(ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetOnlineList")
	}

	var r0 []*repository.TopaiModelExt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string, string, []uint, []uint, bool, string) ([]*repository.TopaiModelExt, error)); ok {
		return rf(ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string, string, []uint, []uint, bool, string) []*repository.TopaiModelExt); ok {
		r0 = rf(ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelExt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int, string, string, []uint, []uint, bool, string) error); ok {
		r1 = rf(ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelExtRepository creates a new instance of TopaiModelExtRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelExtRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelExtRepository {
	mock := &TopaiModelExtRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
