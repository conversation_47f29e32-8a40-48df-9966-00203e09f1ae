---
description: 
globs: 
alwaysApply: false
---
---
description: Document major failure points in this project and they were solved.  To be filled by AI.
globs: 
alwaysApply: false
---

# 错误文档规则

## 1. 错误记录规范

### 1.1 错误分类
- 系统错误：系统级别的错误，如服务崩溃、资源耗尽等
- 业务错误：业务逻辑相关的错误，如参数验证失败、业务规则冲突等
- 网络错误：网络通信相关的错误，如连接超时、请求失败等
- 数据库错误：数据库操作相关的错误，如连接失败、查询超时等
- 第三方服务错误：依赖的第三方服务错误，如 API 调用失败等

### 1.2 错误记录格式
```json
{
  "timestamp": "2024-03-21T10:00:00Z",
  "error_id": "ERR-************",
  "error_type": "system|business|network|database|third_party",
  "error_code": "ERROR_CODE",
  "error_message": "详细的错误描述",
  "stack_trace": "错误堆栈信息",
  "context": {
    "request_id": "请求ID",
    "user_id": "用户ID",
    "service": "服务名称",
    "environment": "环境名称"
  },
  "severity": "critical|error|warning|info",
  "status": "new|in_progress|resolved|closed"
}
```

### 1.3 错误记录位置
- 错误日志：`logs/error.log`
- 错误数据库：`error_records` 表
- 错误追踪系统：如 Sentry、ELK 等

## 2. 错误处理流程

### 2.1 错误发现
1. 系统监控告警
2. 用户反馈
3. 日志分析
4. 测试发现

### 2.2 错误分析
1. 错误分类
2. 影响范围评估
3. 优先级确定
4. 根本原因分析

### 2.3 错误修复
1. 制定修复方案
2. 代码修改
3. 测试验证
4. 部署上线

### 2.4 错误跟踪
1. 状态更新
2. 进度记录
3. 解决方案记录
4. 经验总结

## 3. 错误文档模板

### 3.1 错误报告模板
```markdown
# 错误报告

## 基本信息
- 错误ID：ERR-************
- 发现时间：2024-03-21 10:00:00
- 报告人：张三
- 状态：进行中

## 错误描述
- 错误类型：系统错误
- 错误代码：ERR_SYSTEM_001
- 错误信息：服务崩溃，无法响应请求
- 影响范围：所有用户

## 错误分析
- 发生时间：2024-03-21 10:00:00
- 发生环境：生产环境
- 触发条件：高并发请求
- 根本原因：内存泄漏

## 解决方案
- 修复方案：优化内存管理
- 修复步骤：
  1. 分析内存使用情况
  2. 优化代码逻辑
  3. 增加内存监控
- 验证方法：压力测试

## 预防措施
- 代码审查
- 性能测试
- 监控告警
- 定期维护
```

### 3.2 错误总结模板
```markdown
# 错误总结

## 错误概述
- 错误类型：系统错误
- 发生频率：每月1-2次
- 影响程度：高
- 解决时间：2天

## 错误分析
- 根本原因
- 触发条件
- 影响范围
- 解决方案

## 经验教训
- 技术改进
- 流程优化
- 团队协作
- 预防措施

## 后续计划
- 技术改进
- 流程优化
- 团队培训
- 监控完善
```

## 4. 错误管理工具

### 4.1 日志工具
- ELK Stack
- Graylog
- Fluentd
- Logstash

### 4.2 监控工具
- Prometheus
- Grafana
- Zabbix
- Nagios

### 4.3 错误追踪
- Sentry
- Rollbar
- Airbrake
- Bugsnag

## 5. 错误处理最佳实践

### 5.1 错误预防
- 代码审查
- 单元测试
- 集成测试
- 性能测试

### 5.2 错误监控
- 实时监控
- 告警设置
- 日志分析
- 性能分析

### 5.3 错误响应
- 快速响应
- 准确分析
- 有效解决
- 及时反馈

### 5.4 错误总结
- 定期总结
- 经验分享
- 流程优化
- 团队培训