// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// WalletTransferRecordRepository is an autogenerated mock type for the WalletTransferRecordRepository type
type WalletTransferRecordRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, record
func (_m *WalletTransferRecordRepository) Create(ctx context.Context, record *repository.WalletTransferRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.WalletTransferRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByTxHash provides a mock function with given fields: ctx, txHash
func (_m *WalletTransferRecordRepository) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletTransferRecord, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for GetByTxHash")
	}

	var r0 *repository.WalletTransferRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.WalletTransferRecord, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.WalletTransferRecord); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.WalletTransferRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConfirmed provides a mock function with given fields: ctx
func (_m *WalletTransferRecordRepository) GetConfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetConfirmed")
	}

	var r0 []*repository.WalletTransferRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.WalletTransferRecord, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.WalletTransferRecord); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.WalletTransferRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUnconfirmed provides a mock function with given fields: ctx
func (_m *WalletTransferRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUnconfirmed")
	}

	var r0 []*repository.WalletTransferRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.WalletTransferRecord, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.WalletTransferRecord); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.WalletTransferRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateConfirmedByID provides a mock function with given fields: ctx, id, blockNumber, walletBalanceRecordId
func (_m *WalletTransferRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	ret := _m.Called(ctx, id, blockNumber, walletBalanceRecordId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateConfirmedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, uint64, uint) error); ok {
		r0 = rf(ctx, id, blockNumber, walletBalanceRecordId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateFailedByID provides a mock function with given fields: ctx, id, blockNumber
func (_m *WalletTransferRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	ret := _m.Called(ctx, id, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFailedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, uint64) error); ok {
		r0 = rf(ctx, id, blockNumber)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewWalletTransferRecordRepository creates a new instance of WalletTransferRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewWalletTransferRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *WalletTransferRecordRepository {
	mock := &WalletTransferRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
