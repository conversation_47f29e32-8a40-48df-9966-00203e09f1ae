package middleware

import (
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

// ConcurrencyLimiter 并发量限制器
type ConcurrencyLimiter struct {
	// 全局限制
	globalLimit   int
	globalCurrent int

	// 用户级别限制
	userLimit   int
	userCurrent map[uint]int // userID -> current count

	// 用户+模型级别限制
	userModelLimit   int
	userModelCurrent map[string]int // "userID:modelID" -> current count

	// 并发控制
	mu sync.RWMutex

	// 清理定时器
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewConcurrencyLimiter 创建新的并发量限制器
func NewConcurrencyLimiter(config config.ConcurrencyLimitConfig) *ConcurrencyLimiter {
	limiter := &ConcurrencyLimiter{
		globalLimit:      config.GlobalLimit,
		globalCurrent:    0,
		userLimit:        config.UserLimit,
		userCurrent:      make(map[uint]int),
		userModelLimit:   config.UserModelLimit,
		userModelCurrent: make(map[string]int),
		stopCleanup:      make(chan struct{}),
	}

	// 启动清理任务，每30秒清理一次空的计数器
	limiter.cleanupTicker = time.NewTicker(30 * time.Second)
	go limiter.cleanup()

	return limiter
}

// AcquireGlobal 尝试获取全局并发许可
func (cl *ConcurrencyLimiter) AcquireGlobal() bool {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	if cl.globalLimit <= 0 || cl.globalCurrent < cl.globalLimit {
		cl.globalCurrent++
		return true
	}
	return false
}

// ReleaseGlobal 释放全局并发许可
func (cl *ConcurrencyLimiter) ReleaseGlobal() {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	if cl.globalCurrent > 0 {
		cl.globalCurrent--
	}
}

// AcquireUser 尝试获取用户级别并发许可
func (cl *ConcurrencyLimiter) AcquireUser(userID uint) bool {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	current := cl.userCurrent[userID]
	if cl.userLimit <= 0 || current < cl.userLimit {
		cl.userCurrent[userID] = current + 1
		return true
	}
	return false
}

// ReleaseUser 释放用户级别并发许可
func (cl *ConcurrencyLimiter) ReleaseUser(userID uint) {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	if current := cl.userCurrent[userID]; current > 0 {
		cl.userCurrent[userID] = current - 1
		if cl.userCurrent[userID] == 0 {
			delete(cl.userCurrent, userID)
		}
	}
}

// AcquireUserModel 尝试获取用户+模型级别并发许可
func (cl *ConcurrencyLimiter) AcquireUserModel(userID uint, modelID string) bool {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	key := fmt.Sprintf("%d:%s", userID, modelID)
	current := cl.userModelCurrent[key]
	if cl.userModelLimit <= 0 || current < cl.userModelLimit {
		cl.userModelCurrent[key] = current + 1
		return true
	}
	return false
}

// ReleaseUserModel 释放用户+模型级别并发许可
func (cl *ConcurrencyLimiter) ReleaseUserModel(userID uint, modelID string) {
	cl.mu.Lock()
	defer cl.mu.Unlock()

	key := fmt.Sprintf("%d:%s", userID, modelID)
	if current := cl.userModelCurrent[key]; current > 0 {
		cl.userModelCurrent[key] = current - 1
		if cl.userModelCurrent[key] == 0 {
			delete(cl.userModelCurrent, key)
		}
	}
}

// AcquireAll 尝试获取所有级别的并发许可
func (cl *ConcurrencyLimiter) AcquireAll(userID uint, modelID string) (bool, string) {
	// 检查全局限制
	if !cl.AcquireGlobal() {
		return false, "global concurrency limit exceeded"
	}

	// 检查用户限制
	if !cl.AcquireUser(userID) {
		cl.ReleaseGlobal()
		return false, "user concurrency limit exceeded"
	}

	// 检查用户+模型限制
	if modelID != "" && !cl.AcquireUserModel(userID, modelID) {
		cl.ReleaseUser(userID)
		cl.ReleaseGlobal()
		return false, "user model concurrency limit exceeded"
	}

	return true, ""
}

// ReleaseAll 释放所有级别的并发许可
func (cl *ConcurrencyLimiter) ReleaseAll(userID uint, modelID string) {
	if modelID != "" {
		cl.ReleaseUserModel(userID, modelID)
	}
	cl.ReleaseUser(userID)
	cl.ReleaseGlobal()
}

// GetStats 获取当前统计信息
func (cl *ConcurrencyLimiter) GetStats() map[string]interface{} {
	cl.mu.RLock()
	defer cl.mu.RUnlock()

	return map[string]interface{}{
		"global": map[string]interface{}{
			"limit":   cl.globalLimit,
			"current": cl.globalCurrent,
		},
		"user": map[string]interface{}{
			"limit":        cl.userLimit,
			"active_users": len(cl.userCurrent),
		},
		"user_model": map[string]interface{}{
			"limit":              cl.userModelLimit,
			"active_user_models": len(cl.userModelCurrent),
		},
	}
}

// cleanup 定期清理空的计数器
func (cl *ConcurrencyLimiter) cleanup() {
	for {
		select {
		case <-cl.cleanupTicker.C:
			cl.mu.Lock()
			// 清理用户计数器中为0的条目
			for userID, count := range cl.userCurrent {
				if count <= 0 {
					delete(cl.userCurrent, userID)
				}
			}
			// 清理用户+模型计数器中为0的条目
			for key, count := range cl.userModelCurrent {
				if count <= 0 {
					delete(cl.userModelCurrent, key)
				}
			}
			cl.mu.Unlock()
		case <-cl.stopCleanup:
			cl.cleanupTicker.Stop()
			return
		}
	}
}

// Close 关闭限制器
func (cl *ConcurrencyLimiter) Close() {
	close(cl.stopCleanup)
}

// 全局并发限制器实例
var globalConcurrencyLimiter *ConcurrencyLimiter

// InitConcurrencyLimiter 初始化全局并发限制器
func InitConcurrencyLimiter(config config.ConcurrencyLimitConfig) {
	globalConcurrencyLimiter = NewConcurrencyLimiter(config)
}

// GetConcurrencyLimiter 获取全局并发限制器
func GetConcurrencyLimiter() *ConcurrencyLimiter {
	return globalConcurrencyLimiter
}

// ConcurrencyLimit 并发限制中间件
func ConcurrencyLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		if globalConcurrencyLimiter == nil {
			// 如果没有初始化限制器，则跳过限制
			c.Next()
			return
		}

		// 获取用户信息
		appKeyInfo, exists := c.Get("app_key_info")
		if !exists {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
			c.Abort()
			return
		}

		appKey := appKeyInfo.(*service.AppKeyOP)
		userID := appKey.UserId

		// 从请求中提取模型ID
		modelID := extractModelID(c)

		// 尝试获取并发许可
		acquired, reason := globalConcurrencyLimiter.AcquireAll(userID, modelID)
		if !acquired {
			log.Warn("并发限制触发",
				zap.Uint("user_id", userID),
				zap.String("model_id", modelID),
				zap.String("reason", reason),
				zap.String("path", c.Request.URL.Path),
			)

			api.Fail(c, api.CodeClientError, "concurrency limit exceeded: "+reason, errors.New("concurrency limit exceeded"))
			c.Abort()
			return
		}

		// 确保在请求结束时释放许可
		defer globalConcurrencyLimiter.ReleaseAll(userID, modelID)

		c.Next()
	}
}

// extractModelID 从请求中提取模型ID
func extractModelID(c *gin.Context) string {
	// 从JSON请求体中提取模型ID
	if c.Request.Method == "POST" || c.Request.Method == "PUT" {
		if c.ContentType() == "application/json" {
			// 尝试从请求体中获取模型ID
			var requestBody map[string]interface{}
			if err := c.ShouldBindJSON(&requestBody); err == nil {
				if model, ok := requestBody["model"].(string); ok {
					return model
				}
				if modelId, ok := requestBody["model_id"].(string); ok {
					return modelId
				}
			}
		}
	}

	// 从查询参数中提取
	if model := c.Query("model"); model != "" {
		return model
	}
	if modelId := c.Query("model_id"); modelId != "" {
		return modelId
	}

	// 从路径参数中提取
	if model := c.Param("model"); model != "" {
		return model
	}
	if modelId := c.Param("model_id"); modelId != "" {
		return modelId
	}

	// 从路径中推断模型类型
	path := c.Request.URL.Path
	if strings.Contains(path, "/chat/completions") {
		return "llm" // LLM模型的通用标识
	} else if strings.Contains(path, "/audio/transcriptions") {
		return "asr" // ASR模型的通用标识
	} else if strings.Contains(path, "/audio/speech") {
		return "tts" // TTS模型的通用标识
	} else if strings.Contains(path, "/images/generations") {
		return "tti" // TTI模型的通用标识
	}

	return "" // 如果无法提取模型ID，返回空字符串
}
