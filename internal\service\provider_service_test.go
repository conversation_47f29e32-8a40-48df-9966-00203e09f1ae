package service

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// generateTestWalletAndSignature 生成测试用的钱包地址和签名
func generateTestWalletAndSignature(nonce string) (string, string, error) {
	// 生成私钥
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", err
	}

	// 获取地址
	address := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()

	// 构造以太坊签名消息格式
	msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(nonce), nonce)
	hash := crypto.Keccak256Hash([]byte(msg))

	// 签名
	sig, err := crypto.Sign(hash.Bytes(), privateKey)
	if err != nil {
		return "", "", err
	}

	// 编码签名
	signature := hexutil.Encode(sig)

	return address, signature, nil
}

// TestProviderService_NewProviderService 测试创建提供者服务
func TestProviderService_NewProviderService(t *testing.T) {
	t.Run("TC1-创建服务成功", func(t *testing.T) {
		t.Parallel()

		db := &repository.DB{}
		service := NewProviderService(db)

		assert.NotNil(t, service)
		// 注意：由于service是接口类型，无法直接访问内部字段
		// 我们通过调用方法来验证服务是否正常工作
	})
}

// TestProviderService_GenerateLoginNonce 测试生成登录随机字符串
func TestProviderService_GenerateLoginNonce(t *testing.T) {
	t.Run("TC1-生成随机字符串成功", func(t *testing.T) {
		t.Parallel()

		ctx := context.Background()
		mocks := testutil.NewMockDependencies()
		defer mocks.Cleanup()

		service := NewProviderService(&repository.DB{})

		nonce, err := service.GenerateLoginNonce(ctx)

		assert.NoError(t, err)
		assert.NotEmpty(t, nonce)
		assert.Len(t, nonce, 64) // utils.GenerateNonce() 生成64位字符串
	})
}

// TestProviderService_Login 测试钱包签名登录
func TestProviderService_Login(t *testing.T) {
	walletAddr, signature, _ := generateTestWalletAndSignature("testnonce1234567890")

	tests := []struct {
		name    string
		req     *ProviderAuthRequest
		setup   func(*testutil.MockDependencies)
		want    *ProviderAuthResponse
		wantErr bool
	}{
		{
			name: "TC1-新提供者登录成功",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(nil, nil)
				mocks.DB.TopaiModelProvider.On("Create", mock.Anything, mock.AnythingOfType("*repository.TopaiModelProvider")).Return(nil)
				mocks.DB.ProviderToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.ProviderToken")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-已存在提供者登录成功",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				existingProvider := &repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: walletAddr,
					Uuid:          uuid.New().String(),
				}
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(existingProvider, nil)
				mocks.DB.ProviderToken.On("DeleteByProviderID", mock.Anything, uint(1)).Return(nil)
				mocks.DB.ProviderToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.ProviderToken")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC3-获取提供者失败",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(nil, errors.New("database error"))
			},
			wantErr: true,
		},
		{
			name: "TC4-自动注册提供者失败",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(nil, nil)
				mocks.DB.TopaiModelProvider.On("Create", mock.Anything, mock.AnythingOfType("*repository.TopaiModelProvider")).Return(errors.New("create failed"))
			},
			wantErr: true,
		},
		{
			name: "TC5-删除旧token失败",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				existingProvider := &repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: walletAddr,
					Uuid:          uuid.New().String(),
				}
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(existingProvider, nil)
				mocks.DB.ProviderToken.On("DeleteByProviderID", mock.Anything, uint(1)).Return(errors.New("delete failed"))
			},
			wantErr: true,
		},
		{
			name: "TC6-创建token失败",
			req: &ProviderAuthRequest{
				WalletAddr: walletAddr,
				Signature:  signature,
				Nonce:      "testnonce1234567890",
				Timestamp:  time.Now().Unix(),
			},
			setup: func(mocks *testutil.MockDependencies) {
				existingProvider := &repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: walletAddr,
					Uuid:          uuid.New().String(),
				}
				mocks.DB.TopaiModelProvider.On("GetByWalletAddress", mock.Anything, walletAddr).Return(existingProvider, nil)
				mocks.DB.ProviderToken.On("DeleteByProviderID", mock.Anything, uint(1)).Return(nil)
				mocks.DB.ProviderToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.ProviderToken")).Return(errors.New("create token failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
				ProviderToken:      mocks.DB.ProviderToken,
			})

			got, err := service.Login(ctx, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				if got != nil {
					assert.NotEmpty(t, got.Token)
					assert.Greater(t, got.ExpiresAt, time.Now().Unix())
				}
			}

			mocks.DB.TopaiModelProvider.AssertExpectations(t)
			mocks.DB.ProviderToken.AssertExpectations(t)
		})
	}
}

// TestProviderService_GetProviderInfoByToken 测试根据token获取提供者信息
func TestProviderService_GetProviderInfoByToken(t *testing.T) {
	tests := []struct {
		name    string
		token   string
		setup   func(*testutil.MockDependencies)
		want    *ProviderInfo
		wantErr bool
	}{
		{
			name:  "TC1-获取提供者信息成功",
			token: "valid-token",
			setup: func(mocks *testutil.MockDependencies) {
				providerToken := &repository.ProviderToken{
					ID:         1,
					ProviderID: 1,
					Token:      "valid-token",
					ExpiresAt:  time.Now().Add(24 * time.Hour),
				}
				provider := &repository.TopaiModelProvider{
					Id:            1,
					Uuid:          "test-uuid",
					WalletAddress: "0x1234567890abcdef",
					Name:          "Test Provider",
					CreatedAt:     &time.Time{},
				}

				mocks.DB.ProviderToken.On("GetByToken", mock.Anything, "valid-token").Return(providerToken, nil)
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(provider, nil)
			},
			want: &ProviderInfo{
				ID:            1,
				UUID:          "test-uuid",
				WalletAddress: "0x1234567890abcdef",
				Name:          "Test Provider",
				Token:         "valid-token",
			},
			wantErr: false,
		},
		{
			name:  "TC2-Token不存在",
			token: "invalid-token",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.ProviderToken.On("GetByToken", mock.Anything, "invalid-token").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:  "TC3-获取Token失败",
			token: "error-token",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.ProviderToken.On("GetByToken", mock.Anything, "error-token").Return(nil, errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:  "TC4-提供者不存在",
			token: "valid-token-no-provider",
			setup: func(mocks *testutil.MockDependencies) {
				providerToken := &repository.ProviderToken{
					ID:         1,
					ProviderID: 999,
					Token:      "valid-token-no-provider",
					ExpiresAt:  time.Now().Add(24 * time.Hour),
				}

				mocks.DB.ProviderToken.On("GetByToken", mock.Anything, "valid-token-no-provider").Return(providerToken, nil)
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(999)).Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:  "TC5-获取提供者失败",
			token: "valid-token-provider-error",
			setup: func(mocks *testutil.MockDependencies) {
				providerToken := &repository.ProviderToken{
					ID:         1,
					ProviderID: 1,
					Token:      "valid-token-provider-error",
					ExpiresAt:  time.Now().Add(24 * time.Hour),
				}

				mocks.DB.ProviderToken.On("GetByToken", mock.Anything, "valid-token-provider-error").Return(providerToken, nil)
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(nil, errors.New("provider error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				ProviderToken:      mocks.DB.ProviderToken,
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
			})

			got, err := service.GetProviderInfoByToken(ctx, tt.token)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.UUID, got.UUID)
				assert.Equal(t, tt.want.WalletAddress, got.WalletAddress)
				assert.Equal(t, tt.want.Name, got.Name)
				assert.Equal(t, tt.want.Token, got.Token)
			}

			mocks.DB.ProviderToken.AssertExpectations(t)
			mocks.DB.TopaiModelProvider.AssertExpectations(t)
		})
	}
}

// TestProviderService_UpdateProviderInfo 测试更新提供者信息
func TestProviderService_UpdateProviderInfo(t *testing.T) {
	tests := []struct {
		name         string
		providerInfo *ProviderInfo
		req          *UpdateProviderInfoRequest
		setup        func(*testutil.MockDependencies)
		wantErr      bool
	}{
		{
			name: "TC1-更新提供者信息成功",
			providerInfo: &ProviderInfo{
				ID:            1,
				UUID:          "test-uuid",
				WalletAddress: "0x1234567890abcdef",
				Name:          "Old Name",
			},
			req: &UpdateProviderInfoRequest{
				Name: "New Name",
			},
			setup: func(mocks *testutil.MockDependencies) {
				provider := &repository.TopaiModelProvider{
					Id:            1,
					Uuid:          "test-uuid",
					WalletAddress: "0x1234567890abcdef",
					Name:          "Old Name",
				}

				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(provider, nil)
				mocks.DB.TopaiModelProvider.On("Update", mock.Anything, mock.AnythingOfType("*repository.TopaiModelProvider")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-提供者不存在",
			providerInfo: &ProviderInfo{
				ID:            999,
				UUID:          "test-uuid",
				WalletAddress: "0x1234567890abcdef",
				Name:          "Old Name",
			},
			req: &UpdateProviderInfoRequest{
				Name: "New Name",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(999)).Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name: "TC3-获取提供者失败",
			providerInfo: &ProviderInfo{
				ID:            1,
				UUID:          "test-uuid",
				WalletAddress: "0x1234567890abcdef",
				Name:          "Old Name",
			},
			req: &UpdateProviderInfoRequest{
				Name: "New Name",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(nil, errors.New("database error"))
			},
			wantErr: true,
		},
		{
			name: "TC4-更新提供者失败",
			providerInfo: &ProviderInfo{
				ID:            1,
				UUID:          "test-uuid",
				WalletAddress: "0x1234567890abcdef",
				Name:          "Old Name",
			},
			req: &UpdateProviderInfoRequest{
				Name: "New Name",
			},
			setup: func(mocks *testutil.MockDependencies) {
				provider := &repository.TopaiModelProvider{
					Id:            1,
					Uuid:          "test-uuid",
					WalletAddress: "0x1234567890abcdef",
					Name:          "Old Name",
				}

				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(provider, nil)
				mocks.DB.TopaiModelProvider.On("Update", mock.Anything, mock.AnythingOfType("*repository.TopaiModelProvider")).Return(errors.New("update failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
			})

			err := service.UpdateProviderInfo(ctx, tt.providerInfo, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.DB.TopaiModelProvider.AssertExpectations(t)
		})
	}
}

// TestProviderService_GetProviderModels 测试获取提供者模型列表
func TestProviderService_GetProviderModels(t *testing.T) {
	tests := []struct {
		name         string
		providerInfo *ProviderInfo
		page         int
		limit        int
		setup        func(*testutil.MockDependencies)
		want         *ProviderModelList
		wantErr      bool
	}{
		{
			name: "TC1-成功获取模型列表",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				// Mock provider
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(&repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: "0x1234567890abcdef",
				}, nil)

				// Mock count
				mocks.DB.TopaiModel.On("GetCountByOwnerAddress", mock.Anything, "0x1234567890abcdef").Return(int64(2), nil)

				// Mock models
				models := []*repository.TopaiModel{
					{
						Id:           1,
						ChainModelId: 100,
						OwnerAddress: "0x1234567890abcdef",
						ModelName:    "test-model-1",
						ModelVersion: "1.0",
						ModelType:    "text-generation",
						InputPrice:   "0.001",
						OutputPrice:  "0.002",
					},
					{
						Id:           2,
						ChainModelId: 101,
						OwnerAddress: "0x1234567890abcdef",
						ModelName:    "test-model-2",
						ModelVersion: "1.0",
						ModelType:    "text-generation",
						InputPrice:   "0.002",
						OutputPrice:  "0.003",
					},
				}
				mocks.DB.TopaiModel.On("GetByOwnerAddress", mock.Anything, "0x1234567890abcdef", 0, 10).Return(models, nil)

				// Mock model ext
				mocks.DB.TopaiModelExt.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(&repository.TopaiModelExt{
					ModelId: "model-1",
					Status:  1,
				}, nil)
				mocks.DB.TopaiModelExt.On("GetByTopaiModelId", mock.Anything, uint(2)).Return(nil, nil)
			},
			want: &ProviderModelList{
				Models: []*ProviderModelInfo{
					{
						ChainModelID: 100,
						OwnerAddress: "0x1234567890abcdef",
						ModelName:    "test-model-1",
						ModelVersion: "1.0",
						ModelType:    "text-generation",
						InputPrice:   "0.001",
						OutputPrice:  "0.002",
						ModelID:      "model-1",
						Status:       1,
					},
					{
						ChainModelID: 101,
						OwnerAddress: "0x1234567890abcdef",
						ModelName:    "test-model-2",
						ModelVersion: "1.0",
						ModelType:    "text-generation",
						InputPrice:   "0.002",
						OutputPrice:  "0.003",
					},
				},
				Count: 2,
			},
			wantErr: false,
		},
		{
			name: "TC2-提供者不存在",
			providerInfo: &ProviderInfo{
				ID:            999,
				WalletAddress: "0x1234567890abcdef",
			},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(999)).Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-获取数量失败",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(&repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: "0x1234567890abcdef",
				}, nil)
				mocks.DB.TopaiModel.On("GetCountByOwnerAddress", mock.Anything, "0x1234567890abcdef").Return(int64(0), errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
				TopaiModel:         mocks.DB.TopaiModel,
				TopaiModelExt:      mocks.DB.TopaiModelExt,
			})

			got, err := service.GetProviderModels(ctx, tt.providerInfo, tt.page, tt.limit)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestProviderService_GetProviderModelInfo 测试获取提供者模型信息
func TestProviderService_GetProviderModelInfo(t *testing.T) {
	tests := []struct {
		name         string
		providerInfo *ProviderInfo
		chainModelId uint
		setup        func(*testutil.MockDependencies)
		want         *ProviderModelInfo
		wantErr      bool
	}{
		{
			name: "TC1-成功获取模型信息",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 100,
			setup: func(mocks *testutil.MockDependencies) {
				// Mock model
				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(100)).Return(&repository.TopaiModel{
					Id:           1,
					ChainModelId: 100,
					OwnerAddress: "0x1234567890abcdef",
					ModelName:    "test-model",
					ModelVersion: "1.0",
					ModelType:    "text-generation",
					InputPrice:   "0.001",
					OutputPrice:  "0.002",
					SeriesId:     1,
				}, nil)

				// Mock model ext - use exact parameter matching
				mocks.DB.TopaiModelExt.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(&repository.TopaiModelExt{
					ModelId:     "model-1",
					Description: "Test model description",
					Status:      1,
					ShowPicture: "file-uuid-1",
					ShowVideo:   "file-uuid-2",
					SeriesId:    1,
				}, nil)

				// Mock files
				mocks.DB.File.On("GetByUUID", mock.Anything, "file-uuid-1").Return(&repository.File{
					UUID: "file-uuid-1",
					Path: "/path/to/image.jpg",
				}, nil)
				mocks.DB.File.On("GetByUUID", mock.Anything, "file-uuid-2").Return(&repository.File{
					UUID: "file-uuid-2",
					Path: "/path/to/video.mp4",
				}, nil)

				// Mock category relations
				mocks.DB.TopaiModelCategoryRelation.On("GetByTopaiModelId", mock.Anything, uint(1)).Return([]*repository.TopaiModelCategoryRelation{}, nil)

				// Mock TTS voices (empty for non-TTS model) - only called for TTS models
				// mocks.DB.TTSModelVoice.On("GetByModelName", mock.Anything, "test-model").Return([]*repository.TTSModelVoice{}, nil)
			},
			want: &ProviderModelInfo{
				ChainModelID: 100,
				OwnerAddress: "0x1234567890abcdef",
				ModelName:    "test-model",
				ModelVersion: "1.0",
				ModelType:    "text-generation",
				InputPrice:   "0.001",
				OutputPrice:  "0.002",
				SeriesID:     1,
				ModelID:      "model-1",
				Description:  "Test model description",
				Status:       1,
				ShowPicture:  "/api/v1/files/file/file-uuid-1/content",
				ShowVideo:    "/api/v1/files/file/file-uuid-2/content",
			},
			wantErr: false,
		},
		{
			name: "TC2-模型不存在",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 999,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(999)).Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-模型不属于提供者",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 100,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(100)).Return(&repository.TopaiModel{
					Id:           1,
					ChainModelId: 100,
					OwnerAddress: "0x9876543210fedcba", // 不同的地址
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				File:                       mocks.DB.File,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TTSModelVoice:              mocks.DB.TTSModelVoice,
			})

			got, err := service.GetProviderModelInfo(ctx, tt.providerInfo, tt.chainModelId)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestProviderService_UpdateModelExt 测试更新模型扩展信息
func TestProviderService_UpdateModelExt(t *testing.T) {
	tests := []struct {
		name         string
		providerInfo *ProviderInfo
		chainModelId uint
		req          *UpdateModelExtRequest
		setup        func(*testutil.MockDependencies)
		wantErr      bool
	}{
		{
			name: "TC1-成功更新模型扩展信息",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 100,
			req: &UpdateModelExtRequest{
				ChainModelID: 100,
				Description:  "Updated description",
				SampleCode:   "print('Hello World')",
				ShowPicture:  "file-uuid-1",
				ShowVideo:    "file-uuid-2",
				SeriesId:     1,
				CategoryIds:  []uint{1, 2},
				TTSVoices:    []string{"voice1", "voice2"},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock provider
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(&repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: "0x1234567890abcdef",
				}, nil)

				// Mock model
				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(100)).Return(&repository.TopaiModel{
					Id:           1,
					ChainModelId: 100,
					OwnerAddress: "0x1234567890abcdef",
					ModelName:    "test-model",
					ModelType:    "tts",
				}, nil)

				// Mock model ext
				mocks.DB.TopaiModelExt.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(&repository.TopaiModelExt{
					Id:           1,
					TopaiModelId: 1,
				}, nil)

				// Mock TopaiModelExt.Update
				mocks.DB.TopaiModelExt.On("Update", mock.Anything, mock.AnythingOfType("*repository.TopaiModelExt")).Return(nil)

				// Mock transaction - RunWithTx 会自动调用传入的函数
				// mocks.DB.RunWithTx = func(ctx context.Context, fn func(ctx context.Context, db *repository.DB) error) error {
				// 	return fn(ctx, mocks.DB)
				// }

				// Mock category relations
				mocks.DB.TopaiModelCategoryRelation.On("DeleteByTopaiModelId", mock.Anything, uint(1)).Return(nil)
				mocks.DB.TopaiModelCategoryRelation.On("CreateBatch", mock.Anything, mock.AnythingOfType("[]*repository.TopaiModelCategoryRelation")).Return(nil)

				// Mock TTS voices - use simpler matchers
				mocks.DB.TTSModelVoice.On("DeleteByModelName", mock.Anything, "test-model").Return(nil)
				mocks.DB.TTSModelVoice.On("CreateBatch", mock.Anything, mock.Anything).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-模型不存在",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 999,
			req: &UpdateModelExtRequest{
				ChainModelID: 999,
				Description:  "Updated description",
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock provider
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(&repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: "0x1234567890abcdef",
				}, nil)

				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(999)).Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name: "TC3-模型不属于提供者",
			providerInfo: &ProviderInfo{
				ID:            1,
				WalletAddress: "0x1234567890abcdef",
			},
			chainModelId: 100,
			req: &UpdateModelExtRequest{
				ChainModelID: 100,
				Description:  "Updated description",
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock provider
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(&repository.TopaiModelProvider{
					Id:            1,
					WalletAddress: "0x1234567890abcdef",
				}, nil)

				mocks.DB.TopaiModel.On("GetByChainModelId", mock.Anything, uint(100)).Return(&repository.TopaiModel{
					Id:           1,
					ChainModelId: 100,
					OwnerAddress: "0x9876543210fedcba", // 不同的地址
				}, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TTSModelVoice:              mocks.DB.TTSModelVoice,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				RunWithTx: func(ctx context.Context, fn func(ctx context.Context, db *repository.DB) error) error {
					return fn(ctx, &repository.DB{
						TopaiModel:                 mocks.DB.TopaiModel,
						TopaiModelExt:              mocks.DB.TopaiModelExt,
						TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
						TTSModelVoice:              mocks.DB.TTSModelVoice,
						TopaiModelProvider:         mocks.DB.TopaiModelProvider,
					})
				},
			})

			err := service.UpdateModelExt(ctx, tt.providerInfo, tt.chainModelId, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestProviderService_GetAllCategories 测试获取所有分类
func TestProviderService_GetAllCategories(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    []*TopaiModelCategoryOP
		wantErr bool
	}{
		{
			name: "TC1-成功获取所有分类",
			setup: func(mocks *testutil.MockDependencies) {
				categories := []*repository.TopaiModelCategory{
					{
						Id:   1,
						Name: "分类1",
					},
					{
						Id:   2,
						Name: "分类2",
					},
				}
				mocks.DB.TopaiModelCategory.On("GetAll", mock.Anything).Return(categories, nil)
			},
			want: []*TopaiModelCategoryOP{
				{
					CategoryId:   1,
					CategoryName: "分类1",
				},
				{
					CategoryId:   2,
					CategoryName: "分类2",
				},
			},
			wantErr: false,
		},
		{
			name: "TC2-获取分类失败",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelCategory.On("GetAll", mock.Anything).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewProviderService(&repository.DB{
				TopaiModelCategory: mocks.DB.TopaiModelCategory,
			})

			got, err := service.GetAllCategories(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}
