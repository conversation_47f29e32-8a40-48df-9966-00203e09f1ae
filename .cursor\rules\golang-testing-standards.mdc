---
description: 
globs: 
alwaysApply: true
---
---
description: Testing Implementation Requirements (Revised)
globs: **/*_test.go
alwaysApply: true
---

0. Test Framework Requirements
- Mandatory use of standard `testing` package
- Unified assertion framework: `github.com/stretchr/testify/assert` (v1.8.0+)
- Unified mock framework: `github.com/stretchr/testify/mock` (v1.8.0+)
- Strict prohibition of shared global state between tests

1. Test Structure Specifications
- Test Isolation Requirements
    ```go
    for _, tt := range tests {
        tt := tt // Avoid closure variable capture issues
        t.Run(tt.name, func(t *testing.T) {
            t.Parallel()  // Enable parallel execution by default
            
            // Initialize isolated environment
            mocks := NewMockDependencies()
            defer mocks.Cleanup()  // Add resource cleanup
            
            if tt.setupMocks != nil {
                tt.setupMocks(mocks)
            }
            
            // Test execution...
            
            // Environment validation
            assertNoCrossTestImpact(t, mocks)
        })
    }
    ```
- Enhanced Test Pattern
    ```go
    func TestOperation(t *testing.T) {
        tests := []struct {
            name        string
            input       Input
            setup       func(*testing.T) *Env  // Return isolated test environment
            want        Output
            wantErr     error // Use concrete error type instead of boolean
        }{
            {
                name: "TC1-Normal flow",
                input: Input{ID: "test-id"},
                setup: func(t *testing.T) *Env {
                    env := NewTestEnv(t)
                    env.Mock.Repo.EXPECT().
                        Get(mock.Anything, "test-id").
                        Return(&Entity{}, nil)
                    return env
                },
                want: Output{Status: "success"},
            },
        }

        for _, tt := range tests {
            tt := tt
            t.Run(tt.name, func(t *testing.T) {
                t.Parallel()
                
                env := tt.setup(t)
                defer env.Cleanup()  // Ensure resource release

                got, err := Operation(env.Context, tt.input)

                // Enhanced error assertions
                if tt.wantErr != nil {
                    assert.ErrorIs(t, err, tt.wantErr)
                    assert.Nil(t, got, "Should return zero value on error")
                } else {
                    assert.NoError(t, err)
                    assert.Equal(t, tt.want, got)
                }

                // Enforce mock validation
                env.Mock.Repo.AssertExpectations(t)
                env.Mock.Cache.AssertExpectations(t)
            })
        }
    }
    ```

2. Test Isolation Guarantees
- Environment Isolation
    ```go
    type Env struct {
        T      *testing.T
        Mock   *MockDependencies
        DB     *TestDatabase  // Independent database per test
        TempFS afero.Fs       // Isolated filesystem
    }

    func NewTestEnv(t *testing.T) *Env {
        return &Env{
            T:      t,
            Mock:   NewMockDependencies(),
            DB:     testkit.NewDB(t),  // Auto-create temp database
            TempFS: afero.NewMemMapFs(),
        }
    }
    ```
- Parallel Testing Constraints
  - Default enablement of `t.Parallel()`
  - Prohibit parallelism when:
    * Modifying process-level settings (e.g., environment variables)
    * Using real external services
    * Operating hardware resources

- State Cleanup Protocol
    ```go
    func (e *Env) Cleanup() {
        // Cleanup order is critical
        e.Mock.AssertExpectations(e.T)
        e.DB.Close()
        e.TempFS.RemoveAll("/")
        cleanGlobalState()  // Reset global state
    }
    ```

3. Enhanced Mock Specifications
```go
type MockDependencies struct {
    Repo    *MockRepository `reset:"always"`  // Auto-reset per test case
    Cache   *MockCache      `reset:"always"`
}

// Generate auto-reset code
func NewMockDependencies() *MockDependencies {
    return &MockDependencies{
        Repo:    NewMockRepository(),
        Cache:   NewMockCache(),
    }
}

// Auto-invoked during cleanup
func (m *MockDependencies) Reset() {
    m.Repo.Reset()
    m.Cache.Reset()
}
```

4. Coverage Control Strategy
  - Layered Coverage Requirements:
    * Core business logic: 100%
    * Utility code: ≥85%
    * Third-party adapters: ≥70%
    
  - Incremental Rules:
    * New features must have 100% coverage
    * Max 1% coverage fluctuation per PR

  - Exemption Mechanism:
    ```go
    // Declare in test files
    func TestMain(m *testing.M) {
        if os.Getenv("CI") != "" {
            testutil.VerifyCoverage(m, 85) // Module-level threshold
        }
    }
    ```


5. Performance Testing Standards
- Benchmarks Must Include:
  ```go
  func BenchmarkOperation(b *testing.B) {
      b.ReportAllocs()  // Memory allocation metrics
      b.ResetTimer()
      for i := 0; i < b.N; i++ {
          // Test logic
      }
  }
  ```
- Acceptance Criteria:
  - P99 latency ≤ 200ms
  - Memory allocations ≤ 5 allocs/op
  - Zero goroutine leaks


6. Test Data Management
- Data Generation:
  ```go
  // Use random but deterministic data
  func GenTestUser(t *testing.T) *User {
      r := rand.New(rand.NewSource(0)) // Fixed seed
      return &User{
          ID:   uuid.New(), 
          Name: "testuser_"+r.String(8),
      }
  }
  ```
- Data Cleanup:
    ```go
    func TestCreateUser(t *testing.T) {
        db := testkit.NewDB(t)
        t.Cleanup(func() {
            db.Exec("DELETE FROM users WHERE name LIKE 'testuser_%'")
        })
    }
    ```


