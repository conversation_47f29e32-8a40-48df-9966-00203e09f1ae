// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserShadowWalletBalanceRepository is an autogenerated mock type for the UserShadowWalletBalanceRepository type
type UserShadowWalletBalanceRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, balance
func (_m *UserShadowWalletBalanceRepository) Create(ctx context.Context, balance *repository.UserShadowWalletBalance) error {
	ret := _m.Called(ctx, balance)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserShadowWalletBalance) error); ok {
		r0 = rf(ctx, balance)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByUserIDAndCurrency provides a mock function with given fields: ctx, userID, currency
func (_m *UserShadowWalletBalanceRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserShadowWalletBalance, error) {
	ret := _m.Called(ctx, userID, currency)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserIDAndCurrency")
	}

	var r0 *repository.UserShadowWalletBalance
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.UserShadowWalletBalance, error)); ok {
		return rf(ctx, userID, currency)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.UserShadowWalletBalance); ok {
		r0 = rf(ctx, userID, currency)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserShadowWalletBalance)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, userID, currency)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateBalanceByUserIDAndCurrency provides a mock function with given fields: ctx, userID, currency, balance, accumulated, lastBlockNumber
func (_m *UserShadowWalletBalanceRepository) UpdateBalanceByUserIDAndCurrency(ctx context.Context, userID uint, currency string, balance string, accumulated string, lastBlockNumber uint64) error {
	ret := _m.Called(ctx, userID, currency, balance, accumulated, lastBlockNumber)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBalanceByUserIDAndCurrency")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string, string, string, uint64) error); ok {
		r0 = rf(ctx, userID, currency, balance, accumulated, lastBlockNumber)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserShadowWalletBalanceRepository creates a new instance of UserShadowWalletBalanceRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserShadowWalletBalanceRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserShadowWalletBalanceRepository {
	mock := &UserShadowWalletBalanceRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
