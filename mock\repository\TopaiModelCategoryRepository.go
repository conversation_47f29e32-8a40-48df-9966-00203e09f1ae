// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelCategoryRepository is an autogenerated mock type for the TopaiModelCategoryRepository type
type TopaiModelCategoryRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelCategoryRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelCategory, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelCategory, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelCategory); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *TopaiModelCategoryRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategory, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.TopaiModelCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelCategory, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelCategory); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelCategoryRepository creates a new instance of TopaiModelCategoryRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelCategoryRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelCategoryRepository {
	mock := &TopaiModelCategoryRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
