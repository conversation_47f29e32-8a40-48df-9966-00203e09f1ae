package testutil

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"testing"
	"time"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// GenTestUser 生成测试用户数据（确定性随机）
func GenTestUser(t *testing.T) *repository.User {
	seed := hashTestName(t.Name())
	now := time.Now()
	createdAt := now.Add(-time.Duration(seed%24) * time.Hour)

	return &repository.User{
		ID:         uint(seed % 1000000),
		IdentityID: fmt.Sprintf("test_user_%d", seed),
		Username:   fmt.Sprintf("TestUser_%d", seed),
		Email:      fmt.Sprintf("<EMAIL>", seed),
		Role:       repository.UserRoleUser,
		CreatedAt:  &createdAt,
		UpdatedAt:  &now,
	}
}

// GenTestAdminUser 生成测试管理员用户
func GenTestAdminUser(t *testing.T) *repository.User {
	user := GenTestUser(t)
	user.Role = repository.UserRoleAdmin
	user.Email = fmt.Sprintf("admin_%s", user.Email)
	user.Username = fmt.Sprintf("Admin_%s", user.Username)
	return user
}

// GenTestConversation 生成测试对话数据
func GenTestConversation(t *testing.T, userID uint) *repository.Conversation {
	seed := hashTestName(t.Name())
	now := time.Now()
	createdAt := now.Add(-time.Duration(seed%24) * time.Hour)

	return &repository.Conversation{
		ID:        uint(seed % 1000000),
		UserID:    userID,
		Title:     fmt.Sprintf("Test Conversation %d", seed),
		CreatedAt: &createdAt,
		UpdatedAt: &now,
	}
}

// GenTestUserToken 生成测试用户Token
func GenTestUserToken(t *testing.T, userID uint) *repository.UserToken {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.UserToken{
		ID:        uint(seed % 1000000),
		UserID:    userID,
		Token:     fmt.Sprintf("test_token_%d", seed),
		ExpiresAt: now.Add(24 * time.Hour),
		CreatedAt: &now,
	}
}

// GenTestUserBalance 生成测试用户余额
func GenTestUserBalance(t *testing.T, userID uint) *repository.UserBalance {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.UserBalance{
		ID:        uint(seed % 1000000),
		UserID:    userID,
		Balance:   fmt.Sprintf("%d", (seed%10000)+1000), // 1000-10999的余额
		Currency:  repository.UserBalanceCurrencyUSDT,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
}

// GenTestFile 生成测试文件数据
func GenTestFile(t *testing.T, userID uint) *repository.File {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.File{
		ID:           uint(seed % 1000000),
		UserID:       userID,
		Name:         fmt.Sprintf("test_file_%d.txt", seed),
		OriginalName: fmt.Sprintf("original_test_file_%d.txt", seed),
		Path:         fmt.Sprintf("/uploads/test_%d.txt", seed),
		Size:         int64(seed%10000 + 100), // 100-10099字节
		MimeType:     "text/plain",
		CreatedAt:    &now,
		UpdatedAt:    &now,
	}
}

// GenTestLLMModelConfig 生成测试LLM模型配置
func GenTestLLMModelConfig(t *testing.T) *repository.LLMModelConfig {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.LLMModelConfig{
		ID:        uint(seed % 1000000),
		ModelID:   fmt.Sprintf("test-model-%d", seed),
		Name:      fmt.Sprintf("Test Model %d", seed),
		IsActive:  true,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
}

// GenTestModel 生成测试模型数据（LLMModelConfig的别名）
func GenTestModel(t *testing.T, modelID string) *repository.LLMModelConfig {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.LLMModelConfig{
		ID:          uint(seed % 1000000),
		ModelID:     modelID,
		Name:        fmt.Sprintf("Test Model %s", modelID),
		BaseModelID: fmt.Sprintf("base_%s", modelID),
		IsActive:    true,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	}
}

// GenTestTopaiModel 生成测试TopAI模型数据
func GenTestTopaiModel(t *testing.T, modelName string) *repository.TopaiModel {
	seed := hashTestName(t.Name())
	now := time.Now()

	return &repository.TopaiModel{
		Id:                uint(seed % 1000000),
		ChainModelId:      uint(seed % 10000),
		OwnerAddress:      fmt.Sprintf("0x%d", seed),
		ModelName:         modelName,
		ModelVersion:      "v1.0",
		ModelType:         "llm",
		InputPrice:        "0.001",
		OutputPrice:       "0.002",
		SeriesId:          uint(seed % 100),
		SupportImageLevel: "none",
		CreatedAt:         &now,
	}
}

// hashTestName 将测试名称转换为确定性的数字种子
func hashTestName(name string) int64 {
	hash := int64(0)
	for _, c := range name {
		hash = hash*31 + int64(c)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// GenerateRandomBytes 生成随机字节（用于敏感数据）
func GenerateRandomBytes(n int) ([]byte, error) {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		return nil, err
	}
	return b, nil
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(n int) (string, error) {
	const letters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, n)
	for i := 0; i < n; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			return "", err
		}
		ret[i] = letters[num.Int64()]
	}
	return string(ret), nil
}

// CleanupTestData 清理测试数据
func CleanupTestData(t *testing.T, db *repository.DB) {
	t.Cleanup(func() {
		ctx := context.Background()

		// 这里只做示例，实际项目中需要根据数据库实现来调用正确的方法
		// 清理顺序很重要，要考虑外键约束
		t.Logf("Cleaning up test data...")

		// 注意：在实际实现中，需要添加具体的清理逻辑
		// 例如调用 repository 的删除方法
		_ = ctx // 避免未使用变量错误
		_ = db  // 避免未使用变量错误
	})
}
