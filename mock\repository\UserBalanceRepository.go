// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserBalanceRepository is an autogenerated mock type for the UserBalanceRepository type
type UserBalanceRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, balance
func (_m *UserBalanceRepository) Create(ctx context.Context, balance *repository.UserBalance) error {
	ret := _m.Called(ctx, balance)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserBalance) error); ok {
		r0 = rf(ctx, balance)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByUserIDAndCurrency provides a mock function with given fields: ctx, userID, currency
func (_m *UserBalanceRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserBalance, error) {
	ret := _m.Called(ctx, userID, currency)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserIDAndCurrency")
	}

	var r0 *repository.UserBalance
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.UserBalance, error)); ok {
		return rf(ctx, userID, currency)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.UserBalance); ok {
		r0 = rf(ctx, userID, currency)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserBalance)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, userID, currency)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateBalanceByID provides a mock function with given fields: ctx, id, balance, accumulated
func (_m *UserBalanceRepository) UpdateBalanceByID(ctx context.Context, id uint, balance string, accumulated string) error {
	ret := _m.Called(ctx, id, balance, accumulated)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBalanceByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string, string) error); ok {
		r0 = rf(ctx, id, balance, accumulated)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserBalanceRepository creates a new instance of UserBalanceRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserBalanceRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserBalanceRepository {
	mock := &UserBalanceRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
