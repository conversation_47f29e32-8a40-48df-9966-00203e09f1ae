package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// OpenAIAPIClient 定义OpenAI API客户端接口，用于mock测试
type OpenAIAPIClient interface {
	CreateChatCompletionStream(ctx context.Context, request openai.ChatCompletionRequest) (*openai.ChatCompletionStream, error)
	CreateChatCompletion(ctx context.Context, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error)
	CreateTranscription(ctx context.Context, request openai.AudioRequest) (openai.AudioResponse, error)
	CreateImage(ctx context.Context, request openai.ImageRequest) (openai.ImageResponse, error)
	CreateSpeech(ctx context.Context, request openai.CreateSpeechRequest) (openai.RawResponse, error)
	ListModels(ctx context.Context) (openai.ModelsList, error)
}

// 用于管理兼容openai的连接调用，不提供给外部直接调用
type openaiManager struct {
	// 一个openai client
	client      OpenAIAPIClient
	log         *logger.ModuleLogger
	rwLock      *sync.RWMutex
	modelUpdate *openaiModelUpdate
}

type openaiModelUpdate struct {
	updateTime time.Time
	models     map[string]*openai.Model
}

func newOpenaiManager(ctx context.Context, conf config.Openai) *openaiManager {
	log := logger.GetLogger("openai-manager")
	c := openai.DefaultConfig(conf.ApiKey)
	c.BaseURL = conf.BaseUrl
	return &openaiManager{
		client:      openai.NewClientWithConfig(c),
		log:         log,
		rwLock:      &sync.RWMutex{},
		modelUpdate: &openaiModelUpdate{},
	}
}

// newOpenaiManagerWithClient 构造函数，接受自定义的API客户端，用于测试
func newOpenaiManagerWithClient(ctx context.Context, client OpenAIAPIClient) *openaiManager {
	log := logger.GetLogger("openai-manager")
	return &openaiManager{
		client:      client,
		log:         log,
		rwLock:      &sync.RWMutex{},
		modelUpdate: &openaiModelUpdate{},
	}
}

func (O *openaiManager) ChatLLMStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (<-chan *openai.ChatCompletionStreamResponse, <-chan error, error) {
	O.log.Info("create chat completion stream", zap.Any("req", req), zap.Any("msgs", req.Messages))
	stream, err := O.client.CreateChatCompletionStream(ctx, *req)
	if err != nil {
		return nil, nil, fmt.Errorf("stream creation error: %v", err)
	}

	msgCh := make(chan *openai.ChatCompletionStreamResponse, 1024)
	errCh := make(chan error, 1)
	go func() {
		defer close(msgCh)
		defer close(errCh)
		defer stream.Close()

		for {
			select {
			case <-ctx.Done():
				errCh <- context.Canceled
				O.log.Info("接收到取消信号")
				return
			default:
			}
			resp, err := stream.Recv()
			if err != nil {
				if err == io.EOF {
					return
				}
				errCh <- err
				return
			}
			if len(resp.Choices) > 0 && len(resp.Choices[0].Delta.Content) > 0 {
				//O.log.Info(fmt.Sprintf("Receive message: %s", resp.Choices[0].Delta.Content))
				msgCh <- &resp
			}
		}
	}()
	return msgCh, errCh, nil
}

func (O *openaiManager) ChatLLMNonStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (*openai.ChatCompletionResponse, error) {
	O.log.Info("Create chat completion request", zap.String("remoterModel", remoterModel.ProviderModelName), zap.Any("req", req))
	req.Model = remoterModel.ProviderModelName
	resp, err := O.client.CreateChatCompletion(ctx, *req)
	if err != nil {
		O.log.Error("Create chat completion error", zap.Error(err))
		return nil, err
	}
	O.log.Info("Create chat completion response", zap.Any("resp", resp))
	return &resp, nil
}

// 语音转文本
func (O *openaiManager) ChatASR(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.AudioRequest) (string, error) {
	//O.log.Info("Create transcription request", zap.String("remoterModel", remoterModel.ProviderModelName), zap.Any("req", req), zap.Bool("req.Reader != nil", req.Reader != nil), zap.String("filePath", req.FilePath))
	resp, err := O.client.CreateTranscription(ctx, *req)
	if err != nil {
		O.log.Error("Create transcription error", zap.Error(err))
		return "", err
	}
	return resp.Text, nil
}

// 文本转语音
func (O *openaiManager) ChatTTI(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ImageRequest) ([]byte, error) {
	O.log.Info("Create image request", zap.String("remoterModel", remoterModel.ProviderModelName), zap.Any("req", req))
	resp, err := O.client.CreateImage(ctx, *req)
	if err != nil {
		return nil, err
	}
	//O.log.Info(fmt.Sprintf("Create image response: %+v", resp))
	if resp.Data[0].URL != "" {
		// 通过URL获取
		resp, err := http.Get(resp.Data[0].URL)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		return io.ReadAll(resp.Body)
	}
	O.log.Info("Create image response", zap.Any("resp", resp))
	return base64.StdEncoding.DecodeString(resp.Data[0].B64JSON)
}

// 文本转语音
func (O *openaiManager) ChatTTS(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.CreateSpeechRequest) ([]byte, error) {
	//O.log.Info("Create speech request", zap.String("remoterModel", remoterModel.ProviderModelName), zap.Any("req", req))
	resp, err := O.client.CreateSpeech(ctx, *req)
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(resp)
	if err != nil {
		return nil, err
	}
	O.log.Info("Create speech response", zap.Any("resp", resp))
	return body, nil
}

// 42["chat-events",{"chat_id":"7198b5be-f57f-418f-a32b-df1b24e21230","message_id":"04850b4d-8289-4bda-a5a4-a55e2b0e55be","data":{"type":"files","data":{"files":[{"type":"image","url":"/api/v1/files/image/ff861167-2ea5-45a5-9198-48ea5ed33566/content"}]}}}]

// 获取所有模型信息
// 注意：：：：： 不是所有的平台都有这个方法
func (O *openaiManager) getAllModelInfo(ctx context.Context) (map[string]*openai.Model, error) {
	// 先判断有比较新的，就直接返回，不用每次都去查询，10分钟内查过就不去查询了
	O.rwLock.RLock()
	if O.modelUpdate.models != nil && len(O.modelUpdate.models) > 0 && time.Now().Sub(O.modelUpdate.updateTime).Seconds() < 3000 {
		O.rwLock.RUnlock()
		return O.modelUpdate.models, nil
	}
	O.rwLock.RUnlock()
	models, err := O.client.ListModels(ctx)
	if err != nil {
		return nil, err
	}
	list := map[string]*openai.Model{}
	for _, m := range models.Models {
		list[m.ID] = &m
		O.log.Info(fmt.Sprintf("Get model: %v", m))
	}
	O.rwLock.Lock()
	O.modelUpdate.models = list
	O.modelUpdate.updateTime = time.Now()
	O.rwLock.Unlock()
	return list, nil
}
