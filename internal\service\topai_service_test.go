package service

import (
	"context"
	"errors"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/testutil"
	"topnetwork.ai/topai/chat-webserver/utils/chain"
)

// TestTopaiService_NewTopaiService 测试创建TopAI服务
func TestTopaiService_NewTopaiService(t *testing.T) {
	tests := []struct {
		name       string
		wantNotNil bool
	}{
		{
			name:       "TC1-创建TopAI服务",
			wantNotNil: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			if tt.wantNotNil {
				assert.NotNil(t, service)
			} else {
				assert.Nil(t, service)
			}
		})
	}
}

// TestTopaiService_GetTopaiModelList 测试获取TopAI模型列表
func TestTopaiService_GetTopaiModelList(t *testing.T) {
	tests := []struct {
		name    string
		req     *GetTopaiModelListRequest
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取模型列表成功",
			req: &GetTopaiModelListRequest{
				Page:      1,
				PageSize:  10,
				OrderBy:   "id",
				OrderType: "desc",
			},
			setup: func(mocks *testutil.MockDependencies) {
				modelExts := []*repository.TopaiModelExt{
					{
						Id:                   1,
						ModelId:              "test-model-1",
						TopaiModelId:         1,
						TopaiModelProviderId: 1,
						Status:               1,
					},
				}
				models := []*repository.TopaiModel{
					{
						Id:        1,
						ModelName: "test-model-1",
						ModelType: "llm",
					},
				}
				providers := []*repository.TopaiModelProvider{
					{
						Id:   1,
						Name: "test-provider",
					},
				}
				costs := []*repository.TopaiModelCost{
					{
						TopaiModelId: 1,
						ModelId:      "test-model-1",
					},
				}
				categoryRelations := []*repository.TopaiModelCategoryRelation{
					{
						TopaiModelId: 1,
						CategoryId:   1,
					},
				}

				mocks.DB.TopaiModelExt.On("GetOnlineList", mock.Anything, 0, 10, "id", "desc", mock.Anything, []uint{}, false, "").Return(modelExts, nil)
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(models, nil)
				mocks.DB.TopaiModelProvider.On("GetByIds", mock.Anything, []uint{1}).Return(providers, nil)
				mocks.DB.TopaiModelCost.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return(costs, nil)
				mocks.DB.TopaiModelCategoryRelation.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return(categoryRelations, nil)
				mocks.DB.TopaiModelCategory.On("GetByIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModelCategory{{Id: 1, Name: "test-category"}}, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			req: &GetTopaiModelListRequest{
				Page:     1,
				PageSize: 10,
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelExt.On("GetOnlineList", mock.Anything, 0, 10, "", "", mock.Anything, []uint{}, false, "").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetTopaiModelList(ctx, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestTopaiService_GetTopaiModelDetail 测试获取TopAI模型详情
func TestTopaiService_GetTopaiModelDetail(t *testing.T) {
	tests := []struct {
		name    string
		modelId string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:    "TC1-获取模型详情成功",
			modelId: "test-model-1",
			setup: func(mocks *testutil.MockDependencies) {
				modelExt := &repository.TopaiModelExt{
					Id:                   1,
					ModelId:              "test-model-1",
					TopaiModelId:         1,
					TopaiModelProviderId: 1,
					Status:               1,
					Description:          "desc",
					ContextLength:        2048,
					Price:                "0",
					MaxOutput:            512,
				}
				model := &repository.TopaiModel{
					Id:        1,
					ModelName: "test-model-1",
					ModelType: "llm",
				}
				provider := &repository.TopaiModelProvider{
					Id:   1,
					Name: "test-provider",
				}
				cost := &repository.TopaiModelCost{
					TopaiModelId: 1,
					ModelId:      "test-model-1",
					TotalTokens:  "1000",
				}
				seriesRelation := &repository.TopaiModelSeriesRelation{SeriesId: 1}
				series := &repository.TopaiModelSeries{Id: 1, Name: "test-series"}
				categoryRelations := []*repository.TopaiModelCategoryRelation{{CategoryId: 2}}
				categories := []*repository.TopaiModelCategory{{Id: 2, Name: "cat2"}}
				usageRecords := []*repository.UserUsageDayRecord{{AppId: 3, UsageDate: "2024-01-01", InputTokens: "10", OutputTokens: "20", Count: 1}}
				apps := []*repository.DevAppInfo{{Id: 3, Name: "app3", Uuid: "uuid3", Description: "desc3", Logo: "logo3", Website: "web3"}}
				file := &repository.File{ID: 1, UUID: "logo3"}

				mocks.DB.TopaiModelExt.On("GetByModelId", mock.Anything, "test-model-1").Return(modelExt, nil)
				mocks.DB.TopaiModel.On("GetByID", mock.Anything, uint(1)).Return(model, nil)
				mocks.DB.TopaiModelProvider.On("GetByID", mock.Anything, uint(1)).Return(provider, nil)
				mocks.DB.TopaiModelCost.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(cost, nil)
				mocks.DB.TopaiModel.On("GetByModelName", mock.Anything, "test-model-1").Return([]*repository.TopaiModel{model}, nil)
				mocks.DB.TopaiModelSeriesRelation.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(seriesRelation, nil)
				mocks.DB.TopaiModelSeries.On("GetByID", mock.Anything, uint(1)).Return(series, nil)
				mocks.DB.TopaiModelCategoryRelation.On("GetByTopaiModelId", mock.Anything, uint(1)).Return(categoryRelations, nil)
				mocks.DB.TopaiModelCategory.On("GetByIds", mock.Anything, []uint{2}).Return(categories, nil)
				mocks.DB.UserUsageDayRecord.On("GetByModelId", mock.Anything, "test-model-1").Return(usageRecords, nil)
				mocks.DB.DevAppInfo.On("GetByIds", mock.Anything, []uint{3}).Return(apps, nil)
				mocks.DB.File.On("GetByUUID", mock.Anything, "logo3").Return(file, nil)
				mocks.DB.TopaiModelExt.On("GetOnlineList", mock.Anything, -1000, 1000, "", "", []uint{1}, []uint{}, false, "").Return([]*repository.TopaiModelExt{modelExt}, nil)
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModel{model}, nil)
				mocks.DB.TopaiModelProvider.On("GetByIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModelProvider{provider}, nil)
				mocks.DB.TopaiModelCost.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModelCost{cost}, nil)
				mocks.DB.TopaiModelCategoryRelation.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModelCategoryRelation{}, nil)
				mocks.DB.TopaiModelCategory.On("GetByIds", mock.Anything, []uint{}).Return([]*repository.TopaiModelCategory{}, nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-模型不存在",
			modelId: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelExt.On("GetByModelId", mock.Anything, "non-existent").Return(nil, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSeriesRelation:   mocks.DB.TopaiModelSeriesRelation,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
				UserUsageDayRecord:         mocks.DB.UserUsageDayRecord,
				File:                       mocks.DB.File,
			}
			service := NewTopaiService(db)

			result, err := service.GetTopaiModelDetail(ctx, tt.modelId)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "test-model-1", result.ModelId)
				assert.Equal(t, "test-series", result.SeriesName)
				assert.Equal(t, "test-provider", result.ProviderName)
				assert.Equal(t, "1000", result.TotalTokens)
				assert.Equal(t, "cat2", result.CategoryList[0].CategoryName)
				assert.Equal(t, "app3", result.AppUsed[0].AppName)
			}
		})
	}
}

// TestTopaiService_GetCategoryList 测试获取分类列表
func TestTopaiService_GetCategoryList(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取分类列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				categories := []*repository.TopaiModelCategory{
					{
						Id:   1,
						Name: "test-category",
					},
				}
				relations := []*repository.TopaiModelCategoryRelation{
					{CategoryId: 1},
				}
				mocks.DB.TopaiModelCategory.On("GetAll", mock.Anything).Return(categories, nil)
				mocks.DB.TopaiModelCategoryRelation.On("GetAll", mock.Anything).Return(relations, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelCategory.On("GetAll", mock.Anything).Return(nil, assert.AnError)
				mocks.DB.TopaiModelCategoryRelation.On("GetAll", mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetUsedCategoryList(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestTopaiService_GetSeriesList 测试获取系列列表
func TestTopaiService_GetSeriesList(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取系列列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				series := []*repository.TopaiModelSeries{
					{
						Id:   1,
						Name: "test-series",
					},
				}
				mocks.DB.TopaiModelSeries.On("GetAll", mock.Anything).Return(series, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelSeries.On("GetAll", mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetSeriesList(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestTopaiService_GetProviderList 测试获取提供商列表
func TestTopaiService_GetProviderList(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取提供商列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				providers := []*repository.TopaiModelProvider{
					{
						Id:   1,
						Name: "test-provider",
					},
				}
				mocks.DB.TopaiModelProvider.On("GetAll", mock.Anything).Return(providers, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelProvider.On("GetAll", mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetProviderList(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestTopaiService_GetModelTypeList 测试获取模型类型列表
func TestTopaiService_GetModelTypeList(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取模型类型列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				// GetModelTypeList 返回硬编码的模型类型列表
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetModelTypeList(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 4) // 实际有4个预定义的模型类型
			}
		})
	}
}

// TestTopaiService_GetSupportParamList 测试获取支持参数列表
func TestTopaiService_GetSupportParamList(t *testing.T) {
	tests := []struct {
		name  string
		setup func(*testutil.MockDependencies)
	}{
		{
			name: "TC1-获取支持参数列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				supportParams := []*repository.TopaiModelSupportParam{
					{
						Id:           1,
						TopaiModelId: 1,
						ParamName:    "temperature",
					},
				}
				mocks.DB.TopaiModelSupportParam.On("GetAll", mock.Anything).Return(supportParams, nil)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetSupportParamList(ctx)

			assert.NoError(t, err)
			assert.NotNil(t, result)
		})
	}
}

// TestTopaiService_GetAppKeyList 测试获取应用密钥列表
func TestTopaiService_GetAppKeyList(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取应用密钥列表成功",
			setup: func(mocks *testutil.MockDependencies) {
				apps := []*repository.DevAppInfo{
					{
						Id:   1,
						Name: "test-app",
					},
				}
				keys := []*repository.DevAppKey{
					{
						Id:       1,
						DevAppId: 1,
						Key:      "test-key",
						Name:     "test-key-name",
					},
				}
				mocks.DB.DevAppInfo.On("GetAll", mock.Anything).Return(apps, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIds", mock.Anything, []uint{1}).Return(keys, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetAll", mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				TopaiModelExt:              mocks.DB.TopaiModelExt,
				TopaiModel:                 mocks.DB.TopaiModel,
				TopaiModelProvider:         mocks.DB.TopaiModelProvider,
				TopaiModelCategory:         mocks.DB.TopaiModelCategory,
				TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
				TopaiModelSeries:           mocks.DB.TopaiModelSeries,
				TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
				TopaiModelCost:             mocks.DB.TopaiModelCost,
				DevAppInfo:                 mocks.DB.DevAppInfo,
				DevAppKey:                  mocks.DB.DevAppKey,
			}
			service := NewTopaiService(db)

			result, err := service.GetAppKeyList(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestTopaiService_GetTopaiModelListByTokenSort 测试按token排序获取模型列表
func TestTopaiService_GetTopaiModelListByTokenSort(t *testing.T) {
	tests := []struct {
		name    string
		req     *GetTopaiModelListRequest
		setup   func(*testutil.MockDependencies)
		want    []*TopaiModelsInfoOP
		wantErr bool
	}{
		{
			name: "TC1-成功获取按token排序的模型列表",
			req: &GetTopaiModelListRequest{
				Page:     1,
				PageSize: 10,
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock TopaiModelCost.GetAll
				costs := []*repository.TopaiModelCost{
					{
						Id:           1,
						TopaiModelId: 1,
						TotalTokens:  "1000",
					},
					{
						Id:           2,
						TopaiModelId: 2,
						TotalTokens:  "2000",
					},
				}
				mocks.DB.TopaiModelCost.On("GetAll", mock.Anything, 0, 20, "length(total_tokens) desc,total_tokens desc").Return(costs, nil)

				// Mock TopaiModelExt.GetOnlineList
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						ModelId:      "model-1",
						TopaiModelId: 1,
						Status:       1,
						Weight:       100,
						Description:  "Model 1 description",
						Price:        "0",
					},
					{
						Id:           2,
						ModelId:      "model-2",
						TopaiModelId: 2,
						Status:       1,
						Weight:       200,
						Description:  "Model 2 description",
						Price:        "0.001",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineList", mock.Anything, 0, 20, "", "", []uint{1, 2}, []uint{}, false, "").Return(modelExts, nil)
			},
			want: []*TopaiModelsInfoOP{
				{
					ModelId:     "model-2",
					ModelName:   "model-2",
					Description: "Model 2 description",
					TotalTokens: "2000",
					IsFree:      false,
				},
				{
					ModelId:     "model-1",
					ModelName:   "model-1",
					Description: "Model 1 description",
					TotalTokens: "1000",
					IsFree:      true,
				},
			},
			wantErr: false,
		},
		{
			name: "TC2-获取模型扩展失败",
			req: &GetTopaiModelListRequest{
				Page:     1,
				PageSize: 10,
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelCost.On("GetAll", mock.Anything, 0, 20, "length(total_tokens) desc,total_tokens desc").Return(nil, assert.AnError)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				TopaiModelExt:  mocks.DB.TopaiModelExt,
				TopaiModelCost: mocks.DB.TopaiModelCost,
			}
			service := NewTopaiService(db)

			got, err := service.GetTopaiModelListByTokenSort(ctx, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// BenchmarkTopaiService_NewService 创建TopAI服务性能基准测试
func BenchmarkTopaiService_NewService(b *testing.B) {
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	db := &repository.DB{
		TopaiModelExt:              mocks.DB.TopaiModelExt,
		TopaiModel:                 mocks.DB.TopaiModel,
		TopaiModelProvider:         mocks.DB.TopaiModelProvider,
		TopaiModelCategory:         mocks.DB.TopaiModelCategory,
		TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
		TopaiModelSeries:           mocks.DB.TopaiModelSeries,
		TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
		TopaiModelCost:             mocks.DB.TopaiModelCost,
		DevAppInfo:                 mocks.DB.DevAppInfo,
		DevAppKey:                  mocks.DB.DevAppKey,
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewTopaiService(db)
		_ = service
	}
}

// BenchmarkTopaiService_GetTopaiModelList 获取TopAI模型列表性能基准测试
func BenchmarkTopaiService_GetTopaiModelList(b *testing.B) {
	ctx := context.Background()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	// Setup mocks
	modelExts := []*repository.TopaiModelExt{
		{
			Id:                   1,
			ModelId:              "test-model-1",
			TopaiModelId:         1,
			TopaiModelProviderId: 1,
			Status:               1,
		},
	}
	models := []*repository.TopaiModel{
		{
			Id:        1,
			ModelName: "test-model-1",
			ModelType: "llm",
		},
	}
	providers := []*repository.TopaiModelProvider{
		{
			Id:   1,
			Name: "test-provider",
		},
	}
	costs := []*repository.TopaiModelCost{
		{
			TopaiModelId: 1,
			ModelId:      "test-model-1",
		},
	}
	categoryRelations := []*repository.TopaiModelCategoryRelation{
		{
			TopaiModelId: 1,
			CategoryId:   1,
		},
	}

	mocks.DB.TopaiModelExt.On("GetOnlineList", mock.Anything, 0, 10, "id", "desc", mock.Anything, []uint{}, false, "").Return(modelExts, nil)
	mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(models, nil)
	mocks.DB.TopaiModelProvider.On("GetByIds", mock.Anything, []uint{1}).Return(providers, nil)
	mocks.DB.TopaiModelCost.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return(costs, nil)
	mocks.DB.TopaiModelCategoryRelation.On("GetByTopaiModelIds", mock.Anything, []uint{1}).Return(categoryRelations, nil)
	mocks.DB.TopaiModelCategory.On("GetByIds", mock.Anything, []uint{1}).Return([]*repository.TopaiModelCategory{{Id: 1, Name: "test-category"}}, nil)

	db := &repository.DB{
		TopaiModelExt:              mocks.DB.TopaiModelExt,
		TopaiModel:                 mocks.DB.TopaiModel,
		TopaiModelProvider:         mocks.DB.TopaiModelProvider,
		TopaiModelCategory:         mocks.DB.TopaiModelCategory,
		TopaiModelCategoryRelation: mocks.DB.TopaiModelCategoryRelation,
		TopaiModelSeries:           mocks.DB.TopaiModelSeries,
		TopaiModelSupportParam:     mocks.DB.TopaiModelSupportParam,
		TopaiModelCost:             mocks.DB.TopaiModelCost,
		DevAppInfo:                 mocks.DB.DevAppInfo,
		DevAppKey:                  mocks.DB.DevAppKey,
	}
	service := NewTopaiService(db)

	req := &GetTopaiModelListRequest{
		Page:      1,
		PageSize:  10,
		OrderBy:   "id",
		OrderType: "desc",
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetTopaiModelList(ctx, req)
	}
}

// TestTopaiService_SyncOnchainModel 测试同步链上模型
func TestTopaiService_SyncOnchainModel(t *testing.T) {
	tests := []struct {
		name            string
		chainUrl        string
		aiModelsAddress string
		setupMocks      func(*testutil.MockDependencies)
		wantErr         bool
	}{
		{
			name:            "TC1-同步链上模型成功",
			chainUrl:        "https://test-chain.com",
			aiModelsAddress: "0x1234567890abcdef",
			setupMocks: func(mocks *testutil.MockDependencies) {
				// 模拟链上数据 - 这里只是设置mock，实际数据在chain包中处理

				// 模拟数据库操作
				mocks.DB.TopaiModel.On("GetAll", mock.Anything).Return([]*repository.TopaiModel{}, nil)
				mocks.DB.TopaiModelExt.On("GetAll", mock.Anything).Return([]*repository.TopaiModelExt{}, nil)
				mocks.DB.TopaiModelProvider.On("GetAll", mock.Anything).Return([]*repository.TopaiModelProvider{
					{
						Id:            1,
						WalletAddress: "******************************************", // 使用正确的地址格式
					},
				}, nil)
				mocks.DB.TopaiModel.On("Create", mock.Anything, mock.AnythingOfType("*repository.TopaiModel")).Run(func(args mock.Arguments) {
					model := args.Get(1).(*repository.TopaiModel)
					model.Id = 1 // 设置ID，模拟数据库插入后的行为
				}).Return(nil).Once()
				mocks.DB.TopaiModelExt.On("Create", mock.Anything, mock.AnythingOfType("*repository.TopaiModelExt")).Return(nil).Once()
			},
			wantErr: false,
		},
		{
			name:            "TC2-获取链上数据失败",
			chainUrl:        "https://invalid-chain.com",
			aiModelsAddress: "0x1234567890abcdef",
			setupMocks: func(mocks *testutil.MockDependencies) {
				// 不设置任何mock，让chain.GetAiModelsList失败
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setupMocks != nil {
				tt.setupMocks(mocks)
			}

			db := &repository.DB{
				TopaiModel:         mocks.DB.TopaiModel,
				TopaiModelExt:      mocks.DB.TopaiModelExt,
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
				DevAppInfo:         mocks.DB.DevAppInfo,
				DevAppKey:          mocks.DB.DevAppKey,
			}
			mockUtilChainFunc := DefaultUtilChainFunc
			mockUtilChainFunc.GetAiModelsListFunc = func(ctx context.Context, chainUrl, aiModelsAddress string) ([]*chain.AIModelInfo, error) {
				if chainUrl == "https://test-chain.com" {
					return []*chain.AIModelInfo{
						{
							ModelId:       big.NewInt(1),
							ModelName:     "test-model",
							ModelVersion:  "v1.0",
							Uploader:      common.HexToAddress("0x1234567890abcdef"),
							InTokenPrice:  big.NewInt(1000),
							OutTokenPrice: big.NewInt(2000),
							ExtendInfoExtend: &chain.AIModelInfoExtend{
								Type: "llm",
							},
						},
					}, nil
				}
				return nil, errors.New("mock chain error")
			}
			s := &topaiService{
				db:            db,
				log:           logger.GetLogger("test"),
				utilChainFunc: mockUtilChainFunc,
			}

			err := s.SyncOnchainModel(context.Background(), tt.chainUrl, tt.aiModelsAddress)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestTopaiService_AutoGenerateAppKey 测试自动生成应用密钥
func TestTopaiService_AutoGenerateAppKey(t *testing.T) {
	tests := []struct {
		name       string
		setupMocks func(*testutil.MockDependencies)
		wantErr    bool
	}{
		{
			name: "TC1-自动生成应用密钥成功",
			setupMocks: func(mocks *testutil.MockDependencies) {
				devApps := []*repository.DevAppInfo{
					{Id: 1, Name: "test-app-1"},
					{Id: 2, Name: "test-app-2"},
				}
				mocks.DB.DevAppInfo.On("GetAll", mock.Anything).Return(devApps, nil)

				// 模拟GenerateAppKey的成功调用
				for _, app := range devApps {
					mocks.DB.DevAppInfo.On("GetByID", mock.Anything, app.Id).Return(app, nil)
					mocks.DB.DevAppKey.On("GetByDevAppId", mock.Anything, app.Id).Return([]*repository.DevAppKey{}, nil)
					mocks.DB.DevAppKey.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppKey")).Return(nil)
				}
			},
			wantErr: false,
		},
		{
			name: "TC2-获取应用列表失败",
			setupMocks: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetAll", mock.Anything).Return(nil, errors.New("database error"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setupMocks != nil {
				tt.setupMocks(mocks)
			}

			db := &repository.DB{
				TopaiModel:         mocks.DB.TopaiModel,
				TopaiModelExt:      mocks.DB.TopaiModelExt,
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
				DevAppInfo:         mocks.DB.DevAppInfo,
				DevAppKey:          mocks.DB.DevAppKey,
			}
			s := &topaiService{
				db:  db,
				log: logger.GetLogger("test"),
			}

			err := s.AutoGenerateAppKey(context.Background())

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestTopaiService_GenerateAppKey 测试生成应用密钥
func TestTopaiService_GenerateAppKey(t *testing.T) {
	tests := []struct {
		name       string
		appId      uint
		keyName    string
		setupMocks func(*testutil.MockDependencies)
		wantErr    bool
	}{
		{
			name:    "TC1-生成应用密钥成功",
			appId:   1,
			keyName: "testnet",
			setupMocks: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{Id: 1, Name: "test-app"}
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppId", mock.Anything, uint(1)).Return([]*repository.DevAppKey{}, nil)
				mocks.DB.DevAppKey.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppKey")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-应用不存在",
			appId:   999,
			keyName: "testnet",
			setupMocks: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(999)).Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name:    "TC3-密钥已存在",
			appId:   1,
			keyName: "existing-key",
			setupMocks: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{Id: 1, Name: "test-app"}
				existingKeys := []*repository.DevAppKey{
					{Id: 1, DevAppId: 1, Name: "existing-key", Key: "existing-uuid"},
				}
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppId", mock.Anything, uint(1)).Return(existingKeys, nil)
			},
			wantErr: false, // 密钥已存在时返回nil，不报错
		},
		{
			name:    "TC4-获取应用失败",
			appId:   1,
			keyName: "testnet",
			setupMocks: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(nil, errors.New("database error"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setupMocks != nil {
				tt.setupMocks(mocks)
			}

			db := &repository.DB{
				TopaiModel:         mocks.DB.TopaiModel,
				TopaiModelExt:      mocks.DB.TopaiModelExt,
				TopaiModelProvider: mocks.DB.TopaiModelProvider,
				DevAppInfo:         mocks.DB.DevAppInfo,
				DevAppKey:          mocks.DB.DevAppKey,
			}
			s := &topaiService{
				db:  db,
				log: logger.GetLogger("test"),
			}

			err := s.GenerateAppKey(context.Background(), tt.appId, tt.keyName)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}
