// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserTokenRepository is an autogenerated mock type for the UserTokenRepository type
type UserTokenRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, token
func (_m *UserTokenRepository) Create(ctx context.Context, token *repository.UserToken) error {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserToken) error); ok {
		r0 = rf(ctx, token)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, id
func (_m *UserTokenRepository) Delete(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteByUserID provides a mock function with given fields: ctx, userID
func (_m *UserTokenRepository) DeleteByUserID(ctx context.Context, userID uint) error {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByUserID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByToken provides a mock function with given fields: ctx, token
func (_m *UserTokenRepository) GetByToken(ctx context.Context, token string) (*repository.UserToken, error) {
	ret := _m.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for GetByToken")
	}

	var r0 *repository.UserToken
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.UserToken, error)); ok {
		return rf(ctx, token)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.UserToken); ok {
		r0 = rf(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserToken)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserTokenRepository creates a new instance of UserTokenRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserTokenRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserTokenRepository {
	mock := &UserTokenRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
