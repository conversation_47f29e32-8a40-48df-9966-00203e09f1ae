// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserShadowWalletBalanceSnapshotRepository is an autogenerated mock type for the UserShadowWalletBalanceSnapshotRepository type
type UserShadowWalletBalanceSnapshotRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, snapshot
func (_m *UserShadowWalletBalanceSnapshotRepository) Create(ctx context.Context, snapshot *repository.UserShadowWalletBalanceSnapshot) error {
	ret := _m.Called(ctx, snapshot)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserShadowWalletBalanceSnapshot) error); ok {
		r0 = rf(ctx, snapshot)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserShadowWalletBalanceSnapshotRepository creates a new instance of UserShadowWalletBalanceSnapshotRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserShadowWalletBalanceSnapshotRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserShadowWalletBalanceSnapshotRepository {
	mock := &UserShadowWalletBalanceSnapshotRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
