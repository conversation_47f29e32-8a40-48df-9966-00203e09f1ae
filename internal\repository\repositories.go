package repository

import (
	"context"
	"time"
)

// DB 调用入口
type DB struct {
	// 用户基础信息
	User        UserRepository
	UserToken   UserTokenRepository
	UserSetting UserSettingRepository
	// 用户余额
	UserBalance                   UserBalanceRepository
	UserBalanceRecord             UserBalanceRecordRepository
	UserBalanceSnapshot           UserBalanceSnapshotRepository
	UserRechargeRecord            UserRechargeRecordRepository
	UserShadowWallet              UserShadowWalletRepository
	UserShadowWalletBalance       UserShadowWalletBalanceRepository
	UserShadowWalletBalanceRecord UserShadowWalletBalanceRecordRepository
	WalletTransferRecord          WalletTransferRecordRepository
	WalletReportCostRecord        WalletReportCostRecordRepository
	WalletDepositRecord           WalletDepositRecordRepository
	// 对话相关
	Conversation        ConversationRepository
	ConversationMessage ConversationMessageRepository
	UserChatASRRecord   UserChatASRRecordRepository
	UserChatTTSRecord   UserChatTTSRecordRepository
	UserChatTTIRecord   UserChatTTIRecordRepository
	// 文件
	File FileRepository
	// 系统配置
	SystemConfig SystemConfigRepository
	// 模型相关
	LLMModelConfig             LLMModelConfigRepository
	RemoterModelInfo           RemoterModelInfoRepository
	TTSModelVoice              TTSModelVoiceRepository
	TopaiModel                 TopaiModelRepository
	TopaiModelProvider         TopaiModelProviderRepository
	TopaiModelCategory         TopaiModelCategoryRepository
	TopaiModelCategoryRelation TopaiModelCategoryRelationRepository
	TopaiModelSeries           TopaiModelSeriesRepository
	TopaiModelSeriesRelation   TopaiModelSeriesRelationRepository
	TopaiModelExt              TopaiModelExtRepository
	TopaiModelSupportParam     TopaiModelSupportParamRepository
	TopaiModelCost             TopaiModelCostRepository
	UserUsageDayRecord         UserUsageDayRecordRepository
	// 用户应用
	DevAppInfo      DevAppInfoRepository
	DevAppKey       DevAppKeyRepository
	UserChatRequest UserChatRequestRepository
	UserChatUsage   UserChatUsageRepository
	// 模型提供者
	ProviderToken ProviderTokenRepository
	// 其他
	Close     func() error
	RunWithTx func(ctx context.Context, fn func(ctx context.Context, db *DB) error) error
}

type UserRole string

const (
	UserRoleUser    UserRole = "user"
	UserRoleAdmin   UserRole = "admin"
	UserRolePending UserRole = "pending"
)

func (r UserRole) String() string {
	return string(r)
}

func (r UserRole) Check() bool {
	return r == UserRoleUser || r == UserRoleAdmin
}

// User 用户模型
type User struct {
	ID           uint       `json:"id"`
	IdentityID   string     `json:"identity_id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	Role         UserRole   `json:"role"`
	PasswordHash string     `json:"-"`
	IsDeleted    uint8      `json:"-"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// UserRepository 用户仓库接口
type UserRepository interface {
	// 根据ID获取用户
	GetByID(ctx context.Context, id uint) (*User, error)
	// 根据IdentityID获取用户
	GetByIdentityID(ctx context.Context, identityID string) (*User, error)
	// 根据邮箱获取用户
	GetByEmail(ctx context.Context, email string) (*User, error)
	// 创建用户
	Create(ctx context.Context, user *User) error
	// 更新用户
	Update(ctx context.Context, user *User) error
	// 删除用户
	Delete(ctx context.Context, id uint) error
	// 获取用户列表
	List(ctx context.Context, offset, limit int, order, direction string) ([]*User, error)
	// 获取用户总数
	Count(ctx context.Context) (int64, error)
	// 获取所有用户
	GetAll(ctx context.Context) ([]*User, error)
}

type UserToken struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	// 设置为int，为了方便跟token形成为一索引，为删除 值为0，删除的时候为删除时的时间戳
	DeletedAt uint       `json:"deleted_at"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserToken) TableName() string {
	return "user_tokens"
}

type UserTokenRepository interface {
	Create(ctx context.Context, token *UserToken) error
	GetByToken(ctx context.Context, token string) (*UserToken, error)
	Delete(ctx context.Context, id uint) error
	DeleteByUserID(ctx context.Context, userID uint) error
}

type UserBalance struct {
	ID          uint       `json:"id"`
	UserID      uint       `json:"user_id"`
	Balance     string     `json:"balance"`
	Accumulated string     `json:"accumulated"`
	Currency    string     `json:"currency"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
}

func (UserBalance) TableName() string {
	return "user_balances"
}

var (
	UserBalanceCurrencyUSDT         = "USDT"         // USDT余额账户
	UserBalanceCurrencyUSDT_DEPOSIT = "USDT_DEPOSIT" // USDT质押账户 (deposit合约里)
	UserBalanceCurrencyTOP          = "TOP"          // TOP余额账户
)

type UserBalanceRepository interface {
	Create(ctx context.Context, balance *UserBalance) error
	GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*UserBalance, error)
	UpdateBalanceByID(ctx context.Context, id uint, balance string, accumulated string) error
}

var (
	UserBalanceRecordTypeAdminGift = "admin_gift"
	UserBalanceRecordTypeUserGift  = "user_gift"
	UserBalanceRecordTypeChat      = "chat"
	UserBalanceRecordTypeRecharge  = "recharge"
)

type UserBalanceRecord struct {
	ID              uint       `json:"id"`
	UserID          uint       `json:"user_id"`
	UserBalanceID   uint       `json:"user_balance_id"`
	UserChatUsageID uint       `json:"user_chat_usage_id"`
	GiftUserID      uint       `json:"gift_user_id"`
	Amount          string     `json:"amount"`
	Type            string     `json:"type"`
	Description     string     `json:"description"`
	IsIn            uint8      `json:"is_in"`
	Currency        string     `json:"currency"`
	CurrentBalance  string     `json:"current_balance"`
	CreatedAt       *time.Time `json:"created_at"`
}

func (UserBalanceRecord) TableName() string {
	return "user_balance_records"
}

type UserBalanceRecordRepository interface {
	Create(ctx context.Context, record *UserBalanceRecord) error
	BatchCreate(ctx context.Context, records []*UserBalanceRecord) error
	CountByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (int64, error)
	GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string, offset, pageSize int, orderBy, orderDirection string) ([]*UserBalanceRecord, error)
}

type UserRechargeRecord struct {
	ID                              uint       `json:"id"`
	UserID                          uint       `json:"user_id"`
	UserBalanceRecordID             uint       `json:"user_balance_record_id"`
	UserShadowWalletBalanceRecordID uint       `json:"user_shadow_wallet_balance_record_id"`
	TxHash                          string     `json:"tx_hash"`
	BlockNumber                     uint64     `json:"block_number"`
	Amount                          string     `json:"amount"`
	Currency                        string     `json:"currency"`
	RechargeType                    string     `json:"recharge_type"` // 新增：充值类型
	Status                          int8       `json:"status"`        // 0 待确认, 1 已确认, -1 失败
	Description                     string     `json:"description"`
	CreatedAt                       *time.Time `json:"created_at"`
}

func (UserRechargeRecord) TableName() string {
	return "user_recharge_records"
}

var (
	UserRechargeRecordStatusUnconfirmed int8 = 0  // 已上链，待确认
	UserRechargeRecordStatusConfirmed   int8 = 1  // 执行成功
	UserRechargeRecordStatusFailed      int8 = -1 // 执行失败

	// 充值类型常量
	UserRechargeRecordTypeOnchain   string = "onchain"    // 链上充值
	UserRechargeRecordTypeAdminGift string = "admin_gift" // 管理员赠送
	UserRechargeRecordTypeUserGift  string = "user_gift"  // 用户转赠
)

type UserRechargeRecordRepository interface {
	Create(ctx context.Context, record *UserRechargeRecord) error
	GetUnconfirmed(ctx context.Context) ([]*UserRechargeRecord, error)
	UpdateConfirmedByID(ctx context.Context, id uint, userBalanceRecordId uint) error
	UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error
}

type UserBalanceSnapshot struct {
	ID            uint       `json:"id"`
	UserID        uint       `json:"user_id"`
	UserBalanceID uint       `json:"user_balance_id"`
	Balance       string     `json:"balance"`
	Currency      string     `json:"currency"`
	SnapshotDate  time.Time  `json:"snapshot_date"`
	CreatedAt     *time.Time `json:"created_at"`
}

func (UserBalanceSnapshot) TableName() string {
	return "user_balance_snapshots"
}

type UserBalanceSnapshotRepository interface {
	Create(ctx context.Context, snapshot *UserBalanceSnapshot) error
}

type UserShadowWallet struct {
	ID                  uint       `json:"id"`
	UserID              uint       `json:"user_id"`
	PublicAddress       string     `json:"public_address"`
	EncryptedPrivateKey string     `json:"encrypted_private_key"`
	KeySalt             string     `json:"key_salt"`
	KeyVersion          string     `json:"key_version"`
	Algorithm           string     `json:"algorithm"`
	CreatedAt           *time.Time `json:"created_at"`
	UpdatedAt           *time.Time `json:"updated_at"`
}

func (UserShadowWallet) TableName() string {
	return "user_shadow_wallets"
}

type UserShadowWalletRepository interface {
	Create(ctx context.Context, wallet *UserShadowWallet) error
	GetByUserID(ctx context.Context, userID uint) (*UserShadowWallet, error)
	GetByPublicAddress(ctx context.Context, publicAddress string) (*UserShadowWallet, error)
}

type UserShadowWalletBalance struct {
	ID              uint       `json:"id"`
	UserID          uint       `json:"user_id"`
	PublicAddress   string     `json:"public_address"`
	Balance         string     `json:"balance"`
	Currency        string     `json:"currency"`
	Accumulated     string     `json:"accumulated"`
	LastBlockNumber uint64     `json:"last_block_number"`
	CreatedAt       *time.Time `json:"created_at"`
	UpdatedAt       *time.Time `json:"updated_at"`
}

func (UserShadowWalletBalance) TableName() string {
	return "user_shadow_wallet_balances"
}

type UserShadowWalletBalanceRepository interface {
	Create(ctx context.Context, balance *UserShadowWalletBalance) error
	GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*UserShadowWalletBalance, error)
	UpdateBalanceByUserIDAndCurrency(ctx context.Context, userID uint, currency string, balance, accumulated string, lastBlockNumber uint64) error
}

var (
	UserShadowWalletBalanceRecordStatusUnconfirmed int8   = 0  // 已上链，待确认
	UserShadowWalletBalanceRecordStatusConfirmed   int8   = 1  // 执行成功
	UserShadowWalletBalanceRecordStatusFailed      int8   = -1 // 执行失败
	UserShadowWalletBalanceRecordTypeChatCost      string = "chat_cost"
	UserShadowWalletBalanceRecordTypeDeposit       string = "deposit"
	UserShadowWalletBalanceRecordTypeGift          string = "gift"
	UserShadowWalletBalanceRecordTypeRecharge      string = "recharge"
	UserShadowWalletBalanceRecordTypeUnknown       string = "unknown"
)

type UserShadowWalletBalanceRecord struct {
	ID             uint       `json:"id"`
	UserID         uint       `json:"user_id"`
	PublicAddress  string     `json:"public_address"`
	TxHash         string     `json:"tx_hash"`
	BlockNumber    uint64     `json:"block_number"`
	Amount         string     `json:"amount"`
	CurrentBalance string     `json:"current_balance"`
	IsIn           uint8      `json:"is_in"`
	Currency       string     `json:"currency"`
	Type           string     `json:"type"`
	Description    string     `json:"description"`
	Status         int8       `json:"status"` // 0 待确认, 1 已确认, -1 失败
	CreatedAt      *time.Time `json:"created_at"`
}

func (UserShadowWalletBalanceRecord) TableName() string {
	return "user_shadow_wallet_balance_records"
}

type UserShadowWalletBalanceRecordRepository interface {
	Create(ctx context.Context, record *UserShadowWalletBalanceRecord) error
	BatchCreate(ctx context.Context, records []*UserShadowWalletBalanceRecord) error
	GetUnconfirmed(ctx context.Context) ([]*UserShadowWalletBalanceRecord, error)
	UpdateConfirmedByID(ctx context.Context, id uint) error
	UpdateConfirmedAmountByID(ctx context.Context, id uint, amount string) error
	UpdateFailedByID(ctx context.Context, id uint) error
	GetLastByCurrencys(ctx context.Context, currencies []string) (*UserShadowWalletBalanceRecord, error)
}

type UserShadowWalletBalanceSnapshot struct {
	ID            uint       `json:"id"`
	UserID        uint       `json:"user_id"`
	PublicAddress string     `json:"public_address"`
	Balance       string     `json:"balance"`
	Currency      string     `json:"currency"`
	SnapshotDate  time.Time  `json:"snapshot_date"`
	CreatedAt     *time.Time `json:"created_at"`
}

func (UserShadowWalletBalanceSnapshot) TableName() string {
	return "user_shadow_wallet_balance_snapshots"
}

type UserShadowWalletBalanceSnapshotRepository interface {
	Create(ctx context.Context, snapshot *UserShadowWalletBalanceSnapshot) error
}

var (
	WalletTransferRecordStatusUnconfirmed int8 = 0  // 已上链，待确认
	WalletTransferRecordStatusConfirmed   int8 = 1  // 执行成功
	WalletTransferRecordStatusFailed      int8 = -1 // 执行失败
)

type WalletTransferRecord struct {
	ID                              uint       `json:"id"`
	UserID                          uint       `json:"user_id"`
	FromAddress                     string     `json:"from_address"`
	ToAddress                       string     `json:"to_address"`
	Amount                          string     `json:"amount"`
	Currency                        string     `json:"currency"`
	TxHash                          string     `json:"tx_hash"`
	BlockNumber                     uint64     `json:"block_number"`
	UserShadowWalletBalanceRecordID uint       `json:"user_shadow_wallet_balance_record_id"`
	Status                          int8       `json:"status"` // 0 待确认, 1 已确认, -1 失败
	Description                     string     `json:"description"`
	CreatedAt                       *time.Time `json:"created_at"`
}

func (WalletTransferRecord) TableName() string {
	return "wallet_transfer_records"
}

type WalletTransferRecordRepository interface {
	Create(ctx context.Context, record *WalletTransferRecord) error
	GetUnconfirmed(ctx context.Context) ([]*WalletTransferRecord, error)
	UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error
	UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error
	GetConfirmed(ctx context.Context) ([]*WalletTransferRecord, error)
	GetByTxHash(ctx context.Context, txHash string) (*WalletTransferRecord, error)
}

var (
	WalletReportCostRecordStatusUnconfirmed int8 = 0  // 已上链，待确认
	WalletReportCostRecordStatusConfirmed   int8 = 1  // 执行成功
	WalletReportCostRecordStatusFailed      int8 = -1 // 执行失败
)

type WalletReportCostRecord struct {
	ID                              uint       `json:"id"`
	UserID                          uint       `json:"user_id"`
	PublicAddress                   string     `json:"public_address"`
	UserShadowWalletBalanceRecordID uint       `json:"user_shadow_wallet_balance_record_id"`
	UserChatUsageIDs                string     `json:"user_chat_usage_ids"`
	TxHash                          string     `json:"tx_hash"`
	BlockNumber                     uint64     `json:"block_number"`
	InputTokens                     string     `json:"input_tokens"`
	OutputTokens                    string     `json:"output_tokens"`
	Status                          int8       `json:"status"` // 0 待确认, 1 已确认, -1 失败
	CreatedAt                       *time.Time `json:"created_at"`
}

func (WalletReportCostRecord) TableName() string {
	return "wallet_report_cost_records"
}

type WalletReportCostRecordRepository interface {
	Create(ctx context.Context, record *WalletReportCostRecord) error
	GetByTxHash(ctx context.Context, txHash string) (*WalletReportCostRecord, error)
	GetUnconfirmed(ctx context.Context) ([]*WalletReportCostRecord, error)
	UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error
	UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error
}

var (
	WalletDepositRecordStatusUnconfirmed int8 = 0  // 已上链，待确认
	WalletDepositRecordStatusConfirmed   int8 = 1  // 执行成功
	WalletDepositRecordStatusFailed      int8 = -1 // 执行失败
)

type WalletDepositRecord struct {
	ID                              uint       `json:"id"`
	UserID                          uint       `json:"user_id"`
	PublicAddress                   string     `json:"public_address"`
	UserShadowWalletBalanceRecordID uint       `json:"user_shadow_wallet_balance_record_id"`
	TxHash                          string     `json:"tx_hash"`
	BlockNumber                     uint64     `json:"block_number"`
	Amount                          string     `json:"amount"`
	Status                          int8       `json:"status"` // 0 待确认, 1 已确认, -1 失败
	CreatedAt                       *time.Time `json:"created_at"`
}

func (WalletDepositRecord) TableName() string {
	return "wallet_deposit_records"
}

type WalletDepositRecordRepository interface {
	Create(ctx context.Context, record *WalletDepositRecord) error
	GetUnconfirmed(ctx context.Context) ([]*WalletDepositRecord, error)
	UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error
	UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error
	GetByTxHash(ctx context.Context, txHash string) (*WalletDepositRecord, error)
}

// Conversation 对话模型
type Conversation struct {
	ID             uint       `json:"id"`
	UserID         uint       `json:"user_id"`
	IdentityID     string     `json:"identity_id"`
	UUID           string     `json:"uuid"`
	Title          string     `json:"title"`
	ModelID        string     `json:"model_id"`
	System         string     `json:"system"`
	CurrentMsgUUID string     `json:"current_msg_uuid"`
	IsDeleted      uint8      `json:"-"`
	CreatedAt      *time.Time `json:"created_at"`
	UpdatedAt      *time.Time `json:"updated_at"`
}

func (Conversation) TableName() string {
	return "conversations"
}

// ConversationRepository 对话仓库接口
type ConversationRepository interface {
	// 获取用户的所有对话
	FindByUserIDOrderByUpdatedAtDesc(ctx context.Context, userID uint, limit int, offset int) ([]*Conversation, error)
	// 根据ID获取对话
	GetByID(ctx context.Context, id uint) (*Conversation, error)
	// 根据UUID获取对话
	GetByUUID(ctx context.Context, uuid string) (*Conversation, error)
	// 创建对话
	Create(ctx context.Context, conversation *Conversation) error
	// 删除对话
	Delete(ctx context.Context, id uint) error
	// 根据UUID更新标题
	UpdateTitleByUUID(ctx context.Context, uuid string, title string) error
	// 根据UUID更新当前消息UUID
	UpdateCurrentMsgUUIDByUUID(ctx context.Context, uuid string, currentMsgUUID string) error
}

// Message 消息模型
type ConversationMessage struct {
	ID               uint       `json:"id"`
	UUID             string     `json:"uuid"`
	ConversationID   uint       `json:"conversation_id"`
	ConversationUUID string     `json:"conversation_uuid"`
	Role             string     `json:"role"` // user 或 assistant
	Content          string     `json:"content"`
	ParentID         uint       `json:"parent_id"`
	ParentUUID       string     `json:"parent_uuid"`
	Models           string     `json:"models"`
	FileUUIDs        string     `json:"file_uuids"` // 文件UUID列表，用英文逗号分开
	IsDone           uint8      `json:"is_done"`
	CreatedAt        *time.Time `json:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at"`
}

// ConversationMessageRepository 对话消息仓库接口
type ConversationMessageRepository interface {
	// 根据ID获取消息
	GetByID(ctx context.Context, id uint) (*ConversationMessage, error)
	// 获取对话的所有消息
	GetByConversationID(ctx context.Context, conversationID uint) ([]*ConversationMessage, error)
	// 根据ConversationID和UUID获取消息
	GetByConversationIDAndUUID(ctx context.Context, conversationID uint, uuid string) (*ConversationMessage, error)
	// 根据ID更新消息内容和完成状态
	UpdateContentAndDoneById(ctx context.Context, id uint, content, fileUUIDs string) error
	// 创建消息
	Create(ctx context.Context, message *ConversationMessage) error
	// 更新消息
	Update(ctx context.Context, message *ConversationMessage) error
}

type UserChatRequest struct {
	ID                      uint       `json:"id"`
	UserID                  uint       `json:"user_id"`
	IP                      string     `json:"ip"`
	ModelType               string     `json:"model_type"`
	AssociatedUUID          string     `json:"associated_uuid"` // 根据modelType关联的UUID，llm: conversation_uuid，tts: tti_uuid, asr: conversations.uuid, asr: user_chat_asr_records.uuid, tts: user_chat_tts_records.uuid, tti: user_chat_tti_records.uuid
	ConversationMessageUUID string     `json:"conversation_message_uuid"`
	ModelID                 string     `json:"model_id"`
	CreatedAt               *time.Time `json:"created_at"`
}

func (UserChatRequest) TableName() string {
	return "user_chat_requests"
}

type UserChatRequestRepository interface {
	Create(ctx context.Context, request *UserChatRequest) error
	GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*UserChatRequest, error)
}

type UserChatASRRecord struct {
	ID              uint
	UUID            string
	UserID          uint
	UserChatUsageID uint
	ModelID         string
	FileUUIDs       string
	Content         string
	CreatedAt       *time.Time
}

func (UserChatASRRecord) TableName() string {
	return "user_chat_asr_records"
}

type UserChatASRRecordRepository interface {
	Create(ctx context.Context, record *UserChatASRRecord) error
}

type UserChatTTSRecord struct {
	ID              uint
	UUID            string
	UserID          uint
	UserChatUsageID uint
	ModelID         string
	Voice           string
	Format          string
	Speed           float64
	Content         string
	FileUUIDs       string
	CreatedAt       *time.Time
}

func (UserChatTTSRecord) TableName() string {
	return "user_chat_tts_records"
}

type UserChatTTSRecordRepository interface {
	Create(ctx context.Context, record *UserChatTTSRecord) error
}

type UserChatTTIRecord struct {
	ID              uint
	UUID            string
	UserID          uint
	UserChatUsageID uint
	ModelID         string
	Size            string
	Format          string
	Content         string
	FileUUIDs       string
	IsShared        uint8
	CreatedAt       *time.Time
}

func (UserChatTTIRecord) TableName() string {
	return "user_chat_tti_records"
}

type UserChatTTIRecordRepository interface {
	Create(ctx context.Context, record *UserChatTTIRecord) error
	GetByUUID(ctx context.Context, uuid string) (*UserChatTTIRecord, error)
	UpdateIsSharedByUUID(ctx context.Context, uuid string, isShared uint8) error
	GetSharedByUserID(ctx context.Context, userID uint, limit int, offset int, order string) ([]*UserChatTTIRecord, error)
	GetSharedCountByUserID(ctx context.Context, userID uint) (int64, error)
	GetSharedCount(ctx context.Context) (int64, error)
	GetShared(ctx context.Context, limit int, offset int, order string) ([]*UserChatTTIRecord, error)
}

type UserChatUsage struct {
	ID                      uint
	UUID                    string
	UserID                  uint
	AppKey                  string
	ModelID                 string
	ModelType               string
	ConversationUUID        string
	ConversationMessageUUID string
	FileUUID                string
	InputTokens             string
	OutputTokens            string
	InputPrice              string
	OutputPrice             string
	InputCost               string
	OutputCost              string
	TotalCost               string
	UsedType                string
	Platform                string
	PlatformId              string
	UserReportCostRecordID  uint `json:"user_report_cost_record_id"`
	Status                  uint8
	CreatedAt               *time.Time
	UpdatedAt               *time.Time
}

func (UserChatUsage) TableName() string {
	return "user_chat_usages"
}

var (
	UserChatUsageStatusUnconfirmed = 0 // 产生消费记录，但是未确认消耗
	UserChatUsageStatusConfirmed   = 1 // 确认消耗数量，等待从记账系统中结算
	UserChatUsageStatusSettled     = 2 // 已从记账系统中结算，等待链上结算
	UserChatUsageStatusCompleted   = 3 // 已提交链上消息，具体是否成功由链上结算系统确认
)

type UserChatUsageRepository interface {
	Create(ctx context.Context, usage *UserChatUsage) error
	GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*UserChatUsage, error)
	Update(ctx context.Context, usage *UserChatUsage) error
	GetByID(ctx context.Context, id uint) (*UserChatUsage, error)
	UpdateSettledByID(ctx context.Context, id uint) error
	UpdateSettledByIDs(ctx context.Context, ids []uint) error
	GetUnconfirmed(ctx context.Context) ([]*UserChatUsage, error)
	GetConfirmed(ctx context.Context) ([]*UserChatUsage, error)
	GetSettled(ctx context.Context) ([]*UserChatUsage, error)
	UpdateCompletedByIDs(ctx context.Context, ids []uint, userReportCostRecordID uint) error
	GetByUUID(ctx context.Context, uuid string) (*UserChatUsage, error)
}

// ModelConfig 模型配置
type LLMModelConfig struct {
	ID           uint       `json:"id"`
	ModelID      string     `json:"model_id"`
	ModelVersion string     `json:"model_version"`
	BaseModelID  string     `json:"base_model_id"`
	Name         string     `json:"name"`
	IsActive     bool       `json:"is_active"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
}

func (LLMModelConfig) TableName() string {
	return "llm_model_configs"
}

// ModelConfigRepository 模型配置仓库接口
type LLMModelConfigRepository interface {
	// 获取所有模型
	GetAll(ctx context.Context) ([]*LLMModelConfig, error)
	// 根据ID获取模型
	GetByID(ctx context.Context, id uint) (*LLMModelConfig, error)
	// 根据ModelID获取模型
	GetByModelID(ctx context.Context, modelID string) (*LLMModelConfig, error)
	// 创建模型
	Create(ctx context.Context, model *LLMModelConfig) error
	// 更新模型
	Update(ctx context.Context, model *LLMModelConfig) error
}

// File 文件模型
type File struct {
	ID           uint       `json:"id"`
	UUID         string     `json:"uuid"`
	UserID       uint       `json:"user_id"`
	Name         string     `json:"name"`
	OriginalName string     `json:"original_name"`
	Path         string     `json:"path"`
	Size         int64      `json:"size"`
	MimeType     string     `json:"mime_type"`
	Type         string     `json:"type"`
	Content      string     `json:"content"`
	IsPublic     uint8      `json:"is_public"`
	IsDeleted    uint8      `json:"-"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
}

// FileRepository 文件仓库接口
type FileRepository interface {
	// 创建文件
	Create(ctx context.Context, file *File) error
	// 根据ID获取文件
	GetByID(ctx context.Context, id uint) (*File, error)
	// 根据UUID获取文件
	GetByUUID(ctx context.Context, uuid string) (*File, error)
	// 更新文件内容
	UpdateContentById(ctx context.Context, id uint, content string) error
	// 删除文件
	Delete(ctx context.Context, id uint) error
	// 根据UserID和UUIDs获取文件
	FindByUserIDAndUUIDs(ctx context.Context, userID uint, uuids []string) ([]*File, error)
}

// UserSetting 用户设置模型
type UserSetting struct {
	ID        uint       `json:"id"`
	UserID    uint       `json:"user_id"`
	Key       string     `json:"key"`
	Value     string     `json:"value"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserSetting) TableName() string {
	return "user_settings"
}

// UserSettingRepository 用户设置仓库接口
type UserSettingRepository interface {
	// 获取用户设置
	GetByUserIDAndKey(ctx context.Context, userID uint, key string) (*UserSetting, error)
	// 获取用户的所有设置
	FindByUserID(ctx context.Context, userID uint) ([]*UserSetting, error)
	// 创建或更新用户设置
	Create(ctx context.Context, setting *UserSetting) error
	// 更新用户设置
	Update(ctx context.Context, setting *UserSetting) error
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID        uint       `json:"id"`
	Category  string     `json:"category"`
	Key       string     `json:"key"`
	Value     string     `json:"value"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// SystemConfigRepository 系统配置仓库接口
type SystemConfigRepository interface {
	// 获取配置
	Get(ctx context.Context, category, key string) (*SystemConfig, error)
	// 获取分类的所有配置
	GetByCategory(ctx context.Context, category string) ([]*SystemConfig, error)
	// 获取分类和key的配置
	GetByCategoryAndKey(ctx context.Context, category, key string) (*SystemConfig, error)
	// 通过category和key更新配置
	UpdateByCategoryAndKey(ctx context.Context, category, key, value string) error
	// 创建配置
	Create(ctx context.Context, config *SystemConfig) error
}

type RemoterModelProvider string

func (M RemoterModelProvider) String() string {
	return string(M)
}

// RemoterModelMsgType 模型支持的消息类型
type RemoterModelMsgType string

func (M RemoterModelMsgType) String() string {
	return string(M)
}

// ModelType 模型支持的类型
type ModelType string

func (M ModelType) String() string {
	return string(M)
}

// RemoterModelType 模型支持的类型
type RemoterModelType string

func (M RemoterModelType) String() string {
	return string(M)
}

var (
	RemoterModelProviderTopAi       RemoterModelProvider = "topai"
	RemoterModelProviderSiliconflow RemoterModelProvider = "siliconflow"
	RemoterModelProviderPerplexity  RemoterModelProvider = "perplexity"
	RemoterModelProviderOpenRouter  RemoterModelProvider = "openrouter"

	RemoterModelTypeLLM RemoterModelType = "llm"
	RemoterModelTypeTTS RemoterModelType = "tts"
	RemoterModelTypeASR RemoterModelType = "asr"
	RemoterModelTypeTTI RemoterModelType = "tti"

	RemoterModelMsgTypeTxt   RemoterModelMsgType = "txt"
	RemoterModelMsgTypeImg   RemoterModelMsgType = "img"
	RemoterModelMsgTypeVideo RemoterModelMsgType = "video"
	RemoterModelMsgTypeAudio RemoterModelMsgType = "audio"
)

type RemoterModelInfo struct {
	Id                   uint
	ModelName            string
	ProviderModelId      int
	ProviderModelName    string
	ProviderModelVersion string
	Description          string
	Provider             RemoterModelProvider
	SupportMsgType       string // 支持的类型列表，用英文逗号分开，比如："txt,img,video"
	ModelType            RemoterModelType
	IsOnchain            bool `json:"is_onchain"` // 新增：是否为链上模型
	IsDeleted            int
	CreatedTime          *time.Time
	UpdatedTime          *time.Time
}

func (RemoterModelInfo) TableName() string {
	return "remoter_model_infos"
}

type RemoterModelInfoRepository interface {
	GetAll(ctx context.Context) ([]*RemoterModelInfo, error)
	GetByModelName(ctx context.Context, modelName string) (*RemoterModelInfo, error)
}

type TTSModelVoice struct {
	Id           uint
	ModelName    string
	Name         string
	ProviderName string
	Description  string
	CreatedAt    *time.Time
	UpdatedAt    *time.Time
}

func (TTSModelVoice) TableName() string {
	return "tts_model_voices"
}

type TTSModelVoiceRepository interface {
	GetByModelName(ctx context.Context, modelName string) ([]*TTSModelVoice, error)
	GetByModelNameAndName(ctx context.Context, modelName, name string) (*TTSModelVoice, error)
	CreateBatch(ctx context.Context, voices []*TTSModelVoice) error
	DeleteByModelName(ctx context.Context, modelName string) error
}

// topai 链上模型列表，直接从链上拉取信息
type TopaiModel struct {
	Id                uint
	ChainModelId      uint
	OwnerAddress      string
	ModelName         string
	ModelVersion      string
	ModelType         string
	InputPrice        string
	OutputPrice       string
	SeriesId          uint
	SupportImageLevel string
	CreatedAt         *time.Time
}

func (TopaiModel) TableName() string {
	return "topai_models"
}

type TopaiModelRepository interface {
	Create(ctx context.Context, model *TopaiModel) error
	GetAll(ctx context.Context) ([]*TopaiModel, error)
	GetByModelNameAndVersion(ctx context.Context, modelName, modelVersion string) (*TopaiModel, error)
	GetByModelName(ctx context.Context, modelName string) ([]*TopaiModel, error)
	GetByID(ctx context.Context, id uint) (*TopaiModel, error)
	GetByIds(ctx context.Context, ids []uint) ([]*TopaiModel, error)
	GetBySeriesIds(ctx context.Context, seriesIds []uint) ([]*TopaiModel, error)
	GetByModelTypes(ctx context.Context, modelTypes []string) ([]*TopaiModel, error)
	GetByOwnerAddress(ctx context.Context, ownerAddress string, offset int, limit int) ([]*TopaiModel, error)
	GetCountByOwnerAddress(ctx context.Context, ownerAddress string) (int64, error)
	GetByChainModelId(ctx context.Context, chainModelId uint) (*TopaiModel, error)
	GetByIsOnChain(ctx context.Context, isOnChain bool) ([]*TopaiModel, error)
	Update(ctx context.Context, model *TopaiModel) error
}

type TopaiModelExt struct {
	Id                   uint
	ModelId              string
	TopaiModelProviderId uint
	TopaiModelId         uint
	Description          string
	ContextLength        uint64
	MaxOutput            uint64
	Latency              uint64
	Throughput           uint64
	SampleCode           string
	ShowPicture          string
	ShowVideo            string
	Price                string
	SeriesId             uint
	Status               int
	Weight               uint
	CreatedAt            *time.Time
	UpdatedAt            *time.Time
}

func (TopaiModelExt) TableName() string {
	return "topai_model_exts"
}

type TopaiModelExtRepository interface {
	GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*TopaiModelExt, error)
	GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*TopaiModelExt, error)
	GetOnlineList(ctx context.Context, offset, pageSize int, sortBy, sortOrder string, modelIds []uint, providerIds []uint, isfree bool, searchModelId string) ([]*TopaiModelExt, error)
	GetOnlineAll(ctx context.Context) ([]*TopaiModelExt, error)
	GetAll(ctx context.Context) ([]*TopaiModelExt, error)
	GetByModelId(ctx context.Context, modelId string) (*TopaiModelExt, error)
	GetByModelIds(ctx context.Context, modelIds []string) ([]*TopaiModelExt, error)
	GetBySearchOnlineModelId(ctx context.Context, modelId string) ([]*TopaiModelExt, error)
	Create(ctx context.Context, ext *TopaiModelExt) error
	Update(ctx context.Context, ext *TopaiModelExt) error
	GetByTopaiModelIdsAndStatus(ctx context.Context, topaiModelIds []uint, status []int, offset, limit int) ([]*TopaiModelExt, error)
}

type TopaiModelSupportParam struct {
	Id           uint
	TopaiModelId uint
	ParamName    string
	CreatedAt    *time.Time
}

func (TopaiModelSupportParam) TableName() string {
	return "topai_model_support_params"
}

type TopaiModelSupportParamRepository interface {
	GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*TopaiModelSupportParam, error)
	GetByParams(ctx context.Context, params []string) ([]*TopaiModelSupportParam, error)
	GetAll(ctx context.Context) ([]*TopaiModelSupportParam, error)
}

// topai 链上模型提供者列表，用户登陆后需要编辑
type TopaiModelProvider struct {
	Id            uint
	Uuid          string
	WalletAddress string
	Name          string
	CreatedAt     *time.Time
}

func (TopaiModelProvider) TableName() string {
	return "topai_model_providers"
}

type TopaiModelProviderRepository interface {
	GetAll(ctx context.Context) ([]*TopaiModelProvider, error)
	GetByWalletAddress(ctx context.Context, walletAddress string) (*TopaiModelProvider, error)
	GetByUuids(ctx context.Context, uuids []string) ([]*TopaiModelProvider, error)
	GetByNames(ctx context.Context, names []string) ([]*TopaiModelProvider, error)
	GetByIds(ctx context.Context, ids []uint) ([]*TopaiModelProvider, error)
	GetByID(ctx context.Context, id uint) (*TopaiModelProvider, error)
	Update(ctx context.Context, provider *TopaiModelProvider) error
	Create(ctx context.Context, provider *TopaiModelProvider) error
}

// topai 链上模型分类
type TopaiModelCategory struct {
	Id        uint
	Name      string
	ParentId  uint
	IsDeleted int
	BelongTo  string
	CreatedAt *time.Time
}

func (TopaiModelCategory) TableName() string {
	return "topai_model_categories"
}

type TopaiModelCategoryRepository interface {
	GetAll(ctx context.Context) ([]*TopaiModelCategory, error)
	GetByIds(ctx context.Context, ids []uint) ([]*TopaiModelCategory, error)
	Create(ctx context.Context, category *TopaiModelCategory) error
}

// topai 链上模型所属分类
type TopaiModelCategoryRelation struct {
	Id           uint
	TopaiModelId uint
	CategoryId   uint
	IsDeleted    int
	CreatedAt    *time.Time
}

func (TopaiModelCategoryRelation) TableName() string {
	return "topai_model_category_relations"
}

type TopaiModelCategoryRelationRepository interface {
	GetAll(ctx context.Context) ([]*TopaiModelCategoryRelation, error)
	GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*TopaiModelCategoryRelation, error)
	GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*TopaiModelCategoryRelation, error)
	GetByIds(ctx context.Context, ids []uint) ([]*TopaiModelCategoryRelation, error)
	GetByCategoryIds(ctx context.Context, categoryIds []uint) ([]*TopaiModelCategoryRelation, error)
	DeleteByTopaiModelId(ctx context.Context, topaiModelId uint) error
	CreateBatch(ctx context.Context, relations []*TopaiModelCategoryRelation) error
}

type TopaiModelSeries struct {
	Id        uint
	Name      string
	CreatedAt *time.Time
}

func (TopaiModelSeries) TableName() string {
	return "topai_model_series"
}

type TopaiModelSeriesRepository interface {
	GetAll(ctx context.Context) ([]*TopaiModelSeries, error)
	GetByID(ctx context.Context, id uint) (*TopaiModelSeries, error)
	Create(ctx context.Context, series *TopaiModelSeries) error
}

type TopaiModelSeriesRelation struct {
	Id           uint
	TopaiModelId uint
	SeriesId     uint
	CreatedAt    *time.Time
}

func (TopaiModelSeriesRelation) TableName() string {
	return "topai_model_series_relations"
}

type TopaiModelSeriesRelationRepository interface {
	GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*TopaiModelSeriesRelation, error)
	GetByIds(ctx context.Context, ids []uint) ([]*TopaiModelSeriesRelation, error)
}

type DevAppInfo struct {
	Id            uint
	UserId        uint
	Uuid          string
	WalletAddress string
	Name          string
	AccountId     string
	Description   string
	Website       string
	Logo          string
	Status        int // -1: 异常下架, 0: 正常, 1:终止
	IsDeleted     int
	CreatedAt     *time.Time
}

var (
	DevAppStatusNormal     = 0
	DevAppStatusAbnormal   = -1
	DevAppStatusTerminated = 1
)

func (DevAppInfo) TableName() string {
	return "dev_app_infos"
}

type DevAppInfoRepository interface {
	Create(ctx context.Context, app *DevAppInfo) error
	Update(ctx context.Context, app *DevAppInfo) error
	Delete(ctx context.Context, id uint) error
	GetAll(ctx context.Context) ([]*DevAppInfo, error)
	GetByUuid(ctx context.Context, uuid string) (*DevAppInfo, error)
	GetByIds(ctx context.Context, ids []uint) ([]*DevAppInfo, error)
	GetByID(ctx context.Context, id uint) (*DevAppInfo, error)
	GetAllByUserID(ctx context.Context, userID uint) ([]*DevAppInfo, error)
}

var (
	TopAiModelTaskStatusRunning = 0
	TopAiModelTaskStatusSuccess = 1
	TopAiModelTaskStatusFailed  = -1
)

type DevAppKey struct {
	Id        uint
	UserId    uint
	DevAppId  uint
	Key       string
	Name      string
	IsDeleted int
	CreatedAt *time.Time
}

func (DevAppKey) TableName() string {
	return "dev_app_keys"
}

type DevAppKeyRepository interface {
	GetByDevAppId(ctx context.Context, devAppId uint) ([]*DevAppKey, error)
	GetByDevAppIds(ctx context.Context, devAppIds []uint) ([]*DevAppKey, error)
	GetByKey(ctx context.Context, key string) (*DevAppKey, error)
	Create(ctx context.Context, key *DevAppKey) error
	Delete(ctx context.Context, id uint) error
	GetByDevAppIdAndKey(ctx context.Context, devAppId uint, key string) (*DevAppKey, error)
	GetByDevAppIdAndName(ctx context.Context, devAppId uint, name string) (*DevAppKey, error)
}

type TopaiModelCost struct {
	Id                uint
	TopaiModelId      uint
	ModelId           string
	TotalInputTokens  string
	TotalOutputTokens string
	TotalTokens       string
	TotalCount        int
	CreatedAt         *time.Time
	UpdatedAt         *time.Time
}

func (TopaiModelCost) TableName() string {
	return "topai_model_costs"
}

type TopaiModelCostRepository interface {
	GetByModelId(ctx context.Context, modelId string) (*TopaiModelCost, error)
	GetByModelIds(ctx context.Context, modelIds []string) ([]*TopaiModelCost, error)
	GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*TopaiModelCost, error)
	GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*TopaiModelCost, error)
	Create(ctx context.Context, cost *TopaiModelCost) error
	Update(ctx context.Context, cost *TopaiModelCost) error
	GetAll(ctx context.Context, offset, limit int, sort string) ([]*TopaiModelCost, error)
}

type UserUsageDayRecord struct {
	Id           uint
	UserId       uint
	AppId        uint
	AppKey       string
	ModelId      string
	ModelType    string
	UsageDate    string
	InputTokens  string
	OutputTokens string
	TotalTokens  string
	InputCost    string
	OutputCost   string
	TotalCost    string
	Count        int
	CreatedAt    *time.Time
}

func (UserUsageDayRecord) TableName() string {
	return "user_usage_day_records"
}

type UserUsageDayRecordRepository interface {
	CreateBatch(ctx context.Context, records []*UserUsageDayRecord) error
	GetByModelId(ctx context.Context, modelId string) ([]*UserUsageDayRecord, error)
	GetLast(ctx context.Context) (*UserUsageDayRecord, error)
}

// ProviderToken 模型提供者Token
type ProviderToken struct {
	ID         uint       `json:"id"`
	ProviderID uint       `json:"provider_id"` // 关联提供者ID
	Token      string     `json:"token"`       // 32位随机字符串
	ExpiresAt  time.Time  `json:"expires_at"`  // 过期时间
	DeletedAt  uint       `json:"deleted_at"`  // 软删除时间戳
	CreatedAt  *time.Time `json:"created_at"`
	UpdatedAt  *time.Time `json:"updated_at"`
}

func (ProviderToken) TableName() string {
	return "provider_tokens"
}

type ProviderTokenRepository interface {
	Create(ctx context.Context, token *ProviderToken) error
	GetByToken(ctx context.Context, token string) (*ProviderToken, error)
	Delete(ctx context.Context, id uint) error
	DeleteByProviderID(ctx context.Context, providerID uint) error
}
