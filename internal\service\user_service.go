package service

import (
	"context"
	"errors"
	"math/rand"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type userService struct {
	log  *logger.ModuleLogger
	repo *repository.DB
}

func NewUserService(ctx context.Context, repo *repository.DB) *userService {
	log := logger.GetLogger("user_service")
	return &userService{log: log, repo: repo}
}

var _ UserApi = &userService{}

func (s *userService) RegisterAndGenerateToken(ctx context.Context, username, email, password string) (*UserBaseOP, error) {
	// 判断是否允许注册
	allowRegister, err := s.repo.SystemConfig.GetByCategoryAndKey(ctx, systemConfigCategory, SystemCommonConfigENABLE_SIGNUP)
	if err != nil {
		return nil, err
	}
	// 默认允许注册
	if allowRegister != nil && allowRegister.Value != "true" {
		return nil, api.NewClientError("register is not allowed")
	}

	// 获取用户注册默认角色
	defaultUserRole, err := s.repo.SystemConfig.GetByCategoryAndKey(ctx, systemConfigCategory, SystemCommonConfigDEFAULT_USER_ROLE)
	if err != nil {
		return nil, err
	}
	defaultRole := repository.UserRoleUser
	if defaultUserRole != nil {
		defaultRole = repository.UserRole(defaultUserRole.Value)
	}

	user, err := s.createUser(ctx, email, password, username, defaultRole.String())
	if err != nil {
		return nil, err
	}

	token, err := s.generateToken(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	return &UserBaseOP{
		ID:         user.ID,
		Email:      user.Email,
		Name:       user.Name,
		Role:       string(user.Role),
		IdentityID: user.IdentityID,
		Token:      token.Token,
		TokenType:  "Bearer",
	}, nil
}

func (s *userService) Login(ctx context.Context, email, password string) (*UserBaseOP, error) {
	user, err := s.repo.User.GetByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, api.NewClientError("user not found")
	}

	// 直接使用数据库中存储的密码哈希来验证用户输入的密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, api.NewClientError("invalid password")
	}

	err = s.ClearUserToken(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	token, err := s.generateToken(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	return &UserBaseOP{
		ID:              user.ID,
		Email:           user.Email,
		Name:            user.Username,
		Role:            string(user.Role),
		IdentityID:      user.IdentityID,
		Token:           token.Token,
		TokenType:       "Bearer",
		ProfileImageURL: "",
	}, nil
}

func (s *userService) UpdatePassword(ctx context.Context, userBase *UserBaseOP, oldPassword, newPassword string) error {
	user, err := s.repo.User.GetByID(ctx, userBase.ID)
	if err != nil {
		return err
	}
	if user == nil {
		return api.NewClientError("user not found")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword)); err != nil {
		return api.NewClientError("invalid password")
	}

	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	user.PasswordHash = string(passwordHash)
	err = s.repo.User.Update(ctx, user)
	if err != nil {
		return err
	}

	err = s.ClearUserToken(ctx, user.ID)
	if err != nil {
		return err
	}

	return nil
}

func (s *userService) generateToken(ctx context.Context, userID uint) (*repository.UserToken, error) {
	for {
		// 生成16位随机字母数字token
		const tokenLength = 16
		const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

		b := make([]byte, tokenLength)
		for i := range b {
			b[i] = charset[rand.Intn(len(charset))]
		}
		token := string(b)

		// 检查token是否存在
		userToken, err := s.repo.UserToken.GetByToken(ctx, token)
		if err != nil {
			return nil, err
		}
		if userToken != nil {
			continue
		}
		// 创建token记录
		userToken = &repository.UserToken{
			UserID:    userID,
			Token:     token,
			ExpiresAt: time.Now().Add(24 * time.Hour), // token有效期24小时
		}

		err = s.repo.UserToken.Create(ctx, userToken)
		if err != nil {
			return nil, err
		}

		return userToken, nil
	}
}

func (s *userService) ClearUserToken(ctx context.Context, userID uint) error {
	return s.repo.UserToken.DeleteByUserID(ctx, userID)
}

func (s *userService) Logout(ctx context.Context, userID uint) error {
	return s.ClearUserToken(ctx, userID)
}

func (s *userService) GetUserByToken(ctx context.Context, token string) (*UserBaseOP, error) {
	userToken, err := s.repo.UserToken.GetByToken(ctx, token)
	if err != nil {
		return nil, err
	}
	if userToken == nil {
		return nil, errors.New("user token not found")
	}

	user, err := s.repo.User.GetByID(ctx, userToken.UserID)
	if err != nil {
		return nil, err
	}

	return &UserBaseOP{
		ID:         user.ID,
		Email:      user.Email,
		Name:       user.Username,
		Role:       string(user.Role),
		IdentityID: user.IdentityID,
		ExpiresAt:  nil,
		Token:      userToken.Token,
		TokenType:  "Bearer",
	}, nil
}

func (s *userService) AdminCreateUser(ctx context.Context, email, password, name, role string) (*UserBaseOP, error) {
	return s.createUser(ctx, email, password, name, role)
}

func (s *userService) createUser(ctx context.Context, email, password, name, role string) (*UserBaseOP, error) {
	// 检查邮箱是否存在
	user, err := s.repo.User.GetByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	if user != nil {
		return nil, api.NewClientError("email already exists")
	}

	passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	user = &repository.User{
		IdentityID:   uuid.New().String(),
		Username:     name,
		Email:        email,
		PasswordHash: string(passwordHash),
		Role:         repository.UserRole(role),
	}
	err = s.repo.User.Create(ctx, user)
	if err != nil {
		return nil, err
	}

	return &UserBaseOP{ID: user.ID, Email: user.Email, Name: user.Username, Role: string(user.Role), IdentityID: user.IdentityID}, nil
}

func (s *userService) AdminUpdateUser(ctx context.Context, identityID string, email, name, password, profileImageURL string) (*UserAdminUser, error) {
	user, err := s.repo.User.GetByIdentityID(ctx, identityID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	isUpdate := false
	if email != "" {
		// 判断邮箱是否已经被用
		existingUser, err := s.repo.User.GetByEmail(ctx, email)
		if err != nil {
			return nil, err
		}
		if existingUser != nil && existingUser.IdentityID != identityID {
			return nil, errors.New("email already exists")
		}
		if user.Email != email {
			user.Email = email
			isUpdate = true
		}
	}

	if name != "" && user.Username != name {
		user.Username = name
		isUpdate = true
	}

	if password != "" && password != user.PasswordHash {
		passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			return nil, err
		}
		user.PasswordHash = string(passwordHash)
		isUpdate = true
	}

	if !isUpdate {
		return nil, errors.New("no update")
	}

	err = s.repo.User.Update(ctx, user)
	if err != nil {
		return nil, err
	}

	return &UserAdminUser{
		UserBaseOP: UserBaseOP{
			ID:              user.ID,
			Email:           user.Email,
			Name:            user.Username,
			Role:            string(user.Role),
			IdentityID:      user.IdentityID,
			ProfileImageURL: "",
		},
		CreatedAt:    user.CreatedAt.Unix(),
		UpdatedAt:    user.UpdatedAt.Unix(),
		LastActiveAt: 0,
		Settings:     nil,
		Info:         nil,
		OauthSub:     nil,
		ApiKey:       "",
	}, nil
}

func (s *userService) AdminUpdateUserRole(ctx context.Context, identityID string, role string) (*UserAdminUser, error) {
	user, err := s.repo.User.GetByIdentityID(ctx, identityID)
	if err != nil {
		return nil, err
	}

	if role == user.Role.String() {
		return nil, errors.New("role is the same")
	}

	user.Role = repository.UserRole(role)
	err = s.repo.User.Update(ctx, user)
	if err != nil {
		return nil, err
	}

	return &UserAdminUser{
		UserBaseOP: UserBaseOP{
			ID: user.ID,
		},
	}, nil
}

func (s *userService) AdminDeleteUser(ctx context.Context, identityID string) error {
	user, err := s.repo.User.GetByIdentityID(ctx, identityID)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	err = s.repo.User.Delete(ctx, user.ID)
	if err != nil {
		return err
	}

	err = s.ClearUserToken(ctx, user.ID)
	if err != nil {
		return err
	}

	return nil
}

func (s *userService) AdminGetAllUsers(ctx context.Context, page int, pageSize int, order string, direction string) (*UserAdminOP, error) {
	offset := (page - 1) * pageSize

	total, err := s.repo.User.Count(ctx)
	if err != nil {
		return nil, err
	}
	if offset > int(total) {
		return &UserAdminOP{
			Total: 0,
			Users: make([]*UserAdminUser, 0),
		}, nil
	}

	users, err := s.repo.User.List(ctx, offset, pageSize, order, direction)
	if err != nil {
		return nil, err
	}

	userOP := &UserAdminOP{
		Total: total,
		Users: make([]*UserAdminUser, len(users)),
	}
	for i, user := range users {
		userOP.Users[i] = &UserAdminUser{
			UserBaseOP: UserBaseOP{
				ID:              user.ID,
				Email:           user.Email,
				Name:            user.Username,
				Role:            string(user.Role),
				IdentityID:      user.IdentityID,
				Token:           "",
				TokenType:       "Bearer",
				ProfileImageURL: "",
			},
			CreatedAt: user.CreatedAt.Unix(),
			UpdatedAt: user.UpdatedAt.Unix(),

			// TODO: 后续需要添加
			LastActiveAt: 0,
			Settings:     nil,
			Info:         nil,
			OauthSub:     nil,
			ApiKey:       "",
		}
	}

	return userOP, nil
}

func (s *userService) AdminGetUsersWithBalance(ctx context.Context, page int, pageSize int, order string, direction string) (*AdminUsersListResponse, error) {
	offset := (page - 1) * pageSize

	total, err := s.repo.User.Count(ctx)
	if err != nil {
		return nil, err
	}
	if offset > int(total) {
		return &AdminUsersListResponse{
			Total: 0,
			Users: make([]*AdminUserWithBalance, 0),
		}, nil
	}

	users, err := s.repo.User.List(ctx, offset, pageSize, order, direction)
	if err != nil {
		return nil, err
	}

	userResponse := &AdminUsersListResponse{
		Total: total,
		Users: make([]*AdminUserWithBalance, len(users)),
	}

	// 获取所有用户的余额信息
	for i, user := range users {
		// 获取用户USDT余额
		balance, err := s.repo.UserBalance.GetByUserIDAndCurrency(ctx, user.ID, repository.UserBalanceCurrencyUSDT)
		currentBalance := "0"
		accumulatedTotal := "0"
		if err == nil && balance != nil {
			currentBalance = balance.Balance
			accumulatedTotal = balance.Accumulated
		}

		userResponse.Users[i] = &AdminUserWithBalance{
			ID:               user.ID,
			IdentityID:       user.IdentityID,
			Email:            user.Email,
			Username:         user.Username,
			Role:             string(user.Role),
			CurrentBalance:   currentBalance,
			AccumulatedTotal: accumulatedTotal,
			CreatedAt:        user.CreatedAt.Unix(),
			UpdatedAt:        user.UpdatedAt.Unix(),
		}
	}

	return userResponse, nil
}

func (s *userService) GetUserSettings(ctx context.Context, userID uint) (*UserSettings, error) {
	// todo 目前只有一个system配置，如果有其他的还需要更改
	userSetting, err := s.repo.UserSetting.GetByUserIDAndKey(ctx, userID, UserSettingsSystem)
	if err != nil {
		return nil, err
	}
	if userSetting == nil {
		return &UserSettings{
			SyStem: "",
		}, nil
	}

	return &UserSettings{
		SyStem: userSetting.Value,
	}, nil
}

func (s *userService) UpdateUserSettings(ctx context.Context, userID uint, settings *UserSettings) error {
	userSetting, err := s.repo.UserSetting.GetByUserIDAndKey(ctx, userID, UserSettingsSystem)
	if err != nil {
		return err
	}

	if userSetting == nil {
		userSetting = &repository.UserSetting{
			UserID: userID,
			Key:    UserSettingsSystem,
			Value:  settings.SyStem,
		}
		return s.repo.UserSetting.Create(ctx, userSetting)
	}

	userSetting.Value = settings.SyStem
	return s.repo.UserSetting.Update(ctx, userSetting)
}

func (s *userService) UpdateUserChatRequest(ctx context.Context, user *UserBaseOP, ip string, req *UserChatRequest) error {
	now := time.Now()

	return s.repo.UserChatRequest.Create(ctx, &repository.UserChatRequest{
		UserID:                  user.ID,
		IP:                      ip,
		ModelType:               req.ModelType,
		AssociatedUUID:          req.AssociatedUUID,
		ConversationMessageUUID: req.ConversationMessageUUID,
		ModelID:                 req.ModelID,
		CreatedAt:               &now,
	})
}

func (s *userService) GetDayChatRequest(ctx context.Context, day string) ([]*repository.UserChatRequest, error) {
	start := day + " 00:00:00"
	end := day + " 23:59:59"
	requests, err := s.repo.UserChatRequest.GetByCreatedAtPeriod(ctx, start, end)
	if err != nil {
		return nil, err
	}
	return requests, nil
}
