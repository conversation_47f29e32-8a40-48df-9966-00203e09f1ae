// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserChatTTSRecordRepository is an autogenerated mock type for the UserChatTTSRecordRepository type
type UserChatTTSRecordRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserChatTTSRecordRepository) Create(ctx context.Context, record *repository.UserChatTTSRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatTTSRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserChatTTSRecordRepository creates a new instance of UserChatTTSRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserChatTTSRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserChatTTSRecordRepository {
	mock := &UserChatTTSRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
