-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    identity_id VARCHAR(50) NOT NULL COMMENT '用户唯一标识',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    role VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '用户角色(user/admin)',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_email (email),
    UNIQUE INDEX idx_identity_id (identity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户Token表
CREATE TABLE IF NOT EXISTS user_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '用户tokenID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    token VARCHAR(255) NOT NULL COMMENT 'token',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    deleted_at BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间(0为未删除)',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_token_deleted_at (token, deleted_at),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 对话表
CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '对话ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    identity_id VARCHAR(50) NOT NULL COMMENT '用户唯一标识',
    uuid VARCHAR(100) NOT NULL COMMENT '对话UUID',
    title TEXT NOT NULL COMMENT '对话标题',
    model_id TEXT NOT NULL COMMENT '模型ID',
    `system` TEXT NOT NULL COMMENT '系统提示词',
    current_msg_uuid VARCHAR(100) NOT NULL DEFAULT '' COMMENT '当前消息UUID',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_uuid (uuid),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 对话消息表
CREATE TABLE IF NOT EXISTS conversation_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '消息ID',
    uuid VARCHAR(100) NOT NULL COMMENT '消息UUID',
    conversation_id BIGINT UNSIGNED NOT NULL COMMENT '对话ID',
    conversation_uuid VARCHAR(100) NOT NULL COMMENT '对话UUID',
    role VARCHAR(20) NOT NULL COMMENT '角色(user/assistant/system)',
    content TEXT NOT NULL COMMENT '内容',
    parent_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '父消息ID',
    parent_uuid VARCHAR(100) NOT NULL DEFAULT '' COMMENT '父消息UUID',
    models TEXT NOT NULL COMMENT '模型',
    file_uuids TEXT NOT NULL COMMENT '文件UUID列表,用英文逗号分开',
    is_done TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否完成',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_conversation_id (conversation_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- llm模型配置表
CREATE TABLE IF NOT EXISTS llm_model_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '模型配置ID',
    model_id VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '模型ID',
    base_model_id VARCHAR(100) NOT NULL DEFAULT '' COMMENT '基础模型ID',
    name VARCHAR(100) NOT NULL COMMENT '模型名称',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_model_id (model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文件表
CREATE TABLE IF NOT EXISTS files (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    uuid VARCHAR(100) NOT NULL COMMENT '文件唯一标识',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `name` VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `size` BIGINT UNSIGNED NOT NULL COMMENT '文件大小(字节)',
    mime_type VARCHAR(255) NOT NULL DEFAULT '' COMMENT '文件MIME类型',
    `type` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '文件类型',
    content TEXT NOT NULL COMMENT '文件内容',
    is_public TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_uuid (uuid),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `key` VARCHAR(100) NOT NULL COMMENT '设置键',
    value TEXT NOT NULL COMMENT '设置值',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_user_id_key (user_id, `key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    category VARCHAR(50) NOT NULL COMMENT '分类',
    `key` VARCHAR(100) NOT NULL COMMENT '配置键',
    value TEXT NOT NULL COMMENT '配置值',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_category_key (category, `key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 远程模型信息表
CREATE TABLE IF NOT EXISTS remoter_model_infos (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '模型信息ID',
    model_name VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '模型名称，自定义模型名称，展示给前端的',
    provider_model_id INT NOT NULL COMMENT '提供商模型ID',
    provider_model_name VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '提供商模型名称',
    provider_model_version VARCHAR(50) NOT NULL COMMENT '提供商模型版本',
    description TEXT NOT NULL COMMENT '描述',
    provider VARCHAR(50) NOT NULL COMMENT '提供商',
    support_msg_type VARCHAR(100) NOT NULL COMMENT '支持的消息类型',
    model_type VARCHAR(50) NOT NULL COMMENT '模型类型',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_model_name_version (provider_model_name, provider_model_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- TTS模型音色表
CREATE TABLE IF NOT EXISTS tts_model_voices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '音色ID',
    model_name VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '模型名称',
    `name` VARCHAR(100) NOT NULL COMMENT '音色名称',
    provider_name VARCHAR(100) NOT NULL COMMENT '提供商音色名称',
    `description` TEXT NOT NULL COMMENT '音色描述',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_model_voice (model_name, `name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


# 插入外部调用
insert into remoter_model_infos (model_name, provider_model_id, provider_model_name, provider_model_version, description, provider, support_msg_type, model_type) values
# llm
('gpt-4o-mini-v2', 0, 'openai/gpt-4o-mini', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.0-flash-001-v1', 0, 'google/gemini-2.0-flash-001', 'v1', '', 'openrouter', 'txt', 'llm'),
('claude-sonnet-4-v2', 0, 'anthropic/claude-sonnet-4', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.5-pro-preview-v3', 0, 'google/gemini-2.5-pro-preview', 'v3', '', 'openrouter', 'txt', 'llm'),
('claude-3.7-sonnet-v2', 0, 'anthropic/claude-3.7-sonnet', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.5-flash-preview-v1', 0, 'google/gemini-2.5-flash-preview', 'v1', '', 'openrouter', 'txt', 'llm'),
('deepseek-chat-v2', 0, 'deepseek/deepseek-chat-v3-0324:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.5-flash-preview-05-20-v1', 0, 'google/gemini-2.5-flash-preview-05-20', 'v1', '', 'openrouter', 'txt', 'llm'),
('gpt-4.1-v2', 0, 'openai/gpt-4.1', 'v2', '', 'openrouter', 'txt', 'llm'),
('llama-3.3-70b-instruct-v2', 0, 'meta-llama/llama-3.3-70b-instruct', 'v2', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1-0528-v1', 0, 'deepseek/deepseek-r1-0528:free', 'v1', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1-v2', 0, 'deepseek/deepseek-r1:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.0-flash-lite-001-v2', 0, 'google/gemini-2.0-flash-lite-001', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-flash-1.5-8b-v2', 0, 'google/gemini-flash-1.5-8b', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-2.5-flash-preview-05-20-v3', 0, 'google/gemini-2.5-flash-preview-05-20:thinking', 'v3', '', 'openrouter', 'txt', 'llm'),
('gpt-4.1-mini-v1', 0, 'openai/gpt-4.1-mini', 'v1', '', 'openrouter', 'txt', 'llm'),
('gemini-flash-1.5-v1', 0, 'google/gemini-flash-1.5', 'v1', '', 'openrouter', 'txt', 'llm'),
('claude-opus-4-v1', 0, 'anthropic/claude-opus-4', 'v1', '', 'openrouter', 'txt', 'llm'),
('claude-3.5-sonnet-v1', 0, 'anthropic/claude-3.5-sonnet', 'v1', '', 'openrouter', 'txt', 'llm'),
('llama-4-maverick-v2', 0, 'meta-llama/llama-4-maverick', 'v2', '', 'openrouter', 'txt', 'llm'),
('deepseek-chat-v1', 0, 'deepseek/deepseek-chat', 'v1', '', 'openrouter', 'txt', 'llm'),
('gpt-4.1-nano-v3', 0, 'openai/gpt-4.1-nano', 'v3', '', 'openrouter', 'txt', 'llm'),
('mistral-small-24b-instruct-2501-v3', 0, 'mistralai/mistral-small-24b-instruct-2501', 'v3', '', 'openrouter', 'txt', 'llm'),
('o4-mini-v1', 0, 'openai/o4-mini', 'v1', '', 'openrouter', 'txt', 'llm'),
('claude-3.7-sonnet-v3', 0, 'anthropic/claude-3.7-sonnet:thinking', 'v3', '', 'openrouter', 'txt', 'llm'),
('llama-3.1-8b-instruct-v3', 0, 'meta-llama/llama-3.1-8b-instruct', 'v3', '', 'openrouter', 'txt', 'llm'),
('claude-3.7-sonnet-v1', 0, 'anthropic/claude-3.7-sonnet:beta', 'v1', '', 'openrouter', 'txt', 'llm'),
('llama-3.1-70b-instruct-v2', 0, 'meta-llama/llama-3.1-70b-instruct', 'v2', '', 'openrouter', 'txt', 'llm'),
('grok-3-mini-beta-v3', 0, 'x-ai/grok-3-mini-beta', 'v3', '', 'openrouter', 'txt', 'llm'),
('gemma-3-27b-it-v1', 0, 'google/gemma-3-27b-it', 'v1', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1-v3', 0, 'deepseek/deepseek-r1', 'v3', '', 'openrouter', 'txt', 'llm'),
('gpt-4o-2024-11-20-v2', 0, 'openai/gpt-4o-2024-11-20', 'v2', '', 'openrouter', 'txt', 'llm'),
('wizardlm-2-8x22b-v3', 0, 'microsoft/wizardlm-2-8x22b', 'v3', '', 'openrouter', 'txt', 'llm'),
('qwen3-235b-a22b-v3', 0, 'qwen/qwen3-235b-a22b', 'v3', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1t-chimera-v2', 0, 'tngtech/deepseek-r1t-chimera:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('gpt-4o-v2', 0, 'openai/gpt-4o', 'v2', '', 'openrouter', 'txt', 'llm'),
('qwen-2.5-72b-instruct-v1', 0, 'qwen/qwen-2.5-72b-instruct', 'v1', '', 'openrouter', 'txt', 'llm'),
('gemini-2.0-flash-exp-v2', 0, 'google/gemini-2.0-flash-exp:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('llama-3.2-1b-instruct-v1', 0, 'meta-llama/llama-3.2-1b-instruct', 'v1', '', 'openrouter', 'txt', 'llm'),
('deepseek-chat-v3', 0, 'deepseek/deepseek-chat:free', 'v3', '', 'openrouter', 'txt', 'llm'),
('claude-3.5-sonnet-v2', 0, 'anthropic/claude-3.5-sonnet:beta', 'v2', '', 'openrouter', 'txt', 'llm'),
('llama-3.2-3b-instruct-v2', 0, 'meta-llama/llama-3.2-3b-instruct', 'v2', '', 'openrouter', 'txt', 'llm'),
('devstral-small-v3', 0, 'mistralai/devstral-small:free', 'v3', '', 'openrouter', 'txt', 'llm'),
('grok-3-beta-v2', 0, 'x-ai/grok-3-beta', 'v2', '', 'openrouter', 'txt', 'llm'),
('llama-4-scout-v1', 0, 'meta-llama/llama-4-scout', 'v1', '', 'openrouter', 'txt', 'llm'),
('qwen3-32b-v2', 0, 'qwen/qwen3-32b', 'v2', '', 'openrouter', 'txt', 'llm'),
('hermes-3-llama-3.1-405b-v3', 0, 'nousresearch/hermes-3-llama-3.1-405b', 'v3', '', 'openrouter', 'txt', 'llm'),
('hermes-3-llama-3.1-70b-v3', 0, 'nousresearch/hermes-3-llama-3.1-70b', 'v3', '', 'openrouter', 'txt', 'llm'),
('mythomax-l2-13b-v2', 0, 'gryphe/mythomax-l2-13b', 'v2', '', 'openrouter', 'txt', 'llm'),
('gpt-4o-mini-2024-07-18-v3', 0, 'openai/gpt-4o-mini-2024-07-18', 'v3', '', 'openrouter', 'txt', 'llm'),
('qwen3-30b-a3b-v1', 0, 'qwen/qwen3-30b-a3b', 'v1', '', 'openrouter', 'txt', 'llm'),
('minimax-01-v1', 0, 'minimax/minimax-01', 'v1', '', 'openrouter', 'txt', 'llm'),
('lfm-7b-v3', 0, 'liquid/lfm-7b', 'v3', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1-distill-llama-70b-v1', 0, 'deepseek/deepseek-r1-distill-llama-70b', 'v1', '', 'openrouter', 'txt', 'llm'),
('claude-3.5-haiku-v2', 0, 'anthropic/claude-3.5-haiku', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemma-3-4b-it-v2', 0, 'google/gemma-3-4b-it', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemma-3-27b-it-v3', 0, 'google/gemma-3-27b-it:free', 'v3', '', 'openrouter', 'txt', 'llm'),
('mai-ds-r1-v3', 0, 'microsoft/mai-ds-r1:free', 'v3', '', 'openrouter', 'txt', 'llm'),
('mistral-tiny-v1', 0, 'mistralai/mistral-tiny', 'v1', '', 'openrouter', 'txt', 'llm'),
('qwen3-14b-v1', 0, 'qwen/qwen3-14b', 'v1', '', 'openrouter', 'txt', 'llm'),
('mixtral-8x7b-instruct-v2', 0, 'mistralai/mixtral-8x7b-instruct', 'v2', '', 'openrouter', 'txt', 'llm'),
('mistral-small-3.1-24b-instruct-v1', 0, 'mistralai/mistral-small-3.1-24b-instruct', 'v1', '', 'openrouter', 'txt', 'llm'),
('o4-mini-high-v3', 0, 'openai/o4-mini-high', 'v3', '', 'openrouter', 'txt', 'llm'),
('unslopnemo-12b-v2', 0, 'thedrummer/unslopnemo-12b', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemini-pro-1.5-v1', 0, 'google/gemini-pro-1.5', 'v1', '', 'openrouter', 'txt', 'llm'),
('devstral-small-v2', 0, 'mistralai/devstral-small', 'v2', '', 'openrouter', 'txt', 'llm'),
('claude-3-haiku-v1', 0, 'anthropic/claude-3-haiku', 'v1', '', 'openrouter', 'txt', 'llm'),
('chatgpt-4o-latest-v3', 0, 'openai/chatgpt-4o-latest', 'v3', '', 'openrouter', 'txt', 'llm'),
('qwen3-14b-v2', 0, 'qwen/qwen3-14b:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('qwen3-235b-a22b-v2', 0, 'qwen/qwen3-235b-a22b:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('mistral-nemo-v2', 0, 'mistralai/mistral-nemo:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('hermes-2-pro-llama-3-8b-v3', 0, 'nousresearch/hermes-2-pro-llama-3-8b', 'v3', '', 'openrouter', 'txt', 'llm'),
('deepseek-r1-0528-qwen3-8b-v2', 0, 'deepseek/deepseek-r1-0528-qwen3-8b:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('l3.1-euryale-70b-v2', 0, 'sao10k/l3.1-euryale-70b', 'v2', '', 'openrouter', 'txt', 'llm'),
('qwen-2.5-coder-32b-instruct-v1', 0, 'qwen/qwen-2.5-coder-32b-instruct', 'v1', '', 'openrouter', 'txt', 'llm'),
('lfm-3b-v1', 0, 'liquid/lfm-3b', 'v1', '', 'openrouter', 'txt', 'llm'),
('rocinante-12b-v2', 0, 'thedrummer/rocinante-12b', 'v2', '', 'openrouter', 'txt', 'llm'),
('gemma-3-12b-it-v3', 0, 'google/gemma-3-12b-it', 'v3', '', 'openrouter', 'txt', 'llm'),
('deepseek-prover-v1', 0, 'deepseek/deepseek-prover-v2:free', 'v1', '', 'openrouter', 'txt', 'llm'),
('llama-4-maverick-v3', 0, 'meta-llama/llama-4-maverick:free', 'v3', '', 'openrouter', 'txt', 'llm'),
('qwen3-32b-v1', 0, 'qwen/qwen3-32b:free', 'v1', '', 'openrouter', 'txt', 'llm'),
('qwq-32b-v2', 0, 'qwen/qwq-32b:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('l3-lunaris-8b-v1', 0, 'sao10k/l3-lunaris-8b', 'v1', '', 'openrouter', 'txt', 'llm'),
('ministral-8b-v3', 0, 'mistralai/ministral-8b', 'v3', '', 'openrouter', 'txt', 'llm'),
('llama-3.1-lumimaid-8b-v2', 0, 'neversleep/llama-3.1-lumimaid-8b', 'v2', '', 'openrouter', 'txt', 'llm'),
('codestral-2501-v1', 0, 'mistralai/codestral-2501', 'v1', '', 'openrouter', 'txt', 'llm'),
('qwen-turbo-v1', 0, 'qwen/qwen-turbo', 'v1', '', 'openrouter', 'txt', 'llm'),
('o3-v2', 0, 'openai/o3', 'v2', '', 'openrouter', 'txt', 'llm'),
('mistral-small-3.1-24b-instruct-v2', 0, 'mistralai/mistral-small-3.1-24b-instruct:free', 'v2', '', 'openrouter', 'txt', 'llm'),
('llama-3.1-nemotron-ultra-253b-v1', 0, 'nvidia/llama-3.1-nemotron-ultra-253b-v1:free', 'v1', '', 'openrouter', 'txt', 'llm'),
('DeepSeek-R1-NSFW-v1',0,'sonar-reasoning-pro','v1','','perplexity','txt','llm'),
('GLM-4-32B-v2', 0, 'THUDM/GLM-4-32B-0414', 'v2', '', 'siliconflow', 'txt', 'llm'),
# tts
('Kokoro-82M-v1', 0, 'hexgrad/Kokoro-82M', 'v1', '', 'deepinfra', 'txt', 'tts'),
('Dia-1.6B-v2', 0, 'nari-labs/Dia-1.6B', 'v2', '', 'deepinfra', 'txt', 'tts'),
('orpheus-3b-0.1-ft-v1', 0, 'canopylabs/orpheus-3b-0.1-ft', 'v1', '', 'deepinfra', 'txt', 'tts'),
('csm-1b-v2', 0, 'sesame/csm-1b', 'v2', '', 'deepinfra', 'txt', 'tts'),
('Zonos-v0.1-hybrid-v3', 0, 'Zyphra/Zonos-v0.1-hybrid', 'v3', '', 'deepinfra', 'txt', 'tts'),
('Zonos-v0.1-transformer-v1', 0, 'Zyphra/Zonos-v0.1-transformer', 'v1', '', 'deepinfra', 'txt', 'tts'),
('CosyVoice2-0.5B-v1',0,'FunAudioLLM/CosyVoice2-0.5B','v1','','siliconflow','txt','tts'),
# asr
('whisper-large-v3-turbo-v1', 0, 'openai/whisper-large-v3-turbo', 'v1', '', 'deepinfra', 'audio', 'asr'),
('whisper-large-v3', 0, 'openai/whisper-large-v3', 'v3', '', 'deepinfra', 'audio', 'asr'),
# tti
('FLUX-1-dev-v1', 0, 'black-forest-labs/FLUX-1-dev', 'v1', '', 'deepinfra', 'txt', 'tti'),
('FLUX-1-schnell-v2', 0, 'black-forest-labs/FLUX-1-schnell', 'v2', '', 'deepinfra', 'txt', 'tti'),
('FLUX-1.1-pro-v3', 0, 'black-forest-labs/FLUX-1.1-pro', 'v3', '', 'deepinfra', 'txt', 'tti'),
('FLUX-pro-v2', 0, 'black-forest-labs/FLUX-pro', 'v2', '', 'deepinfra', 'txt', 'tti'),
('Juggernaut-Flux-v1', 0, 'run-diffusion/Juggernaut-Flux', 'v1', '', 'deepinfra', 'txt', 'tti'),
('Juggernaut-Lightning-Flux-v2', 0, 'run-diffusion/Juggernaut-Lightning-Flux', 'v2', '', 'deepinfra', 'txt', 'tti'),
('sd3.5-v2', 0, 'stabilityai/sd3.5', 'v2', '', 'deepinfra', 'txt', 'tti'),
('sd3.5-medium-v1', 0, 'stabilityai/sd3.5-medium', 'v1', '', 'deepinfra', 'txt', 'tti'),
('sdxl-turbo-v2', 0, 'stabilityai/sdxl-turbo', 'v2', '', 'deepinfra', 'txt', 'tti');


insert into tts_model_voices (model_name, `name`, provider_name, `description`) values
('CosyVoice2-0.5B-v1','alex','FunAudioLLM/CosyVoice2-0.5B:alex',''),
('CosyVoice2-0.5B-v1','anna','FunAudioLLM/CosyVoice2-0.5B:anna',''),
('CosyVoice2-0.5B-v1','bella','FunAudioLLM/CosyVoice2-0.5B:bella',''),
('CosyVoice2-0.5B-v1','benjamin','FunAudioLLM/CosyVoice2-0.5B:benjamin',''),
('CosyVoice2-0.5B-v1','charles','FunAudioLLM/CosyVoice2-0.5B:charles',''),
('CosyVoice2-0.5B-v1','claire','FunAudioLLM/CosyVoice2-0.5B:claire',''),
('CosyVoice2-0.5B-v1','david','FunAudioLLM/CosyVoice2-0.5B:david',''),
('CosyVoice2-0.5B-v1','diana','FunAudioLLM/CosyVoice2-0.5B:diana',''),
('Kokoro-82M-v1','None','',''),
('Dia-1.6B-v2','None','',''),
('orpheus-3b-0.1-ft-v1','None','',''),
('csm-1b-v2','None','',''),
('Zonos-v0.1-hybrid-v3','None','',''),
('Zonos-v0.1-transformer-v1','None','',''),
('CosyVoice2-0.5B-v1','None','','');


-- topai 链上模型表
CREATE TABLE IF NOT EXISTS topai_models (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    chain_model_id BIGINT UNSIGNED NOT NULL COMMENT '链上模型ID',
    owner_address VARCHAR(100) NOT NULL COMMENT '所有者地址',
    model_name VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '模型名称',
    model_version VARCHAR(50) COLLATE utf8mb4_bin NOT NULL COMMENT '模型版本',
    model_type VARCHAR(50) NOT NULL COMMENT '模型类型',
    input_price VARCHAR(50) NOT NULL COMMENT '输入价格',
    output_price VARCHAR(50) NOT NULL COMMENT '输出价格',
    series_id BIGINT UNSIGNED NOT NULL COMMENT '系列ID',
    support_image_level VARCHAR(50) NOT NULL COMMENT '支持的图片等级',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_model_name_version (model_name, model_version),
    UNIQUE INDEX idx_chain_model_id (chain_model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型扩展信息表
CREATE TABLE IF NOT EXISTS topai_model_exts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    model_id VARCHAR(100) COLLATE utf8mb4_bin NOT NULL COMMENT '模型ID, 由模型名称和版本号组成, 如: model_name-model_version',
    topai_model_provider_id BIGINT UNSIGNED NOT NULL COMMENT '提供商ID',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型ID',
    `description` TEXT NOT NULL COMMENT '描述',
    context_length BIGINT NOT NULL COMMENT '上下文长度',
    max_output BIGINT NOT NULL COMMENT '最大输出',
    latency BIGINT NOT NULL COMMENT '延迟',
    throughput BIGINT NOT NULL COMMENT '吞吐量',
    sample_code TEXT NOT NULL COMMENT '示例代码',
    show_picture TEXT NOT NULL COMMENT '展示图片',
    show_video TEXT NOT NULL COMMENT '展示视频',
    price VARCHAR(50) NOT NULL COMMENT '价格',
    `status` TINYINT NOT NULL COMMENT '状态: 0 下线, -1 删除, 1 上线',
    weight BIGINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '权重',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_model_id (model_id),
    UNIQUE INDEX idx_topai_model_id (topai_model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型支持参数表
CREATE TABLE IF NOT EXISTS topai_model_support_params (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型表ID',
    param_name VARCHAR(50) NOT NULL COMMENT '参数名称',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_model_param (topai_model_id, param_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型分类表
CREATE TABLE IF NOT EXISTS topai_model_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    parent_id BIGINT UNSIGNED NOT NULL COMMENT '父分类ID',
    belong_to VARCHAR(50) NOT NULL COMMENT '所属',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_name (name),
    INDEX idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型分类关系表
CREATE TABLE IF NOT EXISTS topai_model_category_relations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型ID',
    category_id BIGINT UNSIGNED NOT NULL COMMENT '分类ID',
    is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_model_category (topai_model_id, category_id),
    INDEX idx_category_id (category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型系列表
CREATE TABLE IF NOT EXISTS topai_model_series (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(50) NOT NULL COMMENT '系列名称',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型系列关系表
CREATE TABLE IF NOT EXISTS topai_model_series_relations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型表ID',
    series_id BIGINT UNSIGNED NOT NULL COMMENT '系列ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_model_series (topai_model_id, series_id),
    INDEX idx_series_id (series_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 开发者应用信息表
CREATE TABLE IF NOT EXISTS dev_app_infos (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    uuid VARCHAR(36) NOT NULL COMMENT 'UUID',
    wallet_address VARCHAR(100) NOT NULL COMMENT '钱包地址',
    `name` VARCHAR(100) NOT NULL COMMENT '名称',
    account_id VARCHAR(100) NOT NULL COMMENT '账户ID',
    `description` TEXT NOT NULL COMMENT '描述',
    website VARCHAR(200) NOT NULL COMMENT '网站',
    logo TEXT NOT NULL COMMENT 'logo',
    `status` TINYINT NOT NULL COMMENT '状态',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_uuid (uuid),
    UNIQUE INDEX idx_wallet_address (wallet_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 开发者应用密钥表
CREATE TABLE IF NOT EXISTS dev_app_keys (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    dev_app_id BIGINT UNSIGNED NOT NULL COMMENT '应用ID',
    `key` VARCHAR(100) NOT NULL COMMENT '密钥',
    `name` VARCHAR(100) NOT NULL COMMENT '密钥名称',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_app_key (`key`),
    INDEX idx_dev_app_id (dev_app_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型任务表
CREATE TABLE IF NOT EXISTS topai_model_tasks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    uuid VARCHAR(36) NOT NULL COMMENT 'UUID',
    `status` TINYINT NOT NULL COMMENT '状态',
    dev_app_id BIGINT UNSIGNED NOT NULL COMMENT '应用ID',
    dev_app_key VARCHAR(100) NOT NULL COMMENT '应用密钥',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型表ID',
    input_tokens VARCHAR(50) NOT NULL COMMENT '输入token数',
    output_tokens VARCHAR(50) NOT NULL COMMENT '输出token数',
    ttft VARCHAR(50) NOT NULL COMMENT '首字时间',
    duration VARCHAR(50) NOT NULL COMMENT '持续时间',
    report_to_contract TINYINT NOT NULL COMMENT '是否上报到合约',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_uuid (uuid),
    INDEX idx_dev_app_id (dev_app_id),
    INDEX idx_topai_model_id (topai_model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- topai 模型消耗统计表
CREATE TABLE IF NOT EXISTS topai_model_costs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    topai_model_id BIGINT UNSIGNED NOT NULL COMMENT '模型表ID',
    total_input_tokens VARCHAR(50) NOT NULL COMMENT '总输入token数',
    total_output_tokens VARCHAR(50) NOT NULL COMMENT '总输出token数',
    total_tokens VARCHAR(50) NOT NULL COMMENT '总token数',
    total_count INT NOT NULL COMMENT '总调用次数',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX idx_model_id (topai_model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- topai 链上模型提供者表
CREATE TABLE IF NOT EXISTS topai_model_providers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    uuid VARCHAR(36) NOT NULL COMMENT 'UUID',
    wallet_address VARCHAR(100) NOT NULL COMMENT '钱包地址',
    `name` VARCHAR(100) NOT NULL COMMENT '提供者名称',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX idx_uuid (uuid),
    UNIQUE INDEX idx_wallet_address (wallet_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户聊天请求表
CREATE TABLE IF NOT EXISTS user_chat_requests (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    ip VARCHAR(100) NOT NULL COMMENT 'IP地址',
    conversation_uuid VARCHAR(100) NOT NULL COMMENT '会话ID',
    conversation_message_uuid VARCHAR(100) NOT NULL COMMENT '会话消息ID',
    model_id VARCHAR(100) NOT NULL COMMENT '模型ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_model_id (model_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


# 创建用户
insert into dev_app_infos (uuid, wallet_address, `name`, account_id, `description`, website, logo, `status`) values
('87de60d2-4113-11f0-a5b0-52e60342603b','******************************************','CodeGenX','codegen001','AI coding assistant for auto-generating and optimizing code in multiple languages.','','87de610c-4113-11f0-a5b0-52e60342603b',0),
('87de66bf-4113-11f0-a5b0-52e60342603b','******************************************','VoiceCraft','voicecraft','TTS tool for natural voice synthesis (podcasts, audiobooks)..','','87de66cf-4113-11f0-a5b0-52e60342603b',0),
('87de6880-4113-11f0-a5b0-52e60342603b','******************************************','PolyGlot','polyglot','Real-time multilingual translator (ASR + LLM).','','87de6891-4113-11f0-a5b0-52e60342603b',0),
('87de697f-4113-11f0-a5b0-52e60342603b','******************************************','StoryForge','storyforge','AI creative writing assistant for novels/scripts.','','87de698a-4113-11f0-a5b0-52e60342603b',0),
('87de6a46-4113-11f0-a5b0-52e60342603b','0x5784aA32459b345Ff0F188faA147D264b48F63ef','MathWhiz','mathwhiz','Solves math problems with step-by-step AI explanations.','','87de6a55-4113-11f0-a5b0-52e60342603b',0),
('87de6b18-4113-11f0-a5b0-52e60342603b','0x8122D810aC1965A9d45b9e55F7D7c48F8ed282EA','ArtPrompt','artprompt','Generates art prompts for AI image tools (DALL·E, SD).','','87de6b25-4113-11f0-a5b0-52e60342603b',0),
('87de6be9-4113-11f0-a5b0-52e60342603b','0xE4616B9fC9410cA8499E6E452A933D2E8fEc53b3','LegalEase','legalease','AI legal assistant for contracts/compliance.','','87de6bfa-4113-11f0-a5b0-52e60342603b',0),
('87de6cc2-4113-11f0-a5b0-52e60342603b','0xf0e5CF4Df4adAdeC93228226090D3f03503df527','MindMeld','mindmeld','AI mind-mapping tool for idea organization.','','87de6ccf-4113-11f0-a5b0-52e60342603b',0),
('87de793d-4113-11f0-a5b0-52e60342603b','0xd0950AA1F21868A872850ef08eEdF765806f25Bd','TutorAI','tutorai','Personalized learning assistant with quizzes/courses.','','87de7963-4113-11f0-a5b0-52e60342603b',0),
('87de7a78-4113-11f0-a5b0-52e60342603b','0x32A4d85B930f9D9423Cb0DC30c5EeE8054505e25','SalesBot','salesbot','AI sales coach for pitch generation.','','87de7a88-4113-11f0-a5b0-52e60342603b',0),
('87de7b47-4113-11f0-a5b0-52e60342603b','0x35cbfd295f98DBED650BB5C6Bd43167238a7bD8f','HealthGuard','healthguard','Symptom checker and medical advice AI.','','87de7b58-4113-11f0-a5b0-52e60342603b',0),
('87de7c09-4113-11f0-a5b0-52e60342603b','0xbf6faC74a41733316faf2e2474343fc5bb1fC9b8','MusicLyric','musiclyric','AI lyric generator for songs.','','87de7c24-4113-11f0-a5b0-52e60342603b',0),
('87de7cde-4113-11f0-a5b0-52e60342603b','0xBfebfBa80Be33AE9FAE7Fddc1F8d8Bd3433cD274','DebateMaster','debatemaster','AI debate coach for argument analysis.','','87de7cf3-4113-11f0-a5b0-52e60342603b',0),
('87de7d9c-4113-11f0-a5b0-52e60342603b','0x0c02627613a235f5d786C6F24fd28e4facfDE6c7','CareerPath','careerpath','AI career planner with skill recommendations.','','87de7da7-4113-11f0-a5b0-52e60342603b',0),
('87de7e1c-4113-11f0-a5b0-52e60342603b','0xDc3d54565a8ee002B3e95a8EA34f5173f4E7289C','RecipeGen','recipegen','AI recipe generator based on ingredients.','','87de7e27-4113-11f0-a5b0-52e60342603b',0),
('87de7e95-4113-11f0-a5b0-52e60342603b','0x4B18566a89F37B29EBb9372F7A0e8653F6145a36','NewsBrief','newsbrief','AI news summarizer for key insights.','','87de7e9e-4113-11f0-a5b0-52e60342603b',0),
('87de7f4b-4113-11f0-a5b0-52e60342603b','******************************************','ChatAdvisor','chatadvisor','AI customer service chatbot.','','87de7f5c-4113-11f0-a5b0-52e60342603b',0),
('87de8016-4113-11f0-a5b0-52e60342603b','******************************************','GamePlot','gameplot','AI game narrative designer.','','87de802a-4113-11f0-a5b0-52e60342603b',0),
('87de80b9-4113-11f0-a5b0-52e60342603b','******************************************','FinanceAI','financeai','AI stock/crypto market analyst.','','87de80c3-4113-11f0-a5b0-52e60342603b',0),
('87de8139-4113-11f0-a5b0-52e60342603b','******************************************','FitnessAI','fitnessai','Personalized workout planner.','','87de8141-4113-11f0-a5b0-52e60342603b',0),
('87de81d5-4113-11f0-a5b0-52e60342603b','******************************************','TravelGenie','travelgenie','AI travel itinerary generator.','','87de81e2-4113-11f0-a5b0-52e60342603b',0),
('87de82a3-4113-11f0-a5b0-52e60342603b','******************************************','MemeMaker','mememaker','AI meme generator with captions.','','87de82ac-4113-11f0-a5b0-52e60342603b',0),
('87de831f-4113-11f0-a5b0-52e60342603b','******************************************','PoemCraft','poemcraft','AI poetry writer (classic/modern).','','87de832a-4113-11f0-a5b0-52e60342603b',0),
('87de8398-4113-11f0-a5b0-52e60342603b','******************************************','CryptoInsight','cryptoinsight','AI crypto trading advisor.','','87de83a1-4113-11f0-a5b0-52e60342603b',0),
('87de8410-4113-11f0-a5b0-52e60342603b','******************************************','StudyBuddy','studybuddy','AI flashcard and quiz generator.','','87de8419-4113-11f0-a5b0-52e60342603b',0),
('87de8489-4113-11f0-a5b0-52e60342603b','******************************************','PetCareAI','petcareai','AI pet health/behavior advisor.','','87de8492-4113-11f0-a5b0-52e60342603b',0),
('87de850a-4113-11f0-a5b0-52e60342603b','******************************************','EcoTrack','ecotrack','AI carbon footprint calculator.','','87de8513-4113-11f0-a5b0-52e60342603b',0),
('87de85d4-4113-11f0-a5b0-52e60342603b','******************************************','VoiceClone','voiceclone','AI voice replicator for custom TTS.','','87de85e6-4113-11f0-a5b0-52e60342603b',0);

# 创建用户头像
insert into files (uuid, user_id, name, original_name, path, size, content,mime_type,`type`) VALUES
('87de610c-4113-11f0-a5b0-52e60342603b',0,'image_87de610c-4113-11f0-a5b0-52e60342603b','image_87de610c-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de610c-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de66cf-4113-11f0-a5b0-52e60342603b',0,'image_87de66cf-4113-11f0-a5b0-52e60342603b','image_87de66cf-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de66cf-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de6891-4113-11f0-a5b0-52e60342603b',0,'image_87de6891-4113-11f0-a5b0-52e60342603b','image_87de6891-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de6891-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de698a-4113-11f0-a5b0-52e60342603b',0,'image_87de698a-4113-11f0-a5b0-52e60342603b','image_87de698a-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de698a-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de6a55-4113-11f0-a5b0-52e60342603b',0,'image_87de6a55-4113-11f0-a5b0-52e60342603b','image_87de6a55-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de6a55-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de6b25-4113-11f0-a5b0-52e60342603b',0,'image_87de6b25-4113-11f0-a5b0-52e60342603b','image_87de6b25-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de6b25-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de6bfa-4113-11f0-a5b0-52e60342603b',0,'image_87de6bfa-4113-11f0-a5b0-52e60342603b','image_87de6bfa-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de6bfa-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de6ccf-4113-11f0-a5b0-52e60342603b',0,'image_87de6ccf-4113-11f0-a5b0-52e60342603b','image_87de6ccf-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de6ccf-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7963-4113-11f0-a5b0-52e60342603b',0,'image_87de7963-4113-11f0-a5b0-52e60342603b','image_87de7963-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7963-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7a88-4113-11f0-a5b0-52e60342603b',0,'image_87de7a88-4113-11f0-a5b0-52e60342603b','image_87de7a88-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7a88-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7b58-4113-11f0-a5b0-52e60342603b',0,'image_87de7b58-4113-11f0-a5b0-52e60342603b','image_87de7b58-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7b58-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7c24-4113-11f0-a5b0-52e60342603b',0,'image_87de7c24-4113-11f0-a5b0-52e60342603b','image_87de7c24-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7c24-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7cf3-4113-11f0-a5b0-52e60342603b',0,'image_87de7cf3-4113-11f0-a5b0-52e60342603b','image_87de7cf3-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7cf3-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7da7-4113-11f0-a5b0-52e60342603b',0,'image_87de7da7-4113-11f0-a5b0-52e60342603b','image_87de7da7-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7da7-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7e27-4113-11f0-a5b0-52e60342603b',0,'image_87de7e27-4113-11f0-a5b0-52e60342603b','image_87de7e27-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7e27-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7e9e-4113-11f0-a5b0-52e60342603b',0,'image_87de7e9e-4113-11f0-a5b0-52e60342603b','image_87de7e9e-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7e9e-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de7f5c-4113-11f0-a5b0-52e60342603b',0,'image_87de7f5c-4113-11f0-a5b0-52e60342603b','image_87de7f5c-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de7f5c-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de802a-4113-11f0-a5b0-52e60342603b',0,'image_87de802a-4113-11f0-a5b0-52e60342603b','image_87de802a-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de802a-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de80c3-4113-11f0-a5b0-52e60342603b',0,'image_87de80c3-4113-11f0-a5b0-52e60342603b','image_87de80c3-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de80c3-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de8141-4113-11f0-a5b0-52e60342603b',0,'image_87de8141-4113-11f0-a5b0-52e60342603b','image_87de8141-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de8141-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de81e2-4113-11f0-a5b0-52e60342603b',0,'image_87de81e2-4113-11f0-a5b0-52e60342603b','image_87de81e2-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de81e2-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de82ac-4113-11f0-a5b0-52e60342603b',0,'image_87de82ac-4113-11f0-a5b0-52e60342603b','image_87de82ac-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de82ac-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de832a-4113-11f0-a5b0-52e60342603b',0,'image_87de832a-4113-11f0-a5b0-52e60342603b','image_87de832a-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de832a-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de83a1-4113-11f0-a5b0-52e60342603b',0,'image_87de83a1-4113-11f0-a5b0-52e60342603b','image_87de83a1-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de83a1-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de8419-4113-11f0-a5b0-52e60342603b',0,'image_87de8419-4113-11f0-a5b0-52e60342603b','image_87de8419-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de8419-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de8492-4113-11f0-a5b0-52e60342603b',0,'image_87de8492-4113-11f0-a5b0-52e60342603b','image_87de8492-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de8492-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de8513-4113-11f0-a5b0-52e60342603b',0,'image_87de8513-4113-11f0-a5b0-52e60342603b','image_87de8513-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de8513-4113-11f0-a5b0-52e60342603b',0,'','image/png','image'),
('87de85e6-4113-11f0-a5b0-52e60342603b',0,'image_87de85e6-4113-11f0-a5b0-52e60342603b','image_87de85e6-4113-11f0-a5b0-52e60342603b','2025-06-04/image_87de85e6-4113-11f0-a5b0-52e60342603b',0,'','image/png','image');


# 创建供应商
insert into topai_model_providers (uuid, wallet_address, name) VALUES
(uuid(),'******************************************','devAlex92'),
(uuid(),'******************************************','CodeMara77'),
(uuid(),'******************************************','neoByteX'),
(uuid(),'******************************************','quantLeo08'),
(uuid(),'******************************************','JunoCoder3'),
(uuid(),'******************************************','sysRay41'),
(uuid(),'******************************************','tinkerZane'),
(uuid(),'******************************************','byteKira24'),
(uuid(),'******************************************','dataNicoX1'),
(uuid(),'******************************************','coreEmma09'),
(uuid(),'******************************************','HexNova17'),
(uuid(),'******************************************','logicSage33'),
(uuid(),'******************************************','aiLiam_7'),
(uuid(),'******************************************','patchKai22');

