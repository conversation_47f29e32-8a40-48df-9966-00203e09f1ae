// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelSeriesRelationRepository is an autogenerated mock type for the TopaiModelSeriesRelationRepository type
type TopaiModelSeriesRelationRepository struct {
	mock.Mock
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *TopaiModelSeriesRelationRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelSeriesRelation, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.TopaiModelSeriesRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelSeriesRelation, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelSeriesRelation); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelSeriesRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelId provides a mock function with given fields: ctx, topaiModelId
func (_m *TopaiModelSeriesRelationRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelSeriesRelation, error) {
	ret := _m.Called(ctx, topaiModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelId")
	}

	var r0 *repository.TopaiModelSeriesRelation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModelSeriesRelation, error)); ok {
		return rf(ctx, topaiModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModelSeriesRelation); ok {
		r0 = rf(ctx, topaiModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelSeriesRelation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, topaiModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelSeriesRelationRepository creates a new instance of TopaiModelSeriesRelationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelSeriesRelationRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelSeriesRelationRepository {
	mock := &TopaiModelSeriesRelationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
