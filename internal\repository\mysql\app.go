package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type devAppInfoRepository struct {
	db *gorm.DB
}

func NewDevAppInfoRepository(db *gorm.DB) repository.DevAppInfoRepository {
	return &devAppInfoRepository{db: db}
}

func (r *devAppInfoRepository) GetAll(ctx context.Context) ([]*repository.DevAppInfo, error) {
	var apps []*repository.DevAppInfo
	if err := r.db.WithContext(ctx).Where("is_deleted = 0").Find(&apps).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return apps, nil
}

func (r *devAppInfoRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.DevAppInfo, error) {
	var apps []*repository.DevAppInfo
	if err := r.db.WithContext(ctx).Where("id IN ? AND is_deleted = 0", ids).Find(&apps).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return apps, nil
}

func (r *devAppInfoRepository) GetByUuid(ctx context.Context, uuid string) (*repository.DevAppInfo, error) {
	var app repository.DevAppInfo
	if err := r.db.WithContext(ctx).Where("uuid = ? AND is_deleted = 0", uuid).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

func (r *devAppInfoRepository) GetByID(ctx context.Context, id uint) (*repository.DevAppInfo, error) {
	var app repository.DevAppInfo
	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = 0", id).First(&app).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &app, nil
}

func (r *devAppInfoRepository) Create(ctx context.Context, app *repository.DevAppInfo) error {
	if err := r.db.WithContext(ctx).Create(&app).Error; err != nil {
		return err
	}
	return nil
}

func (r *devAppInfoRepository) Update(ctx context.Context, app *repository.DevAppInfo) error {
	if err := r.db.WithContext(ctx).Save(&app).Error; err != nil {
		return err
	}
	return nil
}

func (r *devAppInfoRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Table(repository.DevAppInfo{}.TableName()).Where("id = ?", id).Update("is_deleted", 1).Error; err != nil {
		return err
	}
	return nil
}

func (r *devAppInfoRepository) GetAllByUserID(ctx context.Context, userID uint) ([]*repository.DevAppInfo, error) {
	var apps []*repository.DevAppInfo
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_deleted = 0", userID).Find(&apps).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return apps, nil
}

type devAppKeyRepository struct {
	db *gorm.DB
}

func NewDevAppKeyRepository(db *gorm.DB) repository.DevAppKeyRepository {
	return &devAppKeyRepository{db: db}
}

func (r *devAppKeyRepository) GetByDevAppId(ctx context.Context, devAppId uint) ([]*repository.DevAppKey, error) {
	var keys []*repository.DevAppKey
	if err := r.db.WithContext(ctx).Where("dev_app_id = ? AND is_deleted = 0", devAppId).Find(&keys).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return keys, nil
}

func (r *devAppKeyRepository) GetByKey(ctx context.Context, key string) (*repository.DevAppKey, error) {
	var devAppKey repository.DevAppKey
	if err := r.db.WithContext(ctx).Where("`key` = ?", key).First(&devAppKey).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &devAppKey, nil
}

func (r *devAppKeyRepository) GetByDevAppIds(ctx context.Context, devAppIds []uint) ([]*repository.DevAppKey, error) {
	var keys []*repository.DevAppKey
	if err := r.db.WithContext(ctx).Where("dev_app_id IN ? AND is_deleted = 0", devAppIds).Find(&keys).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return keys, nil
}

func (r *devAppKeyRepository) Create(ctx context.Context, key *repository.DevAppKey) error {
	if err := r.db.WithContext(ctx).Create(&key).Error; err != nil {
		return err
	}
	return nil
}

func (r *devAppKeyRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Table(repository.DevAppKey{}.TableName()).Where("id = ?", id).Update("is_deleted", 1).Error; err != nil {
		return err
	}
	return nil
}

func (r *devAppKeyRepository) GetByDevAppIdAndKey(ctx context.Context, devAppId uint, key string) (*repository.DevAppKey, error) {
	var devAppKey repository.DevAppKey
	if err := r.db.WithContext(ctx).Where("dev_app_id = ? AND `key` = ? AND is_deleted = 0", devAppId, key).First(&devAppKey).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &devAppKey, nil
}

func (r *devAppKeyRepository) GetByDevAppIdAndName(ctx context.Context, devAppId uint, name string) (*repository.DevAppKey, error) {
	var devAppKey repository.DevAppKey
	if err := r.db.WithContext(ctx).Where("dev_app_id = ? AND `name` = ? AND is_deleted = 0", devAppId, name).First(&devAppKey).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &devAppKey, nil
}
