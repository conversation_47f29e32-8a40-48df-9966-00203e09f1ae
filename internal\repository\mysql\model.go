package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type llmModelConfigRepository struct {
	db *gorm.DB
}

func NewLLMModelConfigRepository(db *gorm.DB) repository.LLMModelConfigRepository {
	return &llmModelConfigRepository{db: db}
}

func (r *llmModelConfigRepository) GetByID(ctx context.Context, id uint) (*repository.LLMModelConfig, error) {
	var model repository.LLMModelConfig
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

func (r *llmModelConfigRepository) GetByModelID(ctx context.Context, modelID string) (*repository.LLMModelConfig, error) {
	var model repository.LLMModelConfig
	if err := r.db.WithContext(ctx).Where("model_id = ?", modelID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

func (r *llmModelConfigRepository) Create(ctx context.Context, model *repository.LLMModelConfig) error {
	return r.db.WithContext(ctx).Create(model).Error
}

func (r *llmModelConfigRepository) Update(ctx context.Context, model *repository.LLMModelConfig) error {
	return r.db.WithContext(ctx).Save(model).Error
}

func (r *llmModelConfigRepository) GetAll(ctx context.Context) ([]*repository.LLMModelConfig, error) {
	var models []*repository.LLMModelConfig
	if err := r.db.WithContext(ctx).Find(&models).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

type remoterModelInfoRepository struct {
	db *gorm.DB
}

func NewRemoterModelInfoRepository(db *gorm.DB) repository.RemoterModelInfoRepository {
	return &remoterModelInfoRepository{db: db}
}

func (r *remoterModelInfoRepository) GetAll(ctx context.Context) ([]*repository.RemoterModelInfo, error) {
	var models []*repository.RemoterModelInfo
	if err := r.db.WithContext(ctx).Where("is_deleted = 0").Find(&models).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *remoterModelInfoRepository) GetByModelName(ctx context.Context, modelName string) (*repository.RemoterModelInfo, error) {
	var model repository.RemoterModelInfo
	if err := r.db.WithContext(ctx).Where("model_name = ? AND is_deleted = 0", modelName).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

type ttsModelVoiceRepository struct {
	db *gorm.DB
}

func NewTTSModelVoiceRepository(db *gorm.DB) repository.TTSModelVoiceRepository {
	return &ttsModelVoiceRepository{db: db}
}

func (r *ttsModelVoiceRepository) GetByModelName(ctx context.Context, modelName string) ([]*repository.TTSModelVoice, error) {
	var voices []*repository.TTSModelVoice
	if err := r.db.WithContext(ctx).Where("model_name = ? AND is_deleted = 0", modelName).Find(&voices).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return voices, nil
}

func (r *ttsModelVoiceRepository) GetByModelNameAndName(ctx context.Context, modelName, name string) (*repository.TTSModelVoice, error) {
	var voice repository.TTSModelVoice
	if err := r.db.WithContext(ctx).Where("model_name = ? AND name = ? AND is_deleted = 0", modelName, name).First(&voice).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &voice, nil
}

func (r *ttsModelVoiceRepository) CreateBatch(ctx context.Context, voices []*repository.TTSModelVoice) error {
	return r.db.WithContext(ctx).Create(voices).Error
}

func (r *ttsModelVoiceRepository) DeleteByModelName(ctx context.Context, modelName string) error {
	return r.db.WithContext(ctx).Model(&repository.TTSModelVoice{}).Where("model_name = ?", modelName).Update("is_deleted", 1).Error
}
