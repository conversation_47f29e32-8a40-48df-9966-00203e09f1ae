package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// UserHandlerTestEnv 用户处理器测试环境
type UserHandlerTestEnv struct {
	T       *testing.T
	Router  *gin.Engine
	Handler *UserHandler
	Service *MockServiceAPI
}

// NewUserHandlerTestEnv 创建用户处理器测试环境
func NewUserHandlerTestEnv(t *testing.T) *UserHandlerTestEnv {
	router := gin.New()
	svc := &MockServiceAPI{}
	handler := &UserHandler{
		// 根据实际UserHandler结构设置字段
		// service: svc,
	}

	env := &UserHandlerTestEnv{
		T:       t,
		Router:  router,
		Handler: handler,
		Service: svc,
	}

	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *UserHandlerTestEnv) Cleanup() {
	e.Service.AssertExpectations(e.T)
}

// TestUserHandler_GetUserInfo 测试获取用户信息
func TestUserHandler_GetUserInfo(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*UserHandlerTestEnv)
		userID         string
		headers        map[string]string
		expectedStatus int
		verify         func(*UserHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取用户信息",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望获取用户信息成功
				// env.Service.On("GetUserByID", mock.AnythingOfType("*gin.Context"), uint(1)).
				//     Return(UserInfo{ID: 1, Username: "testuser"}, nil)
			},
			userID: "1",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-用户不存在",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望用户不存在错误
				// env.Service.On("GetUserByID", mock.AnythingOfType("*gin.Context"), uint(999)).
				//     Return(nil, errors.New("user not found"))
			},
			userID: "999",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusNotFound,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusNotFound, w.Code)
			},
		},
		{
			name: "TC3-无效的用户ID",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为参数解析会失败
			},
			userID: "invalid",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-未授权访问",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为中间件会处理
			},
			userID:         "1",
			headers:        map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewUserHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/users/:id", env.Handler.GetUserInfo)

			// 执行setup
			tt.setup(env)

			// 创建请求
			url := fmt.Sprintf("/users/%s", tt.userID)
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestUserHandler_UpdateUserInfo 测试更新用户信息
func TestUserHandler_UpdateUserInfo(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*UserHandlerTestEnv)
		userID         string
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		verify         func(*UserHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功更新用户信息",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望更新成功
				// env.Service.On("UpdateUser", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("UpdateUserRequest")).
				//     Return(nil)
			},
			userID: "1",
			requestBody: map[string]interface{}{
				"username": "newusername",
				"email":    "<EMAIL>",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-无效的邮箱格式",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			userID: "1",
			requestBody: map[string]interface{}{
				"username": "newusername",
				"email":    "invalid-email",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC3-权限不足",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望权限错误
				// env.Service.On("UpdateUser", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("UpdateUserRequest")).
				//     Return(errors.New("permission denied"))
			},
			userID: "2", // 尝试更新其他用户
			requestBody: map[string]interface{}{
				"username": "newusername",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token", // 当前用户ID为1
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC4-空的请求体",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为没有更新内容
			},
			userID:      "1",
			requestBody: map[string]interface{}{},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewUserHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.PUT("/users/:id", env.Handler.UpdateUserInfo)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			url := fmt.Sprintf("/users/%s", tt.userID)
			req := httptest.NewRequest(http.MethodPut, url, bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestUserHandler_ChangePassword 测试修改密码
func TestUserHandler_ChangePassword(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*UserHandlerTestEnv)
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		verify         func(*UserHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功修改密码",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望修改密码成功
				// env.Service.On("ChangePassword", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("ChangePasswordRequest")).
				//     Return(nil)
			},
			requestBody: map[string]interface{}{
				"old_password": "oldpassword123",
				"new_password": "newpassword123",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-旧密码错误",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望旧密码错误
				// env.Service.On("ChangePassword", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("ChangePasswordRequest")).
				//     Return(errors.New("invalid old password"))
			},
			requestBody: map[string]interface{}{
				"old_password": "wrongpassword",
				"new_password": "newpassword123",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC3-新密码太短",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"old_password": "oldpassword123",
				"new_password": "123", // 太短
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-缺少必需字段",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"old_password": "oldpassword123",
				// 缺少new_password
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC5-新旧密码相同",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"old_password": "samepassword123",
				"new_password": "samepassword123",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewUserHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/users/change-password", env.Handler.ChangePassword)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/users/change-password", bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestUserHandler_GetUserList 测试获取用户列表
func TestUserHandler_GetUserList(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*UserHandlerTestEnv)
		queryParams    map[string]string
		headers        map[string]string
		expectedStatus int
		verify         func(*UserHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取用户列表",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望获取列表成功
				// env.Service.On("GetUserList", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("GetUserListRequest")).
				//     Return(UserListResponse{Users: []User{...}}, nil)
			},
			queryParams: map[string]string{
				"page":     "1",
				"pageSize": "10",
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token", // 管理员Token
			},
			expectedStatus: http.StatusOK,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-非管理员访问",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望权限错误
				// env.Service.On("GetUserList", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("GetUserListRequest")).
				//     Return(nil, errors.New("permission denied"))
			},
			queryParams: map[string]string{
				"page":     "1",
				"pageSize": "10",
			},
			headers: map[string]string{
				"Authorization": "Bearer user_token", // 普通用户Token
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC3-无效的分页参数",
			setup: func(env *UserHandlerTestEnv) {
				// 不需要Mock，因为参数验证会失败
			},
			queryParams: map[string]string{
				"page":     "0", // 无效页码
				"pageSize": "10",
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-搜索用户",
			setup: func(env *UserHandlerTestEnv) {
				// Mock期望搜索成功
				// env.Service.On("SearchUsers", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SearchUsersRequest")).
				//     Return(UserListResponse{Users: []User{...}}, nil)
			},
			queryParams: map[string]string{
				"page":     "1",
				"pageSize": "10",
				"search":   "testuser",
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *UserHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewUserHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/users", env.Handler.GetUserList)

			// 执行setup
			tt.setup(env)

			// 构建URL和查询参数
			url := "/users"
			if len(tt.queryParams) > 0 {
				url += "?"
				first := true
				for key, value := range tt.queryParams {
					if !first {
						url += "&"
					}
					url += fmt.Sprintf("%s=%s", key, value)
					first = false
				}
			}

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// BenchmarkUserHandler_GetUserInfo 获取用户信息性能基准测试
func BenchmarkUserHandler_GetUserInfo(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewUserHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.GET("/users/:id", env.Handler.GetUserInfo)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/users/1", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}

// BenchmarkUserHandler_UpdateUserInfo 更新用户信息性能基准测试
func BenchmarkUserHandler_UpdateUserInfo(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewUserHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.PUT("/users/:id", env.Handler.UpdateUserInfo)

	requestBody := map[string]interface{}{
		"username": "benchuser",
		"email":    "<EMAIL>",
	}

	jsonBody, _ := json.Marshal(requestBody)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		bodyReader := bytes.NewReader(jsonBody)
		req := httptest.NewRequest(http.MethodPut, "/users/1", bodyReader)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}
