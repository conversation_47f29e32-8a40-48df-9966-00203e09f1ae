package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"
	"unsafe"

	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/crypto/hkdf"
)

// SecureErase 安全擦除字节切片
func SecureErase(data []byte) {
	if data == nil {
		return
	}

	for i := range data {
		data[i] = 0
	}

	// 确保编译器不会优化掉擦除操作
	keepAlive(data)
}

func keepAlive(data []byte) {
	// 使用runtime.KeepAlive防止优化
	_ = data
	if len(data) > 0 {
		runtime_keepAlive(unsafe.Pointer(&data[0]))
	}
}

//go:noinline
func runtime_keepAlive(p unsafe.Pointer) {
	// 空函数，用于防止优化
}

type EncryptedKey struct {
	PublicKey     string `json:"public_key"`
	PrivateKey    string `json:"private_key"`
	Algorithm     string `json:"algorithm"`
	Version       int    `json:"version"`
	EncryptedData string `json:"encrypted_data"`
	KeySalt       string `json:"key_salt"`
}

type encryptedData struct {
	IV         []byte `json:"iv"`
	Ciphertext []byte `json:"ct"`
	Version    int    `json:"v"`
}

func (e *encryptedData) ToBase64() (string, error) {
	encBase64, err := json.Marshal(e)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(encBase64), nil
}

func GenerateRandomPrivateKey(masterKeyBase64 string, addtionalData string) (*EncryptedKey, error) {
	masterKey, err := base64.StdEncoding.DecodeString(masterKeyBase64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode master key: %v", err)
	}

	// 生成随机盐值
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return nil, fmt.Errorf("failed to generate random salt: %v", err)
	}

	// 派生账户密钥
	accountKey, err := deriveAccountKey(masterKey, addtionalData, salt)
	if err != nil {
		return nil, fmt.Errorf("failed to derive account key: %v", err)
	}
	defer SecureErase(masterKey)

	// AES-GCM加密
	block, err := aes.NewCipher(accountKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %v", err)
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	iv := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(iv); err != nil {
		return nil, fmt.Errorf("failed to generate random IV: %v", err)
	}

	// 生成影子账户私钥
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate private key: %v", err)
	}

	// 生成影子账户公钥
	publicAddress := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()

	// 加密私钥
	privKeyBytes := crypto.FromECDSA(privateKey)
	ciphertext := gcm.Seal(nil, iv, privKeyBytes, []byte(addtionalData))

	encdata := &encryptedData{
		IV:         iv,
		Ciphertext: ciphertext,
		Version:    1,
	}
	encBase64, err := encdata.ToBase64()
	if err != nil {
		return nil, fmt.Errorf("failed to encode encrypted data: %v", err)
	}

	encryptedKey := &EncryptedKey{
		PublicKey:     publicAddress,
		PrivateKey:    "0x" + hex.EncodeToString(privKeyBytes),
		Algorithm:     "aes-256-gcm", // 固定为aes-256-gcm
		Version:       1,
		EncryptedData: encBase64,
		KeySalt:       base64.StdEncoding.EncodeToString(salt),
	}
	return encryptedKey, nil
}

func DecryptPrivateKey(masterKeyBase64 string, encryptedKey *EncryptedKey, addtionalData string) (*ecdsa.PrivateKey, error) {
	// 解析加密数据
	data, err := base64.StdEncoding.DecodeString(encryptedKey.EncryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode encrypted data: %v", err)
	}

	var ed *encryptedData
	if err := json.Unmarshal(data, &ed); err != nil {
		return nil, fmt.Errorf("failed to unmarshal encrypted data: %v", err)
	}

	// 解析盐值
	salt, err := base64.StdEncoding.DecodeString(encryptedKey.KeySalt)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key salt: %v", err)
	}

	// 解释私钥
	masterKey, err := base64.StdEncoding.DecodeString(masterKeyBase64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode master key: %v", err)
	}
	defer SecureErase(masterKey)

	// 派生账户密钥
	accountKey, err := deriveAccountKey(masterKey, addtionalData, salt)
	if err != nil {
		return nil, fmt.Errorf("failed to derive account key: %v", err)
	}
	defer SecureErase(accountKey)

	block, err := aes.NewCipher(accountKey)
	if err != nil {
		return nil, fmt.Errorf("failed to open ciphertext: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	priKeyBytes, err := gcm.Open(nil, ed.IV, ed.Ciphertext, []byte(addtionalData))
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %v", err)
	}
	defer SecureErase(priKeyBytes)

	privateKey, err := crypto.ToECDSA(priKeyBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt private key: %v", err)
	}

	return privateKey, nil
}

func deriveAccountKey(masterKey []byte, userUUID string, salt []byte) ([]byte, error) {
	hkdf := hkdf.New(sha256.New, masterKey, salt, []byte(userUUID))
	derivedKey := make([]byte, 32) // AES-256需要32字节
	if _, err := io.ReadFull(hkdf, derivedKey); err != nil {
		return nil, fmt.Errorf("HKDF derivation failed: %v", err)
	}
	return derivedKey, nil
}

// ProviderAuth 模型提供者认证相关结构
type ProviderAuth struct {
	Nonce      string `json:"nonce"`       // 随机字符串
	WalletAddr string `json:"wallet_addr"` // 钱包地址
	Signature  string `json:"signature"`   // 签名
	Timestamp  int64  `json:"timestamp"`   // 时间戳
}

// GenerateNonce 生成随机字符串用于签名
func GenerateNonce() string {
	// 生成32字节的随机数
	nonce := make([]byte, 32)
	_, err := rand.Read(nonce)
	if err != nil {
		// 如果随机数生成失败，使用时间戳作为备选
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(nonce)
}

// VerifyProviderSignature 验证模型提供者的钱包签名
func VerifyProviderSignature(nonce, walletAddr, signature string) (bool, error) {
	// 1. 解析签名
	if !strings.HasPrefix(signature, "0x") {
		return false, fmt.Errorf("signature must start with 0x")
	}

	sigBytes, err := hex.DecodeString(signature[2:]) // 去掉0x前缀
	if err != nil {
		return false, fmt.Errorf("invalid signature format: %w", err)
	}

	// 2. 检查签名长度（应该是65字节）
	if len(sigBytes) != 65 {
		return false, fmt.Errorf("signature must be 65 bytes, got %d", len(sigBytes))
	}

	// 3. 处理以太坊签名的V值（使用旧代码的逻辑）
	originalV := sigBytes[64]

	// 对于 chainID 1023，v 值应该是 27 或 28
	if originalV < 27 {
		originalV += 27
	}
	// 转换为 0/1
	sigBytes[64] = originalV - 27

	// 4. 构造消息格式（使用旧代码的格式）
	// 旧代码: msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(req.Message), req.Message)
	message := nonce
	msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)
	msgHash := crypto.Keccak256Hash([]byte(msg))

	// 5. 恢复公钥
	pubKey, err := crypto.SigToPub(msgHash.Bytes(), sigBytes)
	if err != nil {
		return false, fmt.Errorf("failed to recover public key: %w", err)
	}

	// 6. 从公钥获取地址
	recoveredAddr := crypto.PubkeyToAddress(*pubKey)

	// 7. 验证地址是否匹配（忽略大小写）
	return strings.EqualFold(recoveredAddr.Hex(), walletAddr), nil
}
