package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// NonceInfo Nonce信息
type NonceInfo struct {
	Nonce      string    `json:"nonce"`
	WalletAddr string    `json:"wallet_addr"`
	CreatedAt  time.Time `json:"created_at"`
	Used       bool      `json:"used"`
}

type ProviderHandler struct {
	svc *service.Api
	log *logger.ModuleLogger
	// Nonce缓存
	nonceCache map[string]*NonceInfo
	nonceMutex sync.RWMutex
	maxCache   int // 缓存上限
}

func NewProviderHandler(svc *service.Api) *ProviderHandler {
	return &ProviderHandler{
		log:        logger.GetLogger("provider-handler"),
		svc:        svc,
		nonceCache: make(map[string]*NonceInfo),
		maxCache:   1000, // 设置缓存上限为1000个
	}
}

// generateRandomNonce 生成随机Nonce
func (h *ProviderHandler) generateRandomNonce() string {
	nonce := make([]byte, 32)
	_, err := rand.Read(nonce)
	if err != nil {
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(nonce)
}

// addNonceToCache 添加Nonce到缓存
func (h *ProviderHandler) addNonceToCache(nonce, walletAddr string) {
	h.nonceMutex.Lock()
	defer h.nonceMutex.Unlock()

	// 清理过期和已使用的Nonce
	h.cleanupExpiredNonces()

	// 检查缓存上限
	if len(h.nonceCache) >= h.maxCache {
		// 删除最旧的几个条目
		h.removeOldestNonces(100)
	}

	h.nonceCache[nonce] = &NonceInfo{
		Nonce:      nonce,
		WalletAddr: walletAddr,
		CreatedAt:  time.Now(),
		Used:       false,
	}
}

// validateAndUseNonce 验证并使用Nonce
func (h *ProviderHandler) validateAndUseNonce(nonce, walletAddr string) (bool, error) {
	h.nonceMutex.Lock()
	defer h.nonceMutex.Unlock()

	info, exists := h.nonceCache[nonce]
	if !exists {
		return false, nil // Nonce不存在
	}

	// 检查是否已使用
	if info.Used {
		return false, nil // Nonce已使用
	}

	// 检查是否过期（5分钟）
	if time.Since(info.CreatedAt) > 5*time.Minute {
		delete(h.nonceCache, nonce)
		return false, nil // Nonce已过期
	}

	// 检查钱包地址是否匹配
	if info.WalletAddr != walletAddr {
		return false, nil // 钱包地址不匹配
	}

	// 标记为已使用并删除
	delete(h.nonceCache, nonce)

	return true, nil
}

// cleanupExpiredNonces 清理过期的Nonce
func (h *ProviderHandler) cleanupExpiredNonces() {
	now := time.Now()
	for nonce, info := range h.nonceCache {
		// 删除超过5分钟的Nonce或已使用的Nonce
		if now.Sub(info.CreatedAt) > 5*time.Minute || info.Used {
			delete(h.nonceCache, nonce)
		}
	}
}

// removeOldestNonces 删除最旧的Nonce
func (h *ProviderHandler) removeOldestNonces(count int) {
	// 按创建时间排序，删除最旧的
	type nonceWithTime struct {
		nonce string
		time  time.Time
	}

	var nonces []nonceWithTime
	for nonce, info := range h.nonceCache {
		nonces = append(nonces, nonceWithTime{nonce: nonce, time: info.CreatedAt})
	}

	// 按时间排序（最旧的在前）
	for i := 0; i < len(nonces) && i < count; i++ {
		delete(h.nonceCache, nonces[i].nonce)
	}
}

// GenerateLoginNonce 生成登录随机字符串
// @Summary 生成登录随机字符串
// @Description 为模型提供者生成用于钱包签名的随机字符串，需要提供钱包地址
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Param wallet_addr query string true "钱包地址"
// @Success 200 {object} object{nonce=string} "成功"
// @Failure 400 {string} string "钱包地址缺失"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/nonce [get]
func (h *ProviderHandler) GenerateLoginNonce(c *gin.Context) {
	// 从查询参数获取钱包地址
	walletAddr := c.Query("wallet_addr")
	if walletAddr == "" {
		api.Fail(c, api.CodeClientError, "Wallet address is required", api.NewClientError("Wallet address is required"))
		return
	}

	// 生成随机Nonce
	nonce := h.generateRandomNonce()
	nonce = fmt.Sprintf("code:%s-%s", nonce, walletAddr)

	// 添加到缓存
	h.addNonceToCache(nonce, walletAddr)

	api.Success(c, gin.H{
		"nonce": nonce,
	})
}

// Login 钱包签名登录
// @Summary 钱包签名登录
// @Description 模型提供者使用钱包签名进行登录验证
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Param request body service.ProviderAuthRequest true "登录请求参数"
// @Success 200 {object} service.ProviderAuthResponse "登录成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "Nonce无效或已过期"
// @Failure 401 {string} string "登录失败"
// @Router /api/v1/provider/login [post]
func (h *ProviderHandler) Login(c *gin.Context) {
	var req service.ProviderAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "Invalid request parameters", api.NewClientError(err.Error()))
		return
	}
	h.log.Info("Login", zap.Any("req", req))

	// 验证并消费Nonce
	valid, err := h.validateAndUseNonce(req.Nonce, req.WalletAddr)
	if err != nil {
		api.Fail(c, api.CodeServerError, "Failed to validate nonce", err)
		return
	}
	if !valid {
		api.Fail(c, api.CodeUnauthorized, "Invalid or expired nonce", api.NewClientError("Invalid or expired nonce"))
		return
	}

	resp, err := h.svc.Provider.Login(c.Request.Context(), &req)
	if err != nil {
		h.log.Error("Login failed", zap.Error(err))
		api.Fail(c, api.CodeUnauthorized, "Login failed", err)
		return
	}

	api.Success(c, resp)
}

// GetProviderInfo 获取提供者信息
// @Summary 获取提供者信息
// @Description 获取当前登录模型提供者的基本信息
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} service.ProviderInfo "成功"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/info [get]
func (h *ProviderHandler) GetProviderInfo(c *gin.Context) {
	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}

	api.Success(c, providerInfo)
}

// UpdateProviderInfo 更新提供者信息
// @Summary 更新提供者信息
// @Description 更新当前登录模型提供者的基本信息
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body service.UpdateProviderInfoRequest true "更新请求参数"
// @Success 200 {object} object{message=string} "更新成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/info [post]
func (h *ProviderHandler) UpdateProviderInfo(c *gin.Context) {
	var req service.UpdateProviderInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "Invalid request parameters", api.NewClientError(err.Error()))
		return
	}

	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}

	err := h.svc.Provider.UpdateProviderInfo(c.Request.Context(), providerInfo, &req)
	if err != nil {
		h.log.Error("Failed to update provider info", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Failed to update provider info", err)
		return
	}

	api.Success(c, nil)
}

// GetProviderModels 获取提供者的模型列表
// @Summary 获取提供者的模型列表
// @Description 获取当前登录模型提供者上传的所有模型列表
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int true "页码,默认1"
// @Param limit query int true "每页数量,默认20"
// @Success 200 {object} service.ProviderModelList "成功"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/models [get]
func (h *ProviderHandler) GetProviderModels(c *gin.Context) {
	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil {
		api.Fail(c, api.CodeClientError, "Invalid page", api.NewClientError("Invalid page"))
		return
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if err != nil {
		api.Fail(c, api.CodeClientError, "Invalid limit", api.NewClientError("Invalid limit"))
		return
	}

	models, err := h.svc.Provider.GetProviderModels(c.Request.Context(), providerInfo, page, limit)
	if err != nil {
		h.log.Error("Failed to get provider models", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Failed to get provider models", err)
		return
	}

	api.Success(c, models)
}

// UpdateModelExt 更新模型扩展信息
// @Summary 更新模型扩展信息
// @Description 更新指定模型的扩展描述信息
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param chain_model_id path int true "链上模型ID"
// @Param request body service.UpdateModelExtRequest true "更新请求参数"
// @Success 200 {object} object{message=string} "更新成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/models/{chain_model_id} [post]
func (h *ProviderHandler) UpdateModelExt(c *gin.Context) {
	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}

	// 获取模型ID参数
	chainModelIdStr := c.Param("chain_model_id")
	chainModelId, err := strconv.ParseUint(chainModelIdStr, 10, 32)
	if err != nil {
		api.Fail(c, api.CodeClientError, "Invalid model ID", api.NewClientError("Invalid model ID"))
		return
	}

	var req service.UpdateModelExtRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "Invalid request parameters", api.NewClientError(err.Error()))
		return
	}

	err = h.svc.Provider.UpdateModelExt(c.Request.Context(), providerInfo, uint(chainModelId), &req)
	if err != nil {
		h.log.Error("Failed to update model ext", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Failed to update model ext", err)
		return
	}

	api.Success(c, gin.H{
		"message": "Model ext updated successfully",
	})
}

// GetModelInfo 获取模型信息
// @Summary 获取模型信息
// @Description 获取指定模型的信息
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param chain_model_id path int true "链上模型ID"
// @Success 200 {object} service.ProviderModelInfo "成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/models/{chain_model_id} [get]
func (h *ProviderHandler) GetModelInfo(c *gin.Context) {
	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}

	chainModelIdStr := c.Param("chain_model_id")
	chainModelId, err := strconv.ParseUint(chainModelIdStr, 10, 32)
	if err != nil {
		api.Fail(c, api.CodeClientError, "Invalid model ID", api.NewClientError("Invalid model ID"))
		return
	}

	modelInfo, err := h.svc.Provider.GetProviderModelInfo(c.Request.Context(), providerInfo, uint(chainModelId))
	if err != nil {
		h.log.Error("Failed to get model info", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Failed to get model info", err)
		return
	}
	api.Success(c, modelInfo)
}

// GetAllCategories 获取所有分类
// @Summary 获取所有分类
// @Description 获取所有分类
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} []service.TopaiModelCategoryOP "成功"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/categories [get]
func (h *ProviderHandler) GetAllCategories(c *gin.Context) {
	categories, err := h.svc.Provider.GetAllCategories(c.Request.Context())
	if err != nil {
		h.log.Error("Failed to get all categories", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Failed to get all categories", err)
		return
	}
	api.Success(c, categories)
}

// UploadModelImage 上传模型图片
// @Summary 上传模型图片
// @Description 上传模型图片
// @Tags 模型提供者
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param file formData file true "图片文件"
// @Success 200 {object} service.FileInfoOP "成功"
// @Failure 400 {string} string "请求参数错误"
// @Failure 401 {string} string "Token无效或已过期"
// @Failure 500 {string} string "服务器错误"
// @Router /api/v1/provider/models/image [post]
func (h *ProviderHandler) UploadModelImage(c *gin.Context) {
	_providerInfo, _ := c.Get("provider_info")
	providerInfo, ok := _providerInfo.(*service.ProviderInfo)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "Provider info not found", api.NewClientError("Provider info not found"))
		return
	}

	file, err := c.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	ext := filepath.Ext(file.Filename)
	// 只支持 png,jpg,jpeg,gif,webp,svg
	if !slices.Contains([]string{".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg"}, strings.ToLower(ext)) {
		h.log.Error("invalid file type", zap.String("file_name", file.Filename), zap.String("ext", ext))
		api.Fail(c, api.CodeClientError, "invalid file type, only support png,jpg,jpeg,gif,webp,svg", errors.New("invalid file type"))
		return
	}

	// 如果文件大小大于 10MB，则不支持上传
	if file.Size > 10*1024*1024 {
		h.log.Error("file size too large", zap.String("file_name", file.Filename), zap.Int64("size", file.Size))
		api.Fail(c, api.CodeClientError, "file size too large, only support 10MB", errors.New("file size too large"))
		return
	}

	reader, err := file.Open()
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	defer reader.Close()

	fileInfo, err := h.svc.File.UploadFile(c.Request.Context(), &service.UserBaseOP{IdentityID: providerInfo.WalletAddress}, &service.FileUploadIP{
		Name:     file.Filename,
		Size:     file.Size,
		MimeType: file.Header.Get("Content-Type"),
	}, reader, "image", true)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to upload image file", err)
		return
	}

	api.Success(c, fileInfo)
}
