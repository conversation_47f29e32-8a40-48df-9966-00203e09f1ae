package service

import (
	"bytes"
	"context"
	"errors"
	"io"
	"os"
	"path"
	"path/filepath"
	"time"

	"github.com/google/uuid"

	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

type FileService struct {
	log    *logger.ModuleLogger
	db     *repository.DB
	config *config.FileServiceConfig
}

var _ FileApi = &FileService{}

func NewFileService(repo *repository.DB, config config.FileServiceConfig) *FileService {
	log := logger.GetLogger("file_service")
	return &FileService{log: log, db: repo, config: &config}
}

func (s *FileService) uploadFile(ctx context.Context, filename string, reader io.Reader) (string, error) {
	date := time.Now().Format("2006-01-02")
	urlDir := filepath.Join(date, filename)
	savePath := filepath.Join(s.config.Path, urlDir)

	// 创建目录
	if err := os.MkdirAll(filepath.Dir(savePath), 0755); err != nil {
		return "", err
	}

	// 创建文件
	file, err := os.Create(savePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 写入文件
	if _, err := io.Copy(file, reader); err != nil {
		return "", err
	}

	return urlDir, nil
}

func (s *FileService) UploadFile(ctx context.Context, user *UserBaseOP, fileInfo *FileUploadIP, reader io.Reader, fileType string, isPublic bool) (*FileInfoOP, error) {
	if fileInfo.Size > s.config.MaxSize {
		return nil, errors.New("file size is too large")
	}

	fileID := uuid.New().String()
	// 随机生成一个名字
	if fileType == "" {
		fileType = "file"
	}
	fileName := fileType + "_" + fileID + filepath.Ext(fileInfo.Name)

	urlDir, err := s.uploadFile(ctx, fileName, reader)
	if err != nil {
		return nil, err
	}
	if fileInfo.Name == "" {
		fileInfo.Name = fileName
	}

	// 将文件数据保存到数据库
	fileRepo := &repository.File{
		UUID:         fileID,
		Name:         fileName,
		Path:         urlDir,
		Size:         fileInfo.Size,
		UserID:       user.ID,
		IsDeleted:    0,
		MimeType:     fileInfo.MimeType,
		OriginalName: fileInfo.Name,
		Type:         fileType,
		IsPublic:     0,
	}
	if isPublic {
		fileRepo.IsPublic = 1
	}
	if err := s.db.File.Create(ctx, fileRepo); err != nil {
		return nil, err
	}

	return &FileInfoOP{
		ID:        fileRepo.ID,
		FileID:    fileID,
		FileName:  fileInfo.Name,
		URL:       utils.GetFileRouter(fileRepo),
		CreatedAt: time.Now().Unix(),
		UpdatedAt: time.Now().Unix(),
		Hash:      "",
		Data: &FileInfoData{
			Content: "", // 是否要调用模型, 此处只负责上传
		},
		Meta: &FileInfoMeta{
			CollectionName: fileName,
			ContentType:    fileInfo.MimeType,
			Data:           nil,
			Name:           fileInfo.Name,
			Size:           fileInfo.Size,
			SavePath:       path.Join(s.config.Path, urlDir),
		},
		UserIdentityID: user.IdentityID,
	}, nil
}

func (s *FileService) GetFile(ctx context.Context, user *UserBaseOP, fileID string) (*FileInfoOP, error) {
	file, err := s.db.File.GetByUUID(ctx, fileID)
	if err != nil {
		return nil, err
	}

	if file == nil {
		return nil, errors.New("file not found")
	}

	if file.UserID != user.ID {
		return nil, errors.New("file not found")
	}

	// 判断文件是否存在
	if _, err := os.Stat(filepath.Join(s.config.Path, file.Path)); os.IsNotExist(err) {
		return nil, errors.New("file not found")
	}

	return &FileInfoOP{
		FileID:         file.UUID,
		FileName:       file.OriginalName,
		URL:            file.Path,
		UserIdentityID: user.IdentityID,
		CreatedAt:      file.CreatedAt.Unix(),
		UpdatedAt:      file.UpdatedAt.Unix(),
		Hash:           "",
		Data: &FileInfoData{
			Content: "",
		},
		Meta: &FileInfoMeta{
			CollectionName: file.Name,
			ContentType:    file.MimeType,
			Data:           nil,
			Name:           file.OriginalName,
			Size:           file.Size,
		},
	}, nil
}

func (s *FileService) SaveImage(ctx context.Context, user *UserBaseOP, imageBytes []byte) (string, error) {
	// 直接以字节流存在文件中
	fileID := uuid.New().String()
	fileName := "image_" + fileID

	urlPath, err := s.uploadFile(ctx, fileName, bytes.NewReader(imageBytes))
	if err != nil {
		return "", err
	}

	// 将文件数据保存到数据库
	fileRepo := &repository.File{
		UUID:         fileID,
		Name:         fileName,
		Path:         urlPath,
		Size:         int64(len(imageBytes)),
		UserID:       user.ID,
		IsDeleted:    0,
		MimeType:     "image/png",
		Type:         "image",
		OriginalName: fileName,
		IsPublic:     0,
	}

	// 返回文件路径 + content 表示直接读取内容
	// /api/v1/files/image/ 是固定的获取图片url地址
	if err := s.db.File.Create(ctx, fileRepo); err != nil {
		return "", err
	}
	return utils.GetFileRouter(fileRepo), nil
}

func (s *FileService) DeleteFile(ctx context.Context, user *UserBaseOP, fileID string) error {
	file, err := s.db.File.GetByUUID(ctx, fileID)
	if err != nil {
		return err
	}

	if file == nil {
		return errors.New("file not found")
	}

	if file.UserID != user.ID {
		return errors.New("file not found")
	}

	err = s.db.File.Delete(ctx, file.ID)
	if err != nil {
		return err
	}

	// 文件存在则删除
	if _, err := os.Stat(filepath.Join(s.config.Path, file.Path)); os.IsNotExist(err) {
		return nil
	}

	return os.Remove(filepath.Join(s.config.Path, file.Path))
}

func (s *FileService) UpdateFileContent(ctx context.Context, user *UserBaseOP, fileID uint, content string) error {
	file, err := s.db.File.GetByID(ctx, fileID)
	if err != nil {
		return err
	}

	if file == nil {
		return errors.New("file not found")
	}

	if file.UserID != user.ID {
		return errors.New("file not found")
	}

	return s.db.File.UpdateContentById(ctx, file.ID, content)
}

func (s *FileService) GetUserFileBytes(ctx context.Context, user *UserBaseOP, fileUUID string) ([]byte, error) {
	file, err := s.db.File.GetByUUID(ctx, fileUUID)
	if err != nil {
		return nil, err
	}
	if file == nil || (file.Type != "image" && file.Type != "audio") {
		return nil, errors.New("file not found")
	}

	if file.IsPublic == 0 && (user == nil || file.UserID != user.ID) {
		return nil, errors.New("file not found")
	}

	filePath := filepath.Join(s.config.Path, file.Path)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, errors.New("file not found")
	}

	return os.ReadFile(filePath)
}

func (s *FileService) GetUserFileRealPath(ctx context.Context, user *UserBaseOP, fileUUID string) (string, error) {
	file, err := s.db.File.GetByUUID(ctx, fileUUID)
	if err != nil {
		return "", err
	}
	if file == nil || (file.Type != "image" && file.Type != "audio") {
		return "", errors.New("file not found")
	}

	if file.IsPublic == 0 && (user == nil || file.UserID != user.ID) {
		return "", errors.New("file not found")
	}

	filePath := filepath.Join(s.config.Path, file.Path)

	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return "", errors.New("file not found")
	}

	return filePath, nil
}
