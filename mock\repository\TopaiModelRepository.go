// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelRepository is an autogenerated mock type for the TopaiModelRepository type
type TopaiModelRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, model
func (_m *TopaiModelRepository) Create(ctx context.Context, model *repository.TopaiModel) error {
	ret := _m.Called(ctx, model)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.TopaiModel) error); ok {
		r0 = rf(ctx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelRepository) GetAll(ctx context.Context) ([]*repository.TopaiModel, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModel, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModel); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *TopaiModelRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModel, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModel, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModel); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *TopaiModelRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModel, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModel, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModel); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelName provides a mock function with given fields: ctx, modelName
func (_m *TopaiModelRepository) GetByModelName(ctx context.Context, modelName string) ([]*repository.TopaiModel, error) {
	ret := _m.Called(ctx, modelName)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelName")
	}

	var r0 []*repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*repository.TopaiModel, error)); ok {
		return rf(ctx, modelName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*repository.TopaiModel); ok {
		r0 = rf(ctx, modelName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelNameAndVersion provides a mock function with given fields: ctx, modelName, modelVersion
func (_m *TopaiModelRepository) GetByModelNameAndVersion(ctx context.Context, modelName string, modelVersion string) (*repository.TopaiModel, error) {
	ret := _m.Called(ctx, modelName, modelVersion)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelNameAndVersion")
	}

	var r0 *repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*repository.TopaiModel, error)); ok {
		return rf(ctx, modelName, modelVersion)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *repository.TopaiModel); ok {
		r0 = rf(ctx, modelName, modelVersion)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, modelName, modelVersion)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelTypes provides a mock function with given fields: ctx, modelTypes
func (_m *TopaiModelRepository) GetByModelTypes(ctx context.Context, modelTypes []string) ([]*repository.TopaiModel, error) {
	ret := _m.Called(ctx, modelTypes)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelTypes")
	}

	var r0 []*repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModel, error)); ok {
		return rf(ctx, modelTypes)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModel); ok {
		r0 = rf(ctx, modelTypes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, modelTypes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBySeriesIds provides a mock function with given fields: ctx, seriesIds
func (_m *TopaiModelRepository) GetBySeriesIds(ctx context.Context, seriesIds []uint) ([]*repository.TopaiModel, error) {
	ret := _m.Called(ctx, seriesIds)

	if len(ret) == 0 {
		panic("no return value specified for GetBySeriesIds")
	}

	var r0 []*repository.TopaiModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModel, error)); ok {
		return rf(ctx, seriesIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModel); ok {
		r0 = rf(ctx, seriesIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, seriesIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelRepository creates a new instance of TopaiModelRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelRepository {
	mock := &TopaiModelRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
