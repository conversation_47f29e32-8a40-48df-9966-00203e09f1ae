package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// userTokenRepository 实现用户Token相关的数据库操作
type userTokenRepository struct {
	db *gorm.DB
}

// NewUserTokenRepository 创建用户Token仓库实例
func NewUserTokenRepository(db *gorm.DB) repository.UserTokenRepository {
	return &userTokenRepository{
		db: db,
	}
}

// Create 创建Token
func (r *userTokenRepository) Create(ctx context.Context, token *repository.UserToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

// GetByToken 根据Token获取
func (r *userTokenRepository) GetByToken(ctx context.Context, token string) (*repository.UserToken, error) {
	var userToken repository.UserToken
	if err := r.db.WithContext(ctx).Where("token = ? AND deleted_at = 0", token).First(&userToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &userToken, nil
}

// Delete 删除Token
func (r *userTokenRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.UserToken{}).
		Where("id = ? AND deleted_at = 0", id).
		Update("deleted_at", gorm.Expr("UNIX_TIMESTAMP()")).
		Error
}

// DeleteByUserID 删除用户的所有Token
func (r *userTokenRepository) DeleteByUserID(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).Model(&repository.UserToken{}).
		Where("user_id = ? AND deleted_at = 0", userID).
		Update("deleted_at", gorm.Expr("UNIX_TIMESTAMP()")).
		Error
}
