// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserShadowWalletBalanceRecordRepository is an autogenerated mock type for the UserShadowWalletBalanceRecordRepository type
type UserShadowWalletBalanceRecordRepository struct {
	mock.Mock
}

// BatchCreate provides a mock function with given fields: ctx, records
func (_m *UserShadowWalletBalanceRecordRepository) BatchCreate(ctx context.Context, records []*repository.UserShadowWalletBalanceRecord) error {
	ret := _m.Called(ctx, records)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*repository.UserShadowWalletBalanceRecord) error); ok {
		r0 = rf(ctx, records)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserShadowWalletBalanceRecordRepository) Create(ctx context.Context, record *repository.UserShadowWalletBalanceRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserShadowWalletBalanceRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetLastByCurrencys provides a mock function with given fields: ctx, currencies
func (_m *UserShadowWalletBalanceRecordRepository) GetLastByCurrencys(ctx context.Context, currencies []string) (*repository.UserShadowWalletBalanceRecord, error) {
	ret := _m.Called(ctx, currencies)

	if len(ret) == 0 {
		panic("no return value specified for GetLastByCurrencys")
	}

	var r0 *repository.UserShadowWalletBalanceRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) (*repository.UserShadowWalletBalanceRecord, error)); ok {
		return rf(ctx, currencies)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) *repository.UserShadowWalletBalanceRecord); ok {
		r0 = rf(ctx, currencies)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserShadowWalletBalanceRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, currencies)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUnconfirmed provides a mock function with given fields: ctx
func (_m *UserShadowWalletBalanceRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserShadowWalletBalanceRecord, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUnconfirmed")
	}

	var r0 []*repository.UserShadowWalletBalanceRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.UserShadowWalletBalanceRecord, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.UserShadowWalletBalanceRecord); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserShadowWalletBalanceRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateConfirmedAmountByID provides a mock function with given fields: ctx, id, amount
func (_m *UserShadowWalletBalanceRecordRepository) UpdateConfirmedAmountByID(ctx context.Context, id uint, amount string) error {
	ret := _m.Called(ctx, id, amount)

	if len(ret) == 0 {
		panic("no return value specified for UpdateConfirmedAmountByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) error); ok {
		r0 = rf(ctx, id, amount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateConfirmedByID provides a mock function with given fields: ctx, id
func (_m *UserShadowWalletBalanceRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for UpdateConfirmedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateFailedByID provides a mock function with given fields: ctx, id
func (_m *UserShadowWalletBalanceRecordRepository) UpdateFailedByID(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFailedByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserShadowWalletBalanceRecordRepository creates a new instance of UserShadowWalletBalanceRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserShadowWalletBalanceRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserShadowWalletBalanceRecordRepository {
	mock := &UserShadowWalletBalanceRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
