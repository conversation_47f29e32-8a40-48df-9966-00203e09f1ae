alter table dev_app_keys add column user_id bigint not null default 0 comment '用户ID' after id;
alter table dev_app_infos add column user_id bigint not null default 0 comment '用户ID' after id;
alter table dev_app_infos add column is_deleted tinyint(1) not null default 0 comment '是否删除' after `status`;

create table if not exists user_chat_usages (
    id bigint auto_increment primary key,
    uuid varchar(255) not null default '' comment 'UUID',
    user_id bigint not null default 0 comment '用户ID',
    app_key varchar(255) not null default '' comment '应用Key',
    model_id varchar(255) not null default '' comment '模型ID',
    model_type varchar(255) not null default '' comment '模型类型',
    conversation_uuid varchar(255) not null default '' comment '会话UUID',
    conversation_message_uuid varchar(255) not null default '' comment '会话消息UUID',
    file_uuid varchar(255) not null default '' comment '文件UUID',
    input_tokens varchar(255) not null default '' comment '输入Token',
    output_tokens varchar(255) not null default '' comment '输出Token',
    input_price varchar(255) not null default '' comment '输入价格',
    output_price varchar(255) not null default '' comment '输出价格',
    input_cost varchar(255) not null default '' comment '输入成本',
    output_cost varchar(255) not null default '' comment '输出成本',
    total_cost varchar(255) not null default '' comment '总成本',
    used_type varchar(255) not null default '' comment '使用类型',
    platform varchar(255) not null default '' comment '平台',
    platform_id varchar(255) not null default '' comment '平台ID',
    user_report_cost_record_id bigint not null default 0 comment '用户花费上报记录ID',
    `status` tinyint(1) not null default 0 comment '0 待确认, 1 已确认, 2 已结算, 3 已完成',
    created_at datetime not null default current_timestamp comment '创建时间',
    updated_at datetime not null default current_timestamp on update current_timestamp comment '更新时间',
    index idx_created_at (created_at),
    index idx_status (status),
    index idx_user_id (user_id),
    index idx_app_key (app_key),
    index idx_uuid (uuid)
) engine=InnoDB default charset=utf8mb4 comment='用户聊天使用记录';

create table if not exists  user_balances  (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    balance varchar(255) not null default '' comment '余额',
    currency varchar(255) not null default '' comment '货币',
    accumulated varchar(255) not null default '' comment '累计可消费金额: 累计充值-累计转移出去的金额',
    created_at datetime not null default current_timestamp comment '创建时间',
    updated_at datetime not null default current_timestamp on update current_timestamp comment '更新时间',
    unique key idx_user_id_currency (user_id, currency)
) engine=InnoDB default charset=utf8mb4 comment='用户余额';

create table if not exists user_balance_snapshots (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    user_balance_id bigint not null default 0 comment '用户余额ID',
    balance varchar(255) not null default '' comment '余额',
    currency varchar(255) not null default '' comment '货币',
    snapshot_date varchar(255) not null default '' comment '快照日期',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at)
) engine=InnoDB default charset=utf8mb4 comment='用户余额快照';

create table if not exists user_balance_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    user_balance_id bigint not null default 0 comment '用户余额ID',
    user_chat_usage_id bigint not null default 0 comment '用户聊天使用记录ID',
    gift_user_id bigint not null default 0 comment '转赠用户ID: is_in=0时，表示接收用户ID，is_in=1时，表示转出用户ID',
    amount varchar(255) not null default '' comment '金额',
    `is_in` tinyint(1) not null default 0 comment '是否收入,0:支出,1:收入',
    currency varchar(255) not null default '' comment '货币',
    current_balance varchar(255) not null default '' comment '当前余额',
    `type` varchar(255) not null default '' comment '类型',
    `description` varchar(255) not null default '' comment '描述',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at)
) engine=InnoDB default charset=utf8mb4 comment='用户余额记录';

create table if not exists user_shadow_wallets (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    encrypted_private_key varchar(255) not null default '' comment '加密后的私钥,Base64(IV + Ciphertext)',
    key_salt varchar(255) not null default '' comment '密钥盐,Base64编码',
    key_version varchar(255) not null default '' comment '密钥版本',
    algorithm varchar(255) not null default '' comment '算法',
    is_deleted tinyint(1) not null default 0 comment '是否删除',
    created_at datetime not null default current_timestamp comment '创建时间',
    updated_at datetime not null default current_timestamp on update current_timestamp comment '更新时间',
    index idx_created_at (created_at),
    unique key idx_user_id (user_id),
    unique key idx_public_address (public_address)
) engine=InnoDB default charset=utf8mb4 comment='用户影子钱包';

create table if not exists user_shadow_wallet_balances (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    balance varchar(255) not null default '' comment '余额',
    currency varchar(255) not null default '' comment '货币',
    accumulated varchar(255) not null default '' comment '累计可消费金额: 累计充值-累计转移出去的金额',
    last_block_number bigint not null default 0 comment '最后确认的区块高度',
    created_at datetime not null default current_timestamp comment '创建时间',
    updated_at datetime not null default current_timestamp on update current_timestamp comment '更新时间',
    index idx_user_id (user_id),
    unique key idx_public_address_currency (public_address, currency)
) engine=InnoDB default charset=utf8mb4 comment='用户影子钱包余额';

create table if not exists user_shadow_wallet_balance_snapshots (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    balance varchar(255) not null default '' comment '余额',
    currency varchar(255) not null default '' comment '货币',
    snapshot_date varchar(255) not null default '' comment '快照日期',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at)
) engine=InnoDB default charset=utf8mb4 comment='用户影子钱包余额快照';

create table if not exists user_shadow_wallet_balance_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    tx_hash varchar(255) not null default '' comment '交易哈希',
    block_number bigint not null default 0 comment '区块高度',
    amount varchar(255) not null default '' comment '金额',
    current_balance varchar(255) not null default '' comment '当前余额',
    `is_in` tinyint(1) not null default 0 comment '是否收入,0:支出,1:收入',
    currency varchar(255) not null default '' comment '货币',
    `type` varchar(255) not null default '' comment '类型',
    `description` varchar(255) not null default '' comment '描述',
    `status` tinyint(4) not null default 0 comment '0 待确认, 1 已确认, -1 失败',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_user_id (user_id),
    index idx_created_at (created_at),
    index idx_public_address_currency (public_address, currency),
    index idx_status (status)
) engine=InnoDB default charset=utf8mb4 comment='用户影子钱包余额记录';

create table if not exists wallet_transfer_records (
    id bigint auto_increment primary key,
    from_address varchar(255) not null default '' comment '来源地址',
    to_address varchar(255) not null default '' comment '目标地址',
    amount varchar(255) not null default '' comment '金额',
    currency varchar(255) not null default '' comment '货币',
    tx_hash varchar(255) not null default '' comment '交易哈希',
    block_number bigint not null default 0 comment '区块高度',
    user_shadow_wallet_balance_record_id bigint not null default 0 comment '用户影子钱包余额记录ID',
    status tinyint(4) not null default 0 comment '0 待确认, 1 已确认, -1 失败',
    `description` varchar(255) not null default '' comment '描述',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at),
    index idx_status (status),
    unique key idx_tx_hash (tx_hash)
) engine=InnoDB default charset=utf8mb4 comment='钱包转账记录';

create table if not exists wallet_report_cost_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    user_shadow_wallet_balance_record_id bigint not null default 0 comment '用户影子钱包余额记录ID',
    tx_hash varchar(255) not null default '' comment '交易哈希',
    block_number bigint not null default 0 comment '区块高度',
    input_tokens varchar(255) not null default '' comment '输入Token',
    output_tokens varchar(255) not null default '' comment '输出Token',
    `status` tinyint(4) not null default 0 comment '0 待确认, 1 已确认, -1 失败',
    user_chat_usage_ids varchar(255) not null default '' comment '用户聊天使用记录IDs，逗号分隔',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at),
    index idx_status (status),
    unique index idx_tx_hash (tx_hash)
) engine=InnoDB default charset=utf8mb4 comment='用户花费上报记录';

create table if not exists wallet_deposit_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    public_address varchar(255) not null default '' comment '公钥地址',
    user_shadow_wallet_balance_record_id bigint not null default 0 comment '用户影子钱包余额记录ID',
    tx_hash varchar(255) not null default '' comment '交易哈希',
    block_number bigint not null default 0 comment '区块高度',
    amount varchar(255) not null default '' comment '金额',
    `status` tinyint(4) not null default 0 comment '0 待确认, 1 已确认, -1 失败',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at),
    index idx_status (status),
    unique index idx_tx_hash (tx_hash)
) engine=InnoDB default charset=utf8mb4 comment='钱包质押记录';

create table if not exists user_recharge_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    user_balance_record_id bigint not null default 0 comment '用户余额记录ID',
    user_shadow_wallet_balance_record_id bigint not null default 0 comment '用户影子钱包余额记录ID',
    tx_hash varchar(255) not null default '' comment '交易哈希',
    block_number bigint not null default 0 comment '区块高度',
    amount varchar(255) not null default '' comment '金额',
    currency varchar(255) not null default '' comment '货币',
    `status` tinyint(4) not null default 0 comment '0 待确认, 1 已确认, -1 失败',
    `description` varchar(255) not null default '' comment '描述',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_created_at (created_at),
    index idx_status (status)
) engine=InnoDB default charset=utf8mb4 comment='用户充值记录';


create table if not exists user_chat_asr_records (
    id bigint auto_increment primary key,
    uuid varchar(255) not null default '' comment 'UUID',
    user_id bigint not null default 0 comment '用户ID',
    user_chat_usage_id bigint not null default 0 comment '用户聊天使用记录ID',
    model_id varchar(255) not null default '' comment '模型ID',
    file_uuids varchar(255) not null default '' comment 'audio文件UUID,逗号分隔',
    content varchar(255) not null default '' comment '识别的内容',
    created_at datetime not null default current_timestamp comment '创建时间',
    unique index idx_uuid (uuid),
    index idx_user_id (user_id),
    index idx_user_chat_usage_id (user_chat_usage_id),
    index idx_model_id (model_id)
) engine=InnoDB default charset=utf8mb4 comment='用户聊天ASR记录';

create table if not exists user_chat_tts_records (
    id bigint auto_increment primary key,
    uuid varchar(255) not null default '' comment 'UUID',
    user_id bigint not null default 0 comment '用户ID',
    user_chat_usage_id bigint not null default 0 comment '用户聊天使用记录ID',
    model_id varchar(255) not null default '' comment '模型ID',
    voice varchar(255) not null default '' comment '语音',
    format varchar(255) not null default '' comment '格式',
    speed float not null default 1 comment '语速',
    content varchar(255) not null default '' comment '提示内容',
    file_uuids varchar(255) not null default '' comment '生成文件UUID,逗号分隔',
    created_at datetime not null default current_timestamp comment '创建时间',
    unique index idx_uuid (uuid),
    index idx_user_id (user_id),
    index idx_user_chat_usage_id (user_chat_usage_id),
    index idx_model_id (model_id)
) engine=InnoDB default charset=utf8mb4 comment='用户聊天TTS记录';

create table if not exists user_chat_tti_records (
    id bigint auto_increment primary key,
    uuid varchar(255) not null default '' comment 'UUID',
    user_id bigint not null default 0 comment '用户ID',
    user_chat_usage_id bigint not null default 0 comment '用户聊天使用记录ID',
    model_id varchar(255) not null default '' comment '模型ID',
    size varchar(255) not null default '' comment '大小',
    format varchar(255) not null default '' comment '格式',
    content varchar(255) not null default '' comment '提示内容',
    file_uuids varchar(255) not null default '' comment '生成文件UUID,逗号分隔',
    is_shared tinyint(1) not null default 0 comment '是否共享',
    created_at datetime not null default current_timestamp comment '创建时间',
    unique index idx_uuid (uuid),
    index idx_user_id (user_id),
    index idx_user_chat_usage_id (user_chat_usage_id),
    index idx_model_id (model_id)
) engine=InnoDB default charset=utf8mb4 comment='用户聊天TTI记录';

create table if not exists user_usage_day_records (
    id bigint auto_increment primary key,
    user_id bigint not null default 0 comment '用户ID',
    app_id bigint not null default 0 comment '应用ID',
    app_key varchar(255) not null default '' comment '应用Key',
    model_id varchar(255) not null default '' comment '模型ID',
    model_type varchar(255) not null default '' comment '模型类型',
    usage_date varchar(255) not null default '' comment '使用日期',
    input_tokens varchar(255) not null default '' comment '输入Token',
    output_tokens varchar(255) not null default '' comment '输出Token',
    total_tokens varchar(255) not null default '' comment '总Token',
    input_cost varchar(255) not null default '' comment '输入成本',
    output_cost varchar(255) not null default '' comment '输出成本',
    total_cost varchar(255) not null default '' comment '总成本',
    count int not null default 0 comment '使用次数',
    created_at datetime not null default current_timestamp comment '创建时间',
    index idx_user_id (user_id),
    index idx_usage_date (usage_date),
    index idx_model_id (model_id)
) engine=InnoDB default charset=utf8mb4 comment='用户使用日记录';

alter table topai_model_costs add model_id varchar(255) not null default '' comment '模型ID' after topai_model_id;
alter table topai_model_costs drop index idx_model_id;
alter table topai_model_costs add index idx_model_id(model_id);
alter table topai_model_costs add UNIQUE INDEX idx_topai_model_id (topai_model_id);

-- 模型提供者Token表
create table if not exists provider_tokens (
    id bigint auto_increment primary key comment '主键ID',
    provider_id bigint not null default 0 comment '提供者ID',
    token varchar(32) not null default '' comment 'Token字符串',
    expires_at datetime not null comment '过期时间',
    deleted_at bigint not null default 0 comment '删除时间戳，0表示未删除',
    created_at datetime not null default current_timestamp comment '创建时间',
    updated_at datetime not null default current_timestamp on update current_timestamp comment '更新时间',
    unique index idx_token_deleted_at (token, deleted_at),
    index idx_provider_id (provider_id),
    index idx_deleted_at (deleted_at)
) engine=InnoDB default charset=utf8mb4 comment='模型提供者Token表';

alter table topai_model_exts add series_id BIGINT UNSIGNED NOT NULL COMMENT '系列ID' after topai_model_id;
alter table topai_model_category_relations drop index idx_model_category;
alter table topai_model_category_relations add INDEX idx_model_category (topai_model_id, category_id);
alter table tts_model_voices add is_deleted tinyint(1) not null default 0 comment '是否删除' after provider_name;

alter table user_chat_requests add column model_type varchar(255) not null default '' comment '模型类型' after user_id;
alter table user_chat_requests change column conversation_uuid associated_uuid varchar(255) not null default '' comment '根据modelType关联的UUID，llm: conversation_uuid，tts: tti_uuid, asr: conversations.uuid, asr: user_chat_asr_records.uuid, tts: user_chat_tts_records.uuid, tti: user_chat_tti_records.uuid';