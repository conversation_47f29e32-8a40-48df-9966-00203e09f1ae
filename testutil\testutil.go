// Package testutil 提供测试工具和测试环境支持
package testutil

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/repository/mysql"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// TestEnv 测试环境
type TestEnv struct {
	T       *testing.T
	DB      *repository.DB
	Ctx     context.Context
	TempDir string
}

// NewTestEnv 创建测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	// 初始化测试配置
	testConfig := &config.Config{
		Database: config.DatabaseConfig{
			Host:      getEnvOrDefault("TEST_DB_HOST", "localhost"),
			Port:      3306,
			Username:  getEnvOrDefault("TEST_DB_USER", "root"),
			Password:  getEnvOrDefault("TEST_DB_PASSWORD", ""),
			Database:  fmt.Sprintf("test_chat_server_%d", time.Now().UnixNano()),
			ParseTime: true,
			Collation: "utf8mb4_unicode_ci",
			Pool: struct {
				MaxOpenConns    int           `yaml:"max_open_conns"`
				MaxIdleConns    int           `yaml:"max_idle_conns"`
				ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
				ConnMaxIdleTime time.Duration `yaml:"conn_max_idletime"`
			}{
				MaxOpenConns:    10,
				MaxIdleConns:    5,
				ConnMaxLifetime: time.Hour,
				ConnMaxIdleTime: time.Minute * 30,
			},
		},
		Log: config.LogConfig{
			Level:   "debug",
			Console: true,
		},
	}

	// 初始化日志
	logger.InitLogger(&testConfig.Log)

	// 创建测试数据库
	createTestDB(t, &testConfig.Database)

	// 连接数据库
	db, err := mysql.NewClient(&testConfig.Database)
	assert.NoError(t, err, "Failed to connect test database")

	ctx := context.Background()

	env := &TestEnv{
		T:   t,
		DB:  db,
		Ctx: ctx,
	}

	// 注册清理函数
	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	if e.DB != nil {
		// 获取数据库名称
		dbName := getTestDBName(e.T)

		// 关闭连接
		e.DB.Close()

		// 删除测试数据库
		dropTestDB(e.T, dbName)
	}

	if e.TempDir != "" {
		os.RemoveAll(e.TempDir)
	}
}

// createTestDB 创建测试数据库
func createTestDB(t *testing.T, dbConfig *config.DatabaseConfig) {
	// 连接MySQL根数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
	)

	db, err := sql.Open("mysql", dsn)
	assert.NoError(t, err, "Failed to connect to MySQL")
	defer db.Close()

	// 创建测试数据库
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbConfig.Database))
	assert.NoError(t, err, "Failed to create test database")

	t.Logf("Created test database: %s", dbConfig.Database)
}

// dropTestDB 删除测试数据库
func dropTestDB(t *testing.T, dbName string) {
	// 如果在CI环境中，跳过数据库删除
	if os.Getenv("CI") != "" {
		return
	}

	dbConfig := &config.DatabaseConfig{
		Host:     getEnvOrDefault("TEST_DB_HOST", "localhost"),
		Port:     3306,
		Username: getEnvOrDefault("TEST_DB_USER", "root"),
		Password: getEnvOrDefault("TEST_DB_PASSWORD", ""),
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		t.Logf("Warning: Failed to connect to MySQL for cleanup: %v", err)
		return
	}
	defer db.Close()

	_, err = db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName))
	if err != nil {
		t.Logf("Warning: Failed to drop test database %s: %v", dbName, err)
	} else {
		t.Logf("Dropped test database: %s", dbName)
	}
}

// getTestDBName 从测试环境获取数据库名称
func getTestDBName(t *testing.T) string {
	// 这是一个简化的实现，实际使用中需要从配置获取
	return fmt.Sprintf("test_chat_server_%s", t.Name())
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// AssertNoError 断言没有错误
func AssertNoError(t *testing.T, err error, msgAndArgs ...interface{}) {
	assert.NoError(t, err, msgAndArgs...)
}

// AssertEqual 断言相等
func AssertEqual(t *testing.T, expected, actual interface{}, msgAndArgs ...interface{}) {
	assert.Equal(t, expected, actual, msgAndArgs...)
}

// AssertNotNil 断言不为nil
func AssertNotNil(t *testing.T, object interface{}, msgAndArgs ...interface{}) {
	assert.NotNil(t, object, msgAndArgs...)
}

// AssertNil 断言为nil
func AssertNil(t *testing.T, object interface{}, msgAndArgs ...interface{}) {
	assert.Nil(t, object, msgAndArgs...)
}

// SkipIfShort 如果是短测试则跳过
func SkipIfShort(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping test in short mode")
	}
}
