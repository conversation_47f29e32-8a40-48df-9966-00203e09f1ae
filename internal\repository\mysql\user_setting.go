package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// userSettingRepository 实现用户设置相关的数据库操作
type userSettingRepository struct {
	db *gorm.DB
}

// NewUserSettingRepository 创建用户设置仓库实例
func NewUserSettingRepository(db *gorm.DB) repository.UserSettingRepository {
	return &userSettingRepository{
		db: db,
	}
}

// GetByUserID 获取用户设置
func (r *userSettingRepository) GetByUserID(ctx context.Context, userID uint, key string) (*repository.UserSetting, error) {
	var setting repository.UserSetting
	if err := r.db.WithContext(ctx).Where("user_id = ? AND `key` = ?", userID, key).First(&setting).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &setting, nil
}

// GetAllByUserID 获取用户的所有设置
func (r *userSettingRepository) GetAllByUserID(ctx context.Context, userID uint) ([]*repository.UserSetting, error) {
	var settings []*repository.UserSetting
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return settings, nil
}

// Create 创建用户设置
func (r *userSettingRepository) Create(ctx context.Context, setting *repository.UserSetting) error {
	return r.db.WithContext(ctx).Create(setting).Error
}

// Update 更新用户设置
func (r *userSettingRepository) Update(ctx context.Context, setting *repository.UserSetting) error {
	return r.db.WithContext(ctx).Omit("created_at", "updated_at").Where("user_id = ? AND `key` = ?", setting.UserID, setting.Key).Updates(setting).Error
}

// FindByUserID 根据用户ID获取用户设置
func (r *userSettingRepository) FindByUserID(ctx context.Context, userID uint) ([]*repository.UserSetting, error) {
	var settings []*repository.UserSetting
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&settings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return settings, nil
}

// GetByUserIDAndKey 根据用户ID和键获取用户设置
func (r *userSettingRepository) GetByUserIDAndKey(ctx context.Context, userID uint, key string) (*repository.UserSetting, error) {
	var setting repository.UserSetting
	if err := r.db.WithContext(ctx).Where("user_id = ? AND `key` = ?", userID, key).First(&setting).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &setting, nil
}
