package service

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sort"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

// generateModelId 生成模型ID，使用@作为版本分隔符
// 链上模型: model_name@version (如: gpt-4o-mini@v1)
// 自有模型: model_name (如: gpt-4o-mini，无版本号)
func generateModelId(modelName, modelVersion string) string {
	modelName = strings.TrimSpace(modelName)
	modelVersion = strings.TrimSpace(modelVersion)

	// 如果没有版本号（自有模型），直接返回模型名
	if modelVersion == "" {
		return modelName
	}

	// 使用 @ 作为分隔符
	return fmt.Sprintf("%s@%s", modelName, modelVersion)
}

// validateModelNaming 验证模型命名规范
func validateModelNaming(modelName, modelVersion string) error {
	modelName = strings.TrimSpace(modelName)
	modelVersion = strings.TrimSpace(modelVersion)

	if modelName == "" {
		return errors.New("model name cannot be empty")
	}

	// 禁止模型名包含 @ 符号
	if strings.Contains(modelName, "@") {
		return fmt.Errorf("model name '%s' cannot contain @ symbol", modelName)
	}

	// 如果有版本号，也不能包含 @
	if modelVersion != "" && strings.Contains(modelVersion, "@") {
		return fmt.Errorf("model version '%s' cannot contain @ symbol", modelVersion)
	}

	return nil
}

type topaiService struct {
	db            *repository.DB
	log           *logger.ModuleLogger
	utilChainFunc UtilChainFunc
}

func NewTopaiService(db *repository.DB) TopaiApi {
	ts := &topaiService{
		db:            db,
		log:           logger.GetLogger("topai-service"),
		utilChainFunc: DefaultUtilChainFunc,
	}
	return ts
}

func (s *topaiService) GetTopaiModelList(ctx context.Context, req *GetTopaiModelListRequest) ([]*TopaiModelsInfoOP, error) {
	// 收集所有非空数组
	var nonEmptyArrays [][]uint
	resList := make([]*TopaiModelsInfoOP, 0)
	if len(req.ModelIds) > 0 {
		nonEmptyArrays = append(nonEmptyArrays, req.ModelIds)
	}
	// 判断筛选条件
	if len(req.CategoryIds) > 0 {
		// 获取categoryIds关联的modelIds
		categoryRelations, err := s.db.TopaiModelCategoryRelation.GetByCategoryIds(ctx, req.CategoryIds)
		if err != nil {
			return nil, err
		}
		if len(categoryRelations) == 0 {
			return resList, nil
		}
		categoryModelIds := make([]uint, len(categoryRelations))
		for i, relation := range categoryRelations {
			categoryModelIds[i] = relation.TopaiModelId
		}
		nonEmptyArrays = append(nonEmptyArrays, categoryModelIds)
	}

	if len(req.SeriesIds) > 0 {
		// 过滤seriesIds
		models, err := s.db.TopaiModel.GetBySeriesIds(ctx, req.SeriesIds)
		if err != nil {
			return nil, err
		}
		if len(models) == 0 {
			return resList, nil
		}
		modelIds := make([]uint, len(models))
		for i, model := range models {
			modelIds[i] = model.Id
		}
		nonEmptyArrays = append(nonEmptyArrays, modelIds)
	}

	if len(req.ModelTypes) > 0 {
		// 过滤modelTypes
		models, err := s.db.TopaiModel.GetByModelTypes(ctx, req.ModelTypes)
		if err != nil {
			return nil, err
		}
		if len(models) == 0 {
			return resList, nil
		}
		modelIds := make([]uint, len(models))
		for i, model := range models {
			modelIds[i] = model.Id
		}
		nonEmptyArrays = append(nonEmptyArrays, modelIds)
	}

	providerIds := make([]uint, 0)
	if len(req.Providers) > 0 {
		// 过滤providers
		providers, err := s.db.TopaiModelProvider.GetByUuids(ctx, req.Providers)
		if err != nil {
			return nil, err
		}
		if len(providers) == 0 {
			return resList, nil
		}
		providerIds = make([]uint, len(providers))
		for i, provider := range providers {
			providerIds[i] = provider.Id
		}
	}

	if len(req.SupportParams) > 0 {
		// 过滤supportParams
		supportParamRelations, err := s.db.TopaiModelSupportParam.GetByParams(ctx, req.SupportParams)
		if err != nil {
			return nil, err
		}
		if len(supportParamRelations) == 0 {
			return resList, nil
		}
		supportParamModelIds := make([]uint, len(supportParamRelations))
		for i, relation := range supportParamRelations {
			supportParamModelIds[i] = relation.TopaiModelId
		}
		nonEmptyArrays = append(nonEmptyArrays, supportParamModelIds)
	}

	// 存储最终筛选出的modelIds
	var finalModelIds []uint
	// 如果有非空数组
	if len(nonEmptyArrays) > 0 {
		// 使用第一个非空数组初始化map
		idMap := make(map[uint]int)
		for _, id := range nonEmptyArrays[0] {
			idMap[id] = 1
		}

		// 遍历其他非空数组,累加每个id出现的次数
		for i := 1; i < len(nonEmptyArrays); i++ {
			for _, id := range nonEmptyArrays[i] {
				if count, exists := idMap[id]; exists {
					idMap[id] = count + 1
				} else {
					idMap[id] = 1
				}
			}
		}

		// 找出在所有非空数组中都存在的id
		for id, count := range idMap {
			if count == len(nonEmptyArrays) {
				finalModelIds = append(finalModelIds, id)
			}
		}
		if len(finalModelIds) == 0 {
			return resList, nil
		}
	}

	offset := (req.Page - 1) * req.PageSize
	modelExts, err := s.db.TopaiModelExt.GetOnlineList(ctx, offset, req.PageSize, req.OrderBy, req.OrderType, finalModelIds, providerIds, req.PriceFree, req.Search)
	//models, err := s.db.TopaiModel.GetTopaiModelList(ctx, offset, req.PageSize, req.OrderBy, req.OrderType, finalModelIds, ownerAddress, req.ModelTypes, req.SeriesIds, req.PriceFree)
	if err != nil {
		return nil, err
	}

	modelIds := make([]uint, 0)
	providerIds = make([]uint, 0)
	for _, model := range modelExts {
		modelIds = append(modelIds, model.TopaiModelId)
		providerIds = append(providerIds, model.TopaiModelProviderId)
	}
	// 获取链数据
	models, err := s.db.TopaiModel.GetByIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}

	modelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range models {
		modelMap[model.Id] = model
	}

	// 获取provider信息
	providerRelations, err := s.db.TopaiModelProvider.GetByIds(ctx, providerIds)
	if err != nil {
		return nil, err
	}
	providerMap := make(map[uint]string)
	for _, provider := range providerRelations {
		providerMap[provider.Id] = provider.Name
	}

	costMap := make(map[uint]*repository.TopaiModelCost)
	costList, err := s.db.TopaiModelCost.GetByTopaiModelIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}
	for _, cost := range costList {
		costMap[cost.TopaiModelId] = cost
	}

	categoryRelations, err := s.db.TopaiModelCategoryRelation.GetByTopaiModelIds(ctx, modelIds)
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[uint][]uint)
	categoryId := make([]uint, 0)
	for _, relation := range categoryRelations {
		categoryMap[relation.TopaiModelId] = append(categoryMap[relation.TopaiModelId], relation.CategoryId)
		categoryId = append(categoryId, relation.CategoryId)
	}

	categoryList, err := s.db.TopaiModelCategory.GetByIds(ctx, categoryId)
	if err != nil {
		return nil, err
	}
	categoryNameMap := make(map[uint]string)
	for _, category := range categoryList {
		categoryNameMap[category.Id] = category.Name
	}

	resList = make([]*TopaiModelsInfoOP, len(modelExts))
	for i, ext := range modelExts {
		infoOP := &TopaiModelsInfoOP{
			ModelId:       ext.ModelId,
			ModelName:     ext.ModelId,
			ModelType:     modelMap[ext.TopaiModelId].ModelType,
			InputPrice:    modelMap[ext.TopaiModelId].InputPrice,
			OutputPrice:   modelMap[ext.TopaiModelId].OutputPrice,
			TotalTokens:   "0",
			Description:   ext.Description,
			ProviderName:  "",
			CategoryList:  make([]*TopaiModelCategoryOP, 0),
			ContextLength: strconv.FormatUint(ext.ContextLength, 10),
			Weight:        int(ext.Weight),
		}
		if provider, ok := providerMap[ext.TopaiModelProviderId]; ok {
			infoOP.ProviderName = provider
		}
		if cost, ok := costMap[ext.TopaiModelId]; ok {
			infoOP.TotalTokens = cost.TotalTokens
		}
		if categoryIds, ok := categoryMap[ext.TopaiModelId]; ok {
			infoOP.CategoryList = make([]*TopaiModelCategoryOP, len(categoryIds))
			for i, categoryId := range categoryIds {
				infoOP.CategoryList[i] = &TopaiModelCategoryOP{
					CategoryId:   categoryId,
					CategoryName: categoryNameMap[categoryId],
				}
			}
		}
		if ext.ShowPicture != "" {
			file, err := s.db.File.GetByUUID(ctx, ext.ShowPicture)
			if err != nil {
				return nil, err
			}
			if file != nil && file.ID > 0 {
				infoOP.ShowPicture = utils.GetFileRouter(file)
			}
		}

		resList[i] = infoOP
	}
	return resList, nil
}

func (s *topaiService) GetTopaiModelDetail(ctx context.Context, modelId string) (*TopaiModelsInfoDetailOP, error) {

	ext, err := s.db.TopaiModelExt.GetByModelId(ctx, modelId)
	if err != nil {
		return nil, err
	}
	if ext == nil || ext.Id == 0 {
		return nil, errors.New("model not found")
	}
	model, err := s.db.TopaiModel.GetByID(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if model == nil || model.Id == 0 {
		return nil, errors.New("model not found")
	}
	detail := &TopaiModelsInfoDetailOP{
		TopaiModelsInfoOP: TopaiModelsInfoOP{
			ModelId:       ext.ModelId,
			ModelName:     ext.ModelId,
			ModelType:     model.ModelType,
			InputPrice:    model.InputPrice,
			OutputPrice:   model.OutputPrice,
			TotalTokens:   "0",
			Description:   ext.Description,
			IsFree:        ext.Price == "0",
			ContextLength: strconv.FormatUint(ext.ContextLength, 10),
			MaxOutput:     strconv.FormatUint(ext.MaxOutput, 10),
		},
		Throughput: nil,
		SampleCode: nil,
	}

	if ext.ShowPicture != "" {
		file, err := s.db.File.GetByUUID(ctx, ext.ShowPicture)
		if err != nil {
			return nil, err
		}
		if file != nil && file.ID > 0 {
			detail.ShowPicture = utils.GetFileRouter(file)
		}
	}

	// 获取所有的provider
	nameModels, err := s.db.TopaiModel.GetByModelName(ctx, model.ModelName)
	if err != nil {
		return nil, err
	}
	nameModelIds := make([]uint, len(nameModels))
	for i, nameModel := range nameModels {
		nameModelIds[i] = nameModel.Id
	}
	providerModels, err := s.GetTopaiModelList(ctx, &GetTopaiModelListRequest{
		ModelIds: nameModelIds,
		PageSize: 1000,
	})
	if err != nil {
		return nil, err
	}
	sort.Slice(providerModels, func(i, j int) bool {
		if len(providerModels[i].TotalTokens) == len(providerModels[j].TotalTokens) {
			return providerModels[i].ModelId > providerModels[j].ModelId
		}
		return len(providerModels[i].TotalTokens) > len(providerModels[j].TotalTokens)
	})
	detail.Provider = providerModels

	provider, err := s.db.TopaiModelProvider.GetByID(ctx, ext.TopaiModelProviderId)
	if err != nil {
		return nil, err
	}
	if provider != nil {
		detail.ProviderName = provider.Name
	}

	cost, err := s.db.TopaiModelCost.GetByTopaiModelId(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if cost != nil {
		detail.TotalTokens = cost.TotalTokens
	}
	seriesRelation, err := s.db.TopaiModelSeriesRelation.GetByTopaiModelId(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if seriesRelation != nil {
		series, err := s.db.TopaiModelSeries.GetByID(ctx, seriesRelation.SeriesId)
		if err != nil {
			return nil, err
		}
		if series == nil {
			return nil, nil
		}
		detail.SeriesName = series.Name
		detail.SeriesId = series.Id
	}
	categoryRelations, err := s.db.TopaiModelCategoryRelation.GetByTopaiModelId(ctx, ext.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if len(categoryRelations) > 0 {
		categoryIds := make([]uint, len(categoryRelations))
		for i, category := range categoryRelations {
			categoryIds[i] = category.CategoryId
		}
		categoryList, err := s.db.TopaiModelCategory.GetByIds(ctx, categoryIds)
		if err != nil {
			return nil, err
		}
		detail.CategoryList = make([]*TopaiModelCategoryOP, len(categoryList))
		for i, category := range categoryList {
			detail.CategoryList[i] = &TopaiModelCategoryOP{
				CategoryId:   category.Id,
				CategoryName: category.Name,
			}
		}
	}
	// 获取app使用
	usageRecords, err := s.db.UserUsageDayRecord.GetByModelId(ctx, ext.ModelId)
	if err != nil {
		return nil, err
	}
	if len(usageRecords) > 0 {
		appIds := make([]uint, len(usageRecords))
		dayPutData := map[string]map[string]*big.Int{}
		appPutData := map[uint]map[string]*big.Int{}
		for i, usageRecord := range usageRecords {
			day := usageRecord.UsageDate
			if _, ok := dayPutData[day]; !ok {
				dayPutData[day] = map[string]*big.Int{
					"input":  big.NewInt(0),
					"output": big.NewInt(0),
					"total":  big.NewInt(0),
					"count":  big.NewInt(0),
				}
			}
			if _, ok := appPutData[usageRecord.AppId]; !ok {
				appPutData[usageRecord.AppId] = map[string]*big.Int{
					"input":  big.NewInt(0),
					"output": big.NewInt(0),
					"total":  big.NewInt(0),
					"count":  big.NewInt(0),
				}
			}
			input, _ := big.NewInt(0).SetString(usageRecord.InputTokens, 10)
			output, _ := big.NewInt(0).SetString(usageRecord.OutputTokens, 10)
			dayPutData[day]["input"] = dayPutData[day]["input"].Add(dayPutData[day]["input"], input)
			dayPutData[day]["output"] = dayPutData[day]["output"].Add(dayPutData[day]["output"], output)
			dayPutData[day]["total"] = dayPutData[day]["total"].Add(dayPutData[day]["total"], input).Add(dayPutData[day]["total"], output)
			appPutData[usageRecord.AppId]["input"] = appPutData[usageRecord.AppId]["input"].Add(appPutData[usageRecord.AppId]["input"], input)
			appPutData[usageRecord.AppId]["output"] = appPutData[usageRecord.AppId]["output"].Add(appPutData[usageRecord.AppId]["output"], output)
			appPutData[usageRecord.AppId]["total"] = appPutData[usageRecord.AppId]["total"].Add(appPutData[usageRecord.AppId]["total"], input).Add(appPutData[usageRecord.AppId]["total"], output)
			dayPutData[day]["count"] = dayPutData[day]["count"].Add(dayPutData[day]["count"], big.NewInt(int64(usageRecord.Count)))
			appPutData[usageRecord.AppId]["count"] = appPutData[usageRecord.AppId]["count"].Add(appPutData[usageRecord.AppId]["count"], big.NewInt(int64(usageRecord.Count)))
			appIds[i] = usageRecord.AppId
		}
		appList, err := s.db.DevAppInfo.GetByIds(ctx, appIds)
		if err != nil {
			return nil, err
		}

		detail.AppUsed = make([]*TopaiModelAppUsedOP, len(appList))
		for i, app := range appList {
			appAvatar := ""
			if app.Logo != "" {
				file, err := s.db.File.GetByUUID(ctx, app.Logo)
				if err != nil {
					return nil, err
				}
				if file != nil && file.ID > 0 {
					appAvatar = utils.GetFileRouter(file)
				}
			}
			detail.AppUsed[i] = &TopaiModelAppUsedOP{
				AppName:           app.Name,
				AppId:             app.Id,
				AppUuid:           app.Uuid,
				Description:       app.Description,
				AppAvatar:         appAvatar,
				TotalInputTokens:  appPutData[app.Id]["input"].String(),
				TotalOutputTokens: appPutData[app.Id]["output"].String(),
				TotalTokens:       appPutData[app.Id]["total"].String(),
				Website:           app.Website,
			}
		}
		sort.Slice(detail.AppUsed, func(i, j int) bool {
			if len(detail.AppUsed[i].TotalTokens) == len(detail.AppUsed[j].TotalTokens) {
				return detail.AppUsed[i].TotalTokens > detail.AppUsed[j].TotalTokens
			}
			return len(detail.AppUsed[i].TotalTokens) > len(detail.AppUsed[j].TotalTokens)
		})
		detail.DayPutData = make([]*TopaiModelDayPutDataOP, 0, len(dayPutData))
		for day, value := range dayPutData {
			detail.DayPutData = append(detail.DayPutData, &TopaiModelDayPutDataOP{
				Day:               day,
				Count:             int(value["count"].Int64()),
				TotalInputTokens:  value["input"].String(),
				TotalOutputTokens: value["output"].String(),
				TotalTokens:       value["total"].String(),
			})
		}
	}
	return detail, nil
}

func (s *topaiService) GetUsedCategoryList(ctx context.Context) ([]*TopaiModelCategoryOP, error) {
	categoryRelations, err := s.db.TopaiModelCategory.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	// 获取所有的关联标签，只有有关联的标签才返回
	categoryRelationMap := make(map[uint]bool)
	categoryRelationRelations, err := s.db.TopaiModelCategoryRelation.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, relation := range categoryRelationRelations {
		categoryRelationMap[relation.CategoryId] = true
	}
	resList := make([]*TopaiModelCategoryOP, 0, len(categoryRelations))

	for _, category := range categoryRelations {
		if !categoryRelationMap[category.Id] {
			continue
		}
		resList = append(resList, &TopaiModelCategoryOP{
			CategoryId:   category.Id,
			CategoryName: category.Name,
			BelongTo:     category.BelongTo,
		})
	}
	return resList, nil
}

func (s *topaiService) GetSeriesList(ctx context.Context) ([]*TopaiModelSeriesOP, error) {
	seriesRelations, err := s.db.TopaiModelSeries.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	resList := make([]*TopaiModelSeriesOP, len(seriesRelations)+1)
	for i, series := range seriesRelations {
		resList[i] = &TopaiModelSeriesOP{
			SeriesId:   series.Id,
			SeriesName: series.Name,
		}
	}
	resList[len(resList)-1] = &TopaiModelSeriesOP{
		SeriesId:   0,
		SeriesName: "Other",
	}
	return resList, nil
}

func (s *topaiService) GetProviderList(ctx context.Context) ([]*TopaiModelProviderOP, error) {
	providerRelations, err := s.db.TopaiModelProvider.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	resList := make([]*TopaiModelProviderOP, len(providerRelations))
	for i, provider := range providerRelations {
		resList[i] = &TopaiModelProviderOP{
			ProviderId:   provider.Id,
			ProviderName: provider.Name,
			ProviderUuid: provider.Uuid,
		}
	}
	return resList, nil
}

func (s *topaiService) GetModelTypeList(ctx context.Context) ([]string, error) {
	return []string{"LLM", "ASR", "TTS", "TTI"}, nil
}

func (s *topaiService) GetSupportParamList(ctx context.Context) ([]string, error) {
	return []string{
		"top_p",
		"top_k",
		"tools",
		"response_format",
		"n",
		"frequency_penalty",
		"temperature",
		"min_p",
		"max_token",
	}, nil
}

func (s *topaiService) GetAppKeyList(ctx context.Context) ([]*AppKeyOP, error) {
	// 先获取所有的app
	appList, err := s.db.DevAppInfo.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	appIds := make([]uint, len(appList))
	appMap := make(map[uint]*repository.DevAppInfo)
	for i, app := range appList {
		appIds[i] = app.Id
		appMap[app.Id] = app
	}
	appKeys, err := s.db.DevAppKey.GetByDevAppIds(ctx, appIds)
	if err != nil {
		return nil, err
	}
	resList := make([]*AppKeyOP, len(appKeys))
	for i, key := range appKeys {
		resList[i] = &AppKeyOP{
			AppId:   key.DevAppId,
			AppUuid: appMap[key.DevAppId].Uuid,
			AppName: appMap[key.DevAppId].Name,
			Key:     key.Key,
			KeyName: key.Name,
		}
	}
	return resList, nil
}

// 只获取前20个model
func (s *topaiService) GetTopaiModelListByTokenSort(ctx context.Context, req *GetTopaiModelListRequest) ([]*TopaiModelsInfoOP, error) {
	infoOp := make([]*TopaiModelsInfoOP, 0)
	offset := 0
	limit := 20
	categoryModelIdMap := make(map[uint]bool)
	if len(req.CategoryIds) > 0 {
		categoryRelations, err := s.db.TopaiModelCategoryRelation.GetByCategoryIds(ctx, req.CategoryIds)
		if err != nil {
			return nil, err
		}
		if len(categoryRelations) == 0 {
			return nil, nil
		}
		for _, relation := range categoryRelations {
			categoryModelIdMap[relation.TopaiModelId] = true
		}
	}

	for {
		// 先根据token排序获取modelIds
		costs, err := s.db.TopaiModelCost.GetAll(ctx, offset, limit, "length(total_tokens) desc,total_tokens desc")
		if err != nil {
			return nil, err
		}
		if len(costs) == 0 {
			break
		}
		modelIds := make([]uint, len(costs))
		costMap := make(map[uint]*repository.TopaiModelCost)
		for i, cost := range costs {
			if len(categoryModelIdMap) > 0 && !categoryModelIdMap[cost.TopaiModelId] {
				continue
			}

			modelIds[i] = cost.TopaiModelId
			costMap[cost.TopaiModelId] = cost
		}
		modelExts, err := s.db.TopaiModelExt.GetOnlineList(ctx, offset, limit, "", "", modelIds, []uint{}, false, "")
		if err != nil {
			return nil, err
		}
		for _, ext := range modelExts {
			infoOp = append(infoOp, &TopaiModelsInfoOP{
				ModelId:     ext.ModelId,
				ModelName:   ext.ModelId,
				Description: ext.Description,
				TotalTokens: costMap[ext.TopaiModelId].TotalTokens,
				IsFree:      ext.Price == "0",
			})
		}

		if len(costs) < limit || len(infoOp) >= limit {
			break
		}
		offset += limit
	}
	sort.Slice(infoOp, func(i, j int) bool {
		if len(infoOp[i].TotalTokens) == len(infoOp[j].TotalTokens) {
			return infoOp[i].TotalTokens > infoOp[j].TotalTokens
		}
		return len(infoOp[i].TotalTokens) > len(infoOp[j].TotalTokens)
	})
	if len(infoOp) > limit {
		infoOp = infoOp[:limit]
	}
	return infoOp, nil
}

func (s *topaiService) SyncOnchainModel(ctx context.Context, chainUrl, aiModelsAddress string) error {
	// 拉取链上数据
	onchainModelList, err := s.utilChainFunc.GetAiModelsListFunc(ctx, chainUrl, aiModelsAddress)
	if err != nil {
		return err
	}
	// 获取数据库中所有model
	modelList, err := s.db.TopaiModel.GetAll(ctx)
	if err != nil {
		return err
	}
	modelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range modelList {
		// 过滤掉中心化模型
		if model.ChainModelId == 0 {
			continue
		}
		modelMap[model.ChainModelId] = model
	}
	// 获取所有的modelExt

	modelExts, err := s.db.TopaiModelExt.GetAll(ctx)
	if err != nil {
		return err
	}
	modelExtMap := make(map[uint]bool)
	for _, modelExt := range modelExts {
		modelExtMap[modelExt.TopaiModelId] = true
	}
	// 获取所有的供应商
	providerList, err := s.db.TopaiModelProvider.GetAll(ctx)
	if err != nil {
		return err
	}
	providerMap := make(map[string]*repository.TopaiModelProvider)
	for _, provider := range providerList {
		providerMap[provider.WalletAddress] = provider
	}
	for _, onchainmModel := range onchainModelList {
		chainModelId := onchainmModel.ModelId.Uint64()
		topaiModel, ok := modelMap[uint(chainModelId)]
		// 存在，判断ext
		if !ok {
			// 不存在，创建
			modelType := ""
			if onchainmModel.ExtendInfoExtend != nil {
				modelType = onchainmModel.ExtendInfoExtend.Type
			}
			topaiModel = &repository.TopaiModel{
				ChainModelId: uint(chainModelId),
				OwnerAddress: onchainmModel.Uploader.Hex(),
				ModelName:    onchainmModel.ModelName,
				ModelVersion: onchainmModel.ModelVersion,
				ModelType:    modelType,
				InputPrice:   onchainmModel.InTokenPrice.String(),
				OutputPrice:  onchainmModel.OutTokenPrice.String(),
			}
			err = s.db.TopaiModel.Create(ctx, topaiModel)
			if err != nil {
				s.log.Error("create topai model failed", zap.Error(err))
				continue
			}
		}
		if modelExtMap[topaiModel.Id] {
			continue
		}
		if onchainmModel.ExtendInfoExtend == nil || onchainmModel.ExtendInfoExtend.Type == "" {
			continue
		}
		// 不存在，创建
		// 供应商不存在，直接跳过
		provider, ok := providerMap[onchainmModel.Uploader.Hex()]
		if !ok {
			continue
		}
		// 验证模型命名规范
		err = validateModelNaming(topaiModel.ModelName, onchainmModel.ModelVersion)
		if err != nil {
			s.log.Error("model naming validation failed", zap.Error(err),
				zap.String("modelName", topaiModel.ModelName),
				zap.String("modelVersion", onchainmModel.ModelVersion))
			continue
		}

		inputPrice, _ := big.NewInt(0).SetString(onchainmModel.InTokenPrice.String(), 10)
		outputPrice, _ := big.NewInt(0).SetString(onchainmModel.OutTokenPrice.String(), 10)

		modelId := generateModelId(topaiModel.ModelName, onchainmModel.ModelVersion)
		modelExt := &repository.TopaiModelExt{
			TopaiModelId:         topaiModel.Id,
			TopaiModelProviderId: provider.Id,
			ModelId:              modelId,
			Price:                inputPrice.Add(inputPrice, outputPrice).String(),
		}
		err = s.db.TopaiModelExt.Create(ctx, modelExt)
		if err != nil {
			s.log.Error("create model ext failed", zap.Error(err))
		}
		continue
	}
	return nil
}

// 自动生成appKey
func (s *topaiService) AutoGenerateAppKey(ctx context.Context) error {
	// 获取所有的devApp
	devAppList, err := s.db.DevAppInfo.GetAll(ctx)
	if err != nil {
		return err
	}

	for _, devApp := range devAppList {
		err = s.GenerateAppKey(ctx, devApp.Id, "testnet")
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *topaiService) GenerateAppKey(ctx context.Context, appId uint, keyName string) error {
	// 判断app是否存在
	app, err := s.db.DevAppInfo.GetByID(ctx, appId)
	if err != nil {
		return err
	}
	if app == nil || app.Id == 0 {
		return errors.New("app not found")
	}
	appKey, err := s.db.DevAppKey.GetByDevAppId(ctx, appId)
	if err != nil {
		return err
	}
	for _, key := range appKey {
		if key.Name == keyName {
			return nil
		}
	}
	keyRecord := &repository.DevAppKey{
		DevAppId: appId,
		Name:     keyName,
		Key:      uuid.New().String(),
	}
	return s.db.DevAppKey.Create(ctx, keyRecord)
}

// AdminGetModelList 管理员获取模型列表
func (s *topaiService) AdminGetModelList(ctx context.Context, page, pageSize int, status []int, isOnChain int) (*AdminModelListResponse, error) {
	offset := (page - 1) * pageSize

	resp := &AdminModelListResponse{
		Total:  0,
		Models: make([]*AdminModelListItem, 0),
	}

	sum := 0
	allTopaiModelIds := map[uint]int{}
	if isOnChain != -1 {
		sum++
		isOnChainTopaiModels, err := s.db.TopaiModel.GetByIsOnChain(ctx, isOnChain == 1)
		if err != nil {
			return nil, err
		}
		if len(isOnChainTopaiModels) == 0 {
			return resp, nil
		}
		for _, model := range isOnChainTopaiModels {
			allTopaiModelIds[model.Id] = allTopaiModelIds[model.Id] + 1
		}
	}

	// 合并，同时满足status和isOnChain
	existTopaiModelIds := make([]uint, 0)
	if sum > 0 {
		for topaiModelId, num := range allTopaiModelIds {
			if num == sum {
				existTopaiModelIds = append(existTopaiModelIds, topaiModelId)
			}
		}
		if len(existTopaiModelIds) == 0 {
			return resp, nil
		}
	}

	// 获取模型扩展列表
	modelExts, err := s.db.TopaiModelExt.GetByTopaiModelIdsAndStatus(ctx, existTopaiModelIds, status, offset, pageSize)
	if err != nil {
		return nil, err
	}

	topaiModelIds := make([]uint, len(modelExts))
	providerIds := make([]uint, len(modelExts))
	for i, ext := range modelExts {
		topaiModelIds[i] = ext.TopaiModelId
		providerIds[i] = ext.TopaiModelProviderId
	}

	// 获取topai_model信息
	topaiModels, err := s.db.TopaiModel.GetByIds(ctx, topaiModelIds)
	if err != nil {
		return nil, err
	}
	topaiModelMap := make(map[uint]*repository.TopaiModel)
	for _, model := range topaiModels {
		topaiModelMap[model.Id] = model
	}

	// 获取提供商信息
	providers, err := s.db.TopaiModelProvider.GetByIds(ctx, providerIds)
	if err != nil {
		return nil, err
	}
	providerMap := make(map[uint]*repository.TopaiModelProvider)
	for _, provider := range providers {
		providerMap[provider.Id] = provider
	}

	// 获取token使用量
	costList, err := s.db.TopaiModelCost.GetByTopaiModelIds(ctx, topaiModelIds)
	if err != nil {
		return nil, err
	}
	costMap := make(map[uint]*repository.TopaiModelCost)
	for _, cost := range costList {
		costMap[cost.TopaiModelId] = cost
	}

	// 过滤和转换数据
	var filteredModels []*AdminModelListItem
	for _, ext := range modelExts {
		topaiModel := topaiModelMap[ext.TopaiModelId]
		if topaiModel == nil {
			continue
		}

		provider := providerMap[ext.TopaiModelProviderId]
		providerName := ""
		if provider != nil {
			providerName = provider.Name
		}

		// 计算token消耗量
		tokenConsumption := "0"
		if cost, ok := costMap[ext.TopaiModelId]; ok {
			tokenConsumption = cost.TotalTokens
		}

		item := &AdminModelListItem{
			ID:               ext.Id,
			ModelID:          ext.ModelId,
			ModelName:        topaiModel.ModelName,
			ModelVersion:     topaiModel.ModelVersion,
			InputPrice:       topaiModel.InputPrice,
			OutputPrice:      topaiModel.OutputPrice,
			TokenConsumption: tokenConsumption,
			Status:           ext.Status,
			IsOnChain:        topaiModel.ChainModelId > 0,
			ProviderName:     providerName,
			Description:      ext.Description,
			CreatedAt:        ext.CreatedAt.Unix(),
			UpdatedAt:        ext.UpdatedAt.Unix(),
		}
		filteredModels = append(filteredModels, item)
	}

	total := int64(len(filteredModels))

	// 分页
	start := offset
	end := offset + pageSize
	if start > len(filteredModels) {
		start = len(filteredModels)
	}
	if end > len(filteredModels) {
		end = len(filteredModels)
	}

	pagedModels := filteredModels[start:end]

	return &AdminModelListResponse{
		Total:  total,
		Models: pagedModels,
	}, nil
}

// AdminGetModelDetail 管理员获取模型详情
func (s *topaiService) AdminGetModelDetail(ctx context.Context, modelID string) (*AdminModelDetailResponse, error) {
	// 获取模型扩展信息
	modelExt, err := s.db.TopaiModelExt.GetByModelId(ctx, modelID)
	if err != nil {
		return nil, err
	}
	if modelExt == nil {
		return nil, errors.New("model not found")
	}

	// 获取topai_model信息
	topaiModel, err := s.db.TopaiModel.GetByID(ctx, modelExt.TopaiModelId)
	if err != nil {
		return nil, err
	}
	if topaiModel == nil {
		return nil, errors.New("topai model not found")
	}

	// 获取提供商信息
	provider, err := s.db.TopaiModelProvider.GetByID(ctx, modelExt.TopaiModelProviderId)
	if err != nil {
		return nil, err
	}
	providerName := ""
	if provider != nil {
		providerName = provider.Name
	}

	cost, err := s.db.TopaiModelCost.GetByTopaiModelId(ctx, modelExt.TopaiModelId)
	if err != nil {
		return nil, err
	}
	tokenConsumption := "0"
	if cost != nil {
		tokenConsumption = cost.TotalTokens
	}

	return &AdminModelDetailResponse{
		AdminModelListItem: AdminModelListItem{
			ID:               modelExt.Id,
			ModelID:          modelExt.ModelId,
			ModelName:        topaiModel.ModelName,
			ModelVersion:     topaiModel.ModelVersion,
			InputPrice:       topaiModel.InputPrice,
			OutputPrice:      topaiModel.OutputPrice,
			TokenConsumption: tokenConsumption,
			Status:           modelExt.Status,
			IsOnChain:        topaiModel.ChainModelId > 0,
			ProviderName:     providerName,
			Description:      modelExt.Description,
			CreatedAt:        modelExt.CreatedAt.Unix(),
			UpdatedAt:        modelExt.UpdatedAt.Unix(),
		},
		ContextLength: modelExt.ContextLength,
		MaxOutput:     modelExt.MaxOutput,
		Latency:       modelExt.Latency,
		Throughput:    modelExt.Throughput,
		SampleCode:    modelExt.SampleCode,
		ShowPicture:   modelExt.ShowPicture,
		ShowVideo:     modelExt.ShowVideo,
		Weight:        modelExt.Weight,
		SeriesID:      modelExt.SeriesId,
	}, nil
}

// AdminUpdateModel 管理员更新模型
func (s *topaiService) AdminUpdateModel(ctx context.Context, modelID string, req *AdminUpdateModelRequest) error {
	// 获取模型扩展信息
	modelExt, err := s.db.TopaiModelExt.GetByModelId(ctx, modelID)
	if err != nil {
		return err
	}
	if modelExt == nil {
		return errors.New("model not found")
	}

	topaiModel, err := s.db.TopaiModel.GetByID(ctx, modelExt.TopaiModelId)
	if err != nil {
		return err
	}
	if topaiModel == nil {
		return errors.New("topai model not found")
	}

	// 检查是否为链上模型
	isOnChainModel := topaiModel.ChainModelId > 0

	// 如果是链上模型，只允许编辑描述
	if isOnChainModel {
		if req.InputPrice != "" || req.OutputPrice != "" || req.ContextLength > 0 || req.MaxOutput > 0 {
			return errors.New("chain models can only edit description")
		}
	}

	// 更新模型扩展信息
	if req.Description != "" {
		modelExt.Description = req.Description
	}
	if req.Weight > 0 {
		modelExt.Weight = req.Weight
	}

	// 非链上模型可以更新更多字段
	if !isOnChainModel {
		if req.ContextLength > 0 {
			modelExt.ContextLength = req.ContextLength
		}
		if req.MaxOutput > 0 {
			modelExt.MaxOutput = req.MaxOutput
		}
	}

	// 更新模型扩展
	err = s.db.TopaiModelExt.Update(ctx, modelExt)
	if err != nil {
		return err
	}

	// 非链上模型还可以更新topai_model表中的价格信息
	if !isOnChainModel && (req.InputPrice != "" || req.OutputPrice != "") {
		topaiModel, err := s.db.TopaiModel.GetByID(ctx, modelExt.TopaiModelId)
		if err != nil {
			return err
		}
		if topaiModel == nil {
			return errors.New("topai model not found")
		}

		if req.InputPrice != "" {
			topaiModel.InputPrice = req.InputPrice
		}
		if req.OutputPrice != "" {
			topaiModel.OutputPrice = req.OutputPrice
		}

		err = s.db.TopaiModel.Update(ctx, topaiModel)
		if err != nil {
			return err
		}
	}

	return nil
}

// AdminUpdateModelStatus 管理员更新模型状态
func (s *topaiService) AdminUpdateModelStatus(ctx context.Context, modelID string, action string) error {
	// 获取模型扩展信息
	modelExt, err := s.db.TopaiModelExt.GetByModelId(ctx, modelID)
	if err != nil {
		return err
	}
	if modelExt == nil {
		return errors.New("model not found")
	}

	// 根据操作设置状态
	switch action {
	case "online":
		modelExt.Status = 1
	case "offline":
		modelExt.Status = 0
	case "delete":
		modelExt.Status = -1
	default:
		return errors.New("invalid action")
	}

	// 更新状态
	return s.db.TopaiModelExt.Update(ctx, modelExt)
}
