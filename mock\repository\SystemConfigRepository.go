// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// SystemConfigRepository is an autogenerated mock type for the SystemConfigRepository type
type SystemConfigRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, config
func (_m *SystemConfigRepository) Create(ctx context.Context, config *repository.SystemConfig) error {
	ret := _m.Called(ctx, config)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.SystemConfig) error); ok {
		r0 = rf(ctx, config)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Get provides a mock function with given fields: ctx, category, key
func (_m *SystemConfigRepository) Get(ctx context.Context, category string, key string) (*repository.SystemConfig, error) {
	ret := _m.Called(ctx, category, key)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *repository.SystemConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*repository.SystemConfig, error)); ok {
		return rf(ctx, category, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *repository.SystemConfig); ok {
		r0 = rf(ctx, category, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SystemConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, category, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByCategory provides a mock function with given fields: ctx, category
func (_m *SystemConfigRepository) GetByCategory(ctx context.Context, category string) ([]*repository.SystemConfig, error) {
	ret := _m.Called(ctx, category)

	if len(ret) == 0 {
		panic("no return value specified for GetByCategory")
	}

	var r0 []*repository.SystemConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*repository.SystemConfig, error)); ok {
		return rf(ctx, category)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*repository.SystemConfig); ok {
		r0 = rf(ctx, category)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SystemConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, category)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByCategoryAndKey provides a mock function with given fields: ctx, category, key
func (_m *SystemConfigRepository) GetByCategoryAndKey(ctx context.Context, category string, key string) (*repository.SystemConfig, error) {
	ret := _m.Called(ctx, category, key)

	if len(ret) == 0 {
		panic("no return value specified for GetByCategoryAndKey")
	}

	var r0 *repository.SystemConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*repository.SystemConfig, error)); ok {
		return rf(ctx, category, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *repository.SystemConfig); ok {
		r0 = rf(ctx, category, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SystemConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, category, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateByCategoryAndKey provides a mock function with given fields: ctx, category, key, value
func (_m *SystemConfigRepository) UpdateByCategoryAndKey(ctx context.Context, category string, key string, value string) error {
	ret := _m.Called(ctx, category, key, value)

	if len(ret) == 0 {
		panic("no return value specified for UpdateByCategoryAndKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, category, key, value)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewSystemConfigRepository creates a new instance of SystemConfigRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSystemConfigRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *SystemConfigRepository {
	mock := &SystemConfigRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
