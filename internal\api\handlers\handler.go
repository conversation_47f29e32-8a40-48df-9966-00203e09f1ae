package handlers

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/api/middleware"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type Stop func()

// NewHandler 创建处理器实例并注册路由
func NewHandler(ctx context.Context, router *gin.Engine, service *service.Api) Stop {
	log := logger.GetLogger("handler")

	// 初始化所有处理器
	authHandler := NewAuthHandler(service)
	conversationHandler := NewConversationHandler(service)
	modelHandler := NewModelHandler(service)
	userHandler := NewUserHandler(service)
	fileHandler := NewFileHandler(service)
	knowledgeHandler := NewKnowledgeHandler(service)
	toolHandler := NewToolHandler(service)
	configHandler := NewConfigHandler(service)
	systemHandler := NewSystemHandler(service)
	websocketHandler := NewWebSocketHandler(service)
	audioHandler := NewAudioHandler(service)
	imageHandler := NewImageHandler(service)
	topaiModelHandler := NewTopaiModelHandler(ctx, service)
	userAppHandler := NewUserAppHandler(service)
	apiHandler := NewApiHandler(service)
	providerHandler := NewProviderHandler(service)

	// 为appKey提供的openai标准api
	openaiGroup := router.Group("/api/openai")
	openaiGroup.Use(middleware.AuthApiKey(service))
	openaiGroup.Use(middleware.ConcurrencyLimit()) // 应用并发限制
	openaiGroup.POST("/chat/completions", apiHandler.ChatLLM)
	openaiGroup.POST("/audio/transcriptions", apiHandler.ChatASR)
	openaiGroup.POST("/audio/speech", apiHandler.ChatTTS)
	openaiGroup.POST("/images/generations", apiHandler.ChatTTI)
	openaiGroup.GET("/usage/:request_id", apiHandler.GetUsage)

	// 启动WebSocket服务器
	websocketHandler.Start(ctx)
	// 启动topai模型任务
	topaiModelHandler.Start(ctx)

	// WebSocket
	// router.GET("/ws/socket.io/", websocketHandler.HandleWebSocket)
	// router.GET("/socket.io/", websocketHandler.HandleWebSocket)
	// router.POST("/ws/socket.io/", websocketHandler.HandleWebSocket)
	// router.POST("/socket.io/", websocketHandler.HandleWebSocket)
	router.GET("/ws/socket.io/", gin.WrapH(websocketHandler.wsServer.HttpHandler()))
	router.POST("/ws/socket.io/", gin.WrapH(websocketHandler.wsServer.HttpHandler()))
	router.GET("/socket.io/", gin.WrapH(websocketHandler.wsServer.HttpHandler()))
	router.POST("/socket.io/", gin.WrapH(websocketHandler.wsServer.HttpHandler()))

	// api 根路径
	apiGroup := router.Group("/api")
	apiGroup.GET("/version/webhook", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"url": "",
		})
	})

	// 配置
	apiGroup.GET("/config", systemHandler.GetBaseConfig)

	// 模型管理
	apiGroup.GET("/models/base", modelHandler.GetAllBaseLLMModels) // 拉取配置的第三方的模型列表
	apiGroup.GET("/models", modelHandler.GetAvailableLLMModels)    // 获取可以使用的模型列表

	// 聊天
	apiGroup.POST("/chat/completions", websocketHandler.CreateChatCompletion) // 放在 websocket 中处理，方便task和ws绑定
	apiGroup.GET("/tasks/chat/:chat_id", websocketHandler.GetChatTask)        // 放在 websocket 中处理，方便task和ws绑定
	apiGroup.POST("/tasks/stop/:task_id", websocketHandler.StopChatTask)
	apiGroup.POST("/chat/completed", websocketHandler.ChatCompleted)

	// api/v1 根路径
	apiV1Group := apiGroup.Group("/v1")

	// 公共文件
	apiV1Group.GET("/public/files/image/:file_id/content", fileHandler.GetPublicImageFile)
	apiV1Group.GET("/public/files/audio/:file_id/content", fileHandler.GetPublicAudioFile)

	// 模型广场
	apiV1Group.POST("/topai/models", topaiModelHandler.GetTopaiModelList)
	apiV1Group.GET("/topai/models/categories", topaiModelHandler.GetUsedTopaiModelCategoryList)
	apiV1Group.GET("/topai/models/categories/all", topaiModelHandler.AllListCategories)
	apiV1Group.GET("/topai/models/series", topaiModelHandler.GetTopaiModelSeriesList)
	apiV1Group.GET("/topai/models/providers", topaiModelHandler.GetTopaiModelProviderList)
	apiV1Group.GET("/topai/models/support-params", topaiModelHandler.GetTopaiModelSupportParamList)
	apiV1Group.GET("/topai/models/types", topaiModelHandler.GetTopaiModelTypeList)
	apiV1Group.POST("/topai/models/ranking", topaiModelHandler.GetTopaiModelListByTokenSort)
	apiV1Group.GET("/topai/models/:model_id", topaiModelHandler.GetTopaiModelDetail)

	// 分享
	apiV1Group.GET("/chats/tti/all_shared", conversationHandler.GetAllTTIChatShared)

	// 模型提供者接口 - 不需要认证的部分
	apiV1Group.GET("/provider/nonce", providerHandler.GenerateLoginNonce) // 生成登录随机字符串
	apiV1Group.POST("/provider/login", providerHandler.Login)             // 钱包签名登录

	// 模型提供者接口 - 需要认证的部分
	providerRequired := apiV1Group.Group("")
	providerRequired.Use(middleware.ProviderAuth(service))
	providerRequired.GET("/provider/info", providerHandler.GetProviderInfo)                   // 获取提供者信息
	providerRequired.POST("/provider/info", providerHandler.UpdateProviderInfo)               // 更新提供者信息
	providerRequired.GET("/provider/models", providerHandler.GetProviderModels)               // 获取提供者的模型列表
	providerRequired.POST("/provider/models/:chain_model_id", providerHandler.UpdateModelExt) // 更新模型扩展信息
	providerRequired.GET("/provider/models/:chain_model_id", providerHandler.GetModelInfo)    // 获取模型信息
	providerRequired.GET("/provider/categories", providerHandler.GetAllCategories)            // 获取所有分类
	providerRequired.POST("/provider/models/image", providerHandler.UploadModelImage)         // 上传模型图片

	// 认证相关接口 - 不需要认证的部分
	apiV1Group.POST("/auths/signin", authHandler.Signin) // 登录
	apiV1Group.POST("/auths/signup", authHandler.Signup) // 注册
	apiV1Group.GET("/auths/admin/details", func(c *gin.Context) {
		api.Success(c, gin.H{
			"name":  "admin",
			"email": "<EMAIL>",
		})
	}) // 获取管理员信息

	// 系统状态接口
	apiV1Group.GET("/changelog", systemHandler.GetChangelog)

	// 轮播图管理
	apiV1Group.GET("/configs/banners", func(ctx *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(ctx, []interface{}{})
	})

	// 工具管理
	apiV1Group.GET("/tools", toolHandler.GetTools)

	// 函数管理
	apiV1Group.GET("/functions", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})

	// 组管理
	apiV1Group.GET("/groups", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})

	// 通道管理
	apiV1Group.GET("/channels", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})

	// 需要登陆的路径
	authRequired := apiV1Group.Group("")
	authRequired.Use(middleware.RateLimitByUserID(200, time.Minute))
	authRequired.Use(middleware.Auth(service))

	// 认证相关接口 - 需要认证的部分
	authRequired.GET("/auths", authHandler.Me)                              // 获取当前用户信息
	authRequired.GET("/auths/signout", authHandler.Signout)                 // 登出(改为GET)
	authRequired.POST("/auths/update/profile", authHandler.UpdateProfile)   // 更新用户资料(改为POST)
	authRequired.POST("/auths/update/password", authHandler.UpdatePassword) // 更新密码(改为POST)

	// 用户管理
	authRequired.GET("/users/user/settings", userHandler.GetUserSettings)
	authRequired.POST("/users/user/settings/update", userHandler.UpdateUserSettings)
	authRequired.GET("/user/balance", userHandler.GetUserBalance)
	authRequired.GET("/user/balance/record", userHandler.GetUserBalanceRecord)
	authRequired.GET("/user/recharge_address", userHandler.GetUserShadowAddress)

	// 用户app相关
	authRequired.GET("/user/app/list", userAppHandler.GetUserAppList)
	authRequired.GET("/user/app/info/:app_id", userAppHandler.GetUserAppDetail)
	authRequired.POST("/user/app/create", userAppHandler.CreateUserApp)
	authRequired.POST("/user/app/avatar", fileHandler.UploadUserAppAvatar)
	authRequired.POST("/user/app/update/:app_id", userAppHandler.UpdateUserApp)
	authRequired.POST("/user/app/delete/:app_id", userAppHandler.DeleteUserApp)
	authRequired.GET("/user/app/key/list", userAppHandler.GetUserAppKeyList)
	authRequired.POST("/user/app/key/:app_id/create", userAppHandler.CreateUserAppKey)
	authRequired.POST("/user/app/key/:app_id/delete/:key_name", userAppHandler.DeleteUserAppKey)

	// 聊天管理
	authRequired.GET("/chats", conversationHandler.GetConversations)
	authRequired.POST("/chats/new", conversationHandler.CreateConversation)
	authRequired.POST("/chats/asr", websocketHandler.StartChatASR)
	authRequired.POST("/chats/tts", websocketHandler.StartChatTTS)
	authRequired.POST("/chats/tti", websocketHandler.StartChatTTI)
	authRequired.POST("/chats/tti/share", conversationHandler.UpdateTTIChatShare)
	authRequired.GET("/chats/tti/shared", conversationHandler.GetUserTTIChatShared)
	authRequired.GET("/chats/:chat_id", conversationHandler.GetConversation)
	authRequired.POST("/chats/:chat_id", conversationHandler.UpdateConversation)
	authRequired.DELETE("/chats/:chat_id", conversationHandler.DeleteConversation)
	authRequired.GET("/chats/all/tags", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})
	authRequired.GET("/chats/pinned", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})
	authRequired.POST("/audio/speech/:chat_id/:msg_id", audioHandler.CreateSpeech)

	// 文件夹管理
	authRequired.GET("/folders", func(c *gin.Context) {
		// todo 直接返回空，需要的时候再处理
		api.Success(c, []interface{}{})
	})

	// 图像管理
	authRequired.GET("/images/models", imageHandler.GetImageBaseModels)
	// 语音管理
	authRequired.GET("/audio/models", audioHandler.GetAudioBaseModels)

	// 文件管理
	authRequired.POST("/files", fileHandler.UploadFile)
	authRequired.POST("/files/image", fileHandler.UploadImageFile)
	authRequired.DELETE("/files/:id", fileHandler.DeleteFile)
	authRequired.GET("/files/image/:file_id/content", fileHandler.GetImageFile)
	authRequired.GET("/files/audio/:file_id/content", fileHandler.GetAudioFile)
	// 需要管理员的路径
	adminRequired := apiV1Group.Group("admin")
	adminRequired.Use(middleware.AdminAuth(service))

	// 用户管理 - 需要管理员权限
	adminRequired.GET("/users", userHandler.AdminGetAllUsers)
	adminRequired.POST("/auths/add", userHandler.AdminCreateUser)
	adminRequired.POST("/users/:id/update", userHandler.AdminUpdateUser)
	adminRequired.DELETE("/users/:id", userHandler.AdminDeleteUser)
	adminRequired.POST("/users/update/role", userHandler.AdminUpdateUserRole)
	adminRequired.POST("/users/gift/balance", userHandler.AdminGiftBalance)

	// 新增管理员用户管理接口
	adminRequired.GET("/users/balance/list", userHandler.AdminGetUsersWithBalance)
	adminRequired.POST("/users/gift-balance", userHandler.AdminGiftUserBalance)

	// 管理员配置
	adminRequired.GET("/auths/admin/config", configHandler.GetSystemCommonConfig)     // 获取管理员配置
	adminRequired.POST("/auths/admin/config", configHandler.UpdateSystemCommonConfig) // 更新管理员配置

	// 模型管理
	adminRequired.GET("/models/base", modelHandler.GetAllBaseLLMModelsConfig)     // 获取所有基础模型
	adminRequired.POST("/models/create", modelHandler.CreateLLMModelConfig)       // 创建模型
	adminRequired.POST("/models/model/update", modelHandler.UpdateLLMModelConfig) // 更新模型
	adminRequired.POST("/models/model/toggle", modelHandler.ToggleLLMModelConfig) // 删除模型

	// 新增管理员模型管理接口
	adminRequired.GET("/models/list", topaiModelHandler.AdminGetModelList)
	adminRequired.GET("/models/:model_id", topaiModelHandler.AdminGetModelDetail)
	adminRequired.POST("/models/:model_id", topaiModelHandler.AdminUpdateModel)
	adminRequired.POST("/models/:model_id/status", topaiModelHandler.AdminUpdateModelStatus)
	adminRequired.POST("/models/central", topaiModelHandler.AdminCreateCentralModel)

	// 新增管理员供应商管理接口
	adminRequired.GET("/providers/list", topaiModelHandler.AdminGetProviderList)
	adminRequired.POST("/providers/create", topaiModelHandler.AdminCreateProvider)

	// 管理员分类/系列
	adminRequired.POST("/models/categories", topaiModelHandler.AdminCreateCategory)
	adminRequired.POST("/models/series", topaiModelHandler.AdminCreateSeries)

	// 语音管理
	adminRequired.GET("/audio/config", audioHandler.GetAudioConfig)
	adminRequired.POST("/audio/config", audioHandler.UpdateAudioConfig)
	adminRequired.GET("/audio/voices", audioHandler.GetAudioVoices)

	// 图像管理
	adminRequired.GET("/images/config", imageHandler.GetImageConfig)
	adminRequired.POST("/images/config", imageHandler.UpdateImageConfig)

	// 系统接口
	authRequired.GET("/version/updates", systemHandler.GetUpdates)

	// 并发限制状态查询 - 需要管理员权限
	adminRequired.GET("/system/concurrency/stats", systemHandler.GetConcurrencyStats)

	// 返回资源清理函数
	stop := func() {
		log.Info("正在关闭处理器...")
		authHandler.Close()
		conversationHandler.Close()
		modelHandler.Close()
		userHandler.Close()
		fileHandler.Close()
		knowledgeHandler.Close()
		toolHandler.Close()
		configHandler.Close()
		systemHandler.Close()
		websocketHandler.Close()
		audioHandler.Close()
		imageHandler.Close()
		log.Info("所有处理器已关闭")
	}

	return stop
}
