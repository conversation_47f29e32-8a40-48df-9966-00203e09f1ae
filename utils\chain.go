package utils

import (
	"crypto/ecdsa"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/crypto"
)

func MustParsePrivateKey(privateKey string) (*ecdsa.PrivateKey, error) {
	if privateKey[:2] == "0x" {
		privateKey = privateKey[2:]
	}
	key, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return nil, err
	}
	return key, nil
}

func FormatPrice(price *big.Float, precision int) string {
	trimZero := func(s string) string {
		if strings.Contains(s, ".") {
			s = strings.TrimRight(s, "0")
			if s[len(s)-1] == '.' {
				s = s[:len(s)-1]
			}
		}
		return s
	}

	if price.Cmp(big.NewFloat(1)) >= 0 {
		return trimZero(price.Text('f', precision))
	}
	// 判断有几个0，从第三位开始
	zeroCount := 0
	p := price.Text('f', -1)
	for i := 2; i < len(p); i++ {
		if p[i] == '0' {
			zeroCount++
		} else {
			break
		}
	}
	return trimZero(price.Text('f', precision+zeroCount))
}
