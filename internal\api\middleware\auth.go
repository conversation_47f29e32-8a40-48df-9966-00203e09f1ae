package middleware

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// 日志实例
var log *logger.ModuleLogger

func Init() {
	log = logger.GetLogger("middleware")
}

// Auth 认证中间件，验证用户是否已登录
func Auth(svc *service.Api) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		// 打印所有的header
		// for k, v := range c.Request.Header {
		// 	log.Info("header", zap.String("key", k), zap.String("value", strings.Join(v, ",")))
		// }
		if token != "" {
			// 检查token格式
			const prefix = "Bearer "
			if len(token) <= len(prefix) || !strings.HasPrefix(token, prefix) {
				api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("invalid token format"))
				c.Abort()
				return
			}
			// 提取实际token
			token = token[len(prefix):]
		}
		if token == "" {
			// 从cookie中提取
			token, _ = c.Cookie("token")
		}
		if token == "" {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing token"))
			c.Abort()
			return
		}

		// 获取用户信息
		user, err := svc.User.GetUserByToken(c.Request.Context(), token)
		if err != nil {
			log.Error("验证token失败", zap.Error(err))
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("invalid token"))
			c.Abort()
			return
		}

		if user.Role != repository.UserRoleAdmin.String() && user.Role != repository.UserRoleUser.String() {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("invalid user role"))
			c.Abort()
			return
		}

		// 设置只读的token cookie
		c.SetCookie("token", token, 0, "/", "", false, true)

		// 将用户信息保存到上下文
		c.Set("user", user)
		c.Next()
	}
}

// AdminAuth 管理员认证中间件，验证用户是否为管理员
func AdminAuth(svc *service.Api) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先验证用户是否已登录
		Auth(svc)(c)
		if c.IsAborted() {
			return
		}

		// 获取用户信息
		user, exists := c.Get("user")
		if !exists {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("user info not found"))
			c.Abort()
			return
		}

		userOp, _ := user.(*service.UserBaseOP)

		// 验证用户是否为管理员
		if userOp.Role != string(repository.UserRoleAdmin) {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("need admin permission"))
			c.Abort()
			return
		}

		c.Next()
	}
}

func AuthApiKey(svc *service.Api) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("Authorization")
		// 打印所有的header
		for k, v := range c.Request.Header {
			log.Info("header", zap.String("key", k), zap.String("value", strings.Join(v, ",")))
		}
		if apiKey == "" {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing api key"))
			c.Abort()
			return
		}
		// 检查token格式
		const prefix = "Bearer "
		if len(apiKey) <= len(prefix) || !strings.HasPrefix(apiKey, prefix) {
			api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("invalid token format"))
			c.Abort()
			return
		}
		// 提取实际token
		apiKey = apiKey[len(prefix):]

		// 检查apiKey
		app, err := svc.UserApp.GetAppByApiKey(c.Request.Context(), apiKey)
		if err != nil {
			api.Fail(c, api.CodeUnauthorized, "auth failed", err)
			c.Abort()
			return
		}
		c.Set("app_key_info", app)
		c.Next()
	}
}
