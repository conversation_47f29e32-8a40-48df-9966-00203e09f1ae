definitions:
  api.Response:
    properties:
      code:
        description: 业务状态码
        type: integer
      data:
        description: 响应数据
      error:
        description: 错误详情（仅在开发环境显示）
        type: string
      msg:
        description: 用户消息
        type: string
    type: object
  handlers.AdminCreateCategoryRequest:
    properties:
      name:
        type: string
    required:
    - name
    type: object
  handlers.AdminCreateProviderRequest:
    properties:
      name:
        description: 供应商名称
        type: string
    required:
    - name
    type: object
  handlers.AdminCreateSeriesRequest:
    properties:
      name:
        type: string
    required:
    - name
    type: object
  handlers.AdminGiftBalanceRequest:
    properties:
      amount:
        description: 赠送金额
        type: number
      description:
        description: 描述
        type: string
      user_identity_id:
        description: 用户身份ID
        type: string
    required:
    - amount
    - user_identity_id
    type: object
  handlers.AdminModelStatusRequest:
    properties:
      action:
        description: 操作类型：online（上线）、offline（下线）、delete（删除）
        type: string
    required:
    - action
    type: object
  handlers.AdminProviderListItem:
    properties:
      created_at:
        description: 创建时间
        type: integer
      id:
        description: 供应商ID
        type: integer
      is_centralized:
        description: 是否中心化供应商（wallet_address为空）
        type: boolean
      name:
        description: 供应商名称
        type: string
      uuid:
        description: 供应商UUID
        type: string
      wallet_address:
        description: 钱包地址
        type: string
    type: object
  handlers.AdminProviderListResponse:
    properties:
      providers:
        items:
          $ref: '#/definitions/handlers.AdminProviderListItem'
        type: array
      total:
        type: integer
    type: object
  handlers.ChatCompletedRequest:
    properties:
      chat_id:
        type: string
      id:
        type: string
      messages:
        items:
          properties:
            content:
              type: string
            id:
              type: string
            role:
              type: string
            timestamp:
              type: integer
          type: object
        type: array
      model:
        type: string
      model_item:
        $ref: '#/definitions/service.AvailableModel'
      session_id:
        type: string
    type: object
  handlers.Conversation:
    properties:
      created_at:
        type: string
      id:
        type: integer
      title:
        type: string
      updated_at:
        type: string
    type: object
  handlers.CreateUserAppKeyRequest:
    properties:
      key_name:
        type: string
    type: object
  handlers.CreateUserRequest:
    properties:
      email:
        type: string
      name:
        type: string
      password:
        minLength: 6
        type: string
      role:
        type: string
    required:
    - email
    - name
    - password
    - role
    type: object
  handlers.GiftBalanceRequest:
    properties:
      amount:
        type: integer
      to_user_id:
        type: string
    type: object
  handlers.SigninRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  handlers.SignupRequest:
    properties:
      email:
        type: string
      name:
        maxLength: 50
        minLength: 3
        type: string
      password:
        minLength: 6
        type: string
      profile_image_id:
        type: string
    required:
    - email
    - name
    - password
    type: object
  handlers.UpdateChatShareRequest:
    properties:
      id:
        type: string
      is_share:
        type: boolean
    type: object
  handlers.UpdatePasswordRequest:
    properties:
      new_password:
        minLength: 6
        type: string
      password:
        type: string
    required:
    - new_password
    - password
    type: object
  handlers.UpdateProfileRequest:
    properties:
      email:
        type: string
      name:
        type: string
      profile_image_id:
        type: string
    type: object
  handlers.UpdateUserRequest:
    properties:
      email:
        type: string
      name:
        type: string
      password:
        type: string
      profile_image_url:
        type: string
    type: object
  handlers.UpdateUserRoleRequest:
    properties:
      id:
        type: string
      role:
        type: string
    required:
    - id
    - role
    type: object
  service.AdminCreateCentralModelRequest:
    properties:
      category_ids:
        items:
          type: integer
        type: array
      context_length:
        type: integer
      description:
        type: string
      input_price:
        type: string
      max_output:
        type: integer
      model_name:
        type: string
      model_type:
        type: string
      model_version:
        type: string
      output_price:
        type: string
      provider_id:
        type: integer
      series_id:
        type: integer
      weight:
        type: integer
    type: object
  service.AdminModelDetailResponse:
    properties:
      context_length:
        description: 上下文长度
        type: integer
      created_at:
        description: 创建时间
        type: integer
      description:
        description: 描述
        type: string
      id:
        description: 模型ID
        type: integer
      input_price:
        description: 输入价格
        type: string
      is_on_chain:
        description: 是否链上模型
        type: boolean
      latency:
        description: 延迟
        type: integer
      max_output:
        description: 最大输出
        type: integer
      model_id:
        description: 模型标识符
        type: string
      model_name:
        description: 模型名称
        type: string
      model_version:
        description: 模型版本
        type: string
      output_price:
        description: 输出价格
        type: string
      provider_name:
        description: 提供商名称
        type: string
      sample_code:
        description: 示例代码
        type: string
      series_id:
        description: 系列ID
        type: integer
      show_picture:
        description: 展示图片
        type: string
      show_video:
        description: 展示视频
        type: string
      status:
        description: 上下架状态：1上线，0下线，-1删除
        type: integer
      throughput:
        description: 吞吐量
        type: integer
      token_consumption:
        description: token消耗量
        type: string
      updated_at:
        description: 更新时间
        type: integer
      weight:
        description: 权重
        type: integer
    type: object
  service.AdminModelListItem:
    properties:
      created_at:
        description: 创建时间
        type: integer
      description:
        description: 描述
        type: string
      id:
        description: 模型ID
        type: integer
      input_price:
        description: 输入价格
        type: string
      is_on_chain:
        description: 是否链上模型
        type: boolean
      model_id:
        description: 模型标识符
        type: string
      model_name:
        description: 模型名称
        type: string
      model_version:
        description: 模型版本
        type: string
      output_price:
        description: 输出价格
        type: string
      provider_name:
        description: 提供商名称
        type: string
      status:
        description: 上下架状态：1上线，0下线，-1删除
        type: integer
      token_consumption:
        description: token消耗量
        type: string
      updated_at:
        description: 更新时间
        type: integer
    type: object
  service.AdminModelListResponse:
    properties:
      models:
        items:
          $ref: '#/definitions/service.AdminModelListItem'
        type: array
      total:
        type: integer
    type: object
  service.AdminUpdateModelRequest:
    properties:
      context_length:
        description: 上下文长度（仅非链上模型可编辑）
        type: integer
      description:
        description: 描述（链上和非链上模型都可以编辑）
        type: string
      input_price:
        description: 输入价格（仅非链上模型可编辑）
        type: string
      max_output:
        description: 最大输出（仅非链上模型可编辑）
        type: integer
      output_price:
        description: 输出价格（仅非链上模型可编辑）
        type: string
      weight:
        description: 权重
        type: integer
    type: object
  service.AdminUserWithBalance:
    properties:
      accumulated_total:
        description: 累计充值
        type: string
      created_at:
        type: integer
      current_balance:
        description: 当前余额
        type: string
      email:
        type: string
      id:
        type: integer
      identity_id:
        type: string
      role:
        type: string
      updated_at:
        type: integer
      username:
        type: string
    type: object
  service.AdminUsersListResponse:
    properties:
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/service.AdminUserWithBalance'
        type: array
    type: object
  service.AppKeyOP:
    properties:
      app_id:
        type: string
      app_name:
        type: string
      created_at:
        type: integer
      key:
        type: string
      key_name:
        type: string
    type: object
  service.AudioBaseModel:
    properties:
      asr:
        items:
          $ref: '#/definitions/service.AudioModel'
        type: array
      tts:
        items:
          $ref: '#/definitions/service.AudioModel'
        type: array
    type: object
  service.AudioConfig:
    properties:
      asr:
        allOf:
        - $ref: '#/definitions/service.AudioConfigASR'
        description: 语音转文本
      tts:
        allOf:
        - $ref: '#/definitions/service.AudioConfigTTS'
        description: 文本转语音
    type: object
  service.AudioConfigASR:
    properties:
      ENABLE:
        description: 是否启用语音转文本
        type: boolean
      MODEL:
        description: 模型，为空表示使用默认模型
        type: string
    type: object
  service.AudioConfigTTS:
    properties:
      ENABLE:
        description: 是否启用图片生成
        type: boolean
      MODEL:
        description: 模型，为空表示使用默认模型
        type: string
      VOICE:
        description: 音色，为空表示使用默认音色
        type: string
    type: object
  service.AudioModel:
    properties:
      id:
        type: string
      voices:
        items:
          $ref: '#/definitions/service.AudioVoice'
        type: array
    type: object
  service.AudioVoice:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
  service.AvailableModel:
    properties:
      actions:
        items: {}
        type: array
      active:
        type: boolean
      context_window:
        type: integer
      created:
        type: integer
      id:
        type: string
      info:
        $ref: '#/definitions/service.ModelConfigIO'
      max_completion_tokens:
        type: integer
      model_type:
        type: string
      name:
        type: string
      object:
        type: string
      owned_by:
        type: string
      public_apps: {}
      remote:
        $ref: '#/definitions/service.RemoteModel'
      tags:
        items:
          type: string
        type: array
      total_tokens:
        type: string
      urlIdx:
        type: integer
    type: object
  service.BaseModel:
    properties:
      active:
        type: boolean
      context_window:
        type: integer
      created_at:
        type: integer
      id:
        type: string
      max_completion_tokens:
        type: integer
      model_type:
        type: string
      name:
        type: string
      object:
        type: string
      owned_by:
        type: string
      public_apps: {}
      remote:
        $ref: '#/definitions/service.RemoteModel'
      urlIdx:
        type: integer
    type: object
  service.ChatASRByApiResponse:
    properties:
      text:
        type: string
    type: object
  service.ChatCompletionMessage:
    properties:
      content:
        type: string
      file_uuid:
        type: string
      role:
        type: string
    type: object
  service.ChatLLMByApiMessage:
    properties:
      content:
        type: string
      role:
        type: string
    type: object
  service.ChatLLMByApiRequest:
    properties:
      frequency_penalty:
        type: number
      max_tokens:
        type: integer
      messages:
        items:
          $ref: '#/definitions/service.ChatLLMByApiMessage'
        type: array
      model:
        type: string
      presence_penalty:
        type: number
      response_format:
        $ref: '#/definitions/service.ChatLLMByApiResponseFormat'
      seed:
        type: integer
      stop:
        items:
          type: string
        type: array
      stream:
        type: boolean
      temperature:
        type: number
      top_p:
        type: number
      user:
        type: string
    type: object
  service.ChatLLMByApiResponseFormat:
    properties:
      type:
        type: string
    type: object
  service.ChatTTIByApiRequest:
    properties:
      model:
        type: string
      prompt:
        type: string
      response_format:
        type: string
      size:
        type: string
      user:
        type: string
    type: object
  service.ChatTTIByApiResponse:
    properties:
      created:
        type: integer
      data:
        properties:
          b64_json:
            type: string
          revised_prompt:
            type: string
        type: object
    type: object
  service.ChatTTSByApiRequest:
    properties:
      input:
        type: string
      model:
        type: string
      response_format:
        type: string
      speed:
        type: number
      voice:
        type: string
    type: object
  service.Conversation:
    properties:
      files:
        items:
          $ref: '#/definitions/service.ConversationMessageFile'
        type: array
      history:
        $ref: '#/definitions/service.ConversationHistory'
      id:
        type: string
      messages:
        items:
          $ref: '#/definitions/service.ConversationMessage'
        type: array
      models:
        items:
          type: string
        type: array
      params: {}
      system:
        type: string
      tags:
        items:
          type: string
        type: array
      timestamp:
        description: 注意，此处是毫秒
        type: integer
      title:
        type: string
    type: object
  service.ConversationHistory:
    properties:
      currentId:
        type: string
      messages:
        additionalProperties:
          $ref: '#/definitions/service.ConversationMessage'
        type: object
    type: object
  service.ConversationMessage:
    properties:
      childrenIds:
        items:
          type: string
        type: array
      content:
        type: string
      done:
        type: boolean
      files:
        items:
          $ref: '#/definitions/service.ConversationMessageFile'
        type: array
      id:
        type: string
      lastSentence:
        type: string
      model:
        type: string
      models:
        items:
          type: string
        type: array
      parentId:
        type: string
      role:
        type: string
      timestamp:
        description: 注意，此处是秒
        type: integer
      userContext: {}
    type: object
  service.ConversationMessageFile:
    properties:
      collection_name:
        type: string
      error:
        type: string
      file:
        $ref: '#/definitions/service.FileInfoOP'
      id:
        type: string
      item_id:
        type: string
      name:
        type: string
      size:
        type: integer
      status:
        type: string
      type:
        type: string
      url:
        type: string
    type: object
  service.ConversationOP:
    properties:
      archived:
        type: boolean
      chat:
        $ref: '#/definitions/service.Conversation'
      created_at:
        type: integer
      folder_id: {}
      id:
        type: string
      meta: {}
      pinned:
        type: boolean
      share_id: {}
      title:
        type: string
      updated_at:
        type: integer
      user_id:
        type: string
    type: object
  service.CreateChatCompletionRequest:
    properties:
      chat_id:
        type: string
      features:
        properties:
          code_interpreter:
            type: boolean
          image_generation:
            type: boolean
          web_search:
            type: boolean
        type: object
      files:
        items:
          $ref: '#/definitions/service.ConversationMessageFile'
        type: array
      id:
        type: string
      messages:
        items:
          $ref: '#/definitions/service.ChatCompletionMessage'
        type: array
      model:
        type: string
      model_item:
        $ref: '#/definitions/service.AvailableModel'
      params: {}
      session_id:
        type: string
      stream:
        type: boolean
      tool_servers:
        items: {}
        type: array
      variables:
        additionalProperties: true
        type: object
    type: object
  service.CreateConversationRequest:
    properties:
      chat:
        $ref: '#/definitions/service.Conversation'
    type: object
  service.FileInfoData:
    properties:
      content:
        type: string
    type: object
  service.FileInfoMeta:
    properties:
      collection_name:
        type: string
      content_type:
        type: string
      data: {}
      name:
        type: string
      size:
        type: integer
    type: object
  service.FileInfoOP:
    properties:
      created_at:
        type: integer
      data:
        $ref: '#/definitions/service.FileInfoData'
      file_name:
        type: string
      hash:
        type: string
      id:
        type: string
      meta:
        $ref: '#/definitions/service.FileInfoMeta'
      updated_at:
        type: integer
      url:
        type: string
      user_id:
        type: string
    type: object
  service.GetTopaiModelListRequest:
    properties:
      category_ids:
        items:
          type: integer
        type: array
      model_ids:
        items:
          type: integer
        type: array
      model_types:
        items:
          type: string
        type: array
      order_by:
        type: string
      order_type:
        type: string
      page:
        type: integer
      page_size:
        type: integer
      price_free:
        type: boolean
      providers:
        items:
          type: string
        type: array
      search:
        type: string
      series_ids:
        items:
          type: integer
        type: array
      support_params:
        items:
          type: string
        type: array
    type: object
  service.GetUsageResponse:
    properties:
      id:
        type: string
      model:
        type: string
      tokens_completion:
        type: string
      tokens_prompt:
        type: string
      total_cost:
        type: string
    type: object
  service.ImageConfig:
    properties:
      CONFIG:
        description: 模型配置
      ENABLE:
        description: 是否启用图片生成
        type: boolean
      ENGINE:
        description: 模型分类，model/comfuui
        type: string
      MODEL:
        description: 选择的模型id
        type: string
      max_image_size:
        description: 最大图片大小
        type: integer
      max_text_length:
        description: 最大文本长度
        type: integer
    type: object
  service.ImageModel:
    properties:
      engine:
        description: 模型分类，model/comfuui
        type: string
      model_id:
        description: 模型id
        type: string
      params:
        description: 模型配置参数
    type: object
  service.ModelConfigAccessControl:
    properties:
      read:
        properties:
          group_ids:
            items:
              type: string
            type: array
          user_ids:
            items:
              type: string
            type: array
        type: object
      write:
        properties:
          group_ids:
            items:
              type: string
            type: array
          user_ids:
            items:
              type: string
            type: array
        type: object
    type: object
  service.ModelConfigIO:
    properties:
      access_control:
        $ref: '#/definitions/service.ModelConfigAccessControl'
      base_model_id:
        type: string
      created_at:
        type: integer
      id:
        type: string
      is_active:
        type: boolean
      meta:
        $ref: '#/definitions/service.ModelConfigMeta'
      name:
        type: string
      params: {}
      updated_at:
        type: integer
    type: object
  service.ModelConfigMeta:
    properties:
      capabilities:
        properties:
          citations:
            type: boolean
          vision:
            type: boolean
        type: object
      description:
        type: string
      profile_image_url:
        type: string
      suggestion_prompts: {}
      tags:
        items:
          type: string
        type: array
    type: object
  service.ProviderAuthRequest:
    description: 模型提供者登录请求参数
    properties:
      nonce:
        description: 随机字符串
        example: a1b2c3d4e5f6
        type: string
      signature:
        description: 钱包签名
        example: "0xabcdef1234567890"
        type: string
      timestamp:
        description: 时间戳
        example: **********
        type: integer
      wallet_addr:
        description: 钱包地址
        example: "0x1234567890abcdef"
        type: string
    required:
    - nonce
    - signature
    - timestamp
    - wallet_addr
    type: object
  service.ProviderAuthResponse:
    description: 模型提供者登录响应
    properties:
      expires_at:
        description: 过期时间戳
        example: **********
        type: integer
      token:
        description: 访问令牌
        example: abc123def456
        type: string
    type: object
  service.ProviderInfo:
    description: 模型提供者基本信息
    properties:
      created_at:
        description: 创建时间戳
        example: **********
        type: integer
      id:
        description: 提供者ID
        example: 1
        type: integer
      name:
        description: 提供者名称
        example: AI模型提供者
        type: string
      token:
        description: 访问令牌
        type: string
      uuid:
        description: 提供者UUID
        example: 550e8400-e29b-41d4-a716-************
        type: string
      wallet_address:
        description: 钱包地址
        example: "0x1234567890abcdef"
        type: string
    type: object
  service.ProviderModelInfo:
    description: 提供者的模型详细信息
    properties:
      category_ids:
        description: 分类
        items:
          type: integer
        type: array
      chain_model_id:
        description: 链上模型ID
        example: 100
        type: integer
      description:
        description: 扩展信息
        example: 强大的语言模型
        type: string
      input_price:
        description: 输入价格
        example: "0.001"
        type: string
      model_id:
        description: 模型ID
        example: "1"
        type: string
      model_name:
        description: 模型名称
        example: GPT-4
        type: string
      model_type:
        description: 模型类型
        example: text-generation
        type: string
      model_version:
        description: 模型版本
        example: "1.0"
        type: string
      output_price:
        description: 输出价格
        example: "0.002"
        type: string
      owner_address:
        description: 所有者地址
        example: "0x1234567890abcdef"
        type: string
      series_id:
        description: 系列ID
        example: 1
        type: integer
      show_picture:
        description: 展示图片
        example: https://example.com/pic.jpg
        type: string
      show_video:
        description: 展示视频
        example: https://example.com/video.mp4
        type: string
      status:
        description: 状态
        example: 1
        type: integer
      tts_voices:
        description: TTS音色列表，只有模型为TTS时才会被使用
        items:
          type: string
        type: array
    type: object
  service.ProviderModelList:
    properties:
      count:
        type: integer
      models:
        items:
          $ref: '#/definitions/service.ProviderModelInfo'
        type: array
    type: object
  service.RemoteModel:
    properties:
      active:
        type: boolean
      context_window:
        type: integer
      created:
        type: integer
      id:
        type: string
      max_completion_tokens:
        type: integer
      object:
        type: string
      owned_by:
        type: string
      public_apps: {}
    type: object
  service.StartChatASRResponse:
    properties:
      content:
        type: string
      id:
        type: string
    type: object
  service.StartChatTTIRequest:
    properties:
      content:
        type: string
      model_id:
        type: string
      response_format:
        type: string
      size:
        type: string
    type: object
  service.StartChatTTIResponse:
    properties:
      id:
        type: string
      url:
        type: string
    type: object
  service.StartChatTTSRequest:
    properties:
      content:
        type: string
      model_id:
        type: string
      speed:
        type: number
      voice:
        type: string
    type: object
  service.StartChatTTSResponse:
    properties:
      id:
        type: string
      url:
        type: string
    type: object
  service.SystemCommonConfig:
    properties:
      API_KEY_ALLOWED_ENDPOINTS:
        description: api key 允许的路由
        type: string
      DEFAULT_USER_ROLE:
        description: 默认用户角色
        type: string
      ENABLE_API_KEY:
        description: 是否启用 api key
        type: boolean
      ENABLE_API_KEY_ENDPOINT_RESTRICTIONS:
        description: 是否启用 api key 限制路由
        type: boolean
      ENABLE_CHANNELS:
        description: 是否启用频道
        type: boolean
      ENABLE_COMMUNITY_SHARING:
        description: 是否启用社区分享
        type: boolean
      ENABLE_MESSAGE_RATING:
        description: 是否启用消息评分
        type: boolean
      ENABLE_SIGNUP:
        description: 是否启用注册
        type: boolean
      JWT_EXPIRES_IN:
        description: jwt 过期时间
        type: string
      SHOW_ADMIN_DETAILS:
        description: 是否显示管理员详情
        type: boolean
      WEBUI_URL:
        description: 前端 url
        type: string
    type: object
  service.TTIChatSharedListOP:
    properties:
      count:
        type: integer
      list:
        items:
          $ref: '#/definitions/service.TTIChatSharedOP'
        type: array
    type: object
  service.TTIChatSharedOP:
    properties:
      content:
        type: string
      created_at:
        type: integer
      file_urls:
        items:
          type: string
        type: array
      id:
        type: string
      is_shared:
        type: boolean
      model_id:
        type: string
      model_type:
        type: string
    type: object
  service.TopaiModelAppUsedOP:
    properties:
      app_avatar:
        type: string
      app_id:
        type: string
      app_name:
        type: string
      description:
        type: string
      total_input_tokens:
        type: string
      total_output_tokens:
        type: string
      total_tokens:
        type: string
      website:
        type: string
    type: object
  service.TopaiModelCategoryOP:
    properties:
      belong_to:
        type: string
      category_id:
        type: integer
      category_name:
        type: string
    type: object
  service.TopaiModelDayPutDataOP:
    properties:
      count:
        type: integer
      day:
        type: string
      total_input_tokens:
        type: string
      total_output_tokens:
        type: string
      total_tokens:
        type: string
    type: object
  service.TopaiModelProviderOP:
    properties:
      provider_id:
        type: string
      provider_name:
        type: string
    type: object
  service.TopaiModelSeriesOP:
    properties:
      series_id:
        type: integer
      series_name:
        type: string
    type: object
  service.TopaiModelsInfoDetailOP:
    properties:
      app_used:
        items:
          $ref: '#/definitions/service.TopaiModelAppUsedOP'
        type: array
      category_list:
        items:
          $ref: '#/definitions/service.TopaiModelCategoryOP'
        type: array
      context_length:
        type: string
      day_put_data:
        items:
          $ref: '#/definitions/service.TopaiModelDayPutDataOP'
        type: array
      description:
        type: string
      input_price:
        type: string
      is_free:
        type: boolean
      max_output:
        type: string
      model_id:
        type: string
      model_name:
        type: string
      model_type:
        type: string
      output_price:
        type: string
      provider:
        items:
          $ref: '#/definitions/service.TopaiModelsInfoOP'
        type: array
      provider_name:
        type: string
      sample_code:
        additionalProperties:
          type: string
        type: object
      series_id:
        type: integer
      series_name:
        type: string
      show_picture:
        type: string
      throughput:
        items: {}
        type: array
      total_tokens:
        type: string
    type: object
  service.TopaiModelsInfoOP:
    properties:
      category_list:
        items:
          $ref: '#/definitions/service.TopaiModelCategoryOP'
        type: array
      context_length:
        type: string
      description:
        type: string
      input_price:
        type: string
      is_free:
        type: boolean
      max_output:
        type: string
      model_id:
        type: string
      model_name:
        type: string
      model_type:
        type: string
      output_price:
        type: string
      provider_name:
        type: string
      show_picture:
        type: string
      total_tokens:
        type: string
    type: object
  service.UpdateConversationRequest:
    properties:
      chat:
        $ref: '#/definitions/service.Conversation'
    type: object
  service.UpdateModelExtRequest:
    type: object
  service.UpdateProviderInfoRequest:
    description: 更新提供者信息请求参数
    properties:
      name:
        description: 提供者名称
        example: AI模型提供者
        type: string
    required:
    - name
    type: object
  service.UserAdminOP:
    properties:
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/service.UserAdminUser'
        type: array
    type: object
  service.UserAdminUser:
    properties:
      api_key:
        type: string
      created_at:
        type: integer
      email:
        type: string
      expires_at:
        type: integer
      id:
        type: string
      info: {}
      last_active_at:
        type: integer
      name:
        type: string
      oauth_sub: {}
      profile_image_url:
        type: string
      role:
        type: string
      settings: {}
      token:
        type: string
      token_type:
        type: string
      updated_at:
        type: integer
    type: object
  service.UserAppInfoOP:
    properties:
      app_account_id:
        type: string
      app_created_at:
        type: integer
      app_description:
        type: string
      app_id:
        type: string
      app_logo:
        type: string
      app_name:
        type: string
      app_status:
        type: integer
      app_website:
        type: string
    type: object
  service.UserAuthResponse:
    properties:
      expires_at:
        type: integer
      token:
        type: string
      user:
        $ref: '#/definitions/service.UserBaseOP'
    type: object
  service.UserBalanceRecord:
    properties:
      amount:
        type: string
      created_at:
        type: integer
      description:
        type: string
      id:
        type: integer
      is_in:
        type: boolean
      type:
        type: string
    type: object
  service.UserBalanceRecordOP:
    properties:
      records:
        items:
          $ref: '#/definitions/service.UserBalanceRecord'
        type: array
      total:
        type: integer
    type: object
  service.UserBaseOP:
    properties:
      email:
        type: string
      expires_at:
        type: integer
      id:
        type: string
      name:
        type: string
      profile_image_url:
        type: string
      role:
        type: string
      token:
        type: string
      token_type:
        type: string
    type: object
  service.UserSettings:
    properties:
      system:
        type: string
    type: object
host: 127.0.0.1:8080
info:
  contact: {}
  description: 用户登陆，聊天系统
  title: topai web服务api文档
  version: "1.0"
paths:
  /api/chat/completed:
    post:
      consumes:
      - application/json
      description: 聊天完成
      parameters:
      - description: 聊天完成请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.ChatCompletedRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ChatCompletedRequest'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 聊天完成
      tags:
      - websocket
  /api/chat/completions:
    post:
      consumes:
      - application/json
      description: 开始聊天
      parameters:
      - description: 聊天ID
        in: path
        name: chat_id
        required: true
        type: string
      - description: 聊天请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.CreateChatCompletionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 开始聊天
      tags:
      - websocket
  /api/chat/tasks/:chat_id:
    get:
      consumes:
      - application/json
      description: 获取聊天任务
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取聊天任务
      tags:
      - websocket
  /api/config:
    get:
      description: 获取配置
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.SystemCommonConfig'
        "400":
          description: Bad Request
          schema:
            type: string
      summary: 获取配置
      tags:
      - 配置
  /api/models/base:
    get:
      consumes:
      - application/json
      description: 获取所有基础模型
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.BaseModel'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取所有基础模型
      tags:
      - model
  /api/openai/audio/speech:
    post:
      consumes:
      - application/json
      description: 文本转语音
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ChatTTSByApiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: integer
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 文本转语音
      tags:
      - openai-api
  /api/openai/audio/transcriptions:
    post:
      consumes:
      - application/json
      description: 语音转文本
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: file
        in: formData
        name: file
        required: true
        type: file
      - description: model
        in: formData
        name: model
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ChatASRByApiResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 语音转文本
      tags:
      - openai-api
  /api/openai/chat/completions:
    post:
      consumes:
      - application/json
      description: 大语言模型聊天(openai标准流式和非流式)
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ChatLLMByApiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 大语言模型聊天
      tags:
      - openai-api
  /api/openai/images/generations:
    post:
      consumes:
      - application/json
      description: 文本转图像
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ChatTTIByApiRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ChatTTIByApiResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 文本转图像
      tags:
      - openai-api
  /api/openai/usage/{request_id}:
    get:
      consumes:
      - application/json
      description: 获取使用情况
      parameters:
      - description: Authorization
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetUsageResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取使用情况
      tags:
      - openai-api
  /api/v1//topai/models/categories/all:
    get:
      consumes:
      - application/json
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelCategoryOP'
            type: array
      summary: 全部分类列表
      tags:
      - 模型管理
  /api/v1/admin/models/{model_id}:
    get:
      consumes:
      - application/json
      description: 获取指定模型的详细信息
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 模型ID
        in: path
        name: model_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AdminModelDetailResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员查看模型详细信息
      tags:
      - 管理员-模型管理
    post:
      consumes:
      - application/json
      description: 编辑模型详情，链上模型只允许编辑描述，非链上模型可以编辑输入输出价格等
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 模型ID
        in: path
        name: model_id
        required: true
        type: string
      - description: 更新模型请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.AdminUpdateModelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员编辑模型详情
      tags:
      - 管理员-模型管理
  /api/v1/admin/models/{model_id}/status:
    post:
      consumes:
      - application/json
      description: 管理员对模型进行上线、下线或删除操作
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 模型ID
        in: path
        name: model_id
        required: true
        type: string
      - description: 状态操作请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AdminModelStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员模型上下架/删除
      tags:
      - 管理员-模型管理
  /api/v1/admin/models/categories:
    post:
      consumes:
      - application/json
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 创建分类
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AdminCreateCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TopaiModelCategoryOP'
      summary: 管理员新增分类
      tags:
      - 管理员-模型管理
  /api/v1/admin/models/central:
    post:
      consumes:
      - application/json
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 创建中心化模型
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.AdminCreateCentralModelRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AdminModelDetailResponse'
      summary: 管理员新增中心化模型
      tags:
      - 管理员-模型管理
  /api/v1/admin/models/list:
    get:
      consumes:
      - application/json
      description: 获取所有模型信息，包含输入输出价格、token消耗量、上下架状态等
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 状态筛选：1上线，0下线，-1删除，不传则查询上线和下线，多个以逗号分隔
        in: query
        name: status
        type: integer
      - description: 是否链上模型筛选，1是，0否，不传则查询所有
        in: query
        name: is_onchain
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AdminModelListResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员查看模型列表
      tags:
      - 管理员-模型管理
  /api/v1/admin/models/series:
    post:
      consumes:
      - application/json
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 创建系列
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AdminCreateSeriesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TopaiModelSeriesOP'
      summary: 管理员新增系列
      tags:
      - 管理员-模型管理
  /api/v1/admin/providers/create:
    post:
      consumes:
      - application/json
      description: 创建新的中心化供应商（wallet_address为空）
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 创建供应商请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AdminCreateProviderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.AdminProviderListItem'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员新增中心化供应商
      tags:
      - 管理员-供应商管理
  /api/v1/admin/providers/list:
    get:
      consumes:
      - application/json
      description: 获取所有供应商信息，区分中心化/链上供应商
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: page_size
        type: integer
      - description: 是否中心化供应商筛选（wallet_address 以 self- 开头）
        in: query
        name: is_centralized
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.AdminProviderListResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员查看供应商列表
      tags:
      - 管理员-供应商管理
  /api/v1/admin/users/gift-balance:
    post:
      consumes:
      - application/json
      description: 管理员向指定用户赠送余额
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 赠送余额请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.AdminGiftBalanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员赠送用户余额
      tags:
      - 管理员-用户管理
  /api/v1/admin/users/list:
    get:
      consumes:
      - application/json
      description: 获取所有用户信息，包含当前余额和累计充值
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: page_size
        type: integer
      - description: 排序字段，默认created_at
        in: query
        name: order
        type: string
      - description: 排序方向，asc或desc，默认asc
        in: query
        name: direction
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AdminUsersListResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员查看用户列表（带余额信息）
      tags:
      - 管理员-用户管理
  /api/v1/audio/config:
    get:
      consumes:
      - application/json
      description: 语音管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AudioConfig'
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取音频配置
    post:
      consumes:
      - application/json
      description: 语音管理
      parameters:
      - description: 音频配置
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/service.AudioConfig'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AudioConfig'
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新音频配置
  /api/v1/audio/models:
    get:
      consumes:
      - application/json
      description: 语音管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.AudioBaseModel'
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取音频基础模型
  /api/v1/audio/speech/{chat_id}/{msg_id}:
    post:
      consumes:
      - application/json
      description: 语音管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: integer
            type: array
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 创建语音
  /api/v1/audio/voices:
    get:
      consumes:
      - application/json
      description: 语音管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.AudioVoice'
            type: array
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取音频声音
  /api/v1/chats:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有聊天记录
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页条数
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ConversationOP'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取聊天记录列表
      tags:
      - conversation
  /api/v1/chats/:chat_id:
    delete:
      consumes:
      - application/json
      description: 删除指定的对话
      parameters:
      - description: 对话ID
        in: path
        name: chat_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 删除对话
      tags:
      - conversation
  /api/v1/chats/{chat_id}:
    post:
      consumes:
      - application/json
      description: 更新指定ID的聊天
      parameters:
      - description: 聊天ID
        in: path
        name: id
        required: true
        type: string
      - description: 聊天信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.UpdateConversationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.Conversation'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新聊天
      tags:
      - conversation
  /api/v1/chats/asr:
    post:
      consumes:
      - application/json
      description: 开始ASR对话
      parameters:
      - description: file
        in: formData
        name: file
        required: true
        type: file
      - description: model_id
        in: formData
        name: model_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.StartChatASRResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 开始ASR对话
      tags:
      - conversation
  /api/v1/chats/new:
    post:
      consumes:
      - application/json
      description: 创建新的对话
      parameters:
      - description: 对话信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.CreateConversationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ConversationOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 创建新对话
      tags:
      - conversation
  /api/v1/chats/tti:
    post:
      consumes:
      - application/json
      description: 开始TTI对话
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.StartChatTTIRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.StartChatTTIResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 开始TTI对话
      tags:
      - conversation
  /api/v1/chats/tti/all_shared:
    get:
      consumes:
      - application/json
      description: 获取所有分享的tti对话(不需要认证)
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页条数
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TTIChatSharedListOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取所有分享的tti对话
      tags:
      - conversation
  /api/v1/chats/tti/share:
    post:
      consumes:
      - application/json
      description: 分享对话
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateChatShareRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 分享对话
      tags:
      - conversation
  /api/v1/chats/tti/shared:
    get:
      consumes:
      - application/json
      description: 获取用户分享的tti对话
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页条数
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TTIChatSharedListOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户分享的tti对话
      tags:
      - conversation
  /api/v1/chats/tts:
    post:
      consumes:
      - application/json
      description: 开始TTS对话
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.StartChatTTSRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.StartChatTTSResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 开始TTS对话
      tags:
      - conversation
  /api/v1/conversations/{id}:
    get:
      consumes:
      - application/json
      description: 获取指定对话的详细信息
      parameters:
      - description: 对话ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Conversation'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取对话详情
      tags:
      - conversation
  /api/v1/files/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定文件
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 文件ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 删除文件
      tags:
      - 文件管理
  /api/v1/files/audio/{file_id}/content:
    get:
      consumes:
      - application/json
      description: 获取指定音频文件
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 文件ID
        in: path
        name: file_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 音频文件
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取音频文件
      tags:
      - 文件管理
  /api/v1/files/image:
    post:
      consumes:
      - multipart/form-data
      description: 上传新图片文件
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.FileInfoOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 上传图片文件
      tags:
      - 文件管理
  /api/v1/files/image/{file_id}/content:
    get:
      consumes:
      - application/json
      description: 获取指定图片文件
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 目录
        in: path
        name: dir
        required: true
        type: string
      - description: 图片名称
        in: path
        name: image_name
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 图片文件
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取图片文件
      tags:
      - 文件管理
  /api/v1/files/upload:
    post:
      consumes:
      - multipart/form-data
      description: 上传新文件
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.FileInfoOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 上传文件
      tags:
      - 文件管理
  /api/v1/images/config:
    get:
      consumes:
      - application/json
      description: 图像管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ImageConfig'
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取图像配置
    post:
      consumes:
      - application/json
      description: 图像管理
      parameters:
      - description: 图像配置
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/service.ImageConfig'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ImageConfig'
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新图像配置
  /api/v1/images/models:
    get:
      consumes:
      - application/json
      description: 图像管理
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ImageModel'
            type: array
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取图像基础模型
  /api/v1/knowledge:
    get:
      consumes:
      - application/json
      description: 获取所有知识库列表
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取知识库列表
      tags:
      - 知识库管理
  /api/v1/models:
    get:
      consumes:
      - application/json
      description: 获取可以使用的模型列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.AvailableModel'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取可以使用的模型列表
      tags:
      - model
  /api/v1/models/base:
    get:
      consumes:
      - application/json
      description: 获取所有基础模型配置
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ModelConfigIO'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取所有基础模型配置
      tags:
      - model
  /api/v1/models/create:
    post:
      consumes:
      - application/json
      description: 创建新的模型配置
      parameters:
      - description: 模型配置信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.ModelConfigIO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ModelConfigIO'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 创建模型配置
      tags:
      - model
  /api/v1/models/toggle:
    post:
      consumes:
      - application/json
      description: 修改指定模型的启用状态
      parameters:
      - description: 模型ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ModelConfigIO'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 修改模型启用状态
      tags:
      - model
  /api/v1/provider/categories:
    get:
      consumes:
      - application/json
      description: 获取所有分类
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelCategoryOP'
            type: array
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 获取所有分类
      tags:
      - 模型提供者
  /api/v1/provider/info:
    get:
      consumes:
      - application/json
      description: 获取当前登录模型提供者的基本信息
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.ProviderInfo'
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 获取提供者信息
      tags:
      - 模型提供者
    post:
      consumes:
      - application/json
      description: 更新当前登录模型提供者的基本信息
      parameters:
      - description: 更新请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.UpdateProviderInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            properties:
              message:
                type: string
            type: object
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 更新提供者信息
      tags:
      - 模型提供者
  /api/v1/provider/login:
    post:
      consumes:
      - application/json
      description: 模型提供者使用钱包签名进行登录验证
      parameters:
      - description: 登录请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.ProviderAuthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/service.ProviderAuthResponse'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: 登录失败
          schema:
            type: string
      summary: 钱包签名登录
      tags:
      - 模型提供者
  /api/v1/provider/models:
    get:
      consumes:
      - application/json
      description: 获取当前登录模型提供者上传的所有模型列表
      parameters:
      - description: 页码,默认1
        in: query
        name: page
        required: true
        type: integer
      - description: 每页数量,默认20
        in: query
        name: limit
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.ProviderModelList'
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 获取提供者的模型列表
      tags:
      - 模型提供者
  /api/v1/provider/models/{chain_model_id}:
    get:
      consumes:
      - application/json
      description: 获取指定模型的信息
      parameters:
      - description: 链上模型ID
        in: path
        name: chain_model_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.ProviderModelInfo'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 获取模型信息
      tags:
      - 模型提供者
    post:
      consumes:
      - application/json
      description: 更新指定模型的扩展描述信息
      parameters:
      - description: 链上模型ID
        in: path
        name: chain_model_id
        required: true
        type: integer
      - description: 更新请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.UpdateModelExtRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            properties:
              message:
                type: string
            type: object
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 更新模型扩展信息
      tags:
      - 模型提供者
  /api/v1/provider/models/image:
    post:
      consumes:
      - application/json
      description: 上传模型图片
      parameters:
      - description: 图片文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.FileInfoOP'
        "400":
          description: 请求参数错误
          schema:
            type: string
        "401":
          description: Token无效或已过期
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      security:
      - BearerAuth: []
      summary: 上传模型图片
      tags:
      - 模型提供者
  /api/v1/provider/nonce:
    get:
      consumes:
      - application/json
      description: 为模型提供者生成用于钱包签名的随机字符串，需要提供钱包地址
      parameters:
      - description: 钱包地址
        in: query
        name: wallet_addr
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            properties:
              nonce:
                type: string
            type: object
        "400":
          description: 钱包地址缺失
          schema:
            type: string
        "500":
          description: 服务器错误
          schema:
            type: string
      summary: 生成登录随机字符串
      tags:
      - 模型提供者
  /api/v1/public/files/audio/{file_id}/content:
    get:
      consumes:
      - application/json
      description: 获取指定公共音频文件
      parameters:
      - description: 文件ID
        in: path
        name: file_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 音频文件
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取公共音频文件
      tags:
      - 文件管理
  /api/v1/public/files/image/{file_id}/content:
    get:
      consumes:
      - application/json
      description: 获取指定公共图片文件
      parameters:
      - description: 文件ID
        in: path
        name: file_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 图片文件
          schema:
            type: file
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取公共图片文件
      tags:
      - 文件管理
  /api/v1/system/concurrency/stats:
    get:
      description: 获取当前并发限制器的统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取并发限制统计信息
      tags:
      - 系统
  /api/v1/tools:
    get:
      description: 获取工具列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取工具列表
      tags:
      - 工具
  /api/v1/topai/models:
    get:
      consumes:
      - application/json
      description: 获取模型列表
      parameters:
      - description: 请求参数
        in: body
        name: json
        required: true
        schema:
          $ref: '#/definitions/service.GetTopaiModelListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelsInfoOP'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型列表
      tags:
      - topai
  /api/v1/topai/models/{model_id}:
    get:
      consumes:
      - application/json
      description: 获取指定ID的模型详情
      parameters:
      - description: 模型ID
        in: path
        name: model_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TopaiModelsInfoDetailOP'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型详情
      tags:
      - topai
  /api/v1/topai/models/categories:
    get:
      consumes:
      - application/json
      description: 获取模型分类列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelCategoryOP'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型分类列表
      tags:
      - topai
  /api/v1/topai/models/providers:
    get:
      consumes:
      - application/json
      description: 获取模型提供商列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelProviderOP'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型提供商列表
      tags:
      - topai
  /api/v1/topai/models/ranking:
    get:
      consumes:
      - application/json
      description: 获取模型列表（按token排序）
      parameters:
      - description: 请求参数
        in: body
        name: json
        required: true
        schema:
          $ref: '#/definitions/service.GetTopaiModelListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelsInfoOP'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型列表（按token排序）
      tags:
      - topai
  /api/v1/topai/models/series:
    get:
      consumes:
      - application/json
      description: 获取模型系列列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.TopaiModelSeriesOP'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型系列列表
      tags:
      - topai
  /api/v1/topai/models/support-params:
    get:
      consumes:
      - application/json
      description: 获取模型支持参数列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型支持参数列表
      tags:
      - topai
  /api/v1/topai/models/types:
    get:
      consumes:
      - application/json
      description: 获取模型类型列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取模型类型列表
      tags:
      - topai
  /api/v1/user/app/avatar:
    post:
      consumes:
      - application/json
      description: 上传用户应用头像
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.UserAppInfoOP'
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 上传用户应用头像
      tags:
      - UserApp
  /api/v1/user/app/create:
    post:
      consumes:
      - application/json
      description: 创建用户应用
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 应用信息
        in: body
        name: app_info
        required: true
        schema:
          $ref: '#/definitions/service.UserAppInfoOP'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.UserAppInfoOP'
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 创建用户应用
      tags:
      - UserApp
  /api/v1/user/app/delete/{app_id}:
    post:
      consumes:
      - application/json
      description: 删除用户应用
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 应用ID
        in: path
        name: app_uuid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 删除用户应用
      tags:
      - UserApp
  /api/v1/user/app/info/{app_id}:
    get:
      consumes:
      - application/json
      description: 获取用户应用详情
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAppInfoOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户应用详情
      tags:
      - UserApp
  /api/v1/user/app/key/{app_id}/create:
    post:
      consumes:
      - application/json
      description: 创建用户应用密钥
      parameters:
      - description: 应用ID
        in: path
        name: app_id
        required: true
        type: string
      - description: 密钥名称
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserAppKeyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.AppKeyOP'
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 创建用户应用密钥
      tags:
      - UserApp
  /api/v1/user/app/key/{app_id}/delete/{key_name}:
    post:
      consumes:
      - application/json
      description: 删除用户应用密钥
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 应用ID
        in: path
        name: app_uuid
        required: true
        type: string
      - description: 密钥名称
        in: body
        name: key_name
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 删除用户应用密钥
      tags:
      - UserApp
  /api/v1/user/app/key/list:
    get:
      consumes:
      - application/json
      description: 获取用户应用密钥列表
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 应用ID
        in: path
        name: app_uuid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.AppKeyOP'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户应用密钥列表
      tags:
      - UserApp
  /api/v1/user/app/list:
    get:
      consumes:
      - application/json
      description: 获取用户应用列表
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.UserAppInfoOP'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户应用列表
      tags:
      - UserApp
  /api/v1/user/app/update/{app_id}:
    post:
      consumes:
      - application/json
      description: 更新用户应用
      parameters:
      - description: 用户ID
        in: query
        name: user_id
        required: true
        type: string
      - description: 应用ID
        in: path
        name: app_uuid
        required: true
        type: string
      - description: 应用信息
        in: body
        name: app_info
        required: true
        schema:
          $ref: '#/definitions/service.UserAppInfoOP'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/service.UserAppInfoOP'
        "400":
          description: 失败
          schema:
            type: string
        "500":
          description: 失败
          schema:
            type: string
      summary: 更新用户应用
      tags:
      - UserApp
  /api/v1/user/balance:
    get:
      consumes:
      - application/json
      description: 获取用户余额
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户余额
      tags:
      - 用户管理
  /api/v1/user/balance/record:
    get:
      consumes:
      - application/json
      description: 获取用户余额记录
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserBalanceRecordOP'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户余额记录
      tags:
      - 用户管理
  /api/v1/user/recharge_address:
    get:
      consumes:
      - application/json
      description: 获取用户充值地址
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户充值地址
      tags:
      - 用户管理
  /api/v1/users:
    get:
      consumes:
      - application/json
      description: 获取所有用户信息
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.UserAdminOP'
            type: array
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户列表
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 创建新用户
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 创建用户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAdminOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 创建用户
      tags:
      - 用户管理
  /api/v1/users/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定用户
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 用户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 删除用户
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 更新用户信息
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 用户ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新用户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAdminOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新用户
      tags:
      - 用户管理
  /api/v1/users/gift/balance:
    post:
      consumes:
      - application/json
      description: 管理员转赠余额
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 转赠余额请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.GiftBalanceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 管理员转赠余额
      tags:
      - 用户管理
  /api/v1/users/update/role:
    post:
      consumes:
      - application/json
      description: 更新用户角色
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 更新用户角色请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAdminOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新用户角色
      tags:
      - 用户管理
  /api/v1/users/user/settings:
    get:
      consumes:
      - application/json
      description: 获取用户设置
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserSettings'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取用户设置
      tags:
      - 用户管理
  /api/v1/users/user/settings/update:
    post:
      consumes:
      - application/json
      description: 更新用户设置
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      - description: 更新用户设置请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/service.UserSettings'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserSettings'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新用户设置
      tags:
      - 用户管理
  /api/version/updates:
    get:
      description: 获取版本差异
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取版本更新
      tags:
      - 版本
  /auth/:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserBaseOP'
        "401":
          description: Unauthorized
          schema:
            type: string
      summary: 获取当前用户信息
      tags:
      - auth
  /auth/admin/config:
    get:
      consumes:
      - application/json
      description: 获取系统管理员配置(需要管理员权限)
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.SystemCommonConfig'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 获取管理员配置
      tags:
      - auth
    post:
      consumes:
      - application/json
      description: 更新系统管理员配置(需要管理员权限)
      parameters:
      - description: 管理员配置
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/service.SystemCommonConfig'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.SystemCommonConfig'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新管理员配置
      tags:
      - auth
  /auth/signin:
    post:
      consumes:
      - application/json
      description: 用户登录接口
      parameters:
      - description: 登录信息
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/handlers.SigninRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAuthResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 用户登录
      tags:
      - auth
  /auth/signout:
    get:
      consumes:
      - application/json
      description: 用户登出接口
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 用户登出
      tags:
      - auth
  /auth/signup:
    post:
      consumes:
      - application/json
      description: 用户注册接口
      parameters:
      - description: 注册信息
        in: body
        name: register
        required: true
        schema:
          $ref: '#/definitions/handlers.SignupRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserAuthResponse'
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 用户注册
      tags:
      - auth
  /auth/update/password:
    post:
      consumes:
      - application/json
      description: 更新当前登录用户的密码
      parameters:
      - description: 密码信息
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdatePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新用户密码
      tags:
      - auth
  /auth/update/profile:
    post:
      consumes:
      - application/json
      description: 更新当前登录用户的资料
      parameters:
      - description: 用户资料
        in: body
        name: profile
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.UserBaseOP'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新用户资料
      tags:
      - auth
  /model/{model_id}/config:
    post:
      consumes:
      - application/json
      description: 更新指定模型的配置信息
      parameters:
      - description: 模型ID
        in: path
        name: model_id
        required: true
        type: string
      - description: 配置信息
        in: body
        name: config
        required: true
        schema:
          $ref: '#/definitions/service.ModelConfigIO'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ModelConfigIO'
        "400":
          description: Bad Request
          schema:
            type: string
        "401":
          description: Unauthorized
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: 更新模型配置
      tags:
      - model
  /ws/socket.io:
    get:
      consumes:
      - application/json
      description: 处理WebSocket连接
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 处理WebSocket连接
      tags:
      - websocket
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
