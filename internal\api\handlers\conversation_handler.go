package handlers

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// ConversationHandler 处理对话相关的请求
type ConversationHandler struct {
	log     *logger.ModuleLogger
	service *service.Api
}

// NewConversationHandler 创建新的对话处理器
func NewConversationHandler(service *service.Api) *ConversationHandler {
	log := logger.GetLogger("conversation_handler")
	return &ConversationHandler{
		log:     log,
		service: service,
	}
}

// Conversation 对话信息
type Conversation struct {
	ID        uint   `json:"id"`
	Title     string `json:"title"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

// Message 消息信息
type Message struct {
	ID        uint   `json:"id"`
	Role      string `json:"role"` // user 或 assistant
	Content   string `json:"content"`
	CreatedAt string `json:"created_at"`
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	Content string `json:"content" binding:"required"`
}

// ChatImportForm 导入聊天请求
type ChatImportForm struct {
	Chat struct {
		Title    string `json:"title"`
		Messages []struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"messages"`
	} `json:"chat"`
	Meta     map[string]interface{} `json:"meta,omitempty"`
	Pinned   bool                   `json:"pinned,omitempty"`
	FolderID string                 `json:"folder_id,omitempty"`
}

// TagModel 标签模型
type TagModel struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	UserID    string `json:"user_id"`
	CreatedAt int64  `json:"created_at"`
}

// CreateConversation 创建新对话
// @Summary 创建新对话
// @Description 创建新的对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param request body service.CreateConversationRequest true "对话信息"
// @Success 200 {object} service.ConversationOP
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/new [post]
func (h *ConversationHandler) CreateConversation(c *gin.Context) {
	var req service.CreateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	conversation, err := h.service.Conversation.CreateConversation(c, userOP, &req)
	if err != nil {
		h.log.Error("create conversation error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, conversation)
}

type UpdateChatShareRequest struct {
	UUID    string `json:"id"`
	IsShare bool   `json:"is_share"`
}

// chatShare 分享对话
// @Summary 分享对话
// @Description 分享对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param body body UpdateChatShareRequest true "body"
// @Success 200 {object} string
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/tti/share [post]
func (h *ConversationHandler) UpdateTTIChatShare(c *gin.Context) {
	req := &UpdateChatShareRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	if req.UUID == "" {
		api.Fail(c, api.CodeClientError, "uuid is required", errors.New("uuid is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	// 因为只支持tti分享，所以这里直接调用tti model的分享接口
	err := h.service.Conversation.UpdateTTIChatShare(c, userOP, req.UUID, req.IsShare)
	if err != nil {
		h.log.Error("update chat share error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}
	api.Success(c, nil)
}

// GetUserTTIChatShared 获取用户分享的tti对话
// @Summary 获取用户分享的tti对话
// @Description 获取用户分享的tti对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页条数" default(20)
// @Success 200 {object} service.TTIChatSharedListOP
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/tti/shared [get]
func (h *ConversationHandler) GetUserTTIChatShared(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	limit, _ := strconv.Atoi(c.Query("limit"))

	if page < 1 {
		page = 1
	}

	if limit < 1 {
		limit = 20
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	resp, err := h.service.Conversation.GetUserTTIChatShared(c, userOP, page, limit)
	if err != nil {
		h.log.Error("get user tti chat shared error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, resp)
}

// GetAllTTIChatShared 获取所有分享的tti对话
// @Summary 获取所有分享的tti对话
// @Description 获取所有分享的tti对话(不需要认证)
// @Tags conversation
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页条数" default(20)
// @Success 200 {object} service.TTIChatSharedListOP
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/tti/all_shared [get]
func (h *ConversationHandler) GetAllTTIChatShared(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	limit, _ := strconv.Atoi(c.Query("limit"))

	if page < 1 {
		page = 1
	}

	if limit < 1 {
		limit = 20
	}

	resp, err := h.service.Conversation.GetAllTTIChatShared(c, page, limit)
	if err != nil {
		h.log.Error("get all tti chat shared error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, resp)
}

// GetConversation 获取对话详情
// @Summary 获取对话详情
// @Description 获取指定对话的详细信息
// @Tags conversation
// @Accept json
// @Produce json
// @Param id path int true "对话ID"
// @Success 200 {object} Conversation
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 404 {object} string
// @Failure 500 {object} string
// @Router /api/v1/conversations/{id} [get]
func (h *ConversationHandler) GetConversation(c *gin.Context) {
	// 获取对话ID
	conversationUUID := c.Param("chat_id")
	if conversationUUID == "" {
		api.Fail(c, api.CodeClientError, "invalid conversation uuid", errors.New("conversation uuid is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	conversation, err := h.service.Conversation.GetConversation(c, userOP, conversationUUID)
	if err != nil {
		h.log.Error("获取对话失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, conversation)
}

// DeleteConversation 删除对话
// @Summary 删除对话
// @Description 删除指定的对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param chat_id path string true "对话ID"
// @Success 200 {object} string
// @Failure 401 {object} string
// @Failure 404 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/:chat_id [delete]
func (h *ConversationHandler) DeleteConversation(c *gin.Context) {
	// 获取对话ID
	conversationUUID := c.Param("chat_id")
	if conversationUUID == "" {
		api.Fail(c, api.CodeClientError, "invalid conversation uuid", errors.New("conversation uuid is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	err := h.service.Conversation.DeleteConversation(c, userOP, conversationUUID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, nil)
}

// GetConversations 获取用户聊天记录列表
// @Summary 获取聊天记录列表
// @Description 获取当前用户的所有聊天记录
// @Tags conversation
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页条数" default(10)
// @Success 200 {object} []service.ConversationOP
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats [get]
func (h *ConversationHandler) GetConversations(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	limit, _ := strconv.Atoi(c.Query("limit"))

	if page < 1 {
		page = 1
	}

	if limit < 1 {
		limit = 10
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	conversations, err := h.service.Conversation.GetConversationList(c, userOP, page, limit)
	if err != nil {
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, conversations)
}

// UpdateConversation 更新聊天
// @Summary 更新聊天
// @Description 更新指定ID的聊天
// @Tags conversation
// @Accept json
// @Produce json
// @Param id path string true "聊天ID"
// @Param request body service.UpdateConversationRequest true "聊天信息"
// @Success 200 {object} service.Conversation
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 404 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/{chat_id} [post]
func (h *ConversationHandler) UpdateConversation(c *gin.Context) {
	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	conversationUUID := c.Param("chat_id")
	if conversationUUID == "" {
		api.Fail(c, api.CodeClientError, "invalid conversation uuid", errors.New("conversation uuid is required"))
		return
	}

	var req service.UpdateConversationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	// TODO: 实现更新聊天的逻辑
	conversation, err := h.service.Conversation.UpdateConversation(c, userOP, conversationUUID, &req)
	if err != nil {
		h.log.Error("更新聊天失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}

	api.Success(c, conversation)
}

func (h *ConversationHandler) Close() {

}
