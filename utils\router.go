package utils

import (
	"path/filepath"
	"strings"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

func GetFileRouter(file *repository.File) string {
	baseRouter := "/api/v1/"
	if file.IsPublic == 1 {
		baseRouter = "/api/v1/public/"
	}
	if file.Type == "image" {
		return filepath.Join(baseRouter, "files/image/", file.UUID, "content")
	} else if file.Type == "audio" {
		return filepath.Join(baseRouter, "files/audio/", file.UUID, "content")
	}
	return filepath.Join(baseRouter, "files/file/", file.UUID, "content")
}

func GetFileUUIDByUrl(url string) string {
	if url == "" {
		return ""
	}
	split := strings.Split(url, "/")
	if len(split) > 2 {
		return split[len(split)-2]
	}
	return ""
}
