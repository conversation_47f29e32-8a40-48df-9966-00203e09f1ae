package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestApi_StartAndClose 测试Api结构体的启动和关闭
func TestApi_StartAndClose(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() *Api
		wantErr bool
	}{
		{
			name: "TC1-成功启动和关闭API",
			setup: func() *Api {
				mocks := testutil.NewMockDependencies()

				api := &Api{
					start: []func(ctx context.Context) error{},
					close: []func(){},
				}

				_ = mocks // 避免未使用变量警告
				return api
			},
			wantErr: false,
		},
		{
			name: "TC2-带启动函数的API",
			setup: func() *Api {
				called := false
				api := &Api{
					start: []func(ctx context.Context) error{
						func(ctx context.Context) error {
							called = true
							return nil
						},
					},
					close: []func(){
						func() {
							// 清理函数
						},
					},
				}

				// 验证启动函数被调用
				t.Cleanup(func() {
					assert.True(t, called, "启动函数应该被调用")
				})

				return api
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			api := tt.setup()
			ctx := context.Background()

			// 测试启动
			assert.NotPanics(t, func() {
				api.Start(ctx)
			})

			// 测试关闭
			assert.NotPanics(t, func() {
				api.Close()
			})
		})
	}
}

// TestUserBaseOP_Validation 测试UserBaseOP结构体
func TestUserBaseOP_Validation(t *testing.T) {
	tests := []struct {
		name string
		user *UserBaseOP
		want bool
	}{
		{
			name: "TC1-有效用户对象",
			user: &UserBaseOP{
				Email:           "<EMAIL>",
				ID:              123,
				IdentityID:      "user-123",
				Name:            "Test User",
				ProfileImageURL: "https://example.com/avatar.jpg",
				Role:            "user",
				Token:           "valid-token",
				TokenType:       "Bearer",
			},
			want: true,
		},
		{
			name: "TC2-最小有效用户对象",
			user: &UserBaseOP{
				Email:      "<EMAIL>",
				IdentityID: "user-minimal",
				Name:       "Minimal User",
				Role:       "user",
			},
			want: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// 验证用户对象的基本字段
			assert.NotEmpty(t, tt.user.Email, "邮箱不应为空")
			assert.NotEmpty(t, tt.user.IdentityID, "身份ID不应为空")
			assert.NotEmpty(t, tt.user.Name, "用户名不应为空")
			assert.NotEmpty(t, tt.user.Role, "角色不应为空")
		})
	}
}

// TestSystemCommonConfig_Constants 测试系统配置常量
func TestSystemCommonConfig_Constants(t *testing.T) {
	constants := map[string]string{
		SystemCommonConfigAPI_KEY_ALLOWED_ENDPOINTS:            "API_KEY_ALLOWED_ENDPOINTS",
		SystemCommonConfigDEFAULT_USER_ROLE:                    "DEFAULT_USER_ROLE",
		SystemCommonConfigENABLE_API_KEY:                       "ENABLE_API_KEY",
		SystemCommonConfigENABLE_API_KEY_ENDPOINT_RESTRICTIONS: "ENABLE_API_KEY_ENDPOINT_RESTRICTIONS",
		SystemCommonConfigENABLE_CHANNELS:                      "ENABLE_CHANNELS",
		SystemCommonConfigENABLE_COMMUNITY_SHARING:             "ENABLE_COMMUNITY_SHARING",
		SystemCommonConfigENABLE_MESSAGE_RATING:                "ENABLE_MESSAGE_RATING",
		SystemCommonConfigENABLE_SIGNUP:                        "ENABLE_SIGNUP",
		SystemCommonConfigJWT_EXPIRES_IN:                       "JWT_EXPIRES_IN",
		SystemCommonConfigSHOW_ADMIN_DETAILS:                   "SHOW_ADMIN_DETAILS",
		SystemCommonConfigWEBUI_URL:                            "WEBUI_URL",
	}

	for constant, expected := range constants {
		assert.Equal(t, expected, constant, "配置常量值应该匹配")
	}
}

// TestUserSettingsConstant 测试用户设置常量
func TestUserSettingsConstant(t *testing.T) {
	assert.Equal(t, "system", UserSettingsSystem, "UserSettingsSystem常量值应该是'system'")
}

// TestAudioConfigConstants 测试音频配置常量
func TestAudioConfigConstants(t *testing.T) {
	assert.Equal(t, "MODEL", AudioConfigTTS_MODEL)
	assert.Equal(t, "VOICE", AudioConfigTTS_VOICE)
	assert.Equal(t, "ENABLE", AudioConfigTTS_ENABLE)
	assert.Equal(t, "MODEL", AudioConfigASR_MODEL)
	assert.Equal(t, "ENABLE", AudioConfigASR_ENABLE)
}

// TestStructValidation 测试结构体创建和字段验证
func TestStructValidation(t *testing.T) {
	t.Run("SystemCommonConfig", func(t *testing.T) {
		config := &SystemCommonConfig{
			API_KEY_ALLOWED_ENDPOINTS:            "/api/v1/*",
			DEFAULT_USER_ROLE:                    "user",
			ENABLE_API_KEY:                       false,
			ENABLE_API_KEY_ENDPOINT_RESTRICTIONS: false,
			ENABLE_CHANNELS:                      true,
			ENABLE_COMMUNITY_SHARING:             false,
			ENABLE_MESSAGE_RATING:                true,
			ENABLE_SIGNUP:                        true,
			JWT_EXPIRES_IN:                       "7d",
			SHOW_ADMIN_DETAILS:                   false,
			WEBUI_URL:                            "http://localhost:3000",
		}

		assert.NotNil(t, config)
		assert.Equal(t, "/api/v1/*", config.API_KEY_ALLOWED_ENDPOINTS)
		assert.Equal(t, "user", config.DEFAULT_USER_ROLE)
		assert.True(t, config.ENABLE_SIGNUP)
	})

	t.Run("AudioConfigTTS", func(t *testing.T) {
		config := &AudioConfigTTS{
			MODEL:  "tts-1",
			VOICE:  "alloy",
			ENABLE: true,
		}

		assert.NotNil(t, config)
		assert.Equal(t, "tts-1", config.MODEL)
		assert.Equal(t, "alloy", config.VOICE)
		assert.True(t, config.ENABLE)
	})

	t.Run("AudioConfigASR", func(t *testing.T) {
		config := &AudioConfigASR{
			MODEL:  "whisper-1",
			ENABLE: true,
		}

		assert.NotNil(t, config)
		assert.Equal(t, "whisper-1", config.MODEL)
		assert.True(t, config.ENABLE)
	})

	t.Run("AudioConfig", func(t *testing.T) {
		config := &AudioConfig{
			ASR: &AudioConfigASR{
				MODEL:  "whisper-1",
				ENABLE: true,
			},
			TTS: &AudioConfigTTS{
				MODEL:  "tts-1",
				VOICE:  "alloy",
				ENABLE: true,
			},
		}

		assert.NotNil(t, config)
		assert.NotNil(t, config.ASR)
		assert.NotNil(t, config.TTS)
		assert.Equal(t, "whisper-1", config.ASR.MODEL)
		assert.Equal(t, "tts-1", config.TTS.MODEL)
	})

	t.Run("BaseModel", func(t *testing.T) {
		model := &BaseModel{
			Active:              true,
			ContextWindow:       4096,
			CreatedAt:           **********,
			ModelID:             "gpt-4",
			MaxCompletionTokens: 2048,
			Name:                "GPT-4",
			Object:              "model",
			OwnedBy:             "openai",
			URLIdx:              0,
			ModelType:           "LLM",
		}

		assert.NotNil(t, model)
		assert.True(t, model.Active)
		assert.Equal(t, "gpt-4", model.ModelID)
		assert.Equal(t, "LLM", model.ModelType)
		assert.Greater(t, model.ContextWindow, int64(0))
	})

	t.Run("FileInfoOP", func(t *testing.T) {
		fileInfo := &FileInfoOP{
			FileID:         "file-123",
			FileName:       "test.txt",
			URL:            "/api/v1/files/file-123",
			CreatedAt:      **********,
			UpdatedAt:      **********,
			UserIdentityID: "user-123",
			Data: &FileInfoData{
				Content: "Test file content",
			},
			Meta: &FileInfoMeta{
				CollectionName: "test.txt",
				ContentType:    "text/plain",
				Name:           "test.txt",
				Size:           17,
			},
		}

		assert.NotNil(t, fileInfo)
		assert.Equal(t, "file-123", fileInfo.FileID)
		assert.Equal(t, "test.txt", fileInfo.FileName)
		assert.NotNil(t, fileInfo.Data)
		assert.NotNil(t, fileInfo.Meta)
		assert.Equal(t, "text/plain", fileInfo.Meta.ContentType)
	})

	t.Run("Conversation", func(t *testing.T) {
		conv := &Conversation{
			UUID:      "conv-123",
			Title:     "Test Conversation",
			Timestamp: **********000,
			Tags:      []string{"test", "example"},
			Messages: []*ConversationMessage{
				{
					UUID:      "msg-1",
					Role:      "user",
					Content:   "Hello",
					Timestamp: **********,
				},
			},
			System: "You are a helpful assistant",
			Models: []string{"gpt-4"},
		}

		assert.NotNil(t, conv)
		assert.Equal(t, "conv-123", conv.UUID)
		assert.Equal(t, "Test Conversation", conv.Title)
		assert.Len(t, conv.Messages, 1)
		assert.Equal(t, "user", conv.Messages[0].Role)
	})

	t.Run("TopaiModelsInfoOP", func(t *testing.T) {
		model := &TopaiModelsInfoOP{
			ModelId:       "topai-gpt-4",
			ProviderName:  "topai",
			ModelName:     "TopAI GPT-4",
			ModelType:     "LLM",
			InputPrice:    "0.03",
			OutputPrice:   "0.06",
			ContextLength: "8192",
			MaxOutput:     "4096",
			IsFree:        false,
		}

		assert.NotNil(t, model)
		assert.Equal(t, "topai-gpt-4", model.ModelId)
		assert.Equal(t, "LLM", model.ModelType)
		assert.Equal(t, "0.03", model.InputPrice)
		assert.False(t, model.IsFree)
	})
}

// TestStructArrays 测试包含数组的结构体
func TestStructArrays(t *testing.T) {
	t.Run("UserAdminOP", func(t *testing.T) {
		admin := &UserAdminOP{
			Total: 5,
			Users: []*UserAdminUser{
				{
					UserBaseOP: UserBaseOP{
						Email:      "<EMAIL>",
						IdentityID: "admin-1",
						Name:       "Admin User",
						Role:       "admin",
					},
					CreatedAt: **********,
					ApiKey:    "ak-123456",
				},
			},
		}

		assert.NotNil(t, admin)
		assert.Equal(t, int64(5), admin.Total)
		assert.Len(t, admin.Users, 1)
		assert.Equal(t, "admin", admin.Users[0].Role)
	})

	t.Run("UserBalanceRecordOP", func(t *testing.T) {
		records := &UserBalanceRecordOP{
			Total: 3,
			Records: []*UserBalanceRecord{
				{
					ID:          1,
					Type:        "deposit",
					IsIn:        true,
					Amount:      "100.00",
					Description: "充值",
					CreatedAt:   **********,
				},
				{
					ID:          2,
					Type:        "withdraw",
					IsIn:        false,
					Amount:      "50.00",
					Description: "消费",
					CreatedAt:   1672531300,
				},
			},
		}

		assert.NotNil(t, records)
		assert.Equal(t, int64(3), records.Total)
		assert.Len(t, records.Records, 2)
		assert.True(t, records.Records[0].IsIn)
		assert.False(t, records.Records[1].IsIn)
	})
}

// BenchmarkApi_Start API启动性能基准测试
func BenchmarkApi_Start(b *testing.B) {
	api := &Api{
		start: []func(ctx context.Context) error{
			func(ctx context.Context) error {
				return nil
			},
		},
		close: []func(){
			func() {},
		},
	}

	ctx := context.Background()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		api.Start(ctx)
		api.Close()
	}
}

// BenchmarkStructCreation 结构体创建性能基准测试
func BenchmarkStructCreation(b *testing.B) {
	b.Run("UserBaseOP", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			user := &UserBaseOP{
				Email:      "<EMAIL>",
				IdentityID: "user-123",
				Name:       "Test User",
				Role:       "user",
			}
			_ = user
		}
	})

	b.Run("SystemCommonConfig", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			config := &SystemCommonConfig{
				DEFAULT_USER_ROLE: "user",
				ENABLE_SIGNUP:     true,
				JWT_EXPIRES_IN:    "7d",
			}
			_ = config
		}
	})
}
