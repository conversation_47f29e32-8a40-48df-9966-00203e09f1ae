package mysql

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModel 相关实现
type topaiModelRepository struct {
	db *gorm.DB
}

func NewTopaiModelRepository(db *gorm.DB) repository.TopaiModelRepository {
	return &topaiModelRepository{db: db}
}

func (r *topaiModelRepository) Create(ctx context.Context, model *repository.TopaiModel) error {
	if err := r.db.WithContext(ctx).Create(&model).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelRepository) GetAll(ctx context.Context) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Find(&models).Error; err != nil {
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetByModelNameAndVersion(ctx context.Context, modelName, modelVersion string) (*repository.TopaiModel, error) {
	var model repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("model_name = ? AND model_version = ?", modelName, modelVersion).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

func (r *topaiModelRepository) GetByModelName(ctx context.Context, modelName string) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("model_name = ?", modelName).Find(&models).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModel, error) {

	var model repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

func (r *topaiModelRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&models).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetBySeriesIds(ctx context.Context, seriesIds []uint) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("series_id IN ?", seriesIds).Find(&models).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetByModelTypes(ctx context.Context, modelTypes []string) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("model_type IN ?", modelTypes).Find(&models).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetByOwnerAddress(ctx context.Context, ownerAddress string, offset int, limit int) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("owner_address = ?", ownerAddress).Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return models, nil
}

func (r *topaiModelRepository) GetByChainModelId(ctx context.Context, chainModelId uint) (*repository.TopaiModel, error) {
	var model repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("chain_model_id = ?", chainModelId).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

func (r *topaiModelRepository) Update(ctx context.Context, model *repository.TopaiModel) error {
	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelRepository) GetCountByOwnerAddress(ctx context.Context, ownerAddress string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&repository.TopaiModel{}).Where("owner_address = ?", ownerAddress).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *topaiModelRepository) GetByIsOnChain(ctx context.Context, isOnChain bool) ([]*repository.TopaiModel, error) {
	var models []*repository.TopaiModel
	if err := r.db.WithContext(ctx).Where("chain_model_id > 0").Find(&models).Error; err != nil {
		return nil, err
	}
	return models, nil
}

// TopaiModelCategory 相关实现
type topaiModelCategoryRepository struct {
	db *gorm.DB
}

func NewTopaiModelCategoryRepository(db *gorm.DB) repository.TopaiModelCategoryRepository {
	return &topaiModelCategoryRepository{db: db}
}

func (r *topaiModelCategoryRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelCategory, error) {
	var categories []*repository.TopaiModelCategory
	if err := r.db.WithContext(ctx).Where("is_deleted = ?", 0).Find(&categories).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return categories, nil
}

func (r *topaiModelCategoryRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategory, error) {
	var categories []*repository.TopaiModelCategory
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Where("is_deleted = ?", 0).Find(&categories).Error; err != nil {
		return nil, err
	}
	return categories, nil
}

func (r *topaiModelCategoryRepository) Create(ctx context.Context, category *repository.TopaiModelCategory) error {
	if err := r.db.WithContext(ctx).Create(&category).Error; err != nil {
		return err
	}
	return nil
}

// TopaiModelCategoryRelation 相关实现
type topaiModelCategoryRelationRepository struct {
	db *gorm.DB
}

func NewTopaiModelCategoryRelationRepository(db *gorm.DB) repository.TopaiModelCategoryRelationRepository {
	return &topaiModelCategoryRelationRepository{db: db}
}

func (r *topaiModelCategoryRelationRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelCategoryRelation, error) {
	var relations []*repository.TopaiModelCategoryRelation
	if err := r.db.WithContext(ctx).Where("is_deleted = ?", 0).Find(&relations).Error; err != nil {
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelCategoryRelationRepository) GetByCategoryIds(ctx context.Context, categoryIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	var relations []*repository.TopaiModelCategoryRelation
	if err := r.db.WithContext(ctx).Where("category_id IN ?", categoryIds).Where("is_deleted = ?", 0).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelCategoryRelationRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	var relations []*repository.TopaiModelCategoryRelation
	if err := r.db.WithContext(ctx).Where("topai_model_id IN ?", topaiModelIds).Where("is_deleted = ?", 0).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelCategoryRelationRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelCategoryRelation, error) {
	var relations []*repository.TopaiModelCategoryRelation
	if err := r.db.WithContext(ctx).Where("topai_model_id = ?", topaiModelId).Where("is_deleted = ?", 0).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelCategoryRelationRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	var relations []*repository.TopaiModelCategoryRelation
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Where("is_deleted = ?", 0).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelCategoryRelationRepository) DeleteByTopaiModelId(ctx context.Context, topaiModelId uint) error {
	if err := r.db.WithContext(ctx).Table(repository.TopaiModelCategoryRelation{}.TableName()).Where("topai_model_id = ?", topaiModelId).Where("is_deleted = ?", 0).Update("is_deleted", 1).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelCategoryRelationRepository) CreateBatch(ctx context.Context, relations []*repository.TopaiModelCategoryRelation) error {
	if err := r.db.WithContext(ctx).Create(&relations).Error; err != nil {
		return err
	}
	return nil
}

// TopaiModelSeries 相关实现
type topaiModelSeriesRepository struct {
	db *gorm.DB
}

func NewTopaiModelSeriesRepository(db *gorm.DB) repository.TopaiModelSeriesRepository {
	return &topaiModelSeriesRepository{db: db}
}

func (r *topaiModelSeriesRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelSeries, error) {
	var series []*repository.TopaiModelSeries
	if err := r.db.WithContext(ctx).Find(&series).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return series, nil
}

func (r *topaiModelSeriesRepository) Create(ctx context.Context, series *repository.TopaiModelSeries) error {
	if err := r.db.WithContext(ctx).Create(&series).Error; err != nil {
		return err
	}
	return nil
}

// TopaiModelSeriesRelation 相关实现
type topaiModelSeriesRelationRepository struct {
	db *gorm.DB
}

func NewTopaiModelSeriesRelationRepository(db *gorm.DB) repository.TopaiModelSeriesRelationRepository {
	return &topaiModelSeriesRelationRepository{db: db}
}

func (r *topaiModelSeriesRelationRepository) GetBySeriesIds(ctx context.Context, seriesIds []uint) ([]*repository.TopaiModelSeriesRelation, error) {
	var relations []*repository.TopaiModelSeriesRelation
	if err := r.db.WithContext(ctx).Where("series_id IN ?", seriesIds).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

func (r *topaiModelSeriesRelationRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelSeriesRelation, error) {
	var relation repository.TopaiModelSeriesRelation
	if err := r.db.WithContext(ctx).Where("topai_model_id = ?", topaiModelId).First(&relation).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &relation, nil
}

func (r *topaiModelSeriesRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModelSeries, error) {
	var series repository.TopaiModelSeries
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&series).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &series, nil
}

func (r *topaiModelSeriesRelationRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelSeriesRelation, error) {
	var relations []*repository.TopaiModelSeriesRelation
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&relations).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return relations, nil
}

// TopaiModelExt 相关实现
type topaiModelExtRepository struct {
	db *gorm.DB
}

func NewTopaiModelExtRepository(db *gorm.DB) repository.TopaiModelExtRepository {
	return &topaiModelExtRepository{db: db}
}

func (r *topaiModelExtRepository) Create(ctx context.Context, ext *repository.TopaiModelExt) error {
	if err := r.db.WithContext(ctx).Create(&ext).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelExtRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelExt, error) {
	var ext repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("topai_model_id = ?", topaiModelId).First(&ext).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &ext, nil
}

func (r *topaiModelExtRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("topai_model_id IN ?", topaiModelIds).Find(&exts).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) GetOnlineList(ctx context.Context, offset, pageSize int, sortBy, sortOrder string, modelIds []uint, providerIds []uint, isfree bool, searchModelId string) ([]*repository.TopaiModelExt, error) {
	query := r.db.WithContext(ctx)
	if len(modelIds) > 0 {
		query = query.Where("topai_model_id IN ?", modelIds)
	}
	if len(providerIds) > 0 {
		query = query.Where("topai_model_provider_id IN ?", providerIds)
	}
	if isfree {
		query = query.Where("price = ?", 0)
	}
	if sortBy != "" && sortOrder != "" {
		if sortBy == "price" {
			query = query.Order(fmt.Sprintf("LENGTH(price) %s,price %s", sortOrder, sortOrder))
		} else {
			query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
		}
	}
	if searchModelId != "" {
		query = query.Where("model_id LIKE ?", "%"+searchModelId+"%")
	}

	var exts []*repository.TopaiModelExt
	if err := query.Where("`status` = ?", 1).Offset(offset).Limit(pageSize).Find(&exts).Error; err != nil {
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) GetOnlineAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("status = ?", 1).Find(&exts).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelExt, error) {
	var ext repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("model_id = ?", modelId).First(&ext).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &ext, nil
}

func (r *topaiModelExtRepository) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("model_id IN ?", modelIds).Find(&exts).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) GetBySearchOnlineModelId(ctx context.Context, modelId string) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Where("model_id LIKE ?", "%"+modelId+"%").Where("status = ?", 1).Find(&exts).Error; err != nil {
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	if err := r.db.WithContext(ctx).Find(&exts).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return exts, nil
}

func (r *topaiModelExtRepository) Update(ctx context.Context, ext *repository.TopaiModelExt) error {
	if err := r.db.WithContext(ctx).Save(ext).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelExtRepository) GetByTopaiModelIdsAndStatus(ctx context.Context, topaiModelIds []uint, status []int, offset, limit int) ([]*repository.TopaiModelExt, error) {
	var exts []*repository.TopaiModelExt
	db := r.db.WithContext(ctx)
	if len(topaiModelIds) > 0 {
		db = db.Where("topai_model_id IN ?", topaiModelIds)
	}
	if len(status) > 0 {
		db = db.Where("status IN ?", status)
	}
	if err := db.Offset(offset).Order("id DESC").Limit(limit).Find(&exts).Error; err != nil {
		return nil, err
	}
	return exts, nil
}

// TopaiModelSupportParam 相关实现
type topaiModelSupportParamRepository struct {
	db *gorm.DB
}

func NewTopaiModelSupportParamRepository(db *gorm.DB) repository.TopaiModelSupportParamRepository {
	return &topaiModelSupportParamRepository{db: db}
}

func (r *topaiModelSupportParamRepository) GetByParams(ctx context.Context, params []string) ([]*repository.TopaiModelSupportParam, error) {
	var supportParams []*repository.TopaiModelSupportParam
	if err := r.db.WithContext(ctx).Where("param_name IN ?", params).Find(&supportParams).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return supportParams, nil
}

func (r *topaiModelSupportParamRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelSupportParam, error) {
	var supportParams []*repository.TopaiModelSupportParam
	if err := r.db.WithContext(ctx).Where("topai_model_id = ?", topaiModelId).Find(&supportParams).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return supportParams, nil
}

func (r *topaiModelSupportParamRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelSupportParam, error) {
	var supportParams []*repository.TopaiModelSupportParam
	if err := r.db.WithContext(ctx).Find(&supportParams).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return supportParams, nil
}

// TopaiModelCost 相关实现
type topaiModelCostRepository struct {
	db *gorm.DB
}

func NewTopaiModelCostRepository(db *gorm.DB) repository.TopaiModelCostRepository {
	return &topaiModelCostRepository{db: db}
}

func (r *topaiModelCostRepository) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCost, error) {
	var costs []*repository.TopaiModelCost
	if err := r.db.WithContext(ctx).Where("topai_model_id IN ?", topaiModelIds).Find(&costs).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return costs, nil
}

func (r *topaiModelCostRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelCost, error) {
	var cost repository.TopaiModelCost
	if err := r.db.WithContext(ctx).Where("topai_model_id = ?", topaiModelId).First(&cost).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &cost, nil
}

func (r *topaiModelCostRepository) Create(ctx context.Context, cost *repository.TopaiModelCost) error {
	if err := r.db.WithContext(ctx).Create(&cost).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelCostRepository) Update(ctx context.Context, cost *repository.TopaiModelCost) error {
	if err := r.db.WithContext(ctx).Save(&cost).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelCostRepository) GetAll(ctx context.Context, offset, limit int, sort string) ([]*repository.TopaiModelCost, error) {
	query := r.db.WithContext(ctx)
	if sort != "" {
		query = query.Order(sort)
	}
	var costs []*repository.TopaiModelCost
	if err := query.Offset(offset).Limit(limit).Find(&costs).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return costs, nil
}

func (r *topaiModelCostRepository) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelCost, error) {
	var costs []*repository.TopaiModelCost
	if err := r.db.WithContext(ctx).Where("model_id IN ?", modelIds).Find(&costs).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return costs, nil
}

func (r *topaiModelCostRepository) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelCost, error) {
	var cost repository.TopaiModelCost
	if err := r.db.WithContext(ctx).Where("model_id = ?", modelId).First(&cost).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &cost, nil
}
