#!/bin/bash

# TopAI Chat Server 测试脚本
# 遵循 golang-testing-standards 要求

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    go_version=$(go version | awk '{print $3}' | sed 's/go//')
    print_info "Go version: $go_version"
}

# 安装测试依赖
install_test_deps() {
    print_info "Installing test dependencies..."
    
    # 检查并安装 testify
    if ! go list -m github.com/stretchr/testify >/dev/null 2>&1; then
        print_info "Installing testify..."
        go get github.com/stretchr/testify@v1.8.4
    fi
    
    print_success "Test dependencies installed"
}

# 运行单元测试
run_unit_tests() {
    print_info "Running unit tests..."
    
    # 创建覆盖率输出目录
    mkdir -p coverage
    
    # 运行测试并生成覆盖率报告
    go test -v -race -coverprofile=coverage/coverage.out -covermode=atomic ./... 2>&1 | tee coverage/test_output.txt
    
    if [ ${PIPESTATUS[0]} -ne 0 ]; then
        print_error "Unit tests failed"
        exit 1
    fi
    
    print_success "Unit tests passed"
}

# 生成覆盖率报告
generate_coverage_report() {
    print_info "Generating coverage report..."
    
    if [ ! -f coverage/coverage.out ]; then
        print_error "Coverage file not found"
        return 1
    fi
    
    # 生成HTML覆盖率报告
    go tool cover -html=coverage/coverage.out -o coverage/coverage.html
    
    # 计算总覆盖率
    coverage_percentage=$(go tool cover -func=coverage/coverage.out | grep total | awk '{print $3}' | sed 's/%//')
    
    print_info "Total coverage: ${coverage_percentage}%"
    
    # 检查覆盖率目标
    coverage_target=80
    coverage_int=$(echo "$coverage_percentage" | cut -d'.' -f1)
    
    if [ "$coverage_int" -ge "$coverage_target" ]; then
        print_success "Coverage target met: ${coverage_percentage}% >= ${coverage_target}%"
    else
        print_warning "Coverage below target: ${coverage_percentage}% < ${coverage_target}%"
        
        # 在CI环境中将覆盖率不足视为错误
        if [ "$CI" = "true" ]; then
            print_error "Coverage requirement not met in CI environment"
            exit 1
        fi
    fi
    
    print_info "Coverage report generated: coverage/coverage.html"
}

# 运行基准测试
run_benchmarks() {
    print_info "Running benchmark tests..."
    
    go test -bench=. -benchmem -run=^$ ./... > coverage/benchmark_results.txt 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "Benchmark tests completed"
        print_info "Benchmark results saved to: coverage/benchmark_results.txt"
    else
        print_warning "Some benchmark tests failed"
    fi
}

# 检查测试质量
check_test_quality() {
    print_info "Checking test quality..."
    
    # 检查是否有未测试的公共函数
    uncovered_functions=$(go tool cover -func=coverage/coverage.out | grep -E "0\.0%|^[^:]*:[0-9]+:[[:space:]]*[A-Z]" | head -10)
    
    if [ -n "$uncovered_functions" ]; then
        print_warning "Found uncovered public functions:"
        echo "$uncovered_functions"
    fi
    
    # 检查测试文件命名规范
    test_files=$(find . -name "*_test.go" -not -path "./vendor/*" -not -path "./.git/*")
    non_standard_tests=$(echo "$test_files" | grep -v "_test\.go$" || true)
    
    if [ -n "$non_standard_tests" ]; then
        print_warning "Non-standard test file names found:"
        echo "$non_standard_tests"
    fi
    
    print_success "Test quality check completed"
}

# 生成测试报告
generate_test_report() {
    print_info "Generating test report..."
    
    cat > coverage/test_report.md << EOF
# TopAI Chat Server - 测试报告

## 概述
- 测试执行时间: $(date)
- Go版本: $(go version | awk '{print $3}')
- 覆盖率目标: 80%

## 测试结果
$(if [ -f coverage/test_output.txt ]; then
    passed=$(grep -c "PASS:" coverage/test_output.txt || echo "0")
    failed=$(grep -c "FAIL:" coverage/test_output.txt || echo "0")
    echo "- 通过的测试: $passed"
    echo "- 失败的测试: $failed"
fi)

## 覆盖率统计
$(if [ -f coverage/coverage.out ]; then
    echo '```'
    go tool cover -func=coverage/coverage.out | tail -10
    echo '```'
fi)

## 基准测试结果
$(if [ -f coverage/benchmark_results.txt ]; then
    echo '```'
    head -20 coverage/benchmark_results.txt
    echo '```'
fi)

EOF
    
    print_success "Test report generated: coverage/test_report.md"
}

# 清理函数
cleanup() {
    print_info "Cleaning up..."
    # 可以在这里添加清理逻辑
}

# 主函数
main() {
    print_info "Starting TopAI Chat Server test suite..."
    
    # 捕获退出信号进行清理
    trap cleanup EXIT
    
    # 检查Go环境
    check_go_env
    
    # 安装测试依赖
    install_test_deps
    
    # 运行测试
    run_unit_tests
    
    # 生成覆盖率报告
    generate_coverage_report
    
    # 运行基准测试
    if [ "$RUN_BENCHMARKS" = "true" ]; then
        run_benchmarks
    fi
    
    # 检查测试质量
    check_test_quality
    
    # 生成测试报告
    generate_test_report
    
    print_success "All tests completed successfully!"
    print_info "View coverage report: open coverage/coverage.html"
}

# 显示帮助信息
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -b, --benchmarks        Run benchmark tests
    -c, --coverage-only     Only generate coverage report
    -q, --quiet             Quiet mode (less verbose output)
    
Environment Variables:
    CI=true                 Enable CI mode (strict coverage requirements)
    RUN_BENCHMARKS=true     Enable benchmark tests
    COVERAGE_TARGET=80      Set coverage target percentage (default: 80)

Examples:
    $0                      Run all tests with coverage
    $0 --benchmarks         Run tests including benchmarks
    $0 --coverage-only      Only generate coverage report from existing data
    CI=true $0              Run in CI mode with strict requirements

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--benchmarks)
            export RUN_BENCHMARKS=true
            shift
            ;;
        -c|--coverage-only)
            if [ -f coverage/coverage.out ]; then
                generate_coverage_report
                exit 0
            else
                print_error "No coverage data found. Run tests first."
                exit 1
            fi
            ;;
        -q|--quiet)
            exec 1>/dev/null
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main 