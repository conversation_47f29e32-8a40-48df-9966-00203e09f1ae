// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserChatRequestRepository is an autogenerated mock type for the UserChatRequestRepository type
type UserChatRequestRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, request
func (_m *UserChatRequestRepository) Create(ctx context.Context, request *repository.UserChatRequest) error {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatRequest) error); ok {
		r0 = rf(ctx, request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByCreatedAtPeriod provides a mock function with given fields: ctx, start, end
func (_m *UserChatRequestRepository) GetByCreatedAtPeriod(ctx context.Context, start string, end string) ([]*repository.UserChatRequest, error) {
	ret := _m.Called(ctx, start, end)

	if len(ret) == 0 {
		panic("no return value specified for GetByCreatedAtPeriod")
	}

	var r0 []*repository.UserChatRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]*repository.UserChatRequest, error)); ok {
		return rf(ctx, start, end)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []*repository.UserChatRequest); ok {
		r0 = rf(ctx, start, end)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, start, end)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserChatRequestRepository creates a new instance of UserChatRequestRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserChatRequestRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserChatRequestRepository {
	mock := &UserChatRequestRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
