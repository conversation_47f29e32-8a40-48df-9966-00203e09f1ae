package config_test

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
	"topnetwork.ai/topai/chat-webserver/config"
)

// TestEnv 测试环境结构
type TestEnv struct {
	t       *testing.T
	tempDir string
	config  *config.Config
}

// NewTestEnv 创建新的测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	tempDir := t.TempDir()
	return &TestEnv{
		t:       t,
		tempDir: tempDir,
		config:  &config.Config{}, // 使用空配置，因为 DefaultConfig 可能不存在
	}
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	os.RemoveAll(e.tempDir)
}

func TestConfigLoading(t *testing.T) {
	tests := []struct {
		name       string
		setup      func(*testing.T) *TestEnv
		configPath string
		wantErr    bool
		validate   func(*testing.T, *config.Config)
	}{
		{
			name: "加载有效配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				configPath := filepath.Join(env.tempDir, "valid_config.yaml")
				validConfig := &config.Config{
					Version: "1.0.0",
					Server: config.ServerConfig{
						Port:    8080,
						Timeout: 30 * time.Second,
					},
					Database: config.DatabaseConfig{
						Host:     "localhost",
						Port:     3306,
						Username: "test",
						Password: "test",
						Database: "test_db",
					},
				}
				data, err := yaml.Marshal(validConfig)
				require.NoError(t, err)
				err = os.WriteFile(configPath, data, 0644)
				require.NoError(t, err)
				return env
			},
			configPath: "valid_config.yaml",
			wantErr:    false,
			validate: func(t *testing.T, cfg *config.Config) {
				assert.Equal(t, "1.0.0", cfg.Version)
				assert.Equal(t, 8080, cfg.Server.Port)
				assert.Equal(t, "localhost", cfg.Database.Host)
			},
		},
		{
			name: "加载不存在的配置文件",
			setup: func(t *testing.T) *TestEnv {
				return NewTestEnv(t)
			},
			configPath: "nonexistent.yaml",
			wantErr:    true,
		},
		{
			name: "加载无效的配置文件",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				configPath := filepath.Join(env.tempDir, "invalid_config.yaml")
				err := os.WriteFile(configPath, []byte("invalid: yaml: content"), 0644)
				require.NoError(t, err)
				return env
			},
			configPath: "invalid_config.yaml",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := tt.setup(t)
			defer env.Cleanup()

			configPath := filepath.Join(env.tempDir, tt.configPath)
			err := config.LoadConfig(configPath)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tt.validate != nil {
					tt.validate(t, config.GetConfig())
				}
			}
		})
	}
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *config.Config
		wantErr     bool
		errContains string
	}{
		{
			name: "有效配置",
			config: &config.Config{
				Version: "1.0.0",
				Server: config.ServerConfig{
					Port:    8080,
					Timeout: 30 * time.Second,
				},
				Database: config.DatabaseConfig{
					Host:     "localhost",
					Port:     3306,
					Username: "test",
					Password: "test",
					Database: "test_db",
				},
				Auth: config.AuthConfig{
					Secret:   "test-secret",
					TokenTTL: 24 * time.Hour,
				},
			},
			wantErr: false,
		},
		{
			name: "无效的服务器端口",
			config: &config.Config{
				Server: config.ServerConfig{
					Port: -1,
				},
			},
			wantErr:     true,
			errContains: "invalid server port",
		},
		{
			name: "无效的超时时间",
			config: &config.Config{
				Server: config.ServerConfig{
					Timeout: -1 * time.Second,
				},
			},
			wantErr:     true,
			errContains: "invalid server timeout",
		},
		{
			name: "无效的数据库端口",
			config: &config.Config{
				Database: config.DatabaseConfig{
					Port: -1,
				},
			},
			wantErr:     true,
			errContains: "invalid database port",
		},
		{
			name: "无效的Token TTL",
			config: &config.Config{
				Auth: config.AuthConfig{
					TokenTTL: -1 * time.Hour,
				},
			},
			wantErr:     true,
			errContains: "invalid auth token TTL",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConfigHotReload(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 创建初始配置文件
	configPath := filepath.Join(env.tempDir, "hot_reload_config.yaml")
	initialConfig := &config.Config{
		Version: "1.0.0",
		Server: config.ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
	}
	data, err := yaml.Marshal(initialConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	// 加载初始配置
	err = config.LoadConfig(configPath)
	require.NoError(t, err)
	assert.Equal(t, 8080, config.GetConfig().Server.Port)

	// 修改配置文件
	updatedConfig := &config.Config{
		Version: "1.0.1",
		Server: config.ServerConfig{
			Port:    9090,
			Timeout: 30 * time.Second,
		},
	}
	data, err = yaml.Marshal(updatedConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	// 重新加载配置
	err = config.LoadConfig(configPath)
	require.NoError(t, err)
	assert.Equal(t, 9090, config.GetConfig().Server.Port)
}

func TestConfigDefaultValues(t *testing.T) {
	// 由于 DefaultConfig 可能不存在，我们直接测试默认值
	cfg := &config.Config{
		Version: "1.0.0",
		Server: config.ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "",
			Database: "chat_server",
		},
		Auth: config.AuthConfig{
			Secret:   "default-secret-key-for-jwt-token-generation",
			TokenTTL: 24 * time.Hour,
		},
	}

	assert.NotNil(t, cfg)
	assert.Equal(t, "1.0.0", cfg.Version)
	assert.Equal(t, 8080, cfg.Server.Port)
	assert.Equal(t, 30*time.Second, cfg.Server.Timeout)
	assert.Equal(t, "localhost", cfg.Database.Host)
	assert.Equal(t, 3306, cfg.Database.Port)
	assert.Equal(t, 24*time.Hour, cfg.Auth.TokenTTL)
}

func TestConfigChangeNotification(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 创建测试通知器
	notified := false
	notifier := &testConfigNotifier{
		onNotify: func(old, new *config.Config) {
			notified = true
		},
	}

	// 注册通知器
	config.RegisterNotifier(notifier)

	// 加载新配置
	configPath := filepath.Join(env.tempDir, "notification_config.yaml")
	newConfig := &config.Config{
		Version: "1.0.0",
		Server: config.ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
		},
	}
	data, err := yaml.Marshal(newConfig)
	require.NoError(t, err)
	err = os.WriteFile(configPath, data, 0644)
	require.NoError(t, err)

	err = config.LoadConfig(configPath)
	require.NoError(t, err)
	assert.True(t, notified)
}

// testConfigNotifier 用于测试配置变更通知
type testConfigNotifier struct {
	onNotify func(old, new *config.Config)
}

func (n *testConfigNotifier) NotifyChange(old, new *config.Config) {
	if n.onNotify != nil {
		n.onNotify(old, new)
	}
}
