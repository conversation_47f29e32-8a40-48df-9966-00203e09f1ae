// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelSeriesRepository is an autogenerated mock type for the TopaiModelSeriesRepository type
type TopaiModelSeriesRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelSeriesRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelSeries, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelSeries
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelSeries, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelSeries); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelSeries)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *TopaiModelSeriesRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModelSeries, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.TopaiModelSeries
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModelSeries, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModelSeries); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelSeries)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelSeriesRepository creates a new instance of TopaiModelSeriesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelSeriesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelSeriesRepository {
	mock := &TopaiModelSeriesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
