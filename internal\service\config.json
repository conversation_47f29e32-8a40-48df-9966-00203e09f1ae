version: 1.0.0
server:
    port: 8080
    timeout: 30s
    tls:
        enabled: false
        cert: ""
        key: ""
database:
    host: localhost
    port: 3306
    username: root
    password: ""
    database: chat_server
    pool:
        max_open_conns: 100
        max_idle_conns: 10
        conn_max_lifetime: 1h0m0s
        conn_max_idletime: 30m0s
    timeouts:
        connect: 5s
        query: 30s
        transaction: 30s
    parse_time: true
    collation: utf8mb4_unicode_ci
auth:
    secret: default-secret-key-for-jwt-token-generation
    tokenTTL: 24h0m0s
log:
    level: info
    path: logs/app.log
    maxSize: 100
    maxBackups: 3
    maxAge: 7
    compress: true
    console: true
service:
    file:
        max_size: ********
        path: uploads
    openai:
        openai:
            baseurl: https://api.openai.com/v1
            apikey: sk-...
    topai:
        walletaddr: ******************************************
        ipmappath: ip_map.json
    shadow_account:
        version: "1"
        algorithm: aes-256-gcm
        kdf_algorithm: HKDF-SHA256
        base64_key: "1234567890123456789012345678901234567890"
        pool_account_pub: ******************************************
        pool_account_pri: ******************************************
    top_chain:
        chain_url: https://api.topai.com/v1
        chain_id: 1023
        imo_entry_address: ******************************************
        token_vault_address: ******************************************
        proxy_admin_address: ******************************************
        internal_factory_address: ******************************************
        internal_router_address: ******************************************
        model_token_address: ******************************************
        model_lock_token_address: ******************************************
        model_factory_address: ******************************************
        bank_address: ******************************************
        settlement_address: ******************************************
        deposit_address: ******************************************
        asset_management_address: ******************************************
        node_register_address: ******************************************
        ai_models_address: ******************************************
        ai_worker_address: ******************************************
        weth9_address: ******************************************
        uniswap_v2_factory_address: ******************************************
        uniswap_v2_router02_address: ******************************************
        top_erc20_wrapper_address: ******************************************
        usdc_erc20_wrapper_address: ******************************************
        usdt_erc20_wrapper_address: ******************************************
    company_user_id: 0
    concurrency_limit:
        global_limit: 100
        user_limit: 10
        user_model_limit: 3
