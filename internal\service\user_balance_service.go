package service

import (
	"context"
	"errors"
	"math/big"
	"time"

	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type userBalanceService struct {
	log              *logger.ModuleLogger
	repo             *repository.DB
	stop             chan struct{}
	usageIdCh        <-chan uint
	giftBalanceCh    chan *giftBalance
	companyUserID    uint
	shadowAccountApi shadowAccountApi
}

type shadowAccountApi interface {
	GetUserShadowAddress(ctx context.Context, userID uint) (string, error)
}

func NewUserBalanceService(ctx context.Context, repo *repository.DB, usageIdCh <-chan uint, companyUserID uint, shadowAccountApi shadowAccountApi) *userBalanceService {
	return &userBalanceService{
		log:              logger.GetLogger("user_balance_service"),
		repo:             repo,
		stop:             make(chan struct{}),
		usageIdCh:        usageIdCh,
		giftBalanceCh:    make(chan *giftBalance, 1),
		companyUserID:    companyUserID,
		shadowAccountApi: shadowAccountApi,
	}
}

func (s *userBalanceService) start(ctx context.Context) error {
	go s.balanceCron(ctx)
	go s.autoArchiveUsage(ctx)
	return nil
}

func (s *userBalanceService) balanceCron(ctx context.Context) {
	// 启动时，处理一下未结算的usage
	usages, err := s.repo.UserChatUsage.GetConfirmed(ctx)
	if err != nil {
		s.log.Error("get confirmed usage failed", zap.Error(err))
		return
	}
	for _, usage := range usages {
		err := s.settleCost(ctx, usage.ID)
		if err != nil {
			s.log.Error("settle cost failed", zap.Error(err))
		}
	}
	// 链上充值记录
	rechargeCh := make(chan *repository.UserRechargeRecord)
	go func() {
		for {
			select {
			case <-s.stop:
				return
			case <-time.After(time.Second * 10):
			}
			recharge, err := s.repo.UserRechargeRecord.GetUnconfirmed(ctx)
			if err != nil {
				s.log.Error("get unconfirmed recharge record failed", zap.Error(err))
				continue
			}
			for _, recharge := range recharge {
				s.log.Info("recharge record", zap.Any("recharge", recharge))
				rechargeCh <- recharge
			}
		}
	}()

	for {
		select {
		case <-s.stop:
			return
		case usageId := <-s.usageIdCh:
			err := s.settleCost(ctx, usageId)
			if err != nil {
				s.log.Error("calculate cost failed", zap.Error(err))
			}
		case giftBalance := <-s.giftBalanceCh:
			// 直接赠送到余额，不经过链上
			err := s.giftBalance(ctx, giftBalance.FromUserID, giftBalance.ToUserID, giftBalance.Amount, "admin gift")
			if err != nil {
				giftBalance.errCh <- err
				s.log.Error("gift balance failed", zap.Error(err))
			}
			close(giftBalance.errCh)
		case recharge := <-rechargeCh:
			err := s.recharge(ctx, recharge)
			if err != nil {
				s.log.Error("recharge failed", zap.Error(err))
			}
		}
	}
}

// 自动归档usage
func (s *userBalanceService) autoArchiveUsage(ctx context.Context) error {
	for {
		// 获取最后一条每日记录
		lastUsageDayRecord, err := s.repo.UserUsageDayRecord.GetLast(ctx)
		if err != nil {
			s.log.Error("get last usage day record failed", zap.Error(err))
			return err
		}
		lastDay, _ := time.Parse("2006-01-02", "2025-07-15")
		if lastUsageDayRecord != nil && lastUsageDayRecord.UsageDate != "" {
			lastDay, _ = time.Parse("2006-01-02", lastUsageDayRecord.UsageDate)
		}
		// 增加一个小时，等待usage确认完成
		lastDay = lastDay.Add(time.Hour)

		for {
			lastDay = lastDay.AddDate(0, 0, 1)
			s.log.Info("lastDay", zap.Any("lastDay", lastDay))
			if time.Now().Before(lastDay.AddDate(0, 0, 1)) {
				s.log.Info("lastDay", zap.Any("lastDay", lastDay.AddDate(0, 0, 1)), zap.Any("now", time.Now()))
				break
			}
			select {
			case <-s.stop:
				return nil
			default:
			}
			day := lastDay.Format("2006-01-02")
			usages, err := s.repo.UserChatUsage.GetByCreatedAtPeriod(ctx, day+" 00:00:00", day+" 23:59:59")
			if err != nil {
				s.log.Error("get usage by created at period failed", zap.Error(err))
				continue
			}
			if len(usages) == 0 {
				continue
			}
			// 按照appkey和modelid分组
			appKeyModelIdMap := make(map[string]map[string][]*repository.UserChatUsage)
			for _, usage := range usages {
				if usage.AppKey == "" || usage.Status == uint8(repository.UserChatUsageStatusUnconfirmed) {
					continue
				}
				if _, ok := appKeyModelIdMap[usage.AppKey]; !ok {
					appKeyModelIdMap[usage.AppKey] = make(map[string][]*repository.UserChatUsage)
				}
				if _, ok := appKeyModelIdMap[usage.AppKey][usage.ModelID]; !ok {
					appKeyModelIdMap[usage.AppKey][usage.ModelID] = make([]*repository.UserChatUsage, 0)
				}
				appKeyModelIdMap[usage.AppKey][usage.ModelID] = append(appKeyModelIdMap[usage.AppKey][usage.ModelID], usage)
			}
			usageDayRecords := make([]*repository.UserUsageDayRecord, 0)
			modelCostMap := make(map[string]map[string]*big.Int)
			for appKey, modelIdMap := range appKeyModelIdMap {
				devAppKey, err := s.repo.DevAppKey.GetByKey(ctx, appKey)
				if err != nil {
					s.log.Error("get dev app key by app key failed", zap.Error(err))
					continue
				}
				if devAppKey == nil {
					continue
				}
				for modelId, usages := range modelIdMap {
					if _, ok := modelCostMap[modelId]; !ok {
						modelCostMap[modelId] = map[string]*big.Int{
							"inputTokens":  big.NewInt(0),
							"outputTokens": big.NewInt(0),
							"totalTokens":  big.NewInt(0),
							"count":        big.NewInt(0),
						}
					}
					usageDayRecord := &repository.UserUsageDayRecord{
						UsageDate: day,
						ModelId:   modelId,
						AppKey:    appKey,
						UserId:    usages[0].UserID,
						AppId:     devAppKey.DevAppId,
					}
					totalInputTokens := big.NewInt(0)
					totalOutputTokens := big.NewInt(0)
					totalTokens := big.NewInt(0)
					totalInputCost := big.NewInt(0)
					totalOutputCost := big.NewInt(0)
					totalCost := big.NewInt(0)
					for _, usage := range usages {
						inputTokens, _ := big.NewInt(0).SetString(usage.InputTokens, 10)
						outputTokens, _ := big.NewInt(0).SetString(usage.OutputTokens, 10)
						inputCost, _ := big.NewInt(0).SetString(usage.InputCost, 10)
						outputCost, _ := big.NewInt(0).SetString(usage.OutputCost, 10)

						totalInputTokens.Add(totalInputTokens, inputTokens)
						totalOutputTokens.Add(totalOutputTokens, outputTokens)
						totalTokens.Add(totalTokens, inputTokens)
						totalTokens.Add(totalTokens, outputTokens)
						totalInputCost.Add(totalInputCost, inputCost)
						totalOutputCost.Add(totalOutputCost, outputCost)
						totalCost.Add(totalCost, totalInputCost)
						totalCost.Add(totalCost, totalOutputCost)
					}
					usageDayRecord.InputTokens = totalInputTokens.String()
					usageDayRecord.OutputTokens = totalOutputTokens.String()
					usageDayRecord.TotalTokens = totalTokens.String()
					usageDayRecord.InputCost = totalInputCost.String()
					usageDayRecord.OutputCost = totalOutputCost.String()
					usageDayRecord.TotalCost = totalCost.String()
					usageDayRecord.Count = len(usages)
					usageDayRecords = append(usageDayRecords, usageDayRecord)

					modelCostMap[modelId]["inputTokens"].Add(modelCostMap[modelId]["inputTokens"], totalInputTokens)
					modelCostMap[modelId]["outputTokens"].Add(modelCostMap[modelId]["outputTokens"], totalOutputTokens)
					modelCostMap[modelId]["totalTokens"].Add(modelCostMap[modelId]["totalTokens"], totalTokens)
					modelCostMap[modelId]["count"].Add(modelCostMap[modelId]["count"], big.NewInt(int64(len(usages))))
				}
			}
			if len(usageDayRecords) > 0 {
				// 修复：避免预设长度导致前半部分为空串
				modelIds := make([]string, 0, len(modelCostMap))
				for modelId := range modelCostMap {
					modelIds = append(modelIds, modelId)
				}
				modelCosts, err := s.repo.TopaiModelCost.GetByModelIds(ctx, modelIds)
				if err != nil {
					s.log.Error("get topai model cost by topai model ids failed", zap.Error(err))
					break
				}
				savedCostMap := make(map[string]*repository.TopaiModelCost)
				for _, modelCost := range modelCosts {
					savedCostMap[modelCost.ModelId] = modelCost
				}
				extModels, err := s.repo.TopaiModelExt.GetByModelIds(ctx, modelIds)
				if err != nil {
					s.log.Error("get topai model ext by model ids failed", zap.Error(err))
					break
				}
				extModelMap := make(map[string]*repository.TopaiModelExt)
				for _, extModel := range extModels {
					extModelMap[extModel.ModelId] = extModel
				}

				err = s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
					err = db.UserUsageDayRecord.CreateBatch(ctx, usageDayRecords)
					if err != nil {
						return err
					}
					for modelId, costMap := range modelCostMap {
						modelCost := savedCostMap[modelId]
						if modelCost == nil {
							extModel := extModelMap[modelId]
							if extModel == nil {
								continue
							}
							err = db.TopaiModelCost.Create(ctx, &repository.TopaiModelCost{
								ModelId:           modelId,
								TopaiModelId:      extModel.TopaiModelId,
								TotalInputTokens:  costMap["inputTokens"].String(),
								TotalOutputTokens: costMap["outputTokens"].String(),
								TotalTokens:       costMap["totalTokens"].String(),
								TotalCount:        int(costMap["count"].Int64()),
							})
							if err != nil {
								return err
							}
						} else {
							_inputTokens, _ := big.NewInt(0).SetString(modelCost.TotalInputTokens, 10)
							_outputTokens, _ := big.NewInt(0).SetString(modelCost.TotalOutputTokens, 10)
							_totalTokens, _ := big.NewInt(0).SetString(modelCost.TotalTokens, 10)
							_count := big.NewInt(0).SetInt64(int64(modelCost.TotalCount))
							_inputTokens.Add(_inputTokens, costMap["inputTokens"])
							_outputTokens.Add(_outputTokens, costMap["outputTokens"])
							_totalTokens.Add(_totalTokens, costMap["totalTokens"])
							_count.Add(_count, costMap["count"])
							modelCost.TotalInputTokens = _inputTokens.String()
							modelCost.TotalOutputTokens = _outputTokens.String()
							modelCost.TotalTokens = _totalTokens.String()
							modelCost.TotalCount = int(_count.Int64())
							err = db.TopaiModelCost.Update(ctx, modelCost)
							if err != nil {
								return err
							}
						}
					}
					return nil
				})
				if err != nil {
					s.log.Error("create usage day record failed", zap.Error(err))
					break
				}
			}
		}
		select {
		case <-s.stop:
			return nil
		case <-time.After(time.Hour * 4):
		}
	}
}

func (s *userBalanceService) giftBalance(ctx context.Context, userID uint, toUserID uint, amount *big.Int, description string) error {
	if userID > 0 {
		// 暂不支持用户转赠
		return errors.New("user gift balance not supported")
	}
	if userID == toUserID {
		return nil
	}
	if amount.Sign() <= 0 {
		return errors.New("amount must be greater than 0")
	}
	// 直接增加到toUserID的余额
	userBalance, err := s.getUserBalance(ctx, toUserID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return err
	}
	userBalanceInt, _ := big.NewInt(0).SetString(userBalance.Balance, 10)
	accumulatedInt, _ := big.NewInt(0).SetString(userBalance.Accumulated, 10)
	userBalance.Balance = big.NewInt(0).Add(userBalanceInt, amount).String()
	userBalance.Accumulated = big.NewInt(0).Add(accumulatedInt, amount).String()
	balanceRecord := &repository.UserBalanceRecord{
		UserID:          toUserID,
		UserBalanceID:   userBalance.ID,
		UserChatUsageID: 0,
		Amount:          amount.String(),
		Type:            repository.UserBalanceRecordTypeAdminGift,
		Currency:        repository.UserBalanceCurrencyUSDT,
		CurrentBalance:  userBalance.Balance,
		IsIn:            1,
		Description:     description,
	}
	return s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		err = db.UserBalance.UpdateBalanceByID(ctx, userBalance.ID, userBalance.Balance, userBalance.Accumulated)
		if err != nil {
			return err
		}
		return db.UserBalanceRecord.Create(ctx, balanceRecord)
	})
}

func (s *userBalanceService) recharge(ctx context.Context, recharge *repository.UserRechargeRecord) error {
	userBalance, err := s.getUserBalance(ctx, recharge.UserID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		s.log.Error("get user balance by user id and currency failed", zap.Error(err))
		return err
	}

	userBalanceInt, _ := big.NewInt(0).SetString(userBalance.Balance, 10)
	accumulatedInt, _ := big.NewInt(0).SetString(userBalance.Accumulated, 10)
	amount, _ := big.NewInt(0).SetString(recharge.Amount, 10)
	newBalance := big.NewInt(0).Add(userBalanceInt, amount)
	userBalance.Balance = newBalance.String()
	userBalance.Accumulated = big.NewInt(0).Add(accumulatedInt, amount).String()
	balanceRecord := &repository.UserBalanceRecord{
		UserID:          recharge.UserID,
		UserBalanceID:   userBalance.ID,
		UserChatUsageID: 0,
		Amount:          recharge.Amount,
		Type:            repository.UserBalanceRecordTypeRecharge,
		Currency:        recharge.Currency,
		CurrentBalance:  userBalance.Balance,
		IsIn:            1,
		Description:     recharge.Description,
	}

	return s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		err = db.UserBalance.UpdateBalanceByID(ctx, userBalance.ID, userBalance.Balance, userBalance.Accumulated)
		if err != nil {
			return err
		}
		err = db.UserBalanceRecord.Create(ctx, balanceRecord)
		if err != nil {
			return err
		}
		return db.UserRechargeRecord.UpdateConfirmedByID(ctx, recharge.ID, balanceRecord.ID)
	})
}

func (s *userBalanceService) settleCost(ctx context.Context, usageId uint) error {
	usage, err := s.repo.UserChatUsage.GetByID(ctx, usageId)
	if err != nil {
		s.log.Error("get usage by id failed", zap.Error(err))
		return err
	}
	if usage.Status != 1 {
		return nil
	}
	// 非appkey的usage， 认为是聊天界面，设置好userId为0，从公司账户支出
	if usage.AppKey == "" {
		usage.UserID = s.companyUserID
	}
	userBalance, err := s.getUserBalance(ctx, usage.UserID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		s.log.Error("get user balance by user id and currency failed", zap.Error(err))
		return err
	}

	cost, _ := big.NewInt(0).SetString(usage.TotalCost, 10)

	currentBalance, _ := big.NewInt(0).SetString(userBalance.Balance, 10)
	newBalance := big.NewInt(0).Sub(currentBalance, cost)
	userBalance.Balance = newBalance.String()
	balanceRecord := &repository.UserBalanceRecord{
		UserID:          usage.UserID,
		UserBalanceID:   userBalance.ID,
		UserChatUsageID: usage.ID,
		Amount:          cost.String(),
		Type:            repository.UserBalanceRecordTypeChat,
		Currency:        repository.UserBalanceCurrencyUSDT,
		CurrentBalance:  userBalance.Balance,
		IsIn:            0,
		Description:     "chat usage cost",
	}

	return s.repo.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		err = db.UserBalance.UpdateBalanceByID(ctx, userBalance.ID, userBalance.Balance, userBalance.Accumulated)
		if err != nil {
			return err
		}

		err = db.UserBalanceRecord.Create(ctx, balanceRecord)
		if err != nil {
			return err
		}
		return db.UserChatUsage.UpdateSettledByID(ctx, usage.ID)
	})
}

func (s *userBalanceService) Close() {
	close(s.stop)
}

func (s *userBalanceService) getUserBalance(ctx context.Context, userID uint, currency string) (*repository.UserBalance, error) {
	userBalance, err := s.repo.UserBalance.GetByUserIDAndCurrency(ctx, userID, currency)
	if err != nil {
		return nil, err
	}
	if userBalance == nil || userBalance.ID == 0 {
		userBalance = &repository.UserBalance{
			UserID:      userID,
			Balance:     "0",
			Accumulated: "0",
			Currency:    currency,
		}
		err = s.repo.UserBalance.Create(ctx, userBalance)
		if err != nil {
			return nil, err
		}
	}
	return userBalance, nil
}

func (s *userBalanceService) GetUserBalance(ctx context.Context, userID uint) (string, error) {
	userBalance, err := s.repo.UserBalance.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return "0", err
	}
	if userBalance == nil {
		return "0", nil
	}
	return userBalance.Balance, nil
}

func (s *userBalanceService) GetUserBalanceRecord(ctx context.Context, userID uint, page int, pageSize int) (*UserBalanceRecordOP, error) {
	offset := (page - 1) * pageSize
	total, err := s.repo.UserBalanceRecord.CountByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT)
	if err != nil {
		return nil, err
	}
	res := &UserBalanceRecordOP{
		Total:   total,
		Records: make([]*UserBalanceRecord, 0),
	}
	if offset > int(total) {
		return res, nil
	}
	records, err := s.repo.UserBalanceRecord.GetByUserIDAndCurrency(ctx, userID, repository.UserBalanceCurrencyUSDT, offset, pageSize, "id", "desc")
	if err != nil {
		return nil, err
	}

	res.Records = make([]*UserBalanceRecord, len(records))
	for i, record := range records {
		res.Records[i] = &UserBalanceRecord{
			ID:          record.ID,
			Type:        record.Type,
			IsIn:        record.IsIn == 1,
			Amount:      record.Amount,
			Description: record.Description,
			CreatedAt:   record.CreatedAt.Unix(),
		}
	}
	return res, nil
}

func (s *userBalanceService) GetUsage(ctx context.Context, appKey *AppKeyOP, requestId string) (*GetUsageResponse, error) {
	usage, err := s.repo.UserChatUsage.GetByUUID(ctx, requestId)
	if err != nil {
		return nil, err
	}
	if usage == nil {
		return nil, api.NewClientError("usage not found")
	}
	if usage.AppKey != appKey.Key {
		return nil, api.NewClientError("usage not found")
	}
	if usage.Status == uint8(repository.UserChatUsageStatusUnconfirmed) {
		return nil, api.NewClientError("usage not confirmed, please wait for confirmation")
	}
	return &GetUsageResponse{
		Id:               usage.ID,
		Uuid:             usage.UUID,
		Model:            usage.ModelID,
		TokensPrompt:     usage.InputTokens,
		TokensCompletion: usage.OutputTokens,
		TotalCost:        usage.TotalCost,
	}, nil
}

func (s *userBalanceService) GetUserShadowAddress(ctx context.Context, userID uint) (string, error) {
	user, err := s.repo.User.GetByID(ctx, userID)
	if err != nil {
		return "", err
	}
	if user == nil {
		return "", api.NewClientError("user not found")
	}
	shadowAddress, err := s.shadowAccountApi.GetUserShadowAddress(ctx, user.ID)
	if err != nil {
		return "", err
	}
	return shadowAddress, nil
}

// AdminGiftBalance 管理员赠送用户余额
func (s *userBalanceService) AdminGiftBalance(ctx context.Context, toUserIdentityID string, amount *big.Int, description string) error {
	// 查找目标用户
	toUser, err := s.repo.User.GetByIdentityID(ctx, toUserIdentityID)
	if err != nil {
		return err
	}
	if toUser == nil || toUser.ID == 0 {
		return errors.New("user not found")
	}

	// 如果没有提供描述，使用默认描述
	if description == "" {
		description = "admin gift balance"
	}

	git := &giftBalance{
		FromUserID: 0,
		ToUserID:   toUser.ID,
		Amount:     amount,
		errCh:      make(chan error, 1),
	}
	s.giftBalanceCh <- git
	return <-git.errCh
}
