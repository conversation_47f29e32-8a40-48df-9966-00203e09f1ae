package utils

import (
	"fmt"
	"testing"

	"encoding/hex"
	"strings"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/stretchr/testify/assert"
)

// TestMetaMaskPersonalSign 测试小狐狸personal_sign签名验证
func TestMetaMaskPersonalSign(t *testing.T) {
	// 1. 生成测试密钥
	privateKey, err := crypto.GenerateKey()
	assert.NoError(t, err, "生成私钥失败")

	walletAddr := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()
	nonce := "testnonce123"

	fmt.Printf("测试钱包地址: %s\n", walletAddr)
	fmt.Printf("测试Nonce: %s\n", nonce)

	// 2. 模拟小狐狸的personal_sign流程
	// personal_sign的参数是 [message, address]，其中message就是nonce
	message := nonce // 小狐狸直接对nonce进行签名

	// 3. 添加以太坊签名前缀（使用旧代码的格式）
	msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)
	msgHash := crypto.Keccak256Hash([]byte(msg))

	fmt.Printf("小狐狸签名消息: %s\n", message)
	fmt.Printf("带前缀消息哈希: %x\n", msgHash.Bytes())

	// 4. 使用私钥签名
	signatureBytes, err := crypto.Sign(msgHash.Bytes(), privateKey)
	assert.NoError(t, err, "签名失败")

	signature := "0x" + hex.EncodeToString(signatureBytes)
	fmt.Printf("生成的签名: %s\n", signature)
	fmt.Printf("签名长度: %d 字节\n", len(signatureBytes))

	// 6. 验证签名
	ok, err := VerifyProviderSignature(nonce, walletAddr, signature)

	if err != nil {
		fmt.Printf("❌ 验证失败，错误: %v\n", err)
	} else if !ok {
		fmt.Printf("❌ 验证失败，地址不匹配\n")
	} else {
		fmt.Printf("✅ 验证成功！\n")
	}

	fmt.Printf("验证结果: ok=%v, err=%v\n", ok, err)
}

// TestRealMetaMaskData 测试真实的小狐狸数据
func TestRealMetaMaskData(t *testing.T) {
	// {"module": "provider-handler", "req": {"wallet_addr":"******************************************","signature":"0x29aa9d3e3d30684ac9d26862a58a6e62069a772d836b48b143fe0a4923c0dc97064fda88ff7944e680f4d05ca721890dad760c1ca325700b59905c3a5122deea1b","nonce":"574e9c08a2107d98de6c9e9e8c60f86e1b6187c3ec2a6031803f32c713b863a2","timestamp":**********}}

	privateKeyStr := "0x4edf73c3fb18df03994640f1f0156103accc327bd4d8a8e497e969733e87e151"
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(privateKeyStr, "0x"))
	assert.NoError(t, err, "解析私钥失败")

	// 使用您提供的真实数据
	nonce := "code:da1c8389f692b9ce5595ab42fced22addf5aedbaca37351c5c33b8b7375ae33a-******************************************"
	walletAddr := "******************************************"
	signature := "0x8c5864c60a46997bb3ec604745901afc53ade14b57ee87081a8956529111395820f3bb32ad196ad50ec8928fb9503c67c39761589dc1079a820b8f114ea07a6a1b"

	fmt.Printf("\n测试真实小狐狸数据:\n")
	fmt.Printf("Nonce: %s\n", nonce)
	fmt.Printf("钱包地址: %s\n", walletAddr)
	fmt.Printf("签名: %s\n", signature)

	// 验证钱包地址是否与私钥匹配
	expectedAddr := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()
	fmt.Printf("私钥对应的地址: %s\n", expectedAddr)
	fmt.Printf("地址匹配: %v\n", strings.EqualFold(expectedAddr, walletAddr))

	// 1. 先验证我们的验签函数
	ok, err := VerifyProviderSignature(nonce, walletAddr, signature)

	if err != nil {
		fmt.Printf("❌ 验证失败，错误: %v\n", err)
	} else if !ok {
		fmt.Printf("❌ 验证失败，地址不匹配\n")
	} else {
		fmt.Printf("✅ 验证成功！\n")
	}

	fmt.Printf("验证结果: ok=%v, err=%v\n", ok, err)

	// 2. 手动验证签名过程
	fmt.Printf("\n手动验证签名过程:\n")

	// 构造消息格式（使用旧代码的格式）
	message := nonce
	msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)
	msgHash := crypto.Keccak256Hash([]byte(msg))

	fmt.Printf("消息: %s\n", message)
	fmt.Printf("带前缀消息哈希: %x\n", msgHash.Bytes())

	// 解析签名
	sigBytes, err := hex.DecodeString(strings.TrimPrefix(signature, "0x"))
	assert.NoError(t, err, "解析签名失败")

	fmt.Printf("签名长度: %d 字节\n", len(sigBytes))
	fmt.Printf("原始V值: %d\n", sigBytes[64])

	// 处理V值（使用旧代码的逻辑）
	originalV := sigBytes[64]
	if originalV < 27 {
		originalV += 27
	}
	sigBytes[64] = originalV - 27
	fmt.Printf("调整后V值: %d\n", sigBytes[64])

	// 恢复公钥
	pubKey, err := crypto.SigToPub(msgHash.Bytes(), sigBytes)
	assert.NoError(t, err, "恢复公钥失败")

	recoveredAddr := crypto.PubkeyToAddress(*pubKey)
	fmt.Printf("恢复的地址: %s\n", recoveredAddr.Hex())
	fmt.Printf("地址匹配: %v\n", strings.EqualFold(recoveredAddr.Hex(), walletAddr))

	// 3. 使用私钥重新生成签名进行对比
	fmt.Printf("\n使用私钥重新生成签名:\n")
	newSignatureBytes, err := crypto.Sign(msgHash.Bytes(), privateKey)
	assert.NoError(t, err, "重新签名失败")

	newSignature := "0x" + hex.EncodeToString(newSignatureBytes)
	fmt.Printf("重新生成的签名: %s\n", newSignature)
	fmt.Printf("签名是否相同: %v\n", newSignature == signature)

	// 验证重新生成的签名
	newOk, newErr := VerifyProviderSignature(nonce, walletAddr, newSignature)
	fmt.Printf("重新生成签名的验证结果: ok=%v, err=%v\n", newOk, newErr)

	// 4. 分析问题
	fmt.Printf("\n问题分析:\n")
	fmt.Printf("1. 私钥地址匹配: %v\n", strings.EqualFold(expectedAddr, walletAddr))
	fmt.Printf("2. 恢复地址匹配: %v\n", strings.EqualFold(recoveredAddr.Hex(), walletAddr))
	fmt.Printf("3. 重新生成签名验证: %v\n", newOk)

	if strings.EqualFold(expectedAddr, walletAddr) && !strings.EqualFold(recoveredAddr.Hex(), walletAddr) {
		fmt.Printf("❌ 结论: 提供的签名不是由这个私钥生成的\n")
		fmt.Printf("   或者签名对应的消息格式与我们期望的不同\n")
	} else if newOk {
		fmt.Printf("✅ 结论: 我们的验签逻辑是正确的\n")
	}
}

// TestFinalConfirmation 最终确认测试
func TestFinalConfirmation(t *testing.T) {
	// 使用提供的私钥
	privateKeyStr := "0x4edf73c3fb18df03994640f1f0156103accc327bd4d8a8e497e969733e87e151"
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(privateKeyStr, "0x"))
	assert.NoError(t, err, "解析私钥失败")

	walletAddr := "******************************************"
	nonce := "code:da1c8389f692b9ce5595ab42fced22addf5aedbaca37351c5c33b8b7375ae33a-******************************************"

	fmt.Printf("\n最终确认测试:\n")
	fmt.Printf("钱包地址: %s\n", walletAddr)
	fmt.Printf("Nonce: %s\n", nonce)

	// 使用私钥生成签名
	message := nonce
	msg := fmt.Sprintf("\x19Ethereum Signed Message:\n%d%s", len(message), message)
	msgHash := crypto.Keccak256Hash([]byte(msg))

	signatureBytes, err := crypto.Sign(msgHash.Bytes(), privateKey)
	assert.NoError(t, err, "签名失败")

	signature := "0x" + hex.EncodeToString(signatureBytes)
	fmt.Printf("生成的签名: %s\n", signature)

	// 验证签名
	ok, err := VerifyProviderSignature(nonce, walletAddr, signature)
	assert.NoError(t, err, "验证签名时发生错误")
	assert.True(t, ok, "签名验证应该成功")

	fmt.Printf("✅ 最终确认：我们的验签实现是正确的！\n")
}
