package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sashabaranov/go-openai"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	sdk "topnetwork.ai/topai/topainode/sdk/golang"
	"topnetwork.ai/topai/topainode/sdk/golang/util"
)

// TopAIClientInterface 定义TopAI客户端接口，用于mock测试
type TopAIClientInterface interface {
	NewOpenAIClient() *sdk.OpenAIClient
	NewWhisperClient() *sdk.WhisperClient
	NewTTSClient() *sdk.TTSClient
	NewTextToImageClient() *sdk.TextToImageClient
	SetStreamMode(session string, isStream bool)
	GetStreamMode(session string) bool
	CloseSession(ctx context.Context, session string, callback sdk.CloseSessionCallback) error
}

// ChainClientInterface 定义链客户端接口，用于mock测试
type ChainClientInterface interface {
	GetUploadModels(includeOffline bool) (*util.ModelSet, error)
}

// 用于管理topai的连接调用，不提供给外部直接调用
type topaiManage struct {
	client       TopAIClientInterface
	chainClient  ChainClientInterface
	chainUrl     string
	debug        bool
	aiSession    map[string][]*aiSession
	rwLock       *sync.RWMutex
	walletAddr   string
	log          *logger.ModuleLogger
	IpMapFile    string
	mainCtx      context.Context
	modelUpdate  *topaiModelUpdate
	aiSessionReq map[string]bool //用来记录当前请求的ai session，防止同一时间多次请求
}

type aiSession struct {
	modelId  int
	session  string
	isStream bool
}

type topaiModelUpdate struct {
	updateTime time.Time
	models     map[int]*util.ModelInfo
}

func newTopaiManage(ctx context.Context, topAiConf config.TopAi, debug bool, topChainConf config.TopChain) *topaiManage {
	log := logger.GetLogger("topaiManage")
	manage := &topaiManage{
		chainUrl:   topChainConf.ChainUrl,
		aiSession:  map[string][]*aiSession{},
		log:        log,
		debug:      debug,
		rwLock:     new(sync.RWMutex),
		walletAddr: topAiConf.WalletAddr,
		IpMapFile:  topAiConf.IpMapPath,
		mainCtx:    ctx,
		modelUpdate: &topaiModelUpdate{
			updateTime: time.Now(),
			models:     nil,
		},
		aiSessionReq: map[string]bool{},
	}

	return manage
}

// newTopaiManageWithClient 用于测试的构造函数，支持依赖注入
func newTopaiManageWithClient(ctx context.Context, client TopAIClientInterface, chainClient ChainClientInterface) *topaiManage {
	log := logger.GetLogger("topaiManage")
	manage := &topaiManage{
		client:      client,
		chainClient: chainClient,
		aiSession:   map[string][]*aiSession{},
		log:         log,
		debug:       false,
		rwLock:      new(sync.RWMutex),
		mainCtx:     ctx,
		modelUpdate: &topaiModelUpdate{
			updateTime: time.Now(),
			models:     nil,
		},
		aiSessionReq: map[string]bool{},
	}

	return manage
}

func (T *topaiManage) start() {
	// 开启定时任务，每2500s获取一次模型列表
	go T.crontab()
}

func (T *topaiManage) initClient(ctx context.Context) error {
	T.rwLock.Lock()
	defer T.rwLock.Unlock()
	if T.client == nil {
		topAINode, err := sdk.NewTOPAINode(T.chainUrl, T.debug)
		if err != nil {
			T.log.Error(fmt.Sprintf("Init topai node failed: %v", err))
			return err
		}
		T.client = topAINode.TOPAIClient()
		err = sdk.GetIPMapInstance().LoadIPMapFromYAML(T.IpMapFile)
		if err != nil {
			T.log.Error(fmt.Sprintf("Load IpMap failed: %v", err))
			return err
		}
		T.chainClient = sdk.GetTOPAIChainInstance()
	}
	return nil
}

func (T *topaiManage) crontab() {
	go func() {
		_, _ = T.getAllModelInfo(T.mainCtx)
		ticker := time.NewTicker(time.Second * 2500)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				_, _ = T.getAllModelInfo(T.mainCtx)
			case <-T.mainCtx.Done():
				return
			}
		}
	}()
}

func (T *topaiManage) addNewSession(ctx context.Context, session string, model *util.ModelInfo, isStream bool) {
	T.rwLock.Lock()
	defer T.rwLock.Unlock()
	ss, ok := T.aiSession[model.ModelType]
	if !ok {
		ss = make([]*aiSession, 0)
	}
	ss = append(ss, &aiSession{
		modelId:  int(model.ModelId),
		session:  session,
		isStream: isStream,
	})
	T.aiSession[model.ModelType] = ss
	// 都设置为流模式
	T.client.SetStreamMode(session, isStream)
}

func (T *topaiManage) deleteSession(ctx context.Context, session, modelType string) {
	T.rwLock.Lock()
	ss, ok := T.aiSession[modelType]
	if !ok {
		return
	}
	newSS := make([]*aiSession, 0)
	for _, s := range ss {
		if s.session == session {
			continue
		}
		newSS = append(newSS, s)
	}
	T.aiSession[modelType] = newSS
	T.rwLock.Unlock()
	go func() {
		err := T.client.CloseSession(ctx, session, func(sessionId string, client sdk.ClientInterface) error {
			T.log.Info(fmt.Sprintf("Closed session: %s", sessionId))
			return nil
		})
		if err != nil {
			T.log.Error(fmt.Sprintf("Close session(%s) failed:%v", session, err))
		}
	}()
}

// todo 需要更新到数据库
// 具体是调用sdk还是直接调用合约，后续在看
// 现在数据库先写死
func (T *topaiManage) getAllModelInfo(ctx context.Context) (map[int]*util.ModelInfo, error) {
	// 先判断有比较新的，就直接返回，不用每次都去查询，10分钟内查过就不去查询了
	T.rwLock.RLock()
	if T.modelUpdate.models != nil && len(T.modelUpdate.models) > 0 && time.Now().Sub(T.modelUpdate.updateTime).Seconds() < 3000 {
		T.rwLock.RUnlock()
		return T.modelUpdate.models, nil
	}
	T.rwLock.RUnlock()

	// 先初始化client
	err := T.initClient(ctx)
	if err != nil {
		return nil, err
	}
	// 此处会有阻塞，改成channel
	ch := make(chan *util.ModelSet)
	errCh := make(chan error)
	go func() {
		defer close(ch)
		defer close(errCh)
		models, err := T.chainClient.GetUploadModels(true)
		if err != nil {
			T.log.Error(fmt.Sprintf("Get models failed: %v", err))
			select {
			case errCh <- err:
			case <-ctx.Done():
			case <-T.mainCtx.Done():
			}
			return
		}
		select {
		case ch <- models:
		case <-ctx.Done():
		case <-T.mainCtx.Done():
		}
	}()
	var models *util.ModelSet
	select {
	case models = <-ch:
	case err = <-errCh:
	case <-ctx.Done():
		err = context.Canceled
	case <-T.mainCtx.Done():
		err = context.Canceled
	}

	if err != nil {
		return nil, err
	}

	list := models.ModelIdMap
	modelList := map[int]*util.ModelInfo{}
	for _, m := range list {
		// T.log.Info(fmt.Sprintf("Get model: %v", *m))
		modelList[int(m.ModelId)] = m
	}
	T.rwLock.Lock()
	T.modelUpdate.models = modelList
	T.modelUpdate.updateTime = time.Now()
	T.rwLock.Unlock()
	return modelList, nil
}

func (T *topaiManage) getBestModel(ctx context.Context, modelType string, modelId int) (*util.ModelInfo, error) {
	// 先简单一点，只获取第一个
	allModels, err := T.getAllModelInfo(ctx)
	if err != nil {
		return nil, err
	}
	modelInfo, ok := allModels[modelId]
	if !ok || modelInfo.ModelType != modelType {
		return nil, fmt.Errorf("not found model: %s-%d", modelType, modelId)
	}
	return modelInfo, nil
}

func (T *topaiManage) getAiSession(ctx context.Context, modelType string, modelId int, isStream bool) (string, error) {
	// 先初始化client
	err := T.initClient(ctx)
	if err != nil {
		return "", err
	}

	reqKey := fmt.Sprintf("%s-%d", modelType, modelId)
	for {
		T.rwLock.RLock()
		// 如果当前正在获取，则等待
		if T.aiSessionReq[reqKey] {
			T.rwLock.RUnlock()
			T.log.Info(fmt.Sprintf("模型(%s)正在获取session，等待10s", reqKey))
			time.Sleep(10 * time.Second)
			continue
		}
		T.rwLock.RUnlock()
		break
	}

	aiSessions, ok := T.aiSession[modelType]
	if ok && len(aiSessions) > 0 {
		//todo 先返回第一个，后需要不要改成随机在看
		// 需要测试session是否可用
		if modelId > 0 {
			for _, s := range aiSessions {
				if modelId == s.modelId && s.isStream == isStream {
					return s.session, nil
				}
			}
		} else {
			for _, s := range aiSessions {
				if s.isStream == isStream {
					return s.session, nil
				}
			}
		}
	}

	// session不存在，创建一个
	T.rwLock.Lock()
	T.aiSessionReq[reqKey] = true
	T.rwLock.Unlock()

	defer func() {
		T.rwLock.Lock()
		delete(T.aiSessionReq, reqKey)
		T.rwLock.Unlock()
	}()

	bestModel, err := T.getBestModel(ctx, modelType, modelId)
	if err != nil {
		return "", err
	}

	if bestModel == nil {
		return "", errors.New("Not found model ")
	}
	T.log.Info(fmt.Sprintf("选择的模型 %s：%v", modelType, *bestModel))

	// 开始创建session
	var protocolClient sdk.ProtocolClientInterface
	if modelType == repository.RemoterModelTypeASR.String() {
		// Create whisper session
		protocolClient = T.client.NewWhisperClient()
	} else if modelType == repository.RemoterModelTypeLLM.String() {
		// Create openai session
		protocolClient = T.client.NewOpenAIClient()
	} else if modelType == repository.RemoterModelTypeTTS.String() {
		// Create tts session
		protocolClient = T.client.NewTTSClient()
	} else if modelType == repository.RemoterModelTypeTTI.String() {
		// Create text to image session
		protocolClient = T.client.NewTextToImageClient()
	} else {
		return "", fmt.Errorf("unknown model type: %s", modelType)
	}

	// 此处会有阻塞，改成channel
	ch := make(chan string, 1)
	errCh := make(chan error, 1)
	go func() {
		defer close(ch)
		defer close(errCh)
		aiSession, err := protocolClient.CreateSession(
			ctx,
			T.walletAddr,
			"",
			bestModel.ModelName,
			fmt.Sprintf("%d", bestModel.ModelId),
			bestModel.ModelVersion,
			util.ResourceRequirements{ // todo 应该怎么获取
				CPU:    1,
				Memory: 1024,
				GPU:    1,
				Net:    1000,
				Disk:   1000,
			},
			T.debug, // set debug mode
		)
		if err != nil {
			T.log.Error(fmt.Sprintf("create session failed: %v", err))
			select {
			case errCh <- err:
			case <-ctx.Done():
			case <-T.mainCtx.Done():
			}
			return
		}
		select {
		case ch <- aiSession:
		case <-ctx.Done():
		case <-T.mainCtx.Done():
		}
	}()

	var aiSession string
	select {
	case aiSession = <-ch:
	case err = <-errCh:
	case <-ctx.Done():
		err = context.Canceled
	case <-T.mainCtx.Done():
		err = context.Canceled
	}

	if err != nil {
		T.log.Error(fmt.Sprintf("Create session(%s) failed: %v", modelType, err))
		return "", err
	}
	T.log.Info(fmt.Sprintf("Generate new session for %s(modelId:%d): %s", modelType, modelId, aiSession))
	T.addNewSession(ctx, aiSession, bestModel, isStream)
	return aiSession, nil
}

func (T *topaiManage) ChatMock(ctx context.Context, modelType string, msgs []openai.ChatCompletionMessage) (<-chan string, <-chan error, error) {
	respCh := make(chan string)
	errCh := make(chan error, 1)
	//todo mock 数据

	go func() {
		defer close(respCh)
		defer close(errCh)

		now := time.Now().Format("2006-01-02 15:04:05")
		respArr := []string{
			now,
			"这是",
			"回复",
			"信息",
			"end",
		}

		for _, r := range respArr {
			respCh <- r
			time.Sleep(50 * time.Millisecond)
		}
	}()

	return respCh, errCh, nil
}

func (T *topaiManage) ChatLLMStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (<-chan *openai.ChatCompletionStreamResponse, <-chan error, error) {
	modelType := repository.RemoterModelTypeLLM.String()

	openaiClient := T.client.NewOpenAIClient()

	respCh := make(chan *openai.ChatCompletionStreamResponse, 1024)
	errCh := make(chan error, 1)

	go func() {
		defer close(respCh)
		defer close(errCh)
		// 如果session 不存在，则重新创建一次
		session, err := T.getAiSession(ctx, modelType, remoterModel.ProviderModelId, true)
		if err != nil {
			errCh <- err
			return
		}
		if T.client.GetStreamMode(session) {
			err := openaiClient.StreamChatCompletionsWithCallback(
				ctx,
				session,
				req.Messages,
				func(response openai.ChatCompletionStreamResponse) {
					T.log.Info(fmt.Sprintf("Receive data by stream：%s", response.Choices[0].Delta.Content))
					respCh <- &response
				},
			)
			if err != nil {
				T.log.Error(fmt.Sprintf("Stream chat failed: %v", err))
				// todo 临时处理，close
				T.deleteSession(ctx, session, modelType)
				errCh <- err
			}
		} else {
			errCh <- fmt.Errorf("session is not stream mode")
			return
		}
	}()
	return respCh, errCh, nil
}

func (T *topaiManage) ChatLLMNonStream(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ChatCompletionRequest) (*openai.ChatCompletionResponse, error) {
	modelType := repository.RemoterModelTypeLLM.String()
	openaiClient := T.client.NewOpenAIClient()
	session, err := T.getAiSession(ctx, modelType, remoterModel.ProviderModelId, false)
	if err != nil {
		return nil, err
	}
	if T.client.GetStreamMode(session) {
		return nil, fmt.Errorf("session is stream mode")
	}
	resp, err := openaiClient.ChatCompletions(ctx, session, req.Messages)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

func (T *topaiManage) ChatASR(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.AudioRequest) (string, error) {
	modelType := repository.RemoterModelTypeASR.String()
	var transcribeResponse *sdk.TranscribeResponse
	session, err := T.getAiSession(ctx, modelType, remoterModel.ProviderModelId, false)
	if err != nil {
		return "", err
	}
	audioFilePath := req.FilePath
	if audioFilePath == "" && req.Reader != nil {
		audioFilePath = os.TempDir() + "/" + uuid.New().String()
		audioFile, err := os.Create(audioFilePath)
		if err != nil {
			return "", err
		}
		_, err = io.Copy(audioFile, req.Reader)
		if err != nil {
			return "", err
		}
		audioFile.Close()
		defer os.Remove(audioFilePath)
	} else if audioFilePath == "" {
		return "", errors.New("file or file path is required")
	}
	whisperClient := T.client.NewWhisperClient()
	transcribeResponse, err = whisperClient.TranscribeByAudioFile(
		ctx,
		session,
		true,
		false,
		false,
		"json",
		"en",
		"",
		audioFilePath,
		T.debug,
	)

	if err != nil {
		T.deleteSession(ctx, session, modelType)
		return "", err
	}

	return transcribeResponse.Text, nil
}

func (T *topaiManage) ChatTTS(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.CreateSpeechRequest) ([]byte, error) {
	modelType := repository.RemoterModelTypeTTS.String()
	var res []byte
	session, err := T.getAiSession(ctx, modelType, remoterModel.ProviderModelId, false)
	if err != nil {
		return nil, err
	}
	ttsClient := T.client.NewTTSClient()
	res, err = ttsClient.TTS(ctx, session, req.Input, 697, 0.3, 0.7, 20, string(req.ResponseFormat))
	if err != nil {
		T.deleteSession(ctx, session, modelType)
		return nil, err
	}
	return res, nil
}

func (T *topaiManage) ChatTTI(ctx context.Context, remoterModel *repository.RemoterModelInfo, req *openai.ImageRequest) ([]byte, error) {
	modelType := repository.RemoterModelTypeTTI.String()
	var res []byte
	session, err := T.getAiSession(ctx, modelType, remoterModel.ProviderModelId, false)
	if err != nil {
		return nil, err
	}
	textToImageClient := T.client.NewTextToImageClient()
	res, err = textToImageClient.TextToImage(ctx, session, req.Prompt, 1024, string(req.ResponseFormat))
	if err != nil {
		T.deleteSession(ctx, session, modelType)
		return nil, err
	}
	return res, nil
}

func (T *topaiManage) close() {
	<-T.mainCtx.Done()
	T.log.Warn("开始退出，关闭所有的ai session")
	T.rwLock.Lock()
	defer T.rwLock.Unlock()
	wg := new(sync.WaitGroup)
	for _, list := range T.aiSession {
		for _, s := range list {
			wg.Add(1)
			go func(s *aiSession) {
				defer wg.Done()
				T.log.Info(fmt.Sprintf("start close session: %v", *s))
				err := T.client.CloseSession(context.TODO(), s.session, func(sessionId string, client sdk.ClientInterface) error {
					T.log.Info(fmt.Sprintf("Closed session: %s", sessionId))
					return nil
				})
				if err != nil {
					T.log.Error(fmt.Sprintf("Close session(%v) failed:%v", *s, err))
				}
			}(s)

		}
	}
	wg.Wait()
	T.log.Warn("所有的ai session 关闭完成")
}
