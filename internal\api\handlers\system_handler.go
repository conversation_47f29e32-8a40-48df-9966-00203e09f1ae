package handlers

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/api/middleware"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type SystemHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewSystemHandler(service *service.Api) *SystemHandler {
	return &SystemHandler{
		service: service,
		log:     logger.GetLogger("system_handler"),
	}
}

// HealthCheck 健康检查
func (h *SystemHandler) HealthCheck(c *gin.Context) {
	api.Success(c, nil)
}

// GetConcurrencyStats 获取并发限制统计信息
// @Summary 获取并发限制统计信息
// @Description 获取当前并发限制器的统计信息
// @Tags 系统
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} string
// @Router /api/v1/system/concurrency/stats [get]
func (h *SystemHandler) GetConcurrencyStats(c *gin.Context) {
	limiter := middleware.GetConcurrencyLimiter()
	if limiter == nil {
		api.Fail(c, api.CodeServerError, "concurrency limiter not initialized", nil)
		return
	}

	stats := limiter.GetStats()
	api.Success(c, stats)
}

// GetUpdates 版本更新
// @Summary 获取版本更新
// @Description 获取版本差异
// @Tags 版本
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/version/updates [get]
func (h *SystemHandler) GetUpdates(c *gin.Context) {
	api.Success(c, gin.H{
		"current": "0.0.1",
		"latest":  "0.0.1",
	})
}

// GetBaseConfig 获取配置
// @Summary 获取配置
// @Description 获取配置
// @Tags 配置
// @Produce json
// @Success 200 {object} service.SystemCommonConfig
// @Failure 400 {object} string
// @Router /api/config [get]
func (h *SystemHandler) GetBaseConfig(c *gin.Context) {
	conf, err := h.service.Config.GetBaseConfig(c.Request.Context())
	if err != nil {
		h.log.Error("failed to get base config", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get base config", err)
		return
	}
	api.Success(c, conf)
}

// GetChangelog 获取更新日志
func (h *SystemHandler) GetChangelog(c *gin.Context) {
	// 直接返回空
	api.Success(c, map[string]interface{}{})
}

func (h *SystemHandler) Close() {
	// 清理资源
}
