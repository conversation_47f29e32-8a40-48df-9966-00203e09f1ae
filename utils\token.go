package utils

import (
	"bytes"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/go-audio/wav"
	"github.com/pkoukk/tiktoken-go"
)

func defaultGpt4Token(content string) int {
	encoding, _ := tiktoken.EncodingForModel("gpt-4")
	tokens := encoding.Encode(content, nil, nil)
	return len(tokens)
}

// TTI 都是按照输出的图片定价
// inputToken 按照utf8字符数量返回
func GetTTIInputTokens(provider, input string) int {
	if provider == "deepinfra" {
		// 按照输出的图片定价(张/像素)
		// 返回字符数量
		return utf8.RuneCountInString(input)
	}

	// 都在不在范围内，按照gpt-4的token计算
	return defaultGpt4Token(input)
}

// 以 1024/1024 为单位，每个单位 算作50Token
func GetTTIOutputTokens(provider, outputSize string) int {
	sizeArr := strings.Split(outputSize, "x")
	if len(sizeArr) != 2 {
		return 0
	}
	width, err := strconv.Atoi(sizeArr[0])
	if err != nil {
		return 0
	}
	height, err := strconv.Atoi(sizeArr[1])
	if err != nil {
		return 0
	}

	return (width * height) * 50 / 1024 / 1024
}

func GetTTSInputTokens(provider, input string) int {
	if provider == "deepinfra" {
		// 按照输入的字符数量定价
		// 返回字符数量
		return utf8.RuneCountInString(input)
	} else if provider == "siliconflow" {
		// 按照输入的字节数量定价
		return len([]byte(input))
	}

	// 都在不在范围内，按照gpt-4的token计算
	return defaultGpt4Token(input)
}

func GetTTSOutputTokens(provider string, output []byte) int {
	// 按照分钟，每分钟按照100个token
	reader := bytes.NewReader(output)
	decoder := wav.NewDecoder(reader)
	if !decoder.IsValidFile() {
		return 0
	}

	decoder.ReadInfo()
	duration, _ := decoder.Duration()
	ms := duration.Milliseconds()
	return int(ms*100) / 60000
}

func GetASRInputTokens(provider string, input []byte) int {
	// 按照分钟，每分钟按照100个token
	reader := bytes.NewReader(input)
	decoder := wav.NewDecoder(reader)
	if !decoder.IsValidFile() {
		return 0
	}

	decoder.ReadInfo()
	duration, _ := decoder.Duration()
	ms := duration.Milliseconds()
	return int(ms*100) / 60000
}

func GetASROutputTokens(provider, input string) int {
	return defaultGpt4Token(input)
}
