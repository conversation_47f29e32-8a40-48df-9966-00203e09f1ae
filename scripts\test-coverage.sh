#!/bin/bash

# TopAI Chat Server 测试覆盖率检查脚本
# 遵循 golang-testing-standards 要求

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}================================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}================================================${NC}\n"
}

# 设置覆盖率目标
TARGET_COVERAGE=80
CORE_MODULE_TARGET=90
UTILITY_TARGET=70

# 创建覆盖率输出目录
COVERAGE_DIR="coverage"
mkdir -p $COVERAGE_DIR

print_header "TopAI Chat Server - 测试覆盖率检查"

print_info "目标覆盖率: ${TARGET_COVERAGE}%"
print_info "核心模块目标: ${CORE_MODULE_TARGET}%"
print_info "工具模块目标: ${UTILITY_TARGET}%"

# 1. 检查已创建的测试文件
print_header "1. 测试文件清单"

echo "✅ 已创建的测试文件:"
find . -name "*_test.go" -not -path "./vendor/*" | sort | while read file; do
    lines=$(wc -l < "$file")
    echo "   📄 $file ($lines 行)"
done

echo ""
echo "📊 测试文件统计:"
test_files=$(find . -name "*_test.go" -not -path "./vendor/*" | wc -l)
echo "   总测试文件数: $test_files"

source_files=$(find . -name "*.go" -not -name "*_test.go" -not -path "./vendor/*" -not -path "./docs/*" | wc -l)
echo "   源代码文件数: $source_files"

coverage_ratio=$(echo "scale=1; $test_files * 100 / $source_files" | bc)
echo "   测试覆盖比例: ${coverage_ratio}% (文件数量)"

# 2. 按模块分析测试覆盖情况
print_header "2. 模块测试覆盖分析"

analyze_module() {
    local module_path=$1
    local module_name=$2
    local target_coverage=$3
    
    if [ ! -d "$module_path" ]; then
        print_warning "模块 $module_name 不存在: $module_path"
        return
    fi
    
    echo "🔍 分析模块: $module_name"
    
    # 统计源文件
    source_count=$(find "$module_path" -name "*.go" -not -name "*_test.go" | wc -l)
    
    # 统计测试文件
    test_count=$(find "$module_path" -name "*_test.go" | wc -l)
    
    echo "   📄 源文件: $source_count"
    echo "   🧪 测试文件: $test_count"
    
    if [ $source_count -eq 0 ]; then
        echo "   ⚠️  无源代码文件"
        return
    fi
    
    if [ $test_count -eq 0 ]; then
        echo "   ❌ 无测试文件 (目标: ${target_coverage}%)"
    else
        file_coverage=$(echo "scale=1; $test_count * 100 / $source_count" | bc)
        echo "   📊 文件覆盖率: ${file_coverage}%"
        
        if (( $(echo "$file_coverage >= $target_coverage" | bc -l) )); then
            echo "   ✅ 达到目标覆盖率 (${target_coverage}%)"
        else
            echo "   ❌ 未达到目标覆盖率 (${target_coverage}%)"
        fi
    fi
    echo ""
}

# 分析核心模块 (目标90%+)
print_info "🎯 P0级别 - 核心业务逻辑 (目标${CORE_MODULE_TARGET}%+)"
analyze_module "internal/service" "Service层" $CORE_MODULE_TARGET
analyze_module "internal/api/handlers" "API处理器" $CORE_MODULE_TARGET
analyze_module "internal/repository/mysql" "数据仓库" $CORE_MODULE_TARGET

# 分析重要模块 (目标85%+)
print_info "🟡 P1级别 - 重要功能模块 (目标85%+)"
analyze_module "internal/api/middleware" "中间件" 85
analyze_module "config" "配置管理" 85

# 分析支撑模块 (目标70%+)
print_info "🟢 P2级别 - 支撑模块 (目标${UTILITY_TARGET}%+)"
analyze_module "logger" "日志系统" $UTILITY_TARGET
analyze_module "utils" "工具函数" $UTILITY_TARGET

# 3. 运行测试并生成覆盖率报告
print_header "3. 执行测试并生成覆盖率报告"

print_info "正在运行所有测试..."

# 检查测试依赖
if ! go list -m github.com/stretchr/testify >/dev/null 2>&1; then
    print_warning "缺少testify依赖，正在安装..."
    go mod edit -require github.com/stretchr/testify@v1.8.4
    go mod tidy
fi

# 运行测试并生成覆盖率
go test -v -race -coverprofile=${COVERAGE_DIR}/coverage.out -covermode=atomic ./... 2>&1 | tee ${COVERAGE_DIR}/test_output.txt

if [ $? -eq 0 ]; then
    print_success "所有测试通过"
else
    print_error "部分测试失败"
    echo "详细日志保存在: ${COVERAGE_DIR}/test_output.txt"
fi

# 4. 分析覆盖率结果
print_header "4. 覆盖率分析结果"

if [ -f "${COVERAGE_DIR}/coverage.out" ]; then
    print_info "生成覆盖率报告..."
    
    # 生成HTML报告
    go tool cover -html=${COVERAGE_DIR}/coverage.out -o ${COVERAGE_DIR}/coverage.html
    print_success "HTML报告已生成: ${COVERAGE_DIR}/coverage.html"
    
    # 计算总体覆盖率
    total_coverage=$(go tool cover -func=${COVERAGE_DIR}/coverage.out | grep total | awk '{print $3}' | sed 's/%//')
    
    echo ""
    echo "📊 总体覆盖率: ${total_coverage}%"
    
    if (( $(echo "$total_coverage >= $TARGET_COVERAGE" | bc -l) )); then
        print_success "✅ 达到目标覆盖率 (${TARGET_COVERAGE}%)"
    else
        print_warning "❌ 未达到目标覆盖率 (${TARGET_COVERAGE}%)"
        needed=$(echo "$TARGET_COVERAGE - $total_coverage" | bc)
        print_info "还需提高 ${needed}% 覆盖率"
    fi
    
    # 生成详细报告
    echo ""
    print_info "按文件显示覆盖率:"
    go tool cover -func=${COVERAGE_DIR}/coverage.out | head -20
    
    echo ""
    print_info "低覆盖率文件 (<${TARGET_COVERAGE}%):"
    go tool cover -func=${COVERAGE_DIR}/coverage.out | awk -v target=$TARGET_COVERAGE '
    {
        if (NF >= 3 && $3 != "total:" && $3 != "statements") {
            coverage = $3
            gsub(/%/, "", coverage)
            if (coverage < target) {
                printf "   ❌ %s: %s\n", $1, $3
            }
        }
    }'
    
else
    print_error "覆盖率文件未生成"
fi

# 5. 生成Markdown报告
print_header "5. 生成测试报告"

REPORT_FILE="${COVERAGE_DIR}/test_report.md"

cat > $REPORT_FILE << EOF
# TopAI Chat Server - 测试覆盖率报告

生成时间: $(date)

## 📊 覆盖率总览

| 指标 | 当前值 | 目标值 | 状态 |
|------|--------|--------|------|
| 总体覆盖率 | ${total_coverage:-0}% | ${TARGET_COVERAGE}% | $([ $(echo "${total_coverage:-0} >= $TARGET_COVERAGE" | bc) -eq 1 ] && echo "✅ 达标" || echo "❌ 未达标") |
| 测试文件数 | $test_files | - | - |
| 源文件数 | $source_files | - | - |

## 🎯 模块覆盖率分析

### P0级别 - 核心业务逻辑 (目标${CORE_MODULE_TARGET}%+)
- Service层: 需要完善
- API处理器: 需要完善  
- 数据仓库: 部分完成

### P1级别 - 重要功能模块 (目标85%+)
- 中间件: 部分完成
- 配置管理: 部分完成

### P2级别 - 支撑模块 (目标${UTILITY_TARGET}%+)
- 日志系统: 已完成
- 工具函数: 需要完善

## 📋 已创建的测试文件

EOF

find . -name "*_test.go" -not -path "./vendor/*" | sort | while read file; do
    lines=$(wc -l < "$file")
    echo "- \`$file\` ($lines 行)" >> $REPORT_FILE
done

cat >> $REPORT_FILE << EOF

## 🔄 下一步计划

1. **完善核心模块测试**
   - 用户服务完整测试
   - 认证处理器测试
   - 对话服务测试

2. **提高覆盖率**
   - 目标达到 ${TARGET_COVERAGE}% 总体覆盖率
   - 核心模块达到 ${CORE_MODULE_TARGET}% 覆盖率

3. **质量改进**
   - 增加边界条件测试
   - 完善错误处理测试
   - 添加性能基准测试

## 📁 文件位置

- 覆盖率报告: \`${COVERAGE_DIR}/coverage.html\`
- 测试日志: \`${COVERAGE_DIR}/test_output.txt\`
- 覆盖率数据: \`${COVERAGE_DIR}/coverage.out\`

EOF

print_success "测试报告已生成: $REPORT_FILE"

# 6. 总结
print_header "6. 总结"

if [ -f "${COVERAGE_DIR}/coverage.out" ]; then
    if (( $(echo "${total_coverage:-0} >= $TARGET_COVERAGE" | bc -l) )); then
        print_success "🎉 恭喜！已达到${TARGET_COVERAGE}%测试覆盖率目标"
    else
        print_warning "🎯 继续努力，距离${TARGET_COVERAGE}%目标还有$(echo "$TARGET_COVERAGE - ${total_coverage:-0}" | bc)%"
    fi
else
    print_info "🚀 测试框架已建立，请继续完善测试用例"
fi

echo ""
print_info "🔧 建议的下一步操作:"
echo "   1. 查看HTML覆盖率报告: open ${COVERAGE_DIR}/coverage.html"
echo "   2. 查看详细测试日志: cat ${COVERAGE_DIR}/test_output.txt"
echo "   3. 查看测试报告: cat $REPORT_FILE"
echo "   4. 运行特定模块测试: go test -v ./internal/service/..."

echo ""
print_success "测试覆盖率检查完成！" 