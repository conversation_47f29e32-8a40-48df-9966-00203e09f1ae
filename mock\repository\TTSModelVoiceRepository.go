// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TTSModelVoiceRepository is an autogenerated mock type for the TTSModelVoiceRepository type
type TTSModelVoiceRepository struct {
	mock.Mock
}

// GetByModelName provides a mock function with given fields: ctx, modelName
func (_m *TTSModelVoiceRepository) GetByModelName(ctx context.Context, modelName string) ([]*repository.TTSModelVoice, error) {
	ret := _m.Called(ctx, modelName)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelName")
	}

	var r0 []*repository.TTSModelVoice
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*repository.TTSModelVoice, error)); ok {
		return rf(ctx, modelName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*repository.TTSModelVoice); ok {
		r0 = rf(ctx, modelName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TTSModelVoice)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByModelNameAndName provides a mock function with given fields: ctx, modelName, name
func (_m *TTSModelVoiceRepository) GetByModelNameAndName(ctx context.Context, modelName string, name string) (*repository.TTSModelVoice, error) {
	ret := _m.Called(ctx, modelName, name)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelNameAndName")
	}

	var r0 *repository.TTSModelVoice
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*repository.TTSModelVoice, error)); ok {
		return rf(ctx, modelName, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *repository.TTSModelVoice); ok {
		r0 = rf(ctx, modelName, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TTSModelVoice)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, modelName, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTTSModelVoiceRepository creates a new instance of TTSModelVoiceRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTTSModelVoiceRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TTSModelVoiceRepository {
	mock := &TTSModelVoiceRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
