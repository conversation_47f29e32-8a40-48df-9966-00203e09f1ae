package handlers

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type AudioHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewAudioHandler(service *service.Api) *AudioHandler {
	return &AudioHandler{
		service: service,
		log:     logger.GetLogger("audio_handler"),
	}
}

// GetAudioConfig 获取音频配置
// @Summary 获取音频配置
// @Description 语音管理
// @Accept json
// @Produce json
// @Success 200 {object} service.AudioConfig
// @Failure 500 {object} string
// @Router /api/v1/audio/config [get]
func (h *AudioHandler) GetAudioConfig(ctx *gin.Context) {
	config, err := h.service.Audio.GetAudioConfig(ctx)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Get audio config failed", err)
		return
	}
	api.Success(ctx, config)
}

// UpdateAudioConfig 更新音频配置
// @Summary 更新音频配置
// @Description 语音管理
// @Accept json
// @Produce json
// @Param config body service.AudioConfig true "音频配置"
// @Success 200 {object} service.AudioConfig
// @Failure 500 {object} string
// @Router /api/v1/audio/config [post]
func (h *AudioHandler) UpdateAudioConfig(ctx *gin.Context) {
	var config service.AudioConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		api.Fail(ctx, api.CodeServerError, "Update audio config failed", err)
		return
	}
	err := h.service.Audio.UpdateAudioConfig(ctx, &config)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Update audio config failed", err)
		return
	}
	api.Success(ctx, config)
}

// GetAudioVoices 获取音频声音
// @Summary 获取音频声音
// @Description 语音管理
// @Accept json
// @Produce json
// @Success 200 {object} []service.AudioVoice
// @Failure 500 {object} string
// @Router /api/v1/audio/voices [get]
func (h *AudioHandler) GetAudioVoices(ctx *gin.Context) {
	voices, err := h.service.Audio.GetAudioVoices(ctx)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Get audio voices failed", err)
		return
	}
	api.Success(ctx, voices)
}

// GetAudioBaseModels 获取音频基础模型
// @Summary 获取音频基础模型
// @Description 语音管理
// @Accept json
// @Produce json
// @Success 200 {object} service.AudioBaseModel
// @Failure 500 {object} string
// @Router /api/v1/audio/models [get]
func (h *AudioHandler) GetAudioBaseModels(ctx *gin.Context) {
	models, err := h.service.Audio.GetAudioBaseModels(ctx)
	if err != nil {
		h.log.Error("Get audio models failed", zap.Error(err))
		api.Fail(ctx, api.CodeServerError, "Get audio models failed", err)
		return
	}
	api.Success(ctx, models)
}

// CreateSpeech 创建语音
// @Summary 创建语音
// @Description 语音管理
// @Accept json
// @Produce json
// @Success 200 {object} []byte
// @Failure 500 {object} string
// @Router /api/v1/audio/speech/{chat_id}/{msg_id} [post]
func (h *AudioHandler) CreateSpeech(c *gin.Context) {
	chatId := c.Param("chat_id")
	msgId := c.Param("msg_id")

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	audioBytes, err := h.service.Model.CreateSpeech(c.Request.Context(), userOP, chatId, msgId)
	if err != nil {
		h.log.Error("Create speech failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "Create speech failed", err)
		return
	}
	c.Data(200, "audio/wav", audioBytes)
}

func (h *AudioHandler) Close() {

}
