package logger

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"topnetwork.ai/topai/chat-webserver/config"
)

// TestEnv 测试环境结构
type TestEnv struct {
	t       testing.TB
	tempDir string
	logFile string
	config  *config.LogConfig
	logger  *ModuleLogger
}

// ResetGlobalState 重置全局状态，主要用于测试
func ResetGlobalState() {
	registry = sync.Map{}
	logConfig = nil
	logWriter = nil
}

// NewTestEnv 创建新的测试环境
func NewTestEnv(t testing.TB) *TestEnv {
	// 重置全局状态
	ResetGlobalState()

	// 注册清理函数
	t.Cleanup(func() {
		ResetGlobalState()
	})

	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	logConfig := &config.LogConfig{
		Level:      "debug",
		Path:       logFile,
		MaxSize:    1,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
		Console:    false,
	}

	return &TestEnv{
		t:       t,
		tempDir: tempDir,
		logFile: logFile,
		config:  logConfig,
	}
}

// Init 初始化测试环境的日志系统
func (e *TestEnv) Init() {
	// 确保日志目录存在
	err := os.MkdirAll(filepath.Dir(e.logFile), 0755)
	require.NoError(e.t, err, "创建日志目录失败")

	// 初始化日志系统
	InitLogger(e.config)
	e.logger = GetLogger("test")
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	os.RemoveAll(e.tempDir)
}

// TestMain 测试入口点
func TestMain(m *testing.M) {
	// 运行测试
	code := m.Run()

	// 检查覆盖率
	if os.Getenv("CI") != "" {
		// 在 CI 环境中验证覆盖率
		// testutil.VerifyCoverage(m, 90)
	}

	os.Exit(code)
}

func TestLoggerInitialization(t *testing.T) {
	tests := []struct {
		name     string
		setup    func(*testing.T) *TestEnv
		wantErr  bool
		validate func(*testing.T, *TestEnv)
	}{
		{
			name: "有效配置初始化",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				InitLogger(env.config)
				env.logger = GetLogger("test")
				return env
			},
			wantErr: false,
			validate: func(t *testing.T, env *TestEnv) {
				assert.NotNil(t, env.logger)
				env.logger.Info("test message")
				time.Sleep(100 * time.Millisecond)
				assert.FileExists(t, env.logFile)
			},
		},
		{
			name: "无效的日志路径",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				env.config.Path = "/invalid/path/test.log"
				return env
			},
			wantErr: true,
		},
		{
			name: "无效日志级别默认使用INFO",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				env.config.Level = "invalid_level"
				InitLogger(env.config)
				env.logger = GetLogger("test")
				return env
			},
			wantErr: false,
			validate: func(t *testing.T, env *TestEnv) {
				assert.NotNil(t, env.logger)
				env.logger.Info("test message")
				time.Sleep(100 * time.Millisecond)
				assert.FileExists(t, env.logFile)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			env := tt.setup(t)
			defer env.Cleanup()

			if tt.wantErr {
				assert.Panics(t, func() {
					InitLogger(env.config)
				})
			} else {
				if tt.validate != nil {
					tt.validate(t, env)
				}
			}
		})
	}
}

func TestLoggerLevels(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	tests := []struct {
		name     string
		level    string
		message  string
		fields   []zap.Field
		validate func(*testing.T, string)
	}{
		{
			name:    "Debug级别日志",
			level:   "debug",
			message: "debug message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "debug message")
				assert.Contains(t, content, "key")
				assert.Contains(t, content, "value")
			},
		},
		{
			name:    "Info级别日志",
			level:   "info",
			message: "info message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "info message")
			},
		},
		{
			name:    "Warn级别日志",
			level:   "warn",
			message: "warn message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "warn message")
			},
		},
		{
			name:    "Error级别日志",
			level:   "error",
			message: "error message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "error message")
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			// 设置日志级别
			env.config.Level = tt.level
			InitLogger(env.config)
			log := GetLogger("test")

			// 写入日志
			switch tt.level {
			case "debug":
				log.Debug(tt.message, tt.fields...)
			case "info":
				log.Info(tt.message, tt.fields...)
			case "warn":
				log.Warn(tt.message, tt.fields...)
			case "error":
				log.Error(tt.message, tt.fields...)
			}

			// 等待日志写入
			time.Sleep(100 * time.Millisecond)

			// 读取日志文件
			content, err := os.ReadFile(env.logFile)
			require.NoError(t, err)

			// 验证日志内容
			if tt.validate != nil {
				tt.validate(t, string(content))
			}
		})
	}
}

func TestLoggerRotation(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 设置较小的文件大小以触发轮转
	env.config.MaxSize = 1 // 1MB
	InitLogger(env.config)
	log := GetLogger("test")

	// 写入大量日志以触发轮转
	for i := 0; i < 100000; i++ { // 增加写入次数
		log.Info("test message", zap.Int("index", i))
	}

	// 等待日志写入
	time.Sleep(100 * time.Millisecond) // 增加等待时间

	// 检查是否生成了备份文件
	files, err := filepath.Glob(filepath.Join(env.tempDir, "test*.log*"))
	require.NoError(t, err)
	assert.Greater(t, len(files), 1, "应该生成了多个日志文件")
}

func TestLoggerWithFields(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")
	fields := []zap.Field{
		zap.String("string_field", "value"),
		zap.Int("int_field", 123),
		zap.Bool("bool_field", true),
		zap.Time("time_field", time.Now()),
	}

	log.Info("test message", fields...)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证字段是否正确记录
	assert.Contains(t, string(content), "string_field")
	assert.Contains(t, string(content), "value")
	assert.Contains(t, string(content), "int_field")
	assert.Contains(t, string(content), "123")
	assert.Contains(t, string(content), "bool_field")
	assert.Contains(t, string(content), "true")
}

func TestLoggerWithContext(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")
	ctx := []zap.Field{
		zap.String("request_id", "12345"),
		zap.String("user_id", "67890"),
	}

	log.WithContext(ctx...).Info("test message")

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证上下文字段是否正确记录
	assert.Contains(t, string(content), "request_id")
	assert.Contains(t, string(content), "12345")
	assert.Contains(t, string(content), "user_id")
	assert.Contains(t, string(content), "67890")
}

func TestLoggerSync(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")
	err := log.Sync()
	require.NoError(t, err)
}

func TestLoggerWithInvalidFields(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")
	fields := []zap.Field{
		zap.String("", "empty key"),
		zap.String("valid_key", "valid value"),
	}

	log.Info("test message", fields...)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证字段是否正确记录
	assert.Contains(t, string(content), "valid_key")
	assert.Contains(t, string(content), "valid value")
}

func TestLoggerWithEmptyFields(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")
	log.Info("test message")

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证消息是否正确记录
	assert.Contains(t, string(content), "test message")
}

func BenchmarkLogger(b *testing.B) {
	env := NewTestEnv(b)
	env.Init()
	defer env.Cleanup()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		env.logger.Info("benchmark message",
			zap.Int("index", i),
			zap.Time("timestamp", time.Now()),
		)
	}
}

// ExampleLogger 示例测试
func ExampleLogger() {
	// 初始化日志配置
	logConfig := &config.LogConfig{
		Level:      "info",
		Path:       "logs/app.log",
		MaxSize:    100,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
		Console:    true,
	}

	InitLogger(logConfig)

	// 获取日志器
	logger := GetLogger("example")

	// 记录日志
	logger.Info("example message",
		zap.String("key", "value"),
		zap.Int("count", 42),
	)

	// 使用正则表达式匹配输出
	// 输出格式：时间戳 + 日志级别 + 文件位置 + 消息 + 字段
	// 例如：2025-04-25T11:53:11.694+0800    info    logger/logger_test.go:231       example message {"key": "value", "count": 42}
}

func TestLoggerWithCustomName(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	// 测试自定义模块名
	log := GetLogger("custom_module")
	log.Info("test message")

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证模块名是否正确记录
	assert.Contains(t, string(content), "custom_module")
}

func TestLoggerWithAggregator(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	// 创建测试聚合器
	aggregator := &testAggregator{
		entries: make([]*LogEntry, 0),
	}

	// 设置聚合器
	log := GetLogger("test")
	log.SetAggregator(aggregator)

	// 写入日志
	log.Info("test message", zap.String("key", "value"))

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 验证聚合器是否收到日志
	assert.Equal(t, 1, len(aggregator.entries))
	assert.Equal(t, "test message", aggregator.entries[0].Message)
}

// testAggregator 测试用日志聚合器
type testAggregator struct {
	entries []*LogEntry
}

func (a *testAggregator) Send(entry *LogEntry) error {
	a.entries = append(a.entries, entry)
	return nil
}

func TestLoggerWithSensitiveFields(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")

	// 添加敏感字段
	log.AddSensitiveField("password")
	log.AddSensitiveField("token")

	// 写入包含敏感数据的日志
	log.Info("test message",
		zap.String("password", "secret123"),
		zap.String("token", "sensitive_token"),
		zap.String("normal", "visible"),
	)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证敏感数据是否被屏蔽
	assert.Contains(t, string(content), "password")
	assert.Contains(t, string(content), "token")
	assert.Contains(t, string(content), "normal")
	assert.Contains(t, string(content), "visible")
}

func TestLoggerWithLevelChange(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")

	// 测试不同日志级别
	levels := []struct {
		level   zapcore.Level
		message string
	}{
		{zapcore.DebugLevel, "debug message"},
		{zapcore.InfoLevel, "info message"},
		{zapcore.WarnLevel, "warn message"},
		{zapcore.ErrorLevel, "error message"},
	}

	for _, l := range levels {
		// 设置日志级别
		log.SetLevel(l.level)

		// 写入日志
		switch l.level {
		case zapcore.DebugLevel:
			log.Debug(l.message)
		case zapcore.InfoLevel:
			log.Info(l.message)
		case zapcore.WarnLevel:
			log.Warn(l.message)
		case zapcore.ErrorLevel:
			log.Error(l.message)
		}

		// 等待日志写入
		time.Sleep(100 * time.Millisecond)

		// 读取日志文件
		content, err := os.ReadFile(env.logFile)
		require.NoError(t, err)

		// 验证日志是否正确记录
		assert.Contains(t, string(content), l.message)
	}
}

func TestLoggerFatal(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")

	// 设置日志级别为 Fatal
	log.SetLevel(zapcore.FatalLevel)

	// 创建一条 Fatal 级别的日志条目
	entry := zapcore.Entry{
		Level:   zapcore.FatalLevel,
		Message: "fatal message",
		Time:    time.Now(),
	}

	// 直接使用 zapcore.Core 写入日志
	core := log.logger.Core()
	err := core.Write(entry, nil)
	require.NoError(t, err)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 验证日志是否被正确记录
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)
	assert.Contains(t, string(content), "fatal message")
}

func TestLoggerWithCustomNameEncoder(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	// 测试不同格式的模块名
	moduleNames := []string{
		"simple",
		"with.dots",
		"with/slashes",
		"with spaces",
		"with@special#chars",
	}

	for _, name := range moduleNames {
		log := GetLogger(name)
		log.Info("test message")

		// 等待日志写入
		time.Sleep(100 * time.Millisecond)

		// 验证日志是否被正确记录
		content, err := os.ReadFile(env.logFile)
		require.NoError(t, err)
		assert.Contains(t, string(content), name)
	}
}

func TestLoggerWithAllLevels(t *testing.T) {
	env := NewTestEnv(t)
	env.Init()
	defer env.Cleanup()

	log := GetLogger("test")

	// 测试所有日志级别
	levels := []struct {
		name    string
		level   zapcore.Level
		message string
	}{
		{"Debug", zapcore.DebugLevel, "debug message"},
		{"Info", zapcore.InfoLevel, "info message"},
		{"Warn", zapcore.WarnLevel, "warn message"},
		{"Error", zapcore.ErrorLevel, "error message"},
		{"Fatal", zapcore.FatalLevel, "fatal message"},
	}

	for _, l := range levels {
		t.Run(l.name, func(t *testing.T) {
			// 设置日志级别
			log.SetLevel(l.level)

			// 创建日志条目
			entry := zapcore.Entry{
				Level:   l.level,
				Message: l.message,
				Time:    time.Now(),
			}

			// 写入日志
			core := log.logger.Core()
			err := core.Write(entry, nil)
			require.NoError(t, err)

			// 等待日志写入
			time.Sleep(100 * time.Millisecond)

			// 验证日志是否被正确记录
			content, err := os.ReadFile(env.logFile)
			require.NoError(t, err)
			assert.Contains(t, string(content), l.message)
		})
	}
}
