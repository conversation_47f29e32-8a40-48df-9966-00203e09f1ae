{"_format": "hh-sol-artifact-1", "contractName": "Settlement", "sourceName": "contracts/AIPay/Settlement.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_depositContractAddress", "type": "address"}, {"internalType": "address", "name": "_bankContractAddress", "type": "address"}, {"internalType": "address", "name": "_aimodelAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldContract", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newContract", "type": "address"}], "name": "DepositContractUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldInferenceTax", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newInferenceTax", "type": "uint256"}], "name": "InferenceTaxUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldTaxVault", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newTaxVault", "type": "address"}], "name": "TaxVaultUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "workload", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "address[]", "name": "worker", "type": "address[]"}, {"indexed": false, "internalType": "uint256", "name": "modelId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "sessionId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "epochId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "enough", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "isInputToken", "type": "bool"}], "name": "WorkloadDeducted", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "OPERATOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "aimodelContract", "outputs": [{"internalType": "contract AIModels", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bankContract", "outputs": [{"internalType": "contract Bank", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "workload", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "address[]", "name": "worker", "type": "address[]"}, {"internalType": "uint256", "name": "modelId", "type": "uint256"}, {"internalType": "uint256", "name": "sessionId", "type": "uint256"}, {"internalType": "uint256", "name": "epochId", "type": "uint256"}, {"internalType": "bool", "name": "isInputToken", "type": "bool"}], "name": "deductWorkload", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "depositContract", "outputs": [{"internalType": "contract Deposit", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "inferenceTax", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "taxVault", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newDepositContract", "type": "address"}], "name": "updateDepositContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_inferenceTax", "type": "uint256"}], "name": "updateInferenceTax", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_taxVault", "type": "address"}], "name": "updateTaxVault", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}