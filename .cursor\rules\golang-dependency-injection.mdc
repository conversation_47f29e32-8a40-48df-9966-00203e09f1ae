---
description: 
globs: 
alwaysApply: true
---
---
description: Dependency Injection Implementation Guidelines
globs: service/*.go, handlers/*.go
alwaysApply: true
---

When implementing dependency injection:

1. Constructor Injection
- Pass all dependencies through constructors
- Validate dependencies at construction time
- Provide meaningful error messages for missing dependencies
- Use functional options for optional dependencies

Required pattern:
```go
type Service struct {
    repo      Repository
    cache     Cache
    logger    Logger
    metrics   MetricsCollector
    options   Options
}

type Option func(*Options)

func NewService(
    repo Repository,
    cache Cache,
    logger Logger,
    metrics MetricsCollector,
    opts ...Option,
) (*Service, error) {
    if repo == nil {
        return nil, errors.New("repository is required")
    }
    
    options := DefaultOptions()
    for _, opt := range opts {
        opt(&options)
    }
    
    return &Service{
        repo:    repo,
        cache:   cache,
        logger:  logger,
        metrics: metrics,
        options: options,
    }, nil
}
```

2. Interface Design
- Define interfaces at the point of use
- Keep interfaces small and focused
- Provide mock implementations for testing
- Document interface contracts

Example interface:
```go
type Repository interface {
    Get(ctx context.Context, id string) (*Entity, error)
    List(ctx context.Context, opts ListOptions) ([]*Entity, error)
    Create(ctx context.Context, entity *Entity) error
    Update(ctx context.Context, entity *Entity) error
    Delete(ctx context.Context, id string) error
}

type MockRepository struct {
    mock.Mock
}
```

3. Lifecycle Management
- Initialize dependencies in the correct order
- Handle startup dependencies
- Implement proper shutdown sequence
- Clean up resources

4. Testing Support
- Provide easy ways to inject mocks
- Support dependency replacement
- Include test helpers
- Document testing patterns

5. Configuration Injection
- Use structured configuration objects
- Validate configuration at startup
- Support dynamic reconfiguration
- Handle defaults properly

6. Metrics and Monitoring
- Inject monitoring dependencies
- Support tracing context propagation
- Include health check interfaces
- Provide debugging hooks
