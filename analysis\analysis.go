package analysis

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/webhook"
)

// 配置结构
type Config struct {
	StripeWebhookSecret string
	AllowedCountries    []string // 允许的国家列表
	BlockedDomains      []string // 被阻止的邮箱域名
	IPBlacklist         []string // IP黑名单
}

// 用户风险分析数据
type UserRiskData struct {
	UserID          string
	PaymentFailures int
	PaymentDisputes int
	CardCount       int
	ChargeFrequency int
	LastChargeTime  time.Time
	FailureReasons  map[string]int
	Countries       map[string]int
	Emails          []string
	IPs             []string
}

// 风险分析器
type RiskAnalyzer struct {
	config     Config
	userData   map[string]*UserRiskData
	mutex      sync.RWMutex
	timeWindow time.Duration
}

// 支付失败详细信息
type PaymentFailureDetail struct {
	Code        string    `json:"code"`
	DeclineCode string    `json:"decline_code"`
	OutcomeType string    `json:"outcome_type"`
	Message     string    `json:"message"`
	Timestamp   time.Time `json:"timestamp"`
}

// 风险报告
type RiskReport struct {
	UserID             string         `json:"user_id"`
	TimeWindow         string         `json:"time_window"`
	PaymentFailureRate float64        `json:"payment_failure_rate"`
	DisputeRate        float64        `json:"dispute_rate"`
	UniqueCardsUsed    int            `json:"unique_cards_used"`
	ChargeFrequency    int            `json:"charge_frequency"`
	FailureBreakdown   map[string]int `json:"failure_breakdown"`
	CountryRiskFlags   []string       `json:"country_risk_flags"`
	EmailRiskFlags     []string       `json:"email_risk_flags"`
	IPRiskFlags        []string       `json:"ip_risk_flags"`
	RiskScore          float64        `json:"risk_score"`
}

func NewRiskAnalyzer(config Config) *RiskAnalyzer {
	return &RiskAnalyzer{
		config:     config,
		userData:   make(map[string]*UserRiskData),
		timeWindow: 24 * time.Hour, // 24小时窗口
	}
}

// 获取或创建用户数据
func (ra *RiskAnalyzer) getOrCreateUserData(userID string) *UserRiskData {
	ra.mutex.Lock()
	defer ra.mutex.Unlock()

	if _, exists := ra.userData[userID]; !exists {
		ra.userData[userID] = &UserRiskData{
			UserID:         userID,
			FailureReasons: make(map[string]int),
			Countries:      make(map[string]int),
			Emails:         make([]string, 0),
			IPs:            make([]string, 0),
		}
	}
	return ra.userData[userID]
}

// 处理支付成功事件
func (ra *RiskAnalyzer) handlePaymentSucceeded(charge *stripe.Charge) {
	if charge.Customer == nil {
		return
	}

	userID := string(charge.Customer.ID)
	userData := ra.getOrCreateUserData(userID)

	ra.mutex.Lock()
	defer ra.mutex.Unlock()

	// 更新充值频率
	userData.ChargeFrequency++
	userData.LastChargeTime = time.Now()

	// 记录卡片信息
	if charge.PaymentMethodDetails != nil && charge.PaymentMethodDetails.Card != nil {
		country := charge.PaymentMethodDetails.Card.Country
		if country != "" {
			userData.Countries[country]++
		}
	}

	// 记录IP
	if charge.Outcome != nil && charge.Outcome.NetworkStatus != "" {
		// 在实际实现中，您需要从请求头中获取真实IP
		ip := ra.getClientIP(charge)
		if ip != "" && !ra.containsString(userData.IPs, ip) {
			userData.IPs = append(userData.IPs, ip)
		}
	}

	log.Printf("Payment succeeded for user %s, amount: %d", userID, charge.Amount)
}

// 处理支付失败事件
func (ra *RiskAnalyzer) handlePaymentFailed(event stripe.Event) {
	var charge stripe.Charge
	if err := json.Unmarshal(event.Data.Raw, &charge); err != nil {
		log.Printf("Error parsing charge: %v", err)
		return
	}

	if charge.Customer == nil {
		return
	}

	userID := string(charge.Customer.ID)
	userData := ra.getOrCreateUserData(userID)

	ra.mutex.Lock()
	defer ra.mutex.Unlock()

	// 增加失败计数
	userData.PaymentFailures++

	// 记录失败原因
	if charge.FailureCode != "" {
		userData.FailureReasons[charge.FailureCode]++
	}

	// 记录outcome type
	if charge.Outcome != nil && charge.Outcome.Type != "" {
		key := fmt.Sprintf("outcome_%s", charge.Outcome.Type)
		userData.FailureReasons[key]++
	}

	log.Printf("Payment failed for user %s, reason: %s, outcome: %s",
		userID, charge.FailureCode, charge.Outcome.Type)
}

// 处理争议事件
func (ra *RiskAnalyzer) handleDisputeCreated(event stripe.Event) {
	var dispute stripe.Dispute
	if err := json.Unmarshal(event.Data.Raw, &dispute); err != nil {
		log.Printf("Error parsing dispute: %v", err)
		return
	}

	if dispute.Charge == nil || dispute.Charge.Customer == nil {
		return
	}

	userID := string(dispute.Charge.Customer.ID)
	userData := ra.getOrCreateUserData(userID)

	ra.mutex.Lock()
	userData.PaymentDisputes++
	ra.mutex.Unlock()

	log.Printf("Dispute created for user %s, reason: %s", userID, dispute.Reason)
}

// 处理退款事件
func (ra *RiskAnalyzer) handleRefundCreated(event stripe.Event) {
	var refund stripe.Refund
	if err := json.Unmarshal(event.Data.Raw, &refund); err != nil {
		log.Printf("Error parsing refund: %v", err)
		return
	}

	log.Printf("Refund created: %s, amount: %d", refund.ID, refund.Amount)
}

// 处理支付方法附加事件
func (ra *RiskAnalyzer) handlePaymentMethodAttached(event stripe.Event) {
	var paymentMethod stripe.PaymentMethod
	if err := json.Unmarshal(event.Data.Raw, &paymentMethod); err != nil {
		log.Printf("Error parsing payment method: %v", err)
		return
	}

	if paymentMethod.Customer == nil {
		return
	}

	userID := string(paymentMethod.Customer.ID)
	userData := ra.getOrCreateUserData(userID)

	ra.mutex.Lock()
	defer ra.mutex.Unlock()

	// 增加卡片数量
	userData.CardCount++

	// 记录国家信息
	if paymentMethod.Card != nil && paymentMethod.Card.Country != "" {
		userData.Countries[paymentMethod.Card.Country]++
	}

	log.Printf("Payment method attached for user %s, type: %s", userID, paymentMethod.Type)
}

// 生成风险报告
func (ra *RiskAnalyzer) GenerateRiskReport(userID string) *RiskReport {
	ra.mutex.RLock()
	defer ra.mutex.RUnlock()

	userData, exists := ra.userData[userID]
	if !exists {
		return nil
	}

	totalTransactions := userData.PaymentFailures + userData.ChargeFrequency

	report := &RiskReport{
		UserID:             userID,
		TimeWindow:         ra.timeWindow.String(),
		PaymentFailureRate: 0,
		DisputeRate:        0,
		UniqueCardsUsed:    userData.CardCount,
		ChargeFrequency:    userData.ChargeFrequency,
		FailureBreakdown:   userData.FailureReasons,
		CountryRiskFlags:   ra.checkCountryRisks(userData.Countries),
		EmailRiskFlags:     ra.checkEmailRisks(userData.Emails),
		IPRiskFlags:        ra.checkIPRisks(userData.IPs),
	}

	if totalTransactions > 0 {
		report.PaymentFailureRate = float64(userData.PaymentFailures) / float64(totalTransactions)
	}

	if userData.ChargeFrequency > 0 {
		report.DisputeRate = float64(userData.PaymentDisputes) / float64(userData.ChargeFrequency)
	}

	// 计算风险评分
	report.RiskScore = ra.calculateRiskScore(report)

	return report
}

// 检查国家风险
func (ra *RiskAnalyzer) checkCountryRisks(countries map[string]int) []string {
	risks := make([]string, 0)

	for country := range countries {
		if !ra.containsString(ra.config.AllowedCountries, country) {
			risks = append(risks, fmt.Sprintf("High-risk country: %s", country))
		}
	}

	return risks
}

// 检查邮箱风险
func (ra *RiskAnalyzer) checkEmailRisks(emails []string) []string {
	risks := make([]string, 0)

	for _, email := range emails {
		for _, blockedDomain := range ra.config.BlockedDomains {
			if strings.Contains(email, blockedDomain) {
				risks = append(risks, fmt.Sprintf("Blocked domain: %s", email))
			}
		}
	}

	return risks
}

// 检查IP风险
func (ra *RiskAnalyzer) checkIPRisks(ips []string) []string {
	risks := make([]string, 0)

	for _, ip := range ips {
		for _, blacklistedIP := range ra.config.IPBlacklist {
			if ip == blacklistedIP || ra.isInSubnet(ip, blacklistedIP) {
				risks = append(risks, fmt.Sprintf("Blacklisted IP: %s", ip))
			}
		}
	}

	return risks
}

// 计算风险评分
func (ra *RiskAnalyzer) calculateRiskScore(report *RiskReport) float64 {
	score := 0.0

	// 失败率权重 (30%)
	score += report.PaymentFailureRate * 30

	// 争议率权重 (40%)
	score += report.DisputeRate * 40

	// 风险标记权重 (30%)
	riskFlags := len(report.CountryRiskFlags) + len(report.EmailRiskFlags) + len(report.IPRiskFlags)
	if riskFlags > 0 {
		score += float64(riskFlags) * 10
	}

	// 确保评分在0-100范围内
	if score > 100 {
		score = 100
	}

	return score
}

// 工具函数
func (ra *RiskAnalyzer) getClientIP(charge *stripe.Charge) string {
	// 在实际实现中，您需要从HTTP请求头中获取IP
	// 这里返回模拟IP
	return "***********"
}

func (ra *RiskAnalyzer) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func (ra *RiskAnalyzer) isInSubnet(ip, subnet string) bool {
	if !strings.Contains(subnet, "/") {
		return ip == subnet
	}

	_, ipNet, err := net.ParseCIDR(subnet)
	if err != nil {
		return false
	}

	userIP := net.ParseIP(ip)
	return userIP != nil && ipNet.Contains(userIP)
}

// Webhook处理器
func (ra *RiskAnalyzer) handleWebhook(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Unable to read body"})
		return
	}

	// 验证webhook签名
	event, err := webhook.ConstructEvent(body, c.GetHeader("Stripe-Signature"), ra.config.StripeWebhookSecret)
	if err != nil {
		log.Printf("Webhook signature verification failed: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature"})
		return
	}

	// 处理不同类型的事件
	switch event.Type {
	case stripe.EventTypeChargeSucceeded:
		var charge stripe.Charge
		if err := json.Unmarshal(event.Data.Raw, &charge); err == nil {
			ra.handlePaymentSucceeded(&charge)
		}

	case stripe.EventTypeChargeFailed:
		ra.handlePaymentFailed(event)

	case stripe.EventTypeInvoicePaymentFailed:
		ra.handlePaymentFailed(event)

	case stripe.EventTypeChargeDisputeCreated:
		ra.handleDisputeCreated(event)

	case stripe.EventTypeRefundCreated:
		ra.handleRefundCreated(event)

	case stripe.EventTypePaymentMethodAttached:
		ra.handlePaymentMethodAttached(event)

	default:
		log.Printf("Unhandled event type: %s", event.Type)
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// 获取风险报告的API
func (ra *RiskAnalyzer) getRiskReport(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	report := ra.GenerateRiskReport(userID)
	if report == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, report)
}

// 获取所有用户风险概览
func (ra *RiskAnalyzer) getAllUsersRisk(c *gin.Context) {
	ra.mutex.RLock()
	defer ra.mutex.RUnlock()

	reports := make([]*RiskReport, 0)
	for userID := range ra.userData {
		if report := ra.GenerateRiskReport(userID); report != nil {
			reports = append(reports, report)
		}
	}

	c.JSON(http.StatusOK, gin.H{"users": reports})
}

func AnalysisStart() {
	// 配置
	config := Config{
		StripeWebhookSecret: "whsec_your_webhook_secret_here",
		AllowedCountries:    []string{"US", "CA", "GB", "AU", "DE", "FR", "JP"},
		BlockedDomains:      []string{"tempmail.org", "guerrillamail.com", "10minutemail.com"},
		IPBlacklist:         []string{"*************", "10.0.0.0/8"},
	}

	// 初始化风险分析器
	riskAnalyzer := NewRiskAnalyzer(config)

	// 设置Gin路由
	r := gin.Default()

	// Webhook端点
	r.POST("/webhook/stripe", riskAnalyzer.handleWebhook)

	// API端点
	r.GET("/risk-report/:user_id", riskAnalyzer.getRiskReport)
	r.GET("/risk-overview", riskAnalyzer.getAllUsersRisk)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 启动服务器
	log.Println("Starting Stripe Webhook server on :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
