package handlers

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"slices"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sashabaranov/go-openai"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

// 用于api借口调用的handler

type ApiHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewApiHandler(service *service.Api) *ApiHandler {
	return &ApiHandler{
		service: service,
		log:     logger.GetLogger("api_handler"),
	}
}

// @Summary 大语言模型聊天
// @Description 大语言模型聊天(openai标准流式和非流式)
// @Tags openai-api
// @Accept json
// @Produce json
// @Param Authorization header string true "Authorization"
// @Param body body service.ChatLLMByApiRequest true "body"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/openai/chat/completions [post]
func (h *ApiHandler) ChatLLM(c *gin.Context) {
	appKeyInfo, ok := c.Get("app_key_info")
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
		c.Abort()
		return
	}
	appKey := appKeyInfo.(*service.AppKeyOP)
	// 设置默认值
	req := &service.ChatLLMByApiRequest{
		Stream:      false,
		Temperature: 1,
		TopP:        1,
		ResponseFormat: service.ChatLLMByApiResponseFormat{
			Type: string(openai.ChatCompletionResponseFormatTypeJSONObject),
		},
	}
	if err := c.ShouldBindJSON(req); err != nil {
		api.Fail(c, api.CodeClientError, "bad request", err)
		c.Abort()
		return
	}
	if req.Model == "" {
		api.Fail(c, api.CodeClientError, "model is required", errors.New("model is required"))
		c.Abort()
		return
	}
	if len(req.Messages) == 0 {
		api.Fail(c, api.CodeClientError, "messages is required", errors.New("messages is required"))
		c.Abort()
		return
	}
	if req.Temperature < 0 || req.Temperature > 2 {
		api.Fail(c, api.CodeClientError, "temperature must be between 0 and 2", errors.New("temperature must be between 0 and 2"))
		c.Abort()
		return
	}
	if req.TopP < 0 || req.TopP > 1 {
		api.Fail(c, api.CodeClientError, "top_p must be between 0 and 1", errors.New("top_p must be between 0 and 1"))
		c.Abort()
		return
	}
	if req.PresencePenalty < -2 || req.PresencePenalty > 2 {
		api.Fail(c, api.CodeClientError, "presence_penalty must be between -2 and 2", errors.New("presence_penalty must be between -2 and 2"))
		c.Abort()
		return
	}
	if req.FrequencyPenalty < -2 || req.FrequencyPenalty > 2 {
		api.Fail(c, api.CodeClientError, "frequency_penalty must be between -2 and 2", errors.New("frequency_penalty must be between -2 and 2"))
		c.Abort()
		return
	}
	req.ModelId = req.Model
	// todo
	if req.Stream {
		h.handlerChatLLMStream(c, appKey, req)
	} else {
		h.handlerChatLLMNonStream(c, appKey, req)
	}

}

func (h *ApiHandler) handlerChatLLMStream(c *gin.Context, appKey *service.AppKeyOP, req *service.ChatLLMByApiRequest) {
	// 限制请求参数
	newReq := &service.ChatLLMByApiRequest{
		ModelId:          req.Model,
		Messages:         req.Messages,
		Stream:           req.Stream,
		MaxTokens:        req.MaxTokens,
		Temperature:      req.Temperature,
		TopP:             req.TopP,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		Seed:             req.Seed,
		User:             req.User,
		Stop:             req.Stop,
	}
	streamCh, errCh, err := h.service.Model.ChatLLMStreamByApi(c.Request.Context(), appKey.UserId, appKey.Key, newReq)
	if err != nil {
		h.log.Error("failed to chat llm", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to chat llm", err)
		c.Abort()
		return
	}
	// 设置sse stream响应
	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	// 创建SSE流
	flusher, _ := c.Writer.(http.Flusher)
	for stream := range streamCh {
		// 按照OpenAI标准格式返回SSE流
		data, _ := json.Marshal(stream)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", string(data))))
		flusher.Flush()
	}
	// 发送结束标记
	c.Writer.Write([]byte("data: [DONE]\n\n"))
	flusher.Flush()

	// 处理错误流
	err = <-errCh
	if err != nil {
		h.log.Error("failed to chat llm", zap.Error(err))
		// 发送错误信息
		errorData := map[string]interface{}{
			"error": map[string]interface{}{
				"message": err.Error(),
				"type":    "server_error",
			},
		}
		data, _ := json.Marshal(errorData)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", string(data))))
		flusher.Flush()
	}

}

func (h *ApiHandler) handlerChatLLMNonStream(c *gin.Context, appKey *service.AppKeyOP, req *service.ChatLLMByApiRequest) {
	// 限制请求参数
	newReq := &service.ChatLLMByApiRequest{
		ModelId:          req.Model,
		Messages:         req.Messages,
		Stream:           req.Stream,
		MaxTokens:        req.MaxTokens,
		Temperature:      req.Temperature,
		TopP:             req.TopP,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		Seed:             req.Seed,
		User:             req.User,
		Stop:             req.Stop,
	}

	// 调用非流式LLM API
	response, err := h.service.Model.ChatLLMNonStreamByApi(c.Request.Context(), appKey.UserId, appKey.Key, newReq)
	if err != nil {
		h.log.Error("failed to chat llm", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to chat llm", err)
		c.Abort()
		return
	}

	// 返回成功响应
	api.Success(c, response)
}

// @Summary 语音转文本
// @Description 语音转文本
// @Tags openai-api
// @Accept json
// @Produce json
// @Param Authorization header string true "Authorization"
// @Param file formData file true "file"
// @Param model formData string true "model"
// @Success 200 {object} service.ChatASRByApiResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/openai/audio/transcriptions [post]
func (h *ApiHandler) ChatASR(c *gin.Context) {
	// 获取应用密钥
	appKeyInfo, ok := c.Get("app_key_info")
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
		c.Abort()
		return
	}
	appKey := appKeyInfo.(*service.AppKeyOP)

	// 解析请求参数
	model := c.PostForm("model")
	if model == "" {
		api.Fail(c, api.CodeClientError, "model is required", errors.New("model is required"))
		c.Abort()
		return
	}

	// 获取上传的音频文件
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "missing audio file", err)
		c.Abort()
		return
	}
	defer file.Close()

	newReq := &service.ChatASRByApiRequest{
		ModelId: model,
		File:    file,
	}

	// 调用ASR API
	response, err := h.service.Model.ChatASRByApi(c.Request.Context(), appKey.UserId, appKey.Key, newReq)
	if err != nil {
		h.log.Error("failed to process audio", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to process audio", err)
		c.Abort()
		return
	}

	// 返回成功响应
	api.Success(c, response)
}

// @Summary 文本转语音
// @Description 文本转语音
// @Tags openai-api
// @Accept json
// @Produce json
// @Param Authorization header string true "Authorization"
// @Param body body service.ChatTTSByApiRequest true "body"
// @Success 200 {object} []byte
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/openai/audio/speech [post]
func (h *ApiHandler) ChatTTS(c *gin.Context) {
	// 获取应用密钥
	appKeyInfo, ok := c.Get("app_key_info")
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
		c.Abort()
		return
	}
	appKey := appKeyInfo.(*service.AppKeyOP)
	// 解析请求参数
	req := service.ChatTTSByApiRequest{
		ResponseFormat: string(openai.SpeechResponseFormatWav),
		Speed:          1,
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request body", err)
		c.Abort()
		return
	}
	if req.ResponseFormat == "" {
		req.ResponseFormat = string(openai.SpeechResponseFormatWav)
	}
	if req.Speed < 0.25 || req.Speed > 4 {
		api.Fail(c, api.CodeClientError, "speed must be between 0.25 and 4", errors.New("speed must be between 0.25 and 4"))
		c.Abort()
		return
	}
	if !slices.Contains([]string{"mp3", "opus", "flac", "wav", "pcm"}, req.ResponseFormat) {
		api.Fail(c, api.CodeClientError, "response_format must be one of mp3, opus, flac, wav, pcm", errors.New("response_format must be one of mp3, opus, flac, wav, pcm"))
		c.Abort()
		return
	}
	if req.Model == "" {
		api.Fail(c, api.CodeClientError, "model is required", errors.New("model is required"))
		c.Abort()
		return
	}
	if req.Input == "" {
		api.Fail(c, api.CodeClientError, "input is required", errors.New("input is required"))
		c.Abort()
		return
	}

	newReq := &service.ChatTTSByApiRequest{
		ModelId:        req.Model,
		Input:          req.Input,
		Voice:          req.Voice,
		ResponseFormat: req.ResponseFormat,
		Speed:          req.Speed,
	}
	// 调用TTS API
	audioData, err := h.service.Model.ChatTTSByApi(c.Request.Context(), appKey.UserId, appKey.Key, newReq)
	if err != nil {
		h.log.Error("failed to generate speech", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to generate speech", err)
		c.Abort()
		return
	}

	// 设置响应头
	c.Header("Content-Type", "audio/"+req.ResponseFormat)
	c.Header("Content-Length", fmt.Sprintf("%d", len(audioData)))
	c.Header("Content-Disposition", "attachment; filename=\"speech.mp3\"")

	// 返回音频数据
	c.Data(http.StatusOK, "audio/"+req.ResponseFormat, audioData)
}

// @Summary 文本转图像
// @Description 文本转图像
// @Tags openai-api
// @Accept json
// @Produce json
// @Param Authorization header string true "Authorization"
// @Param body body service.ChatTTIByApiRequest true "body"
// @Success 200 {object} service.ChatTTIByApiResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/openai/images/generations [post]
func (h *ApiHandler) ChatTTI(c *gin.Context) {
	// 获取应用密钥
	appKeyInfo, ok := c.Get("app_key_info")
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
		c.Abort()
		return
	}
	appKey := appKeyInfo.(*service.AppKeyOP)
	// 解析请求参数
	req := service.ChatTTIByApiRequest{
		ResponseFormat: string(openai.CreateImageResponseFormatB64JSON),
		Size:           string(openai.CreateImageSize512x512),
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request body", err)
		c.Abort()
		return
	}
	sizeArr := strings.Split(req.Size, "x")
	if len(sizeArr) != 2 {
		api.Fail(c, api.CodeClientError, "size must be in the format of widthxheight", errors.New("size must be in the format of widthxheight"))
		c.Abort()
		return
	}
	width, err := strconv.Atoi(sizeArr[0])
	if err != nil {
		api.Fail(c, api.CodeClientError, "width must be a number", errors.New("width must be a number"))
		c.Abort()
		return
	}
	height, err := strconv.Atoi(sizeArr[1])
	if err != nil {
		api.Fail(c, api.CodeClientError, "height must be a number", errors.New("height must be a number"))
		c.Abort()
		return
	}
	if width < 128 || width > 1920 || height < 128 || height > 1920 {
		api.Fail(c, api.CodeClientError, "size must be between 128 and 1920", errors.New("size must be between 128 and 1920"))
		c.Abort()
		return
	}
	if req.ResponseFormat != string(openai.CreateImageResponseFormatB64JSON) {
		api.Fail(c, api.CodeClientError, "response_format must be b64_json", errors.New("response_format must be b64_json"))
		c.Abort()
		return
	}

	newReq := &service.ChatTTIByApiRequest{
		ModelId:        req.Model,
		Prompt:         req.Prompt,
		ResponseFormat: req.ResponseFormat,
		Size:           req.Size,
		User:           req.User,
	}

	// 调用图像生成API
	imageData, err := h.service.Model.ChatTTIByApi(c.Request.Context(), appKey.UserId, appKey.Key, newReq)
	if err != nil {
		h.log.Error("failed to generate image", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to generate image", err)
		c.Abort()
		return
	}

	api.Success(c, imageData)
}

// @Summary 获取使用情况
// @Description 获取使用情况
// @Tags openai-api
// @Accept json
// @Produce json
// @Param Authorization header string true "Authorization"
// @Success 200 {object} service.GetUsageResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/openai/usage/{request_id} [get]
func (h *ApiHandler) GetUsage(c *gin.Context) {
	appKeyInfo, ok := c.Get("app_key_info")
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "auth failed", errors.New("missing app key info"))
		c.Abort()
		return
	}
	appKey := appKeyInfo.(*service.AppKeyOP)
	requestId := c.Param("request_id")
	usage, err := h.service.UserBalance.GetUsage(c.Request.Context(), appKey, requestId)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get usage", err)
		c.Abort()
		return
	}
	// 处理cost
	cost, _ := big.NewFloat(0).SetString(usage.TotalCost)
	cost.Quo(cost, big.NewFloat(1e18)) // 换算成USDT
	usage.TotalCost = utils.FormatPrice(cost, 5)
	api.Success(c, usage)
}
func (h *ApiHandler) Close() {

}
