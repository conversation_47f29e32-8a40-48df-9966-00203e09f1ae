package middleware

import (
	"errors"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

// RateLimiter 速率限制器
type RateLimiter struct {
	// 限制规则配置
	limit  int           // 限制次数
	window time.Duration // 时间窗口

	// 用户请求计数
	tokens       map[string]int       // 用户令牌数量
	lastAccessed map[string]time.Time // 上次访问时间

	// 并发控制
	mu sync.Mutex
}

// NewRateLimiter 创建新的速率限制器
func NewRateLimiter(limit int, window time.Duration) *RateLimiter {
	return &RateLimiter{
		limit:        limit,
		window:       window,
		tokens:       make(map[string]int),
		lastAccessed: make(map[string]time.Time),
	}
}

// Allow 判断请求是否允许通过
func (rl *RateLimiter) Allow(key string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	lastAccessed, exists := rl.lastAccessed[key]

	// 如果是新用户或者超过了时间窗口，重置计数
	if !exists || now.Sub(lastAccessed) > rl.window {
		rl.tokens[key] = 1
		rl.lastAccessed[key] = now
		return true
	}

	// 如果在时间窗口内，检查是否超过了限制
	if rl.tokens[key] < rl.limit {
		rl.tokens[key]++
		rl.lastAccessed[key] = now
		return true
	}

	return false
}

// Cleanup 清理过期的记录
func (rl *RateLimiter) Cleanup() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()
	for key, lastAccessed := range rl.lastAccessed {
		if now.Sub(lastAccessed) > rl.window {
			delete(rl.tokens, key)
			delete(rl.lastAccessed, key)
		}
	}
}

// RateLimit 中间件，根据IP限制请求速率
func RateLimitByIP(limit int, window time.Duration) gin.HandlerFunc {
	limiter := NewRateLimiter(limit, window)

	// 启动清理过期记录的goroutine
	go func() {
		ticker := time.NewTicker(window / 2)
		defer ticker.Stop()

		for range ticker.C {
			limiter.Cleanup()
		}
	}()

	return func(c *gin.Context) {
		// 获取用户IP作为限制键
		clientIP := c.ClientIP()

		// 如果允许请求，则继续
		if limiter.Allow(clientIP) {
			c.Next()
			return
		}

		// 否则返回限流错误
		log.Warn("请求频率过高",
			zap.String("ip", clientIP),
			zap.String("path", c.Request.URL.Path),
		)

		api.Fail(c, api.CodeClientError, "rate limit exceeded, please try again later", errors.New("rate limit exceeded"))
		c.Abort()
	}
}

// RateLimitByUserID 中间件，根据用户ID限制请求速率
func RateLimitByUserID(limit int, window time.Duration) gin.HandlerFunc {
	limiter := NewRateLimiter(limit, window)

	// 启动清理过期记录的goroutine
	go func() {
		ticker := time.NewTicker(window / 2)
		defer ticker.Stop()

		for range ticker.C {
			limiter.Cleanup()
		}
	}()

	return func(c *gin.Context) {
		// 获取用户ID，如果未登录则使用IP
		var key string
		if user, exists := c.Get("user"); exists {
			if userOP, ok := user.(*service.UserBaseOP); ok {
				key = userOP.IdentityID
			} else {
				key = c.ClientIP()
			}
		} else {
			key = c.ClientIP()
		}

		// 如果允许请求，则继续
		if limiter.Allow(key) {
			c.Next()
			return
		}

		// 否则返回限流错误
		log.Warn("用户请求频率过高",
			zap.String("user_key", key),
			zap.String("path", c.Request.URL.Path),
		)

		api.Fail(c, api.CodeClientError, "rate limit exceeded, please try again later", errors.New("rate limit exceeded"))
		c.Abort()
	}
}

// APIKeyRateLimit 基于API密钥的速率限制
func APIKeyRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	limiter := NewRateLimiter(limit, window)

	go func() {
		ticker := time.NewTicker(window / 2)
		defer ticker.Stop()

		for range ticker.C {
			limiter.Cleanup()
		}
	}()

	return func(c *gin.Context) {
		// 获取API密钥，如果没有则使用IP
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = c.ClientIP()
		}

		if limiter.Allow(apiKey) {
			c.Next()
			return
		}

		log.Warn("API密钥请求频率过高",
			zap.String("api_key", apiKey),
			zap.String("path", c.Request.URL.Path),
		)

		api.Fail(c, api.CodeClientError, "rate limit exceeded, please try again later", errors.New("rate limit exceeded"))
		c.Abort()
	}
}
