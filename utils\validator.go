package utils

import (
	"regexp"
	"strings"
)

// IsEmailValid 检查邮箱格式是否有效
func IsEmailValid(email string) bool {
	email = strings.TrimSpace(email)
	if email == "" {
		return false
	}

	// 简单的邮箱验证正则表达式
	// 更复杂的验证可能需要更复杂的正则
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsPasswordValid 检查密码是否有效
func IsPasswordValid(password string) bool {
	// 检查密码长度
	if len(password) < 6 {
		return false
	}

	// 可以添加更多的密码强度验证规则
	// 例如：必须包含数字、大写字母、小写字母、特殊字符等

	return true
}

// IsUsernameValid 检查用户名是否有效
func IsUsernameValid(username string) bool {
	username = strings.TrimSpace(username)

	// 检查长度
	if len(username) < 3 || len(username) > 50 {
		return false
	}

	// 用户名只允许字母、数字、下划线和中文
	usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_\p{Han}]+$`)
	return usernameRegex.MatchString(username)
}

// IsAPIKeyValid 检查API密钥是否有效
func IsAPIKeyValid(apiKey string) bool {
	apiKey = strings.TrimSpace(apiKey)

	// API密钥长度应该至少为16个字符
	if len(apiKey) < 16 {
		return false
	}

	// API密钥通常由字母和数字组成
	apiKeyRegex := regexp.MustCompile(`^[a-zA-Z0-9\-_]+$`)
	return apiKeyRegex.MatchString(apiKey)
}

// SanitizeString 清理字符串，防止XSS攻击
func SanitizeString(input string) string {
	// 移除HTML标签
	htmlTagRegex := regexp.MustCompile(`<[^>]*>`)
	sanitized := htmlTagRegex.ReplaceAllString(input, "")

	// 可以添加更多的清理规则

	return sanitized
}

// ValidateURL 验证URL是否有效
func ValidateURL(url string) bool {
	url = strings.TrimSpace(url)
	if url == "" {
		return false
	}

	// URL验证正则表达式
	urlRegex := regexp.MustCompile(`^(http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}([\/\?\#][^\s]*)?$`)
	return urlRegex.MatchString(url)
}

// IsPhoneNumberValid 检查手机号码是否有效
func IsPhoneNumberValid(phone string) bool {
	phone = strings.TrimSpace(phone)
	if phone == "" {
		return false
	}

	// 国内手机号验证，可根据实际需求调整
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}
