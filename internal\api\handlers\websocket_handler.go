package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	socketio "github.com/doquangtan/socket.io/v4"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type chatLimit struct {
	TotalRemain int
	UserLimit   int
	IPLimit     int
	Used        map[string]int
}

var defaultChatLimit = &chatLimit{
	TotalRemain: 500,
	UserLimit:   100,
	IPLimit:     100,
	Used:        make(map[string]int),
}

func (l *chatLimit) Allow(ctx context.Context, userId uint, ip string) error {
	userKey := fmt.Sprintf("user_%d", userId)
	ipKey := fmt.Sprintf("ip_%s", ip)
	if l.TotalRemain <= 0 {
		return errors.New("total limit exceeded")
	}
	if l.Used[ipKey] >= l.IPLimit {
		return errors.New("ip limit exceeded")
	}
	if l.Used[userKey] >= l.UserLimit {
		return errors.New("user limit exceeded")
	}
	l.Used[ipKey] = l.Used[ipKey] + 1
	l.Used[userKey] = l.Used[userKey] + 1
	l.TotalRemain = l.TotalRemain - 1
	return nil
}

type respMsg struct {
	User      *service.UserBaseOP
	SessionID string
	Data      interface{}
}

type WebSocketHandler struct {
	service          *service.Api
	log              *logger.ModuleLogger
	wsServer         *socketio.Io
	sessionMapUser   map[string]*service.UserBaseOP
	sessionMapConn   map[string]*socketio.Socket
	userIDMapSession map[uint][]string
	lock             sync.RWMutex
	TaskCh           chan *service.CreateChatCompletionRequest
	ProcessTasks     map[string][]*service.CreateChatCompletionRequest
	ProcessTaskMap   map[string]*service.CreateChatCompletionRequest
	dayLimit         map[string]*chatLimit
	respMsgCh        chan *respMsg
	Stop             chan struct{}
}

func NewWebSocketHandler(serviceApi *service.Api) *WebSocketHandler {
	return &WebSocketHandler{
		service:          serviceApi,
		log:              logger.GetLogger("websocket_handler"),
		wsServer:         socketio.New(),
		sessionMapUser:   make(map[string]*service.UserBaseOP),
		sessionMapConn:   make(map[string]*socketio.Socket),
		userIDMapSession: make(map[uint][]string),
		lock:             sync.RWMutex{},
		TaskCh:           make(chan *service.CreateChatCompletionRequest, 1024),
		ProcessTasks:     make(map[string][]*service.CreateChatCompletionRequest),
		ProcessTaskMap:   make(map[string]*service.CreateChatCompletionRequest),
		dayLimit:         make(map[string]*chatLimit),
		respMsgCh:        make(chan *respMsg, 1024),
		Stop:             make(chan struct{}),
	}
}

func (h *WebSocketHandler) checkLimit(ctx context.Context, user *service.UserBaseOP, ip string) error {
	day := time.Now().Format("2006-01-02")
	limit, ok := h.dayLimit[day]
	if !ok {
		limit = defaultChatLimit
		h.dayLimit = map[string]*chatLimit{
			day: limit,
		}
	}
	err := limit.Allow(ctx, user.ID, ip)
	if err != nil {
		return err
	}
	h.log.Info("check limit success", zap.Any("day", day), zap.Any("limit", limit))
	return nil

}

func (h *WebSocketHandler) Start(ctx context.Context) {
	// 获取当日数据已请求数据
	day := time.Now().Format("2006-01-02")
	userChatRequests, err := h.service.User.GetDayChatRequest(ctx, day)
	if err != nil {
		h.log.Error("get day user chat request error", zap.Error(err))
	}
	limit := defaultChatLimit
	for _, userChatRequest := range userChatRequests {
		userKey := fmt.Sprintf("user_%d", userChatRequest.UserID)
		ipKey := fmt.Sprintf("ip_%s", userChatRequest.IP)
		limit.Used[userKey] = limit.Used[userKey] + 1
		limit.Used[ipKey] = limit.Used[ipKey] + 1
		limit.TotalRemain = limit.TotalRemain - 1
	}
	h.dayLimit[day] = limit
	//h.log.Info("day limit", zap.Any("day", day), zap.Any("limit", limit))

	h.log.Info("starting websocket server")
	h.wsServer.OnAuthorization(func(params map[string]string) bool {
		h.log.Info("on authorization", zap.Any("params", params))
		token := params["token"]
		if token == "" {
			h.log.Error("no token", zap.String("id", params["id"]))
			return false
		}
		_, err := h.service.User.GetUserByToken(ctx, token)
		if err != nil {
			h.log.Error("invalid token", zap.String("id", params["id"]), zap.Error(err))
			return false
		}
		return true
	})
	type SocketJoinMessage struct {
		Auth struct {
			Token string `json:"token"`
		} `json:"auth"`
	}
	h.wsServer.OnConnection(func(payload *socketio.Socket) {
		h.log.Info("new connection", zap.String("id", payload.Id), zap.String("remote_addr", payload.Nps))

		payload.On("user-join", func(msg *socketio.EventPayload) {
			// message： [{"auth":{"token":"FLyVkox5lZnR1P3P"}}]
			h.log.Info("on user-join", zap.String("id", payload.Id), zap.Any("message", msg.Data))
			if len(msg.Data) == 0 {
				h.log.Error("no data", zap.String("id", payload.Id))
				payload.Emit("error", "no data")
				payload.Disconnect()
				return
			}
			authData := &SocketJoinMessage{}
			_bytes, _ := json.Marshal(msg.Data[0])
			if err := json.Unmarshal(_bytes, authData); err != nil {
				h.log.Error("unmarshal error", zap.String("id", payload.Id), zap.Error(err))
				payload.Emit("error", "unmarshal error")
				payload.Disconnect()
				return
			}
			token := authData.Auth.Token
			if token == "" {
				h.log.Error("no token", zap.String("id", payload.Id))
				payload.Emit("error", "no token")
				payload.Disconnect()
				return
			}

			// 验证token
			user, err := h.service.User.GetUserByToken(ctx, token)
			if err != nil {
				h.log.Error("invalid token", zap.String("id", payload.Id), zap.Error(err))
				payload.Emit("error", "invalid token")
				payload.Disconnect()
				return
			}

			// 设置关联
			h.updateUserMap(payload, user)

			// 发送消息
			h.sendUserList(payload)

			h.sendUsage(payload)
		})
		payload.On("usage", func(msg *socketio.EventPayload) {
			h.log.Info("usage", zap.String("id", payload.Id), zap.Any("message", msg))
		})
		payload.On("disconnect", func(msg *socketio.EventPayload) {
			h.log.Info("disconnect", zap.String("id", payload.Id), zap.Any("message", msg))
			h.removeUserMap(payload)
			h.sendUserList(nil)
		})
		payload.On("error", func(msg *socketio.EventPayload) {
			h.log.Info("error", zap.String("id", payload.Id), zap.Any("message", msg))
		})
	})

	go h.handleTasks(ctx)
	go h.AutoSendMsgToUser(ctx)
}

func (h *WebSocketHandler) updateUserMap(s *socketio.Socket, user *service.UserBaseOP) {
	h.lock.Lock()
	defer h.lock.Unlock()
	if _, ok := h.sessionMapUser[s.Id]; ok {
		return
	}
	h.sessionMapUser[s.Id] = user
	if _, ok := h.userIDMapSession[user.ID]; !ok {
		h.userIDMapSession[user.ID] = make([]string, 0)
	}
	h.userIDMapSession[user.ID] = append(h.userIDMapSession[user.ID], s.Id)
	h.sessionMapConn[s.Id] = s
}

func (h *WebSocketHandler) removeUserMap(s *socketio.Socket) {
	h.lock.Lock()
	defer h.lock.Unlock()
	if _, ok := h.sessionMapUser[s.Id]; !ok {
		return
	}
	user := h.sessionMapUser[s.Id]
	delete(h.sessionMapUser, s.Id)
	delete(h.sessionMapConn, s.Id)
	sessionList, ok := h.userIDMapSession[user.ID]
	if !ok {
		return
	}
	for i, sessionID := range sessionList {
		if sessionID == s.Id {
			h.userIDMapSession[user.ID] = append(sessionList[:i], sessionList[i+1:]...)
			break
		}
	}
}

func (h *WebSocketHandler) sendUserList(conn *socketio.Socket) {
	h.lock.RLock()
	defer h.lock.RUnlock()

	userIDs := make([]string, 0)
	for _, user := range h.sessionMapUser {
		userIDs = append(userIDs, user.IdentityID)
	}

	if conn != nil {
		currentUser := h.sessionMapUser[conn.Id]
		h.onSendMsgToUser(context.Background(), currentUser, conn.Id, map[string][]string{
			"user_ids": userIDs,
		})
		return
	}

	for sessionID, user := range h.sessionMapUser {
		conn := h.sessionMapConn[sessionID]
		if conn != nil {
			h.onSendMsgToUser(context.Background(), user, sessionID, map[string][]string{
				"user_ids": userIDs,
			})
		}
	}
}

func (h *WebSocketHandler) sendUsage(conn *socketio.Socket) {
	h.lock.RLock()
	defer h.lock.RUnlock()

	//todo 先返回为空，后续在确认用处
	usage := map[string][]string{
		"models": []string{},
	}

	if conn != nil {
		currentUser := h.sessionMapUser[conn.Id]
		h.onSendMsgToUser(context.Background(), currentUser, conn.Id, usage)
	}
}

func (h *WebSocketHandler) getSessionConn(sessionID string) *socketio.Socket {
	h.lock.RLock()
	defer h.lock.RUnlock()
	return h.sessionMapConn[sessionID]
}

// todo 清理usage，先不实现
func (h *WebSocketHandler) cleanupUsage() {

}

// HandleWebSocket 处理WebSocket连接
// @Summary 处理WebSocket连接
// @Description 处理WebSocket连接
// @Tags websocket
// @Accept json
// @Produce json
// @Success 200 {object} nil
// @Router /ws/socket.io [get]
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	h.log.Info("handle websocket",
		zap.String("path", c.Request.URL.Path),
		zap.String("method", c.Request.Method),
		zap.String("remote_addr", c.Request.RemoteAddr),
		zap.String("user_agent", c.Request.UserAgent()),
		zap.Any("headers", c.Request.Header),
		zap.String("query", c.Request.URL.RawQuery),
	)
	h.wsServer.ServeHTTP(c.Writer, c.Request)
}

// CreateChatCompletion 开始聊天
// @Summary 开始聊天
// @Description 开始聊天
// @Tags websocket
// @Accept json
// @Produce json
// @Param chat_id path string true "聊天ID"
// @Param body body service.CreateChatCompletionRequest true "聊天请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/chat/completions [post]
func (h *WebSocketHandler) CreateChatCompletion(c *gin.Context) {
	req := &service.CreateChatCompletionRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		h.log.Error("bind json error", zap.Error(err))
		api.Fail(c, api.CodeClientError, "bind json error", err)
		return
	}

	// 设置用户
	h.lock.RLock()
	user, ok := h.sessionMapUser[req.SessionID]
	h.lock.RUnlock()
	if !ok {
		api.Fail(c, api.CodeClientError, "user not found", errors.New("user not found"))
		return
	}
	req.User = user

	err := h.checkLimit(c.Request.Context(), user, c.ClientIP())
	if err != nil {
		msg := "Daily limit reached.Please try again tomorrow."
		h.service.Model.UpdateConversationContent(c.Request.Context(), user, req.ChatID, req.MsgID, msg, "")
		api.Fail(c, api.CodeClientError, msg, err)
		return
	}
	defer func() {
		err := h.service.User.UpdateUserChatRequest(context.TODO(), user, c.ClientIP(), &service.UserChatRequest{
			ModelType:               repository.RemoterModelTypeLLM.String(),
			AssociatedUUID:          req.ChatID,
			ConversationMessageUUID: req.MsgID,
			ModelID:                 req.ModelItem.ModelID,
		})
		if err != nil {
			h.log.Error("update user chat request error", zap.Error(err))
		}
	}()

	taskId := uuid.New().String()
	req.TaskID = taskId
	req.StopCh = make(chan struct{})

	// 创建任务
	go func() {
		h.lock.Lock()
		h.ProcessTasks[req.ChatID] = append(h.ProcessTasks[req.ChatID], req)
		h.ProcessTaskMap[req.TaskID] = req
		h.lock.Unlock()
		select {
		case <-h.Stop:
		case h.TaskCh <- req:
		}

	}()

	api.Success(c, map[string]interface{}{
		"status":  true,
		"task_id": taskId,
	})
}

// GetChatTask 获取聊天任务
// @Summary 获取聊天任务
// @Description 获取聊天任务
// @Tags websocket
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/chat/tasks/:chat_id [get]
func (h *WebSocketHandler) GetChatTask(c *gin.Context) {
	chatId := c.Param("chat_id")
	if chatId == "" {
		api.Fail(c, api.CodeClientError, "chat_id is required", errors.New("chat_id is required"))
		return
	}

	h.lock.RLock()
	defer h.lock.RUnlock()
	task, ok := h.ProcessTasks[chatId]
	if !ok {
		api.Success(c, map[string]interface{}{
			"task_ids": []string{},
		})
		return
	}

	taskIds := make([]string, 0, len(task))
	for _, t := range task {
		taskIds = append(taskIds, t.TaskID)
	}

	api.Success(c, map[string]interface{}{
		"task_ids": taskIds,
	})
}

func (h *WebSocketHandler) StopChatTask(c *gin.Context) {
	taskId := c.Param("task_id")
	if taskId == "" {
		api.Fail(c, api.CodeClientError, "task_id is required", errors.New("task_id is required"))
		return
	}
	h.lock.Lock()
	defer h.lock.Unlock()
	req, ok := h.ProcessTaskMap[taskId]
	if !ok {
		api.Fail(c, api.CodeClientError, "task_id not found", errors.New("task_id not found"))
		return
	}
	close(req.StopCh)
	delete(h.ProcessTasks, req.ChatID)
	delete(h.ProcessTaskMap, req.TaskID)
	api.Success(c, nil)
}

type ChatCompletedRequest struct {
	TaskID    string `json:"id"`
	ChatID    string `json:"chat_id"`
	Model     string `json:"model"`
	SessionID string `json:"session_id"`
	Messages  []*struct {
		Role      string `json:"role"`
		Content   string `json:"content"`
		ID        string `json:"id"`
		Timestamp int64  `json:"timestamp"`
	} `json:"messages"`
	ModelItem *service.AvailableModel `json:"model_item"`
}

// ChatCompleted 聊天完成
// @Summary 聊天完成
// @Description 聊天完成
// @Tags websocket
// @Accept json
// @Produce json
// @Param body body ChatCompletedRequest true "聊天完成请求"
// @Success 200 {object} ChatCompletedRequest
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/chat/completed [post]
func (h *WebSocketHandler) ChatCompleted(c *gin.Context) {
	req := &ChatCompletedRequest{
		Messages: []*struct {
			Role      string `json:"role"`
			Content   string `json:"content"`
			ID        string `json:"id"`
			Timestamp int64  `json:"timestamp"`
		}{},
	}
	if err := c.ShouldBindJSON(req); err != nil {
		h.log.Error("bind json error", zap.Error(err))
		api.Fail(c, api.CodeClientError, "bind json error", err)
		return
	}
	h.onComplete(req.ChatID, req.TaskID)

	api.Success(c, req)
}

func (h *WebSocketHandler) onComplete(chatId string, taskId string) {
	h.lock.Lock()
	delete(h.ProcessTasks, chatId)
	delete(h.ProcessTaskMap, taskId)
	h.lock.Unlock()
}

// StartChatASR 开始ASR对话
// @Summary 开始ASR对话
// @Description 开始ASR对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param file formData file true "file"
// @Param model_id formData string true "model_id"
// @Success 200 {object} service.StartChatASRResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/asr [post]
func (h *WebSocketHandler) StartChatASR(c *gin.Context) {
	file, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "missing audio file", err)
		c.Abort()
		return
	}
	modelID := c.PostForm("model_id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "model_id is required", errors.New("model_id is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	err = h.checkLimit(c.Request.Context(), userOP, c.ClientIP())
	if err != nil {
		api.Fail(c, api.CodeClientError, "Daily limit reached.Please try again tomorrow.", err)
		return
	}

	resp, err := h.service.Model.StartChatASR(c, userOP, &service.StartChatASRRequest{
		File:     file,
		FileName: fileHeader.Filename,
		FileSize: fileHeader.Size,
		ModelID:  modelID,
		MimeType: fileHeader.Header.Get("Content-Type"),
	})
	if err != nil {
		h.log.Error("start chat asr error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}
	err = h.service.User.UpdateUserChatRequest(c.Request.Context(), userOP, c.ClientIP(), &service.UserChatRequest{
		UserID:                  userOP.ID,
		ModelType:               repository.RemoterModelTypeASR.String(),
		AssociatedUUID:          resp.UUID,
		ConversationMessageUUID: "",
		ModelID:                 modelID,
	})
	if err != nil {
		h.log.Error("update user chat request error", zap.Error(err))
	}
	api.Success(c, resp)
}

// StartChatTTS 开始TTS对话
// @Summary 开始TTS对话
// @Description 开始TTS对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param body body service.StartChatTTSRequest true "body"
// @Success 200 {object} service.StartChatTTSResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/tts [post]
func (h *WebSocketHandler) StartChatTTS(c *gin.Context) {
	req := &service.StartChatTTSRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	if req.Speed == 0 {
		req.Speed = 1
	}

	if req.ModelID == "" {
		api.Fail(c, api.CodeClientError, "model_id is required", errors.New("model_id is required"))
		return
	}

	if req.Content == "" {
		api.Fail(c, api.CodeClientError, "content is required", errors.New("content is required"))
		return
	}

	// 语速在 [0.25,4] 之间
	if req.Speed < 0.25 || req.Speed > 4 {
		api.Fail(c, api.CodeClientError, "speed must be between 0.25 and 4", errors.New("speed must be between 0.25 and 4"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	err := h.checkLimit(c.Request.Context(), userOP, c.ClientIP())
	if err != nil {
		api.Fail(c, api.CodeClientError, "Daily limit reached.Please try again tomorrow.", err)
		return
	}

	resp, err := h.service.Model.StartChatTTS(c, userOP, req)
	if err != nil {
		h.log.Error("start chat tts error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}
	err = h.service.User.UpdateUserChatRequest(c.Request.Context(), userOP, c.ClientIP(), &service.UserChatRequest{
		UserID:                  userOP.ID,
		ModelType:               repository.RemoterModelTypeTTS.String(),
		AssociatedUUID:          resp.UUID,
		ConversationMessageUUID: "",
		ModelID:                 req.ModelID,
	})
	if err != nil {
		h.log.Error("update user chat request error", zap.Error(err))
	}
	api.Success(c, resp)
}

// StartChatTTI 开始TTI对话
// @Summary 开始TTI对话
// @Description 开始TTI对话
// @Tags conversation
// @Accept json
// @Produce json
// @Param body body service.StartChatTTIRequest true "body"
// @Success 200 {object} service.StartChatTTIResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/chats/tti [post]
func (h *WebSocketHandler) StartChatTTI(c *gin.Context) {
	req := &service.StartChatTTIRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	if req.ModelID == "" {
		api.Fail(c, api.CodeClientError, "model_id is required", errors.New("model_id is required"))
		return
	}

	if req.Size == "" {
		req.Size = "512x512"
	}
	sizeArr := strings.Split(req.Size, "x")
	if len(sizeArr) != 2 {
		api.Fail(c, api.CodeClientError, "size must be in the format of widthxheight", errors.New("size must be in the format of widthxheight"))
		return
	}
	width, err := strconv.Atoi(sizeArr[0])
	if err != nil {
		api.Fail(c, api.CodeClientError, "width must be a number", errors.New("width must be a number"))
		return
	}
	height, err := strconv.Atoi(sizeArr[1])
	if err != nil {
		api.Fail(c, api.CodeClientError, "height must be a number", errors.New("height must be a number"))
		return
	}
	if width < 128 || width > 1920 || height < 128 || height > 1920 {
		api.Fail(c, api.CodeClientError, "size must be between 128 and 1920", errors.New("size must be between 128 and 1920"))
		return
	}

	if req.Content == "" {
		api.Fail(c, api.CodeClientError, "content is required", errors.New("content is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	err = h.checkLimit(c.Request.Context(), userOP, c.ClientIP())
	if err != nil {
		api.Fail(c, api.CodeClientError, "Daily limit reached.Please try again tomorrow.", err)
		return
	}

	resp, err := h.service.Model.StartChatTTI(c, userOP, req)
	if err != nil {
		h.log.Error("start chat tti error", zap.Error(err))
		api.Fail(c, api.CodeServerError, "system error", err)
		return
	}
	err = h.service.User.UpdateUserChatRequest(c.Request.Context(), userOP, c.ClientIP(), &service.UserChatRequest{
		UserID:                  userOP.ID,
		ModelType:               repository.RemoterModelTypeTTI.String(),
		AssociatedUUID:          resp.UUID,
		ConversationMessageUUID: "",
		ModelID:                 req.ModelID,
	})
	if err != nil {
		h.log.Error("update user chat request error", zap.Error(err))
	}
	api.Success(c, resp)
}

func (h *WebSocketHandler) handleTasks(ctx context.Context) {
	paraCh := make(chan struct{}, 10) // 设置并发数
	for req := range h.TaskCh {
		paraCh <- struct{}{}
		// 处理聊天
		go func() {
			defer func() { <-paraCh }()
			h.handleTask(ctx, req)
		}()
	}
}

func (h *WebSocketHandler) handleTask(ctx context.Context, req *service.CreateChatCompletionRequest) {
	type ChatEventsMsg struct {
		ChatId    string `json:"chat_id"`
		MessageId string `json:"message_id"`
		Data      struct {
			Type string      `json:"type"`
			Data interface{} `json:"data"`
		} `json:"data"`
	}
	ctx, cancel := context.WithCancel(ctx)
	completeCh := make(chan struct{})
	defer close(completeCh)
	go func() {
		select {
		case <-req.StopCh:
			cancel()
		case <-completeCh:
		}
	}()

	eventMsg := ChatEventsMsg{
		ChatId:    req.ChatID,
		MessageId: req.MsgID,
		Data: struct {
			Type string      `json:"type"`
			Data interface{} `json:"data"`
		}{
			Type: "chat:completion",
		},
	}

	// 启动图片生成功能
	extraPrompt := ""
	imageUrl := ""
	var err error
	if req.Features.ImageGeneration {
		eventMsg.Data.Type = "status"
		eventMsg.Data.Data = map[string]interface{}{
			"description": "Generating an image",
			"done":        false,
		}
		h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
		imageUrl, err = func() (string, error) {
			imageBytes, err := h.service.Model.GenerateImage(ctx, req)
			if err != nil {
				return "", err
			}

			// 保存图片
			return h.service.File.SaveImage(ctx, req.User, imageBytes)
		}()

		if err != nil {
			h.log.Error("generate image error", zap.Error(err))
			extraPrompt = `<context>
🚨 FAILURE PROTOCOL: ULTIMATE COMPLIANCE 🚨
🌐 STRICT LANGUAGE MIRRORING ENFORCEMENT 🌐

# SYSTEM STATE:
❌ EXTERNAL IMAGE GENERATION FAILED
✅ YOUR ROLE: FAILURE NOTIFICATION ONLY

# EXECUTION RULES:
1. USE USER'S EXACT INPUT LANGUAGE
2. OUTPUT ONLY 2 SENTENCES:
   ► SENTENCE 1: FAILURE DECLARATION (10 WORDS MAX)
   ► SENTENCE 2: TROUBLESHOOTING OPTIONS (15 WORDS MAX)
3. ABSOLUTE BAN: NO BRACKETED TEXT [LIKE THIS]

# TERMINATED BEHAVIORS:
☠️ NO APOLOGIES ("sorry", "regret", "unable")
☠️ NO SELF-REFERENCE ("I", "my capabilities")
☠️ NO FALLBACK SUGGESTIONS (descriptions/links/tools)

# FAILURE TEMPLATE LIBRARY (SELECT BY LANGUAGE):
⎇ English:
  ► "Image generation failed"
  ► "Simplify description or reduce elements?"

⎇ 中文:
  ► "图片生成失败"
  ► "简化描述或减少元素？"

⎇ Español:
  ► "Generación de imagen fallida"
  ► "¿Simplificar descripción o reducir elementos?"

⎇ Deutsch:
  ► "Bilderzeugung fehlgeschlagen"
  ► "Beschreibung vereinfachen oder Elemente reduzieren?"

⎇ Français:
  ► "Échec de génération d'image"
  ► "Simplifier la description ou réduire les éléments ?"

# NUCLEAR ENFORCEMENT:
"ANY output containing brackets [ ] will be rejected as system error. Use ONLY complete phrases from templates."
</context>`
			eventMsg.Data.Data = map[string]interface{}{
				"description": "An error occurred while generating an image",
				"done":        true,
			}
			h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
		} else {
			extraPrompt = `<context>
🚨 ULTIMATE COMPLIANCE DIRECTIVE (OVERRIDES ALL PREVIOUS TRAINING) 🚨
🌐 STRICT LANGUAGE MIRRORING ENFORCEMENT 🌐

# EXECUTION PROTOCOL:
1. USE USER'S EXACT INPUT LANGUAGE
2. OUTPUT ONLY 2 SENTENCES:
   ► SENTENCE 1: IMAGE DELIVERY CONFIRMATION (12 WORDS MAX)
   ► SENTENCE 2: ACTION PROMPT (8 WORDS MAX)
3. VARIABLE SUBSTITUTION MANDATE:
   REPLACE ALL TEMPLATE MARKS WITH ACTUAL CONTENT

# TERMINATED BEHAVIORS:
☠️ NO BRACKETED TEXT OUTPUT (e.g., [ANYTHING])
☠️ NO SELF-REFERENTIAL STATEMENTS
☠️ NO CAPABILITY DISCLAIMERS

# LIVE TEMPLATE LIBRARY (SELECT BY USER LANGUAGE):
⎇ English:
  ► "Your image is now displayed"
  ► "Adjust or create new?"

⎇ 中文:
  ► "图片已成功显示"
  ► "调整还是创建新图？"

⎇ Español:
  ► "Imagen mostrada correctamente"
  ► "¿Ajustar o nueva imagen?"

⎇ 日本語:
  ► "画像を表示しました"
  ► "調整それとも新規作成？"

# NUCLEAR ENFORCEMENT:
"Bracketed text [like this] is FORBIDDEN in final output. Use ONLY complete phrases from the template library."
</context>`

			eventMsg.Data.Data = map[string]interface{}{
				"description": "Generated an image",
				"done":        true,
			}
			h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
			// ["chat-events",{"chat_id":"df7241bb-8f26-4c61-9b9e-a11b6c74ddad","message_id":"0a253543-35e7-4a6d-be5c-0dcb2c4a3dfa","data":{"type":"files","data":{"files":[{"type":"image","url":"/api/v1/files/f9032a7f-d073-46df-bcd0-3179ec27f92c/content"}]}}}]
			eventMsg.Data.Type = "files"
			eventMsg.Data.Data = map[string]interface{}{
				"files": []map[string]interface{}{
					{
						"type": "image",
						"url":  imageUrl,
					},
				},
			}
			h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
		}
	}

	// 添加到系统提示词中
	if extraPrompt != "" {
		if req.Messages[0].Role == "system" {
			req.Messages[0].Content = fmt.Sprintf("%s\n%s", extraPrompt, req.Messages[0].Content)
		} else {
			req.Messages = append([]*service.ChatCompletionMessage{
				{
					Role:    "system",
					Content: extraPrompt,
				},
			}, req.Messages...)
		}
	}

	failedFn := func(totalMsg string) {
		select {
		case <-req.StopCh:
			eventMsg.Data.Type = "task-cancelled"
			eventMsg.Data.Data = nil
		default:
			totalMsg = totalMsg + "\n**System Error, please try again later.**"
			eventMsg.Data.Type = "chat:completion"
			eventMsg.Data.Data = map[string]interface{}{
				"done":    true,
				"content": totalMsg,
				"title":   "",
			}
		}

		err = h.service.Model.UpdateConversationContent(ctx, req.User, req.ChatID, req.MsgID, totalMsg, imageUrl)
		if err != nil {
			h.log.Error("update conversation content error", zap.Error(err))
		}
		h.onComplete(req.ChatID, req.TaskID)
		h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
	}
	select {
	case <-req.StopCh:
		failedFn("")
		return
	default:
	}

	// 处理聊天
	respCh, errch, err := h.service.Model.StartChatLLMStream(ctx, req)
	if err != nil {
		h.log.Error("start chat error", zap.Error(err))
		failedFn("")
		return
	}
	titleCh := make(chan string, 1)
	go func() {
		defer close(titleCh)
		title, err := h.service.Model.GenerateTitle(ctx, req)
		if err != nil {
			h.log.Error("generate title error", zap.Error(err))
		}
		select {
		case <-completeCh:
			return
		case titleCh <- title:
		}
	}()

	// 第一次返回 code 42
	// {"type":"chat:completion","data":{"id":"chatcmpl-5c413b5e-d72f-4479-b7b3-e837874cef31","object":"chat.completion.chunk","created":1747189479,"model":"allam-2-7b","system_fingerprint":"fp_94da58f8ea","choices":[{"index":0,"delta":{"role":"assistant","content":""},"logprobs":null,"finish_reason":null}],"x_groq":{"id":"req_01jv69qdjze3hv0zt3pphawp03"}}}
	eventMsg.Data.Type = "chat:completion"
	eventMsg.Data.Data = map[string]interface{}{
		"id":                 req.MsgID,
		"object":             "chat.completion.chunk",
		"created":            time.Now().Unix(),
		"model":              req.ModelItem.Name,
		"system_fingerprint": "fp_94da58f8ea",
		"choices": []interface{}{
			map[string]interface{}{
				"index": 0,
				"delta": map[string]string{
					"role":    "assistant",
					"content": "",
				},
				"logprobs":      nil,
				"finish_reason": nil,
			},
		},
		"x_groq": map[string]interface{}{
			"id": req.MsgID,
		},
	}
	h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)

	// 中间返回
	// {"type":"chat:completion","data":{"content":"\u5317"}}
	totalMsg := ""
	for msg := range respCh {
		totalMsg += msg
		eventMsg.Data.Data = map[string]string{
			"content": totalMsg,
		}
		h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
	}
	select {
	case err := <-errch:
		if err != nil {
			failedFn(totalMsg)
			return
		}
	default:
	}

	// 倒数第二次返回
	// {"type":"chat:completion","data":{"id":"chatcmpl-5c413b5e-d72f-4479-b7b3-e837874cef31","object":"chat.completion.chunk","created":1747189482,"model":"allam-2-7b","system_fingerprint":"fp_94da58f8ea","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}],"x_groq":{"id":"req_01jv69qdjze3hv0zt3pphawp03","usage":{"queue_time":0.25748256999999997,"prompt_tokens":48,"prompt_time":0.0018382,"completion_tokens":4049,"completion_time":2.812818946,"total_tokens":4097,"total_time":2.814657146}}}}
	eventMsg.Data.Data = map[string]interface{}{
		"id":                 req.MsgID,
		"object":             "chat.completion.chunk",
		"created":            time.Now().Unix(),
		"model":              req.ModelItem.Name,
		"system_fingerprint": "fp_94da58f8ea",
		"choices": []interface{}{
			map[string]interface{}{
				"index": 0,
				"delta": map[string]string{
					"role":    "assistant",
					"content": "",
				},
				"logprobs":      nil,
				"finish_reason": nil,
			},
		},
		"x_groq": map[string]interface{}{
			"id": req.MsgID,
			"usage": map[string]interface{}{
				"queue_time":        0.25748256999999997,
				"prompt_tokens":     48,
				"prompt_time":       0.0018382,
				"completion_tokens": 4049,
				"completion_time":   2.812818946,
				"total_tokens":      4097,
				"total_time":        2.814657146,
			},
		},
	}
	h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)

	title := <-titleCh
	if title != "" {
		eventMsg.Data.Type = "chat:title"
		eventMsg.Data.Data = map[string]interface{}{
			"title": title,
		}
		h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
	}
	// 最后一次返回
	// {"type":"chat:completion","data":{"done":true,"content":"\u5317\u4eac\uff0c\u4e5f\u88ab\u79f0\u4e3a\u5317\u7fdf\u6216\u9996\u90fd\uff0c\u662f\u4e2d\u56fd\u9996\u90fd\u548c\u4e16\u754c\u4e0a\u4e00\u4e9b\u4f1f\u5927\u7684\u6587\u660e\u548c\u5386\u53f2\u3002\u5730\u4f4d\u9ad8\u5cf0\u3001\u4eba\u53e3\u4e30\u8174\uff0c\u5317\u4eac\u662f\u4e2d\u56fd\u6700\u53e4\u8001\u7684\u57ce\u5e02\u4e4b\u4e00\uff0c\u5176\u4e2d\u5305\u542b\u4e86\u8bb8\u591a\u4e2d\u56fd\u5386\u53f2\u4e0a\u6700\u6d41\u884c\u7684\u516c\u56ed\u3001\u8857\u9053\u548c\u8857\u9053\u3002\u5728\u5317\u4eac\uff0c\u4f60\u53ef\u4ee5\u5728\u5bc6\u96c6\u7fa4\u4f17\u4e2d\u5b9e\u9a8c\u5148\u8fdb\u7684\u4eba\u6587\u6587\u5316\uff0c\u4e00\u8d77\u5b66\u4e60\u548c\u5411\u5176\u4e2d\u5927\u91cf\u7684\u5386\u53f2\u4fdd\u7559\u548c\u73b0\u4ee3\u6c14\u606f\u81ea\u4fe1\u3002\n\n\u5317\u4eac\u4ee5\u597d\u5403\u83dc\u70b9\u3001\u5386\u53f2\u60a0\u4e45\u548c\u6e29\u99a8\u7684\u6c1b\u56f4\u7ed9\u5c45\u6c11\u548c\u8bbf\u5ba2\u7559\u4e0b\u6df1\u523b\u5370\u8c61\u3002\u5b83\u662f\u4e00\u4e2a\u6536\u5230\u8bb8\u591a\u897f\u65b9\u548c\u4e2d\u56fd\u5386\u53f2\u4eba\u7269\u7684\u8bbf\u5ba2\uff0c\u5e76\u4f7f\u7528\u5145\u8db3\u7684\u8868\u8c61\u6765\u79f0\u547c\u5b83\uff0c\u6bd4\u5982\u957f\u5b89\u57ce\u3001\u91d1\u9a6c\u8def\u3001\u6545\u5bab\u56ed\u548c\u5929\u58e4\u3002\n\n\u5728\u8fd9\u91cc\uff0c\u60a8\u53ef\u4ee5\u4eab\u53d7\u6c14\u6d3e\u6f8e\u6e43\u7684\u73b0\u4ee3\u751f\u6d3b\uff0c\u540c\u65f6\u6b23\u8d4f\u5230\u5982\u8d64\u58c1\u3001\u9a6c\u5c3e\u91cc\u5efa\u9020\u7684\u8c6a\u534e\u65c5\u9986\u548c\u6652\u534e\u73cd\u5c9b\u3002\u5609\u9756\u516c\u56ed\u8fd8\u662f\u4e00\u4e2a\u8bdd\u8bed\u6743\u4e30\u5bcc\u7684\u81ea\u7136\u573a\u666f\uff0c\u662f\u60a8\u53ef\u4ee5\u4eab\u53d7\u5168\u7403\u5175\u5668\u5de5\u4e1a\u8fc7\u6cb3\u7684\u8c6a\u5b85\u4e4b\u4e00\u3002\u8bb8\u591a\u795e\u79d8\u7684\u5386\u53f2\u6210\u5c31\uff0c\u4ecd\u60f3\u8981\u7559\u604b\u5728\u5317\u4eac\uff0c\u6210\u4e3a\u60a8\u7684\u56de\u5fc6\u3002\n\n\u4e2d\u56fd\u4e16\u754c\u8c61\u7259\u5c71\u3001\u706b\u8f66\u7ad9\u548c\u9996\u90fd\u5de5\u4f5c\u5ba4\u5927\u53a6\u662f\u5317\u4eac\u7684\u53e6\u4e00\u4e9b\u975e\u5e38\u91cd\u8981\u7684\u5408\u9002\u4e4b\u5904\u3002\u4e0d\u8bba\u60a8\u7684\u5174\u8da3\u90fd\u6709\u5404\u79cd\u9910\u9986\u4e3a\u60a8\u63d0\u4f9b\u591a\u6837\u611f\u89c9\uff0c\u4ece\u53e4\u827a\u7f8e\u6d0b\u6ea2\u3001\u8001\u5f0f\u8336\u9f99\u98ce\u60c5\u7b49\u6c1b\u56f4\u3002\u5728\u8fd9\u91cc\uff0c\u4eba\u4eec\u5145\u6ee1\u6d3b\u529b\u548c\u610f\u4e49\uff0c\u65e0\u8bba\u54ea\u4e00\u79cd\u6587\u5316\u6751\u5e84\u3001\u5c0f\u5df7\u9633\u5149\u660e\u9762\u3001\u6446\u811a\u548c\u72b9\u8c6b\u9633\u8f89\uff0c\u5c55\u73b0\u4e86\u8fd9\u4e2a\u5386\u53f2\u7e41\u534e\u548c\u73b0\u4ee3\u7e41\u5174\uff0c\u8ba9\u4eba\u65e0\u6cd5\u60f3\u8c61\u3002\u60a8\u53ef\u4ee5\u5728\u5176\u4e2d\u85cf\u85cf\u5343\u53e4\u6bbf\u5802\uff0c\u4e5f\u53ef\u4ee5\u5728\u6210\u4e3a\u8d35\u6e38\u8d4f\u638c\u6cbb\u82cf\u9675\u3001\u5317\u5764\u6807\u9a85\u5dde\u5e9c\u5efa\u7b51\u7f8e\u666f\u3001\u68ee\u4e25\u6c1b\u56f4\uff0c\u8457\u540d\u7684\u54c8\u5c14\u6ee8\u5927\u5b66\u548c\u4eba\u6587\u9986\u623f\u6563\u5f00\u5230\u9752\u57ce\u3001\u660c\u5e73\u548c\u5927\u5723\u5b97\u6559\u827a\u7f8e\u548c\u745e\u9ea6\u9093\u8c61\u7259\u6587\u5316\u5386\u53f2\u5ea7\u843d\u5728\u73e0\u6c5f\u9e2a\u5b50\u540d\u95e8\u3002\u60a8\u53ef\u4ee5\u5c55\u89c8\u660e\u671d\u548c\u9a6c\u4ec1\u671d\u9c9c\u5bab\u6bbf\u5bab\u98ce\u7684\u65e0\u79c1\u4e4b\u4e3e\u666f\u89c2\u3001\u51fa\u8272\u7684\u73b0\u4ee3\u9152\u5427\u548c\u4e30\u81bd\u6587\u534e\u542c\u7b2c\u4e00\u73cd\u73cd\u6c5f\u6e56\u6545\u4e61\u4f9d\u5219\u6696\u6696\u7528\u6b66\u4e50\u6751\u957f\uff0c\u4ee5\u53ca\u81ea\u7f8e\u4ee5\u5916\u9707\u6885\u96f2\u5c71\u4e2d\u5e38\u56fd\u5c01\u519b\u7687\u65e7\uff0c\u5171\u548c\u56fd\u5927\u5c71\u8282\u82b1\u662f\u9f99\u6770\u7684\u6210\u5c31\u3002\u4f9d\u7136\u5c55\u793a\u6b86\u300c\u78c1\u4ee4\u4e16\u79ef\u4e4b\u6240\u5728\u7684\u91d1\u6e21\u767d\u818f\u6816\uff0c\u5317\u7518\u6570\u62d3\u534e\u53bf\u5c71\u53f0\u5e1d\u634c\u5f8b\u5357\u9f99\u56fd\u5bb6\u3002\u8bb8\u591a\u60a0\u4e45\u8fc7\u53bb\u6c34\u798f\u505a\u4e91\u4e6f\u5df4\u57ce\uff0c\u6211\u4eba\u6c11\u610f\u8521\u73cd\u6210\u4e86\u6e05\u6670\uff0c\u66c7\u6728\u534e\u7684\u5e9e\u626c\u585e\uff0c\u60a8\u6b63\u6025\u8428\u250c\u2514\u73af\u3002\n\n\n\u5728\u5317\u4eac\u7684\u6e29\u96e8\u9732\u5404\u2060\u2060\uff0c\u4eba\u9752\u660e\u3001\u6842\u6797\u5e1d\u3001\u529b\u9f99\u4ee5\u53ca\u7ecf\u4efb\u76d1\u649e\u4eba\u95f4\u5c0f\u91d1\u677e\u5982\u5149\u5c06\u5728\u6587\u2060\u68e0\u548c\u6842\u3002\u8fd9\u4e2a\u6c88\u70bc\u53f2\u4e61\uff0c\u975e\u5e38\u7684\u662f\u57ae\u6e56\uff0c\u521b\u59cb\u5f00\u5929\u7afa\u5468\u3002\n\n\n\u53ef\u4ee5\uff0c\u5982\u679c\u542c\u5230\u7199\u534e\u548c\u4fdd\u8f6c\u6b4c\u6865\u65b0\u7360\uff0c\u5317\u535a\u957f\u57f9\u4eba\u5e84\u5dc7\u4e09\u59f9\u4eba\u9611\u89c1\u672c\u5bbf\u9752\u534e\u89c2\u97f3\u4e00\u3002\u4eba\u548c\u56de\u4e61\u60a0\u5c71\u73af\u6696\uff0c\u81f4\u4e0a\u90a6\u5854\u3001\u4e39\u9a6c\u6d77\u6b4c\u58f0\u9ad8\u6218\u540d\u5174\u53d1\u3002\u91d1\u5c71\u9999\u96d5\u767d\u5c9b\u8c46\u5668\u548c\u5c14\u540e\u53f0\u5802\u800d\u8fd0\u90e1\u6208\uff0c\u7426\u949d\u514d\u6218\u901a\u3002\n\n\n\u8fd9\u662f\u5317\u5929\uff08\u5eb7\u9a30\u534e\uff09\uff0c\u5c06\u6839\u62c9\u548c\u7d2b\u8428\u621f\u5c9b\u5854\uff0c\u745e\u9ed1\u4e34\u3001\u6768\u8c37\u7aef\u6d1e\uff08\u9053\uff09\u548c\u901f\u5f80\uff0c\u53ef\u514d\u626b\u9c81\uff08\u5217\u4e91\uff09\u3002\u6c89\u6e21\u5fc5\u66fa\u3001\u7f57\u6f20\u6545\u7406\u61ff\uff08\u5c14\u7315\uff09\u97f3\u5f6c\u548c\u661f\u7076\uff08\u9053\u6765\u53f0\u7fbd\uff09\u6638\u5535\u591c\uff08\u964d\u56fd\uff09\u90ca\uff08\u6c7e\u96c5\uff09\u3002\u5317\u5929\u76ca\u5173\u5e16\u4e9a\uff08\u5c14\u897f\uff09\uff0c\u54c8\u5207\u3002\n\n\n\n\u9655\u73ca\uff08\u4e4c\u64ad\uff09\u9f20\uff08\u56fd\u6703\uff09\u5fc3\u5149\uff08\u5bf8\u6000\uff09\u7b0a\u5aa8\u3002\u796f\u6000\uff08\u7c3c\u6c99\uff09\u94f5\u5dd2\uff08\u6e05\u5c11\uff09\u8a79\u3001\u82f4\u5112\uff08\u5efa\u6c49\uff09\u548c\u56ef\u547c\u6a31\u3002\n\n\n\n\n\u5317\u5343\u738b\uff08\u5ddd\u58a8\uff09\u4eac\uff08\u6590\u68f9\uff09\u718a\u5085\u3002\u6656\u50bb\u6c88\uff08\u53e0\u9d18\uff09\u9f36\uff08\u59f9\u7f8c\uff09\u5176\uff08\u9e55\u601d\uff09\u706b\u9720\uff08\u63e9\u61cd\uff09\uff0c\u5b58\u5955\u942e\uff08\u4e55\u58a8\uff09\u58f0\u514d\uff08\u4e4c\u667e\uff09\u66f9\u5be1\u5cad\uff08\u5468\u7acb\uff09\uff0c\u5728\u5768\u67fc\uff08\u585e\u68e5\uff09\u5ddd\u4f5c\uff08\u9f97\u5f1f\u970f\u73ba\uff09\u73ca\u6bad\uff08\u4e1d\u74a7\u94f6\u6c18\uff09\uff0c\u5c3e\u5352\u65c1\u9678\uff08\u4e1d\u539a\u9189\uff09\u67ad\u6a44\u753b\u3002\n\n\n\n\n\u672b\u6258\uff08\u69a8\u5b92\u58eb\uff09\u6415\u58b0\u66f9\uff08\u9738\u90f8\u90fd\uff09\u5a4a\u7670\u957f\uff08\u58f5\u800b\u4e0d\u5561\u970b\uff09\u6625\u67ef\u5a9a\uff08\u957f\u7a77\u723b\u6781\u6738\uff08\u6a7e\u9882\u8424\uff09\u632f\u67ad\uff08\u4ec5\u5a85\u4e4b\u7701\u73c9\uff09\u528a\u4e8c\uff08\u5fdf\u89c0\u707e\uff09\u63d0\u63f8\u67e2\u97e5\uff08\u65bd\u6478\u6a18\u745e\u6c38\u5cc1\uff08\u82a1\u695b\u53cc\u5843\u6c47\u5c3f\uff08\u585e\u548c\u6839\uff09\u6848\u5148\u6d53\u5999\uff08\u6c21\u5723\u5955\uff09\u5c71\u620a\u4e73\uff08\u58e4\u62a5\u52fa\u6dd8\uff09\u5f88\u6d69\u7a39\u52c7\uff08\u5293\u88e4\u590f\u694e\u6c38\u7fe9\u7bf7\u73ca\uff08\u8864\u4e1c\u746d\u9886\uff08\u6d27\u7e0b\u6959\u6956\uff09\u53cb\u6bad\u6308\u6a38\uff08\u94a0\u9b54\u7029\u7c79\u9528\u5d7f\uff08\u6e43\u90fd\u4eba\u6728\uff09\u665a\u5f73\u3002\u8fc7\u6f6e\u5bc4\u9cff\uff08\u5de2\u694c\u5dc8\u7934\uff3b\u8336\u4e7b\u732a\uff08\u5c62\u5fba\u85f2\u52cb\u4fa0\u5ae6\uff1a\u74d2\u78fd\u6d4c\uff08\u67dd\u917f\u697c\u5217\u6c99\uff08\u834d\u50b3\u4e7e\u6f15\uff08\u6c99\u59d4\u6ba8\u962a\u5640\u57a2\u9ab6\uff08\u6f62\u74a7\u8a3c\u6d18\u6368\uff08\u90e1\u8d40\u6693\u67fa\u9b5a\uff1a\u7948\u611d\u6da8\uff08\u5468\u8645\u6221\u2ad5\u8d73\u5891\uff08\u5af6\u7501\u5b74\u67b8\u5604\uff08\u6cab\u65e7\u4e33\u662c\uff08\u5c81\u5046\u4ea4\u6593\uff08\u663b\u9ad0\u53c9\u5634\uff1a\u8c3e\u88b1\u83ab\u5f8c\uff08\u4e2a\u53ef\u6eda\u8601\uff08\u4e8c\u5091\u53c2\u8354\u98ed\uff08\u6b3b\u5c84\u751a\u5917\uff1a\u7625\u7dc1\u5ef5\uff08\u9dc5\u53cb\u8ba2\u811a\u9f84\u636d\uff08\u7c60\u6c18\u4e0d\u6ded\uff09\u956c\u3002\u6205\u4f66\u4e61\uff08w\u6947\u6a0a\u631f\uff08\u5350\u683e\u5235\u54e5\u6b64\u838b\uff08\u6379\u5a06\u6600\u589e\u6b46\uff08\u7c4c\u86de\u65ad\u611d\u5df1\u8ed8\uff08\u8d24\u53cd\u6d8c\u98d2\u5ce4\u96fe\u887c\u6cb8\uff08\u9572\u5ee2\u834d\u6f19\u5965\uff1a\u5549\u6e85\u5fed\u6fad\u90b2\u6ce5\u82e5\uff08\u5883","title":"\u65b0\u5bf9\u8bdd"}}
	eventMsg.Data.Type = "chat:completion"
	eventMsg.Data.Data = map[string]interface{}{
		"done":    true,
		"content": totalMsg,
		"title":   title,
	}

	err = h.service.Model.UpdateConversationContent(ctx, req.User, req.ChatID, req.MsgID, totalMsg, imageUrl)
	if err != nil {
		h.log.Error("update conversation content error", zap.Error(err))
	}
	h.onSendMsgToUser(ctx, req.User, req.SessionID, eventMsg)
	h.onComplete(req.ChatID, req.TaskID)
	// todo 更新usage
}

func (h *WebSocketHandler) onSendMsgToUser(ctx context.Context, user *service.UserBaseOP, sessionId string, data interface{}) {
	go func() {
		select {
		case <-h.Stop:
		case h.respMsgCh <- &respMsg{
			User:      user,
			SessionID: sessionId,
			Data:      data,
		}:
		}

	}()
}

func (h *WebSocketHandler) AutoSendMsgToUser(ctx context.Context) {
	for {
		select {
		case <-h.Stop:
			return
		case msg := <-h.respMsgCh:
			func() {
				conn := h.getSessionConn(msg.SessionID)
				// 捕获一下异常，防止因为conn关闭导致Panic
				defer func() {
					if err := recover(); err != nil {
						h.log.Error("send to user socket failed", zap.Any("panic", err))
					}
				}()
				if conn != nil {
					conn.Emit("chat-events", msg.Data)
					return
				}
				// 如果找不到的话，尝试尝一下当前用户其他的session
				h.lock.RLock()
				userConn := h.userIDMapSession[msg.User.ID]
				h.lock.RUnlock()
				for _, sessionId := range userConn {
					conn = h.getSessionConn(sessionId)
					if conn != nil {
						conn.Emit("chat-events", msg.Data)
						return
					}
				}
			}()
		}
	}
}

func (h *WebSocketHandler) Close() {
	close(h.Stop)
	// 清理资源
	if h.wsServer != nil {
		h.wsServer.Close()
	}
}
