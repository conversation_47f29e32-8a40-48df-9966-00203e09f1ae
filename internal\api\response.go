package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// 业务状态码
const (
	CodeSuccess      = 0   // 成功
	CodeUnauthorized = 401 // 未授权
	CodeClientError  = 400 // 客户端错误
	CodeServerError  = 500 // 服务器错误
)

// Response 统一响应结构
type Response struct {
	Code  int         `json:"code"`            // 业务状态码
	Data  interface{} `json:"data"`            // 响应数据
	Msg   string      `json:"msg"`             // 用户消息
	Error string      `json:"error,omitempty"` // 错误详情（仅在开发环境显示）
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, data)
}

// Fail 错误响应
func Fail(c *gin.Context, code int, userMsg string, error error) {
	// 在非生产环境显示错误详情
	_, isClientError := error.(*clientError)
	if gin.Mode() != gin.ReleaseMode || isClientError {
		userMsg = error.Error()
	}

	c.JSON(httpStatusCode(code), userMsg)
}

// httpStatusCode 将业务状态码映射为HTTP状态码
func httpStatusCode(bizCode int) int {
	switch bizCode {
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeClientError:
		return http.StatusBadRequest
	case CodeServerError:
		return http.StatusInternalServerError
	default:
		return http.StatusOK
	}
}

type clientError struct {
	Msg string `json:"msg"`
}

func (e *clientError) Error() string {
	return e.Msg
}

func NewClientError(msg string) *clientError {
	return &clientError{Msg: msg}
}
