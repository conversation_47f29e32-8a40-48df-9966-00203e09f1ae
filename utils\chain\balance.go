package chain

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

// GetErc20ContractBalance 获取ERC20合约余额
func GetErc20ContractBalance(ctx context.Context, chainUrl, contractAddress, userAddress string, blockNumber uint64) (*big.Int, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, fmt.Errorf("dial chain url error: %v", err)
	}

	data, err := erc20Abi.Pack("balanceOf", common.HexToAddress(userAddress))
	if err != nil {
		return nil, fmt.Errorf("pack balanceOf error: %v", err)
	}

	var blockNumberPtr *big.Int
	if blockNumber > 0 {
		blockNumberPtr = big.NewInt(int64(blockNumber))
	}

	contractAddr := common.HexToAddress(contractAddress)
	balance, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddr,
		Data: data,
	}, blockNumberPtr)
	if err != nil {
		return nil, fmt.Errorf("call contract error: %v", err)
	}

	var s interface{}
	err = erc20Abi.UnpackIntoInterface(&s, "balanceOf", balance)
	if err != nil {
		return nil, fmt.Errorf("unpack balanceOf error: %v", err)
	}
	fmt.Println("s", s)
	balanceInt, ok := s.(*big.Int)
	if !ok {
		return nil, fmt.Errorf("balanceOf is not a big.Int")
	}
	return balanceInt, nil
}

type UserDepositBalance struct {
	Total   *big.Int
	Current *big.Int
}

func GetDepositBalance(ctx context.Context, chainUrl, depositContractAddress, userAddress string, blockNumber uint64) (*UserDepositBalance, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}

	data, err := depositAbi.Pack("getUserBalance", common.HexToAddress(userAddress))
	if err != nil {
		return nil, err
	}

	var blockNumberPtr *big.Int
	if blockNumber > 0 {
		blockNumberPtr = big.NewInt(int64(blockNumber))
	}

	contractAddr := common.HexToAddress(depositContractAddress)
	res, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddr,
		Data: data,
	}, blockNumberPtr)
	if err != nil {
		return nil, err
	}

	balance := new(UserDepositBalance)
	err = depositAbi.UnpackIntoInterface(balance, "getUserBalance", res)
	if err != nil {
		return nil, err
	}

	return balance, nil
}

func TransferErc20(ctx context.Context, chainUrl, contractAddress string, fromKey *ecdsa.PrivateKey, to string, amount *big.Int) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", fmt.Errorf("dial chain url error: %v", err)
	}
	contract := bind.NewBoundContract(common.HexToAddress(contractAddress), erc20Abi, client, client, client)
	if err != nil {
		return "", fmt.Errorf("create contract error: %v", err)
	}
	auth, err := createLatestAuth(client, fromKey, contractAddress)
	if err != nil {
		return "", fmt.Errorf("create auth error: %v", err)
	}
	tx, err := contract.Transact(auth, "transfer", common.HexToAddress(to), amount)
	if err != nil {
		return "", fmt.Errorf("transfer erc20 error: %v", err)
	}
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}
	return tx.Hash().Hex(), nil
}

func DepositBalance(ctx context.Context, chainUrl, depositContractAddress string, fromKey *ecdsa.PrivateKey, amount *big.Int) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(depositContractAddress), depositAbi, client, client, client)
	auth, err := createLatestAuth(client, fromKey, depositContractAddress)
	if err != nil {
		return "", err
	}
	tx, err := contract.Transact(auth, "deposit", amount)
	if err != nil {
		return "", err
	}
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}
	return tx.Hash().Hex(), nil
}

// 授权
func Approve(ctx context.Context, chainUrl, contractAddress string, fromKey *ecdsa.PrivateKey, spender string, amount *big.Int) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(contractAddress), erc20Abi, client, client, client)
	auth, err := createLatestAuth(client, fromKey, contractAddress)
	if err != nil {
		return "", err
	}
	tx, err := contract.Transact(auth, "approve", common.HexToAddress(spender), amount)
	if err != nil {
		return "", err
	}
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}
	return tx.Hash().Hex(), nil
}

type Erc20TransferEvent struct {
	ContractAddress string
	From            string
	To              string
	Value           *big.Int
	TxHash          string
	BlockNumber     uint64
}

func GetErc20TransferEvents(ctx context.Context, chainUrl string, contractAddresses []string, fromBlock, toBlock uint64) ([]Erc20TransferEvent, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}
	contractAddrs := make([]common.Address, len(contractAddresses))
	for i, contractAddress := range contractAddresses {
		contractAddrs[i] = common.HexToAddress(contractAddress)
	}
	filter := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: contractAddrs,
		Topics:    [][]common.Hash{{erc20Abi.Events["Transfer"].ID}},
	}
	logs, err := client.FilterLogs(ctx, filter)
	if err != nil {
		return nil, err
	}
	events := make([]Erc20TransferEvent, len(logs))
	for i, log := range logs {
		var event Erc20TransferEvent
		event.ContractAddress = log.Address.Hex()
		event.From = common.BytesToAddress(log.Topics[1].Bytes()).Hex()
		event.To = common.BytesToAddress(log.Topics[2].Bytes()).Hex()
		event.TxHash = log.TxHash.Hex()
		event.BlockNumber = log.BlockNumber
		err := erc20Abi.UnpackIntoInterface(&event, "Transfer", log.Data)
		if err != nil {
			return nil, err
		}
		events[i] = event
	}
	return events, nil
}

type UserBalanceUpdatedEvent struct {
	User            string
	NewBalance      *big.Int
	Directory       bool
	CurrentBalance  *big.Int
	TxHash          string
	BlockNumber     uint64
	ContractAddress string
}

func GetUsdtDepositEvents(ctx context.Context, chainUrl string, depositContractAddress string, fromBlock, toBlock uint64) ([]UserBalanceUpdatedEvent, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}
	contractAddr := common.HexToAddress(depositContractAddress)
	filter := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: []common.Address{contractAddr},
		Topics:    [][]common.Hash{{depositAbi.Events["UserBalanceUpdated"].ID}},
	}
	logs, err := client.FilterLogs(ctx, filter)
	if err != nil {
		return nil, err
	}
	events := make([]UserBalanceUpdatedEvent, len(logs))
	for i, log := range logs {
		var event UserBalanceUpdatedEvent
		event.User = common.BytesToAddress(log.Topics[1].Bytes()).Hex()
		event.ContractAddress = log.Address.Hex()
		event.TxHash = log.TxHash.Hex()
		event.BlockNumber = log.BlockNumber
		err := depositAbi.UnpackIntoInterface(&event, "UserBalanceUpdated", log.Data)
		if err != nil {
			return nil, err
		}
		events[i] = event
	}
	return events, nil
}
