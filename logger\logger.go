package logger

import (
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"topnetwork.ai/topai/chat-webserver/config"
)

// 自定义时间格式
func customTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006-01-02 15:04:05"))
}

// 自定义模块名编码器
func customNameEncoder(loggerName string, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(loggerName)
}

// LogEntry 表示一个日志条目
type LogEntry struct {
	Time       time.Time
	Level      zapcore.Level
	Message    string
	Fields     map[string]interface{}
	Caller     string
	StackTrace string
}

// LogAggregator 定义日志聚合接口
type LogAggregator interface {
	Send(entry *LogEntry) error
}

// ModuleLogger 模块日志器
type ModuleLogger struct {
	name         string
	logger       *zap.Logger
	level        zap.AtomicLevel
	context      map[string]interface{}
	aggregator   LogAggregator
	entryPool    *sync.Pool // 使用指针避免复制锁
	sensitiveMap map[string]struct{}
}

var (
	registry  sync.Map
	logConfig *config.LogConfig
	logWriter *lumberjack.Logger // 全局日志写入器
)

// InitLogger 初始化日志系统
func InitLogger(config *config.LogConfig) {
	logConfig = config

	// 确保日志目录存在
	dir := filepath.Dir(logConfig.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		panic("创建日志目录失败: " + err.Error())
	}

	// 初始化全局日志写入器
	logWriter = &lumberjack.Logger{
		Filename:   logConfig.Path,
		MaxSize:    logConfig.MaxSize,
		MaxBackups: logConfig.MaxBackups,
		MaxAge:     logConfig.MaxAge,
		Compress:   logConfig.Compress,
	}
}

// GetLogger 获取或创建模块日志器
func GetLogger(module string) *ModuleLogger {
	if logConfig == nil {
		panic("日志系统未初始化，请先调用 InitLogger")
	}

	if val, ok := registry.Load(module); ok {
		return val.(*ModuleLogger)
	}

	level := zapcore.InfoLevel
	switch logConfig.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	case "fatal":
		level = zapcore.FatalLevel
	}

	newLogger := &ModuleLogger{
		name:      module,
		level:     zap.NewAtomicLevelAt(level),
		context:   make(map[string]interface{}),
		entryPool: &sync.Pool{New: func() interface{} { return &LogEntry{} }},
		sensitiveMap: map[string]struct{}{
			"password":       {},
			"token":          {},
			"api_key":        {},
			"api_secret":     {},
			"api_token":      {},
			"access_token":   {},
			"refresh_token":  {},
			"session_token":  {},
			"session_id":     {},
			"session_key":    {},
			"session_secret": {},
		},
	}
	newLogger.initLogger()
	registry.Store(module, newLogger)
	return newLogger
}

// initLogger 初始化日志器
func (ml *ModuleLogger) initLogger() {
	// 创建日志编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "module",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder, // 大写日志级别
		EncodeTime:     customTimeEncoder,           // 自定义时间格式
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder, // 短格式调用者信息
		EncodeName:     customNameEncoder,          // 自定义模块名编码器
	}

	// 创建核心
	cores := []zapcore.Core{
		zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig), // 使用控制台编码器
			zapcore.AddSync(logWriter),               // 使用全局日志写入器
			ml.level,
		),
	}

	// 如果配置了同时输出到控制台
	if logConfig.Console {
		cores = append(cores, zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig),
			zapcore.AddSync(os.Stdout),
			ml.level,
		))
	}

	// 创建日志器
	ml.logger = zap.New(zapcore.NewTee(cores...),
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
		zap.Fields(zap.String("module", ml.name)),
	)
}

// SetAggregator 设置日志聚合器
func (ml *ModuleLogger) SetAggregator(aggregator LogAggregator) {
	ml.aggregator = aggregator
}

// AddSensitiveField 添加敏感字段
func (ml *ModuleLogger) AddSensitiveField(field string) {
	ml.sensitiveMap[field] = struct{}{}
}

// processSensitiveData 处理敏感数据
func (ml *ModuleLogger) processSensitiveData(fields ...zap.Field) {
	for _, field := range fields {
		if _, ok := ml.sensitiveMap[field.Key]; ok {
			field.String = "******"
		}
	}
}

// SetLevel 动态设置日志级别
func (ml *ModuleLogger) SetLevel(level zapcore.Level) {
	ml.level.SetLevel(level)
}

// WithContext 创建带上下文的日志器
func (ml *ModuleLogger) WithContext(fields ...zap.Field) *ModuleLogger {
	newLogger := &ModuleLogger{
		name:         ml.name,
		logger:       ml.logger.With(fields...),
		level:        ml.level,
		context:      make(map[string]interface{}),
		aggregator:   ml.aggregator,
		entryPool:    ml.entryPool, // 共享指针，避免复制锁
		sensitiveMap: ml.sensitiveMap,
	}
	for k, v := range ml.context {
		newLogger.context[k] = v
	}
	return newLogger
}

// Debug 输出调试日志
func (ml *ModuleLogger) Debug(msg string, fields ...zap.Field) {
	ml.processSensitiveData(fields...)
	ml.logger.Debug(msg, fields...)
	if ml.aggregator != nil {
		entry := ml.entryPool.Get().(*LogEntry)
		entry.Time = time.Now()
		entry.Level = zapcore.DebugLevel
		entry.Message = msg
		ml.aggregator.Send(entry)
		ml.entryPool.Put(entry)
	}
}

// Info 输出信息日志
func (ml *ModuleLogger) Info(msg string, fields ...zap.Field) {
	ml.processSensitiveData(fields...)
	ml.logger.Info(msg, fields...)
	if ml.aggregator != nil {
		entry := ml.entryPool.Get().(*LogEntry)
		entry.Time = time.Now()
		entry.Level = zapcore.InfoLevel
		entry.Message = msg
		ml.aggregator.Send(entry)
		ml.entryPool.Put(entry)
	}
}

// Warn 输出警告日志
func (ml *ModuleLogger) Warn(msg string, fields ...zap.Field) {
	ml.processSensitiveData(fields...)
	ml.logger.Warn(msg, fields...)
	if ml.aggregator != nil {
		entry := ml.entryPool.Get().(*LogEntry)
		entry.Time = time.Now()
		entry.Level = zapcore.WarnLevel
		entry.Message = msg
		ml.aggregator.Send(entry)
		ml.entryPool.Put(entry)
	}
}

// Error 输出错误日志
func (ml *ModuleLogger) Error(msg string, fields ...zap.Field) {
	ml.processSensitiveData(fields...)
	ml.logger.Error(msg, fields...)
	if ml.aggregator != nil {
		entry := ml.entryPool.Get().(*LogEntry)
		entry.Time = time.Now()
		entry.Level = zapcore.ErrorLevel
		entry.Message = msg
		ml.aggregator.Send(entry)
		ml.entryPool.Put(entry)
	}
}

// Fatal 输出致命错误日志
func (ml *ModuleLogger) Fatal(msg string, fields ...zap.Field) {
	ml.processSensitiveData(fields...)
	ml.logger.Fatal(msg, fields...)
	if ml.aggregator != nil {
		entry := ml.entryPool.Get().(*LogEntry)
		entry.Time = time.Now()
		entry.Level = zapcore.FatalLevel
		entry.Message = msg
		ml.aggregator.Send(entry)
		ml.entryPool.Put(entry)
	}
}

// Sync 同步日志
func (ml *ModuleLogger) Sync() error {
	return ml.logger.Sync()
}
