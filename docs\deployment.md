# TopAI Chat Server 部署指南

## 1. 部署环境要求

### 1.1 硬件要求

#### 最小配置（开发/测试环境）
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 10Mbps带宽

#### 推荐配置（生产环境）
- **CPU**: 4核心+
- **内存**: 8GB+ RAM
- **存储**: 100GB+ SSD
- **网络**: 100Mbps+带宽

### 1.2 软件要求

#### 基础环境
- **操作系统**: Ubuntu 20.04+, CentOS 8+, RHEL 8+
- **Go**: 1.23+
- **MySQL**: 8.0+
- **Git**: 2.x

#### 可选组件
- **Docker**: 20.10+
- **Kubernetes**: 1.20+
- **Nginx**: 1.18+
- **Redis**: 6.0+ (缓存，可选)

## 2. 本地部署

### 2.1 源码编译部署

#### 步骤1: 准备环境
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y git curl wget build-essential

# 安装Go
wget https://golang.org/dl/go1.23.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc

# 验证Go安装
go version
```

#### 步骤2: 安装MySQL
```bash
# 安装MySQL
sudo apt install -y mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库
sudo mysql -u root -p
```

```sql
-- 在MySQL中执行
CREATE DATABASE chat_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'chat_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON chat_server.* TO 'chat_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 步骤3: 部署应用
```bash
# 克隆代码
git clone https://github.com/topnetwork/chat-server.git
cd chat-server

# 下载依赖
go mod download

# 编译应用
go build -o chat-server cmd/server/main.go

# 创建配置目录
sudo mkdir -p /etc/chat-server
sudo mkdir -p /var/log/chat-server
sudo mkdir -p /var/lib/chat-server/uploads

# 复制配置文件
sudo cp config.example.yaml /etc/chat-server/config.yaml

# 编辑配置文件
sudo nano /etc/chat-server/config.yaml
```

#### 步骤4: 配置文件
```yaml
# /etc/chat-server/config.yaml
version: "1.0.0"

server:
  port: 8080
  timeout: 30s
  tls:
    enabled: false

database:
  host: localhost
  port: 3306
  username: chat_user
  password: "strong_password"
  database: chat_server
  parse_time: true
  collation: utf8mb4_unicode_ci
  pool:
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 1h
    conn_max_idletime: 30m

auth:
  secret: "your-jwt-secret-key-change-this"
  tokenTTL: 24h

log:
  level: info
  path: /var/log/chat-server/app.log
  console: false

service:
  file:
    max_size: 10485760
    path: /var/lib/chat-server/uploads
  
  openai:
    openai:
      base_url: "https://api.openai.com/v1"
      api_key: "your-openai-api-key"
  
  concurrency_limit:
    global_limit: 100
    user_limit: 10
    user_model_limit: 3
```

#### 步骤5: 数据库迁移
```bash
# 运行数据库迁移
mysql -u chat_user -p chat_server < internal/repository/migrations/0001_initial_schema.sql
mysql -u chat_user -p chat_server < internal/repository/migrations/0002_schema.sql
```

#### 步骤6: 创建系统服务
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/chat-server.service > /dev/null <<EOF
[Unit]
Description=TopAI Chat Server
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=chat-server
Group=chat-server
WorkingDirectory=/opt/chat-server
ExecStart=/opt/chat-server/chat-server -config /etc/chat-server/config.yaml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/chat-server /var/lib/chat-server

[Install]
WantedBy=multi-user.target
EOF

# 创建用户和目录
sudo useradd --system --shell /bin/false chat-server
sudo mkdir -p /opt/chat-server
sudo cp chat-server /opt/chat-server/
sudo chown -R chat-server:chat-server /opt/chat-server
sudo chown -R chat-server:chat-server /var/log/chat-server
sudo chown -R chat-server:chat-server /var/lib/chat-server

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable chat-server
sudo systemctl start chat-server

# 检查服务状态
sudo systemctl status chat-server
```

### 2.2 Nginx反向代理配置

#### 安装Nginx
```bash
sudo apt install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 配置Nginx
```bash
# 创建站点配置
sudo tee /etc/nginx/sites-available/chat-server > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /etc/ssl/certs/chat-server.crt;
    ssl_certificate_key /etc/ssl/private/chat-server.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 日志
    access_log /var/log/nginx/chat-server.access.log;
    error_log /var/log/nginx/chat-server.error.log;

    # 客户端配置
    client_max_body_size 10M;

    # WebSocket升级
    map \$http_upgrade \$connection_upgrade {
        default upgrade;
        '' close;
    }

    # 反向代理到应用
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection \$connection_upgrade;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket特定配置
    location /ws/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket超时设置
        proxy_read_timeout 86400;
    }

    # 静态文件（如果有）
    location /static/ {
        alias /var/lib/chat-server/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/chat-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2.3 SSL证书配置

#### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 3. Docker部署

### 3.1 Dockerfile
```dockerfile
# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o chat-server cmd/server/main.go

# Final stage
FROM alpine:latest

# 安装必要工具
RUN apk --no-cache add ca-certificates tzdata

# 创建非root用户
RUN addgroup -g 1001 app && \
    adduser -u 1001 -G app -s /bin/sh -D app

# 创建目录
RUN mkdir -p /app/logs /app/uploads /app/config && \
    chown -R app:app /app

# 切换到非root用户
USER app

WORKDIR /app

# 复制二进制文件
COPY --from=builder --chown=app:app /app/chat-server .

# 复制配置文件
COPY --chown=app:app config.example.yaml config/config.yaml

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动命令
CMD ["./chat-server", "-config", "config/config.yaml"]
```

### 3.2 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    container_name: chat-server
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=production
    volumes:
      - ./config/config.yaml:/app/config/config.yaml:ro
      - chat_logs:/app/logs
      - chat_uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - chat-network

  mysql:
    image: mysql:8.0
    container_name: chat-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: chat_server
      MYSQL_USER: chat_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./internal/repository/migrations:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    networks:
      - chat-network

  redis:
    image: redis:6.2-alpine
    container_name: chat-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - chat-network

  nginx:
    image: nginx:alpine
    container_name: chat-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - chat_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - chat-network

volumes:
  mysql_data:
  redis_data:
  chat_logs:
  chat_uploads:

networks:
  chat-network:
    driver: bridge
```

### 3.3 环境配置文件
```bash
# .env
MYSQL_ROOT_PASSWORD=strong_root_password
MYSQL_PASSWORD=strong_user_password
JWT_SECRET=your-jwt-secret-key
OPENAI_API_KEY=your-openai-api-key
```

### 3.4 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "部署TopAI Chat Server..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装"
    exit 1
fi

# 检查环境文件
if [ ! -f .env ]; then
    echo "错误: .env文件不存在"
    exit 1
fi

# 停止现有服务
echo "停止现有服务..."
docker-compose down

# 构建镜像
echo "构建Docker镜像..."
docker-compose build

# 启动服务
echo "启动服务..."
docker-compose up -d

# 等待服务就绪
echo "等待服务启动..."
sleep 30

# 健康检查
echo "执行健康检查..."
if curl -f http://localhost:8080/health; then
    echo "部署成功！"
else
    echo "部署失败，请检查日志："
    docker-compose logs app
    exit 1
fi

echo "服务已启动在 http://localhost:8080"
```

## 4. Kubernetes部署

### 4.1 命名空间
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: chat-server
  labels:
    name: chat-server
```

### 4.2 ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: chat-server-config
  namespace: chat-server
data:
  config.yaml: |
    version: "1.0.0"
    server:
      port: 8080
      timeout: 30s
    database:
      host: mysql-service
      port: 3306
      username: chat_user
      database: chat_server
      parse_time: true
      collation: utf8mb4_unicode_ci
    auth:
      tokenTTL: 24h
    log:
      level: info
      console: true
    service:
      file:
        max_size: 10485760
        path: /app/uploads
      concurrency_limit:
        global_limit: 100
        user_limit: 10
        user_model_limit: 3
```

### 4.3 Secret
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: chat-server-secret
  namespace: chat-server
type: Opaque
data:
  mysql-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
  openai-api-key: <base64-encoded-openai-key>
```

### 4.4 MySQL部署
```yaml
# k8s/mysql.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: chat-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-server-secret
              key: mysql-password
        - name: MYSQL_DATABASE
          value: chat_server
        - name: MYSQL_USER
          value: chat_user
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-server-secret
              key: mysql-password
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: mysql-storage
        persistentVolumeClaim:
          claimName: mysql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: mysql-service
  namespace: chat-server
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: chat-server
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
```

### 4.5 应用部署
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-server
  namespace: chat-server
  labels:
    app: chat-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chat-server
  template:
    metadata:
      labels:
        app: chat-server
    spec:
      containers:
      - name: chat-server
        image: topai/chat-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: chat-server-secret
              key: mysql-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: chat-server-secret
              key: jwt-secret
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: chat-server-secret
              key: openai-api-key
        volumeMounts:
        - name: config
          mountPath: /app/config
        - name: uploads
          mountPath: /app/uploads
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: chat-server-config
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: chat-server-service
  namespace: chat-server
spec:
  selector:
    app: chat-server
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: chat-server
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
```

### 4.6 Ingress配置
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chat-server-ingress
  namespace: chat-server
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/websocket-services: chat-server-service
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: chat-server-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chat-server-service
            port:
              number: 80
```

### 4.7 HPA自动伸缩
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: chat-server-hpa
  namespace: chat-server
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: chat-server
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 4.8 部署脚本
```bash
#!/bin/bash
# k8s-deploy.sh

set -e

echo "部署到Kubernetes..."

# 检查kubectl
if ! command -v kubectl &> /dev/null; then
    echo "错误: kubectl未安装"
    exit 1
fi

# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 创建Secret（需要先编码）
echo "请确保已正确配置Secret..."
kubectl apply -f k8s/secret.yaml

# 部署ConfigMap
kubectl apply -f k8s/configmap.yaml

# 部署MySQL
kubectl apply -f k8s/mysql.yaml

# 等待MySQL就绪
echo "等待MySQL启动..."
kubectl wait --for=condition=ready pod -l app=mysql -n chat-server --timeout=300s

# 部署应用
kubectl apply -f k8s/deployment.yaml

# 等待应用就绪
echo "等待应用启动..."
kubectl wait --for=condition=ready pod -l app=chat-server -n chat-server --timeout=300s

# 部署Ingress
kubectl apply -f k8s/ingress.yaml

# 部署HPA
kubectl apply -f k8s/hpa.yaml

echo "部署完成！"
echo "检查状态："
kubectl get pods -n chat-server
kubectl get svc -n chat-server
kubectl get ingress -n chat-server
```

## 5. 监控和日志

### 5.1 Prometheus监控
```yaml
# monitoring/prometheus.yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'chat-server'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: /metrics
```

### 5.2 Grafana仪表板
```json
{
  "dashboard": {
    "title": "TopAI Chat Server",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{path}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### 5.3 ELK日志收集
```yaml
# logging/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/chat-server/*.log
  fields:
    service: chat-server
  json.keys_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "chat-server-%{+yyyy.MM.dd}"
```

## 6. 备份和恢复

### 6.1 数据库备份
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
DB_NAME="chat_server"

mkdir -p $BACKUP_DIR

# 创建备份
mysqldump -u root -p$MYSQL_ROOT_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/chat_server_$DATE.sql

# 压缩备份
gzip $BACKUP_DIR/chat_server_$DATE.sql

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "备份完成: chat_server_$DATE.sql.gz"
```

### 6.2 文件备份
```bash
#!/bin/bash
# file-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
UPLOAD_DIR="/var/lib/chat-server/uploads"
BACKUP_DIR="/backup/files"

mkdir -p $BACKUP_DIR

# 创建tar备份
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C $UPLOAD_DIR .

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "uploads_*.tar.gz" -mtime +30 -delete

echo "文件备份完成: uploads_$DATE.tar.gz"
```

### 6.3 自动备份配置
```bash
# 添加到crontab
# 每天凌晨2点执行数据库备份
0 2 * * * /path/to/backup.sh

# 每天凌晨3点执行文件备份
0 3 * * * /path/to/file-backup.sh
```

## 7. 故障排除

### 7.1 常见问题

#### 服务无法启动
```bash
# 检查端口占用
sudo netstat -tulpn | grep :8080

# 检查配置文件
./chat-server -config /etc/chat-server/config.yaml -check

# 检查日志
sudo journalctl -u chat-server -f
```

#### 数据库连接问题
```bash
# 测试数据库连接
mysql -h localhost -u chat_user -p chat_server

# 检查MySQL状态
sudo systemctl status mysql

# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 内存不足
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head

# 调整Go GC
export GOGC=100  # 默认值，可以调低减少内存使用
```

### 7.2 性能调优

#### 数据库优化
```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 优化配置
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
max_connections = 200
```

#### 应用优化
```yaml
# 配置调优
server:
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 120s

database:
  pool:
    max_open_conns: 50
    max_idle_conns: 25
    conn_max_lifetime: 30m
```

## 8. 安全加固

### 8.1 防火墙配置
```bash
# UFW防火墙规则
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 8080/tcp  # 不直接暴露应用端口
sudo ufw enable
```

### 8.2 系统安全
```bash
# 禁用root登录
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 使用密钥登录
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config

# 重启SSH服务
sudo systemctl restart sshd

# 自动安全更新
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

### 8.3 应用安全
```yaml
# 配置安全选项
server:
  tls:
    enabled: true
    cert_file: /etc/ssl/certs/server.crt
    key_file: /etc/ssl/private/server.key
    
security:
  cors:
    allowed_origins: ["https://your-domain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
  
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
  content_security_policy:
    enabled: true
    policy: "default-src 'self'"
```

---

**文档版本**: v2.0  
**更新时间**: 2024-12-28  
**维护人员**: TopAI开发团队 