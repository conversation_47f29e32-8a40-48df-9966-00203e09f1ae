package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserShadowWalletRepository MySQL实现
type userShadowWalletRepository struct {
	db *gorm.DB
}

func NewUserShadowWalletRepository(db *gorm.DB) repository.UserShadowWalletRepository {
	return &userShadowWalletRepository{db: db}
}

func (r *userShadowWalletRepository) Create(ctx context.Context, wallet *repository.UserShadowWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

func (r *userShadowWalletRepository) GetByUserID(ctx context.Context, userID uint) (*repository.UserShadowWallet, error) {
	var wallet repository.UserShadowWallet
	err := r.db.WithContext(ctx).Where("user_id = ? AND is_deleted = 0", userID).First(&wallet).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get shadow wallet by user ID %d: %w", userID, err)
	}
	return &wallet, nil
}

func (r *userShadowWalletRepository) GetByPublicAddress(ctx context.Context, publicAddress string) (*repository.UserShadowWallet, error) {
	var wallet repository.UserShadowWallet
	err := r.db.WithContext(ctx).Where("public_address = ? AND is_deleted = 0", publicAddress).First(&wallet).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get shadow wallet by public address %s: %w", publicAddress, err)
	}
	return &wallet, nil
}

func (r *userShadowWalletRepository) Update(ctx context.Context, wallet *repository.UserShadowWallet) error {
	return r.db.WithContext(ctx).Save(wallet).Error
}

func (r *userShadowWalletRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.UserShadowWallet{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// UserShadowWalletBalanceRepository MySQL实现
type userShadowWalletBalanceRepository struct {
	db *gorm.DB
}

func NewUserShadowWalletBalanceRepository(db *gorm.DB) repository.UserShadowWalletBalanceRepository {
	return &userShadowWalletBalanceRepository{db: db}
}

func (r *userShadowWalletBalanceRepository) Create(ctx context.Context, balance *repository.UserShadowWalletBalance) error {
	return r.db.WithContext(ctx).Create(balance).Error
}

func (r *userShadowWalletBalanceRepository) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserShadowWalletBalance, error) {
	var balance repository.UserShadowWalletBalance
	err := r.db.WithContext(ctx).Where("user_id = ? AND currency = ?", userID, currency).First(&balance).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get shadow wallet balance for user %d currency %s: %w", userID, currency, err)
	}
	return &balance, nil
}

func (r *userShadowWalletBalanceRepository) UpdateBalanceByUserIDAndCurrency(ctx context.Context, userID uint, currency string, balance, accumulated string, lastBlockNumber uint64) error {
	return r.db.WithContext(ctx).Model(&repository.UserShadowWalletBalance{}).
		Where("user_id = ? AND currency = ?", userID, currency).
		Updates(map[string]interface{}{
			"balance":           balance,
			"accumulated":       accumulated,
			"last_block_number": lastBlockNumber,
			"updated_at":        time.Now(),
		}).Error
}

// UserShadowWalletBalanceRecordRepository MySQL实现
type userShadowWalletBalanceRecordRepository struct {
	db *gorm.DB
}

func NewUserShadowWalletBalanceRecordRepository(db *gorm.DB) repository.UserShadowWalletBalanceRecordRepository {
	return &userShadowWalletBalanceRecordRepository{db: db}
}

func (r *userShadowWalletBalanceRecordRepository) Create(ctx context.Context, record *repository.UserShadowWalletBalanceRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *userShadowWalletBalanceRecordRepository) BatchCreate(ctx context.Context, records []*repository.UserShadowWalletBalanceRecord) error {
	return r.db.WithContext(ctx).Create(records).Error
}

func (r *userShadowWalletBalanceRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserShadowWalletBalanceRecord, error) {
	var records []*repository.UserShadowWalletBalanceRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.UserShadowWalletBalanceRecordStatusUnconfirmed).Find(&records).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get unconfirmed shadow wallet balance records: %w", err)
	}
	return records, nil
}

func (r *userShadowWalletBalanceRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.UserShadowWalletBalanceRecord{}).
		Where("id = ?", id).
		Update("status", repository.UserShadowWalletBalanceRecordStatusConfirmed).Error
}

func (r *userShadowWalletBalanceRecordRepository) UpdateConfirmedAmountByID(ctx context.Context, id uint, amount string) error {
	return r.db.WithContext(ctx).Model(&repository.UserShadowWalletBalanceRecord{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status": repository.UserShadowWalletBalanceRecordStatusConfirmed,
			"amount": amount,
		}).Error
}

func (r *userShadowWalletBalanceRecordRepository) UpdateFailedByID(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.UserShadowWalletBalanceRecord{}).
		Where("id = ?", id).
		Update("status", repository.UserShadowWalletBalanceRecordStatusFailed).Error
}

func (r *userShadowWalletBalanceRecordRepository) GetLastByCurrencys(ctx context.Context, currencies []string) (*repository.UserShadowWalletBalanceRecord, error) {
	var record repository.UserShadowWalletBalanceRecord
	err := r.db.WithContext(ctx).Where("currency IN (?)", currencies).Order("id DESC").First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get last shadow wallet balance record by currencies %v: %w", currencies, err)
	}
	return &record, nil
}

// WalletTransferRecordRepository MySQL实现
type walletTransferRecordRepository struct {
	db *gorm.DB
}

func NewWalletTransferRecordRepository(db *gorm.DB) repository.WalletTransferRecordRepository {
	return &walletTransferRecordRepository{db: db}
}

func (r *walletTransferRecordRepository) Create(ctx context.Context, record *repository.WalletTransferRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *walletTransferRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	var records []*repository.WalletTransferRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.WalletTransferRecordStatusUnconfirmed).Find(&records).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get unconfirmed wallet transfer records: %w", err)
	}
	return records, nil
}

func (r *walletTransferRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	return r.db.WithContext(ctx).Model(&repository.WalletTransferRecord{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":                               repository.WalletTransferRecordStatusConfirmed,
			"block_number":                         blockNumber,
			"user_shadow_wallet_balance_record_id": walletBalanceRecordId,
		}).Error
}

func (r *walletTransferRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	return r.db.WithContext(ctx).Model(&repository.WalletTransferRecord{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       repository.WalletTransferRecordStatusFailed,
			"block_number": blockNumber,
		}).Error
}

func (r *walletTransferRecordRepository) GetConfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	var records []*repository.WalletTransferRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.WalletTransferRecordStatusConfirmed).Find(&records).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get confirmed wallet transfer records: %w", err)
	}
	return records, nil
}

func (r *walletTransferRecordRepository) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletTransferRecord, error) {
	var record repository.WalletTransferRecord
	err := r.db.WithContext(ctx).Where("tx_hash = ?", txHash).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get wallet transfer record by tx hash %s: %w", txHash, err)
	}
	return &record, nil
}

type walletReportCostRecordRepository struct {
	db *gorm.DB
}

func NewWalletReportCostRecordRepository(db *gorm.DB) repository.WalletReportCostRecordRepository {
	return &walletReportCostRecordRepository{db: db}
}

func (r *walletReportCostRecordRepository) Create(ctx context.Context, record *repository.WalletReportCostRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *walletReportCostRecordRepository) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletReportCostRecord, error) {
	var record repository.WalletReportCostRecord
	err := r.db.WithContext(ctx).Where("tx_hash = ?", txHash).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get wallet report cost record by tx hash %s: %w", txHash, err)
	}
	return &record, nil
}

func (r *walletReportCostRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.WalletReportCostRecord, error) {
	var records []*repository.WalletReportCostRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.WalletReportCostRecordStatusUnconfirmed).Find(&records).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get unconfirmed wallet report cost records: %w", err)
	}
	return records, nil
}

func (r *walletReportCostRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	return r.db.WithContext(ctx).Table(repository.WalletReportCostRecord{}.TableName()).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":                               repository.WalletReportCostRecordStatusConfirmed,
			"block_number":                         blockNumber,
			"user_shadow_wallet_balance_record_id": walletBalanceRecordId,
		}).Error
}

func (r *walletReportCostRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	return r.db.WithContext(ctx).Table(repository.WalletReportCostRecord{}.TableName()).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       repository.WalletReportCostRecordStatusFailed,
			"block_number": blockNumber,
		}).Error
}

type walletDepositRecordRepository struct {
	db *gorm.DB
}

func NewWalletDepositRecordRepository(db *gorm.DB) repository.WalletDepositRecordRepository {
	return &walletDepositRecordRepository{db: db}
}

func (r *walletDepositRecordRepository) Create(ctx context.Context, record *repository.WalletDepositRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *walletDepositRecordRepository) GetUnconfirmed(ctx context.Context) ([]*repository.WalletDepositRecord, error) {
	var records []*repository.WalletDepositRecord
	err := r.db.WithContext(ctx).Where("status = ?", repository.WalletDepositRecordStatusUnconfirmed).Find(&records).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get unconfirmed wallet deposit records: %w", err)
	}
	return records, nil
}

func (r *walletDepositRecordRepository) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	return r.db.WithContext(ctx).Table(repository.WalletDepositRecord{}.TableName()).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":                               repository.WalletDepositRecordStatusConfirmed,
			"block_number":                         blockNumber,
			"user_shadow_wallet_balance_record_id": walletBalanceRecordId,
		}).Error
}

func (r *walletDepositRecordRepository) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	return r.db.WithContext(ctx).Table(repository.WalletDepositRecord{}.TableName()).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       repository.WalletDepositRecordStatusFailed,
			"block_number": blockNumber,
		}).Error
}

func (r *walletDepositRecordRepository) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletDepositRecord, error) {
	var record repository.WalletDepositRecord
	err := r.db.WithContext(ctx).Where("tx_hash = ?", txHash).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get wallet deposit record by tx hash %s: %w", txHash, err)
	}
	return &record, nil
}
