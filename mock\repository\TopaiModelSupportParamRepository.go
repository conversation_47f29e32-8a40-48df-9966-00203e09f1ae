// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelSupportParamRepository is an autogenerated mock type for the TopaiModelSupportParamRepository type
type TopaiModelSupportParamRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelSupportParamRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelSupportParam, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelSupportParam
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelSupportParam, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelSupportParam); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelSupportParam)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByParams provides a mock function with given fields: ctx, params
func (_m *TopaiModelSupportParamRepository) GetByParams(ctx context.Context, params []string) ([]*repository.TopaiModelSupportParam, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetByParams")
	}

	var r0 []*repository.TopaiModelSupportParam
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModelSupportParam, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModelSupportParam); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelSupportParam)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByTopaiModelId provides a mock function with given fields: ctx, topaiModelId
func (_m *TopaiModelSupportParamRepository) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelSupportParam, error) {
	ret := _m.Called(ctx, topaiModelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByTopaiModelId")
	}

	var r0 []*repository.TopaiModelSupportParam
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.TopaiModelSupportParam, error)); ok {
		return rf(ctx, topaiModelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.TopaiModelSupportParam); ok {
		r0 = rf(ctx, topaiModelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelSupportParam)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, topaiModelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelSupportParamRepository creates a new instance of TopaiModelSupportParamRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelSupportParamRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelSupportParamRepository {
	mock := &TopaiModelSupportParamRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
