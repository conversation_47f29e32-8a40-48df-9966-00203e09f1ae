// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// DevAppKeyRepository is an autogenerated mock type for the DevAppKeyRepository type
type DevAppKeyRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, key
func (_m *DevAppKeyRepository) Create(ctx context.Context, key *repository.DevAppKey) error {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.DevAppKey) error); ok {
		r0 = rf(ctx, key)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, id
func (_m *DevAppKeyRepository) Delete(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByDevAppId provides a mock function with given fields: ctx, devAppId
func (_m *DevAppKeyRepository) GetByDevAppId(ctx context.Context, devAppId uint) ([]*repository.DevAppKey, error) {
	ret := _m.Called(ctx, devAppId)

	if len(ret) == 0 {
		panic("no return value specified for GetByDevAppId")
	}

	var r0 []*repository.DevAppKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.DevAppKey, error)); ok {
		return rf(ctx, devAppId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.DevAppKey); ok {
		r0 = rf(ctx, devAppId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DevAppKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, devAppId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByDevAppIdAndKey provides a mock function with given fields: ctx, devAppId, key
func (_m *DevAppKeyRepository) GetByDevAppIdAndKey(ctx context.Context, devAppId uint, key string) (*repository.DevAppKey, error) {
	ret := _m.Called(ctx, devAppId, key)

	if len(ret) == 0 {
		panic("no return value specified for GetByDevAppIdAndKey")
	}

	var r0 *repository.DevAppKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.DevAppKey, error)); ok {
		return rf(ctx, devAppId, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.DevAppKey); ok {
		r0 = rf(ctx, devAppId, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DevAppKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, devAppId, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByDevAppIdAndName provides a mock function with given fields: ctx, devAppId, name
func (_m *DevAppKeyRepository) GetByDevAppIdAndName(ctx context.Context, devAppId uint, name string) (*repository.DevAppKey, error) {
	ret := _m.Called(ctx, devAppId, name)

	if len(ret) == 0 {
		panic("no return value specified for GetByDevAppIdAndName")
	}

	var r0 *repository.DevAppKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.DevAppKey, error)); ok {
		return rf(ctx, devAppId, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.DevAppKey); ok {
		r0 = rf(ctx, devAppId, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DevAppKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, devAppId, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByDevAppIds provides a mock function with given fields: ctx, devAppIds
func (_m *DevAppKeyRepository) GetByDevAppIds(ctx context.Context, devAppIds []uint) ([]*repository.DevAppKey, error) {
	ret := _m.Called(ctx, devAppIds)

	if len(ret) == 0 {
		panic("no return value specified for GetByDevAppIds")
	}

	var r0 []*repository.DevAppKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.DevAppKey, error)); ok {
		return rf(ctx, devAppIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.DevAppKey); ok {
		r0 = rf(ctx, devAppIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.DevAppKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, devAppIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByKey provides a mock function with given fields: ctx, key
func (_m *DevAppKeyRepository) GetByKey(ctx context.Context, key string) (*repository.DevAppKey, error) {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for GetByKey")
	}

	var r0 *repository.DevAppKey
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.DevAppKey, error)); ok {
		return rf(ctx, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.DevAppKey); ok {
		r0 = rf(ctx, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DevAppKey)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewDevAppKeyRepository creates a new instance of DevAppKeyRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDevAppKeyRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *DevAppKeyRepository {
	mock := &DevAppKeyRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
