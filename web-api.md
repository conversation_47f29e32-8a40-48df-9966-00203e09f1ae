# Web API 文档

## 认证相关 API

### 会话管理

#### 获取当前会话用户
- **路由**: `GET /auth/`
- **响应模型**: `SessionUserResponse`
- **描述**: 获取当前登录用户的信息和权限
- **参数**: 无
- **响应**:
  ```json
  {
    "token": "string",
    "token_type": "string",
    "expires_at": "integer",
    "id": "string",
    "email": "string",
    "name": "string",
    "role": "string",
    "profile_image_url": "string",
    "permissions": {
      "workspace": {
        "models": "boolean",
        "knowledge": "boolean",
        "prompts": "boolean",
        "tools": "boolean"
      },
      "sharing": {
        "public_models": "boolean",
        "public_knowledge": "boolean",
        "public_prompts": "boolean",
        "public_tools": "boolean"
      },
      "chat": {
        "controls": "boolean",
        "file_upload": "boolean",
        "delete": "boolean",
        "edit": "boolean",
        "stt": "boolean",
        "tts": "boolean",
        "call": "boolean",
        "multiple_models": "boolean",
        "temporary": "boolean",
        "temporary_enforced": "boolean"
      },
      "features": {
        "direct_tool_servers": "boolean",
        "web_search": "boolean",
        "image_generation": "boolean",
        "code_interpreter": "boolean"
      }
    }
  }
  ```

#### 更新用户资料
- **路由**: `POST /auth/update/profile`
- **请求模型**: `UpdateProfileForm`
- **响应模型**: `UserResponse`
- **描述**: 更新当前用户的个人资料
- **请求参数**:
  ```json
  {
    "profile_image_url": "string",
    "name": "string"
  }
  ```
- **响应参数**:
  ```json
  {
    "id": "string",
    "email": "string",
    "name": "string",
    "role": "string",
    "profile_image_url": "string"
  }
  ```

#### 更新密码
- **路由**: `POST /auth/update/password`
- **请求模型**: `UpdatePasswordForm`
- **响应模型**: `bool`
- **描述**: 更新当前用户的密码
- **请求参数**:
  ```json
  {
    "password": "string",
    "new_password": "string"
  }
  ```

#### LDAP 认证
- **路由**: `POST /auth/ldap`
- **请求模型**: `LdapForm`
- **响应模型**: `SessionUserResponse`
- **描述**: 使用 LDAP 进行身份验证
- **请求参数**:
  ```json
  {
    "user": "string",
    "password": "string"
  }
  ```

#### 登录
- **路由**: `POST /auth/signin`
- **请求模型**: `SigninForm`
- **响应模型**: `SessionUserResponse`
- **描述**: 用户登录
- **请求参数**:
  ```json
  {
    "email": "string",
    "password": "string"
  }
  ```

#### 注册
- **路由**: `POST /auth/signup`
- **请求模型**: `SignupForm`
- **响应模型**: `SessionUserResponse`
- **描述**: 用户注册
- **请求参数**:
  ```json
  {
    "name": "string",
    "email": "string",
    "password": "string",
    "profile_image_url": "string" // 可选，默认为 "/user.png"
  }
  ```

#### 登出
- **路由**: `GET /auth/signout`
- **描述**: 用户登出

### API 密钥管理

#### 生成 API 密钥
- **路由**: `POST /auth/api_key`
- **响应模型**: `ApiKey`
- **描述**: 为当前用户生成新的 API 密钥
- **响应参数**:
  ```json
  {
    "api_key": "string"
  }
  ```

#### 删除 API 密钥
- **路由**: `DELETE /auth/api_key`
- **响应模型**: `bool`
- **描述**: 删除当前用户的 API 密钥

#### 获取 API 密钥
- **路由**: `GET /auth/api_key`
- **响应模型**: `ApiKey`
- **描述**: 获取当前用户的 API 密钥

### LDAP 配置管理

#### 获取 LDAP 服务器配置
- **路由**: `GET /auth/admin/config/ldap/server`
- **响应模型**: `LdapServerConfig`
- **描述**: 获取 LDAP 服务器配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "label": "string",
    "host": "string",
    "port": "integer",
    "attribute_for_mail": "string",
    "attribute_for_username": "string",
    "app_dn": "string",
    "app_dn_password": "string",
    "search_base": "string",
    "search_filters": "string",
    "use_tls": "boolean",
    "certificate_path": "string",
    "ciphers": "string"
  }
  ```

#### 更新 LDAP 服务器配置
- **路由**: `POST /auth/admin/config/ldap/server`
- **请求模型**: `LdapServerConfig`
- **响应模型**: `LdapServerConfig`
- **描述**: 更新 LDAP 服务器配置（需要管理员权限）
- **请求参数**: 同上

#### 获取 LDAP 配置
- **路由**: `GET /auth/admin/config/ldap`
- **响应模型**: `dict`
- **描述**: 获取 LDAP 配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "enable_ldap": "boolean"
  }
  ```

#### 更新 LDAP 配置
- **路由**: `POST /auth/admin/config/ldap`
- **请求模型**: `LdapConfigForm`
- **响应模型**: `dict`
- **描述**: 更新 LDAP 配置（需要管理员权限）
- **请求参数**:
  ```json
  {
    "enable_ldap": "boolean"
  }
  ```

### 管理员配置

#### 获取管理员配置
- **路由**: `GET /auth/admin/config`
- **响应模型**: `AdminConfig`
- **描述**: 获取系统管理员配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "SHOW_ADMIN_DETAILS": "boolean",
    "WEBUI_URL": "string",
    "ENABLE_SIGNUP": "boolean",
    "ENABLE_API_KEY": "boolean",
    "ENABLE_API_KEY_ENDPOINT_RESTRICTIONS": "boolean",
    "API_KEY_ALLOWED_ENDPOINTS": ["string"],
    "ENABLE_CHANNELS": "boolean",
    "DEFAULT_USER_ROLE": "string",
    "JWT_EXPIRES_IN": "string",
    "ENABLE_COMMUNITY_SHARING": "boolean",
    "ENABLE_MESSAGE_RATING": "boolean",
    "ENABLE_USER_WEBHOOKS": "boolean"
  }
  ```

#### 更新管理员配置
- **路由**: `POST /auth/admin/config`
- **请求模型**: `AdminConfig`
- **响应模型**: `AdminConfig`
- **描述**: 更新系统管理员配置（需要管理员权限）
- **请求参数**: 同上

### 认证 API

#### 添加认证方式

- **路由**: `POST /auth/add`
- **描述**: 添加新的认证方式
- **请求体**: 
  ```json
  {
    "username": "string",
    "password": "string",
    "email": "string",
    "avatar": "string",
    "role": "string"
  }
  ```
- **响应**: `SigninResponse` 对象
  ```json
  {
    "token": "string",
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "avatar": "string",
      "role": "string"
    }
  }
  ```

#### 获取管理员详情

- **路由**: `GET /auth/admin/details`
- **描述**: 获取管理员详细信息
- **响应**: 管理员详细信息对象
  ```json
  {
    "id": "string",
    "username": "string",
    "email": "string",
    "avatar": "string",
    "role": "string"
  }
  ```

## 聊天相关 API

### 聊天列表管理

#### 获取聊天列表
- **路由**: `GET /chats/` 或 `GET /chats/list`
- **响应模型**: `list[ChatTitleIdResponse]`
- **描述**: 获取当前用户的聊天列表
- **参数**: 
  - `page`: 可选，分页页码
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "title": "string",
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

#### 删除所有聊天
- **路由**: `DELETE /chats/`
- **响应模型**: `bool`
- **描述**: 删除当前用户的所有聊天

#### 创建新聊天
- **路由**: `POST /chats/new`
- **请求模型**: `ChatForm`
- **响应模型**: `ChatResponse`
- **描述**: 创建新的聊天
- **请求参数**:
  ```json
  {
    "chat": {
      "title": "string",
      "messages": [
        {
          "role": "string",
          "content": "string"
        }
      ]
    }
  }
  ```
- **响应参数**:
  ```json
  {
    "id": "string",
    "user_id": "string",
    "title": "string",
    "chat": {
      "title": "string",
      "messages": [
        {
          "role": "string",
          "content": "string"
        }
      ]
    },
    "updated_at": "integer",
    "created_at": "integer",
    "share_id": "string", // 可选
    "archived": "boolean",
    "pinned": "boolean", // 可选
    "meta": {}, // 可选
    "folder_id": "string" // 可选
  }
  ```

#### 导入聊天
- **路由**: `POST /chats/import`
- **请求模型**: `ChatImportForm`
- **响应模型**: `ChatResponse`
- **描述**: 导入聊天记录
- **请求参数**:
  ```json
  {
    "chat": {
      "title": "string",
      "messages": [
        {
          "role": "string",
          "content": "string"
        }
      ]
    },
    "meta": {}, // 可选
    "pinned": "boolean", // 可选
    "folder_id": "string" // 可选
  }
  ```

#### 搜索聊天
- **路由**: `GET /chats/search`
- **响应模型**: `list[ChatTitleIdResponse]`
- **参数**:
  - `text`: 搜索文本
  - `page`: 可选，分页页码

### 聊天标签管理

#### 获取所有标签
- **路由**: `GET /chats/all/tags`
- **响应模型**: `list[TagModel]`
- **描述**: 获取当前用户的所有标签
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "user_id": "string",
      "created_at": "integer"
    }
  ]
  ```

#### 按标签搜索聊天
- **路由**: `POST /chats/tags`
- **请求模型**: `TagFilterForm`
- **响应模型**: `list[ChatTitleIdResponse]`
- **描述**: 获取指定标签的聊天列表
- **请求参数**:
  ```json
  {
    "name": "string",
    "skip": "integer", // 可选，默认 0
    "limit": "integer" // 可选，默认 50
  }
  ```

#### 获取聊天标签
- **路由**: `GET /chats/{id}/tags`
- **响应模型**: `list[TagModel]`
- **描述**: 获取指定聊天的所有标签

#### 添加标签
- **路由**: `POST /chats/{id}/tags`
- **请求模型**: `TagForm`
- **响应模型**: `list[TagModel]`
- **描述**: 为指定聊天添加标签
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```

#### 删除标签
- **路由**: `DELETE /chats/{id}/tags`
- **请求模型**: `TagForm`
- **响应模型**: `list[TagModel]`
- **描述**: 删除指定聊天的标签
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```

#### 删除所有标签
- **路由**: `DELETE /chats/{id}/tags/all`
- **响应模型**: `bool`
- **描述**: 删除指定聊天的所有标签

### 聊天文件夹管理

#### 获取文件夹中的聊天
- **路由**: `GET /chats/folder/{folder_id}`
- **响应模型**: `list[ChatResponse]`
- **描述**: 获取指定文件夹中的所有聊天（包括子文件夹中的聊天）

#### 更新聊天文件夹
- **路由**: `POST /chats/{id}/folder`
- **请求模型**: `ChatFolderIdForm`
- **响应模型**: `ChatResponse`
- **描述**: 更新指定聊天的文件夹
- **请求参数**:
  ```json
  {
    "folder_id": "string" // 可选，null 表示移出文件夹
  }
  ```

### 聊天克隆

#### 克隆聊天
- **路由**: `POST /chats/{id}/clone`
- **请求模型**: `CloneForm`
- **响应模型**: `ChatResponse`
- **描述**: 克隆指定的聊天
- **请求参数**:
  ```json
  {
    "title": "string" // 可选，新聊天的标题
  }
  ```

#### 克隆共享聊天
- **路由**: `POST /chats/{id}/clone/shared`
- **响应模型**: `ChatResponse`
- **描述**: 克隆共享的聊天

### 聊天操作

#### 获取聊天详情
- **路由**: `GET /chats/{id}`
- **响应模型**: `ChatResponse`
- **描述**: 获取指定 ID 的聊天详情

#### 更新聊天
- **路由**: `POST /chats/{id}`
- **请求模型**: `ChatForm`
- **响应模型**: `ChatResponse`
- **描述**: 更新指定 ID 的聊天

#### 删除聊天
- **路由**: `DELETE /chats/{id}`
- **响应模型**: `bool`
- **描述**: 删除指定 ID 的聊天

#### 固定聊天
- **路由**: `POST /chats/{id}/pin`
- **响应模型**: `ChatResponse`
- **描述**: 固定指定 ID 的聊天

#### 归档聊天
- **路由**: `POST /chats/{id}/archive`
- **响应模型**: `ChatResponse`
- **描述**: 归档指定 ID 的聊天

#### 分享聊天
- **路由**: `POST /chats/{id}/share`
- **响应模型**: `ChatResponse`
- **描述**: 分享指定 ID 的聊天

#### 取消分享聊天
- **路由**: `DELETE /chats/{id}/share`
- **响应模型**: `bool`
- **描述**: 取消分享指定 ID 的聊天

## 用户管理 API

### 获取用户列表
- **路由**: `GET /users/`
- **响应模型**: `list[UserModel]`
- **描述**: 获取用户列表（需要管理员权限）
- **请求参数**:
  - `skip`: 可选，跳过数量
  - `limit`: 可选，限制数量
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "email": "string",
      "role": "string",
      "profile_image_url": "string",
      "last_active_at": "integer",
      "updated_at": "integer",
      "created_at": "integer",
      "api_key": "string", // 可选
      "settings": {
        "ui": {} // 可选
      },
      "info": {} // 可选
    }
  ]
  ```

#### 获取用户组
- **路由**: `GET /users/groups`
- **描述**: 获取当前用户的组信息

#### 获取用户权限
- **路由**: `GET /users/permissions`
- **描述**: 获取当前用户的权限信息

### 用户设置

#### 获取用户设置
- **路由**: `GET /users/user/settings`
- **响应模型**: `UserSettings`
- **描述**: 获取当前用户的设置
- **响应参数**:
  ```json
  {
    "ui": {} // 可选
  }
  ```

#### 更新用户设置
- **路由**: `POST /users/user/settings/update`
- **请求模型**: `UserSettings`
- **响应模型**: `UserSettings`
- **描述**: 更新当前用户的设置
- **请求参数**:
  ```json
  {
    "ui": {} // 可选
  }
  ```

#### 获取用户信息
- **路由**: `GET /users/user/info`
- **响应模型**: `dict`
- **描述**: 获取当前用户的详细信息

#### 更新用户信息
- **路由**: `POST /users/user/info/update`
- **请求模型**: `dict`
- **响应模型**: `dict`
- **描述**: 更新当前用户的详细信息

### 用户管理（管理员）

#### 更新用户角色
- **路由**: `POST /users/update/role`
- **请求模型**: `UserRoleUpdateForm`
- **响应模型**: `UserModel`
- **描述**: 更新指定用户的角色
- **请求参数**:
  ```json
  {
    "id": "string",
    "role": "string"
  }
  ```

#### 更新用户信息（管理员）
- **路由**: `POST /users/{user_id}/update`
- **请求模型**: `UserUpdateForm`
- **响应模型**: `UserModel`
- **描述**: 管理员更新指定用户的信息
- **请求参数**:
  ```json
  {
    "name": "string",
    "email": "string",
    "profile_image_url": "string",
    "password": "string" // 可选
  }
  ```

#### 删除用户
- **路由**: `DELETE /users/{user_id}`
- **响应模型**: `bool`
- **描述**: 删除指定 ID 的用户（需要管理员权限，且不能删除自己）
- **响应参数**:
  ```json
  true
  ```

## 文件管理 API

### 文件上传
- **路由**: `POST /files/`
- **响应模型**: `FileModelResponse`
- **描述**: 上传新文件
- **请求参数**:
  - `file`: 文件内容（multipart/form-data）
  - `file_metadata`: 可选，文件元数据
  - `process`: 可选，是否处理文件，默认为 true
- **响应参数**:
  ```json
  {
    "id": "string",
    "filename": "string",
    "path": "string",
    "meta": {
      "name": "string",
      "content_type": "string",
      "size": "integer",
      "data": {}
    }
  }
  ```

### 文件列表
- **路由**: `GET /files/`
- **响应模型**: `list[FileModelResponse]`
- **描述**: 获取文件列表
- **参数**:
  - `content`: 可选，是否包含文件内容，默认为 true
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "filename": "string",
      "path": "string",
      "meta": {
        "name": "string",
        "content_type": "string",
        "size": "integer",
        "data": {}
      }
    }
  ]
  ```

### 文件搜索
- **路由**: `GET /files/search`
- **响应模型**: `list[FileModelResponse]`
- **描述**: 搜索文件
- **参数**:
  - `filename`: 文件名模式（支持通配符，如 "*.txt"）
  - `content`: 可选，是否包含文件内容，默认为 true

### 获取文件内容
- **路由**: `GET /files/{id}/content`
- **描述**: 获取文件内容
- **参数**:
  - `attachment`: 可选，是否作为附件下载，默认为 false

### 更新文件内容
- **路由**: `POST /files/{id}/data/content/update`
- **请求模型**: `ContentForm`
- **描述**: 更新指定文件的内容
- **请求参数**:
  ```json
  {
    "content": "string"
  }
  ```
- **响应参数**:
  ```json
  {
    "content": "string"
  }
  ```

### 删除所有文件
- **路由**: `DELETE /files/all`
- **描述**: 删除所有文件（需要管理员权限）
- **响应参数**:
  ```json
  {
    "message": "All files deleted successfully"
  }
  ```

## 知识库 API

### 获取知识库列表
- **路由**: `GET /knowledge/`
- **响应模型**: `list[KnowledgeUserResponse]`
- **描述**: 获取知识库列表
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "data": {
        "file_ids": ["string"]
      },
      "files": [
        {
          "id": "string",
          "filename": "string",
          "path": "string",
          "meta": {
            "name": "string",
            "content_type": "string",
            "size": "integer",
            "data": {}
          }
        }
      ]
    }
  ]
  ```

### 创建知识库
- **路由**: `POST /knowledge/create`
- **请求模型**: `KnowledgeForm`
- **响应模型**: `KnowledgeResponse`
- **描述**: 创建新的知识库
- **请求参数**:
  ```json
  {
    "name": "string",
    "description": "string",
    "data": {
      "file_ids": ["string"]
    }
  }
  ```

### 添加文件到知识库
- **路由**: `POST /knowledge/{id}/file/add`
- **请求模型**: `KnowledgeFileIdForm`
- **响应模型**: `KnowledgeFilesResponse`
- **描述**: 添加文件到知识库
- **请求参数**:
  ```json
  {
    "file_id": "string"
  }
  ```

## 模型管理 API

### 获取模型列表
- **路由**: `GET /models/`
- **响应模型**: `list[ModelUserResponse]`
- **描述**: 获取模型列表（管理员可以获取所有模型，普通用户只能获取自己的模型）
- **请求参数**:
  - `id`: 可选，特定模型 ID
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "user_id": "string",
      "access_control": {},
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

### 创建模型
- **路由**: `POST /models/create`
- **请求模型**: `ModelForm`
- **响应模型**: `ModelModel`
- **描述**: 创建新模型
- **请求参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "description": "string",
    "access_control": {}
  }
  ```

### 更新模型
- **路由**: `POST /models/model/update`
- **请求模型**: `ModelForm`
- **响应模型**: `ModelModel`
- **描述**: 更新模型信息
- **请求参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "description": "string",
    "access_control": {}
  }
  ```

### 切换模型状态
- **路由**: `POST /models/model/toggle`
- **响应模型**: `ModelResponse`
- **描述**: 切换指定模型的激活状态
- **请求参数**:
  ```json
  {
    "id": "string"
  }
  ```
- **响应参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "description": "string",
    "user_id": "string",
    "access_control": {},
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 删除所有模型
- **路由**: `DELETE /models/delete/all`
- **响应模型**: `bool`
- **描述**: 删除所有模型（需要管理员权限）
- **响应参数**:
  ```json
  true
  ```

### 删除模型
- **路由**: `DELETE /models/model/delete`
- **响应模型**: `bool`
- **描述**: 删除指定 ID 的模型（需要管理员权限或模型所有者权限）
- **请求参数**:
  - `id`: 模型 ID
- **响应参数**:
  ```json
  true
  ```

## 提示词 API

### 获取提示词列表
- **路由**: `GET /prompts/`
- **响应模型**: `list[PromptModel]`
- **描述**: 获取提示词列表（管理员可以获取所有提示词，普通用户只能获取有读取权限的提示词）
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "command": "string",
      "name": "string",
      "description": "string",
      "content": "string",
      "user_id": "string",
      "access_control": {},
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

### 创建提示词
- **路由**: `POST /prompts/create`
- **请求模型**: `PromptForm`
- **响应模型**: `PromptModel`
- **描述**: 创建新提示词
- **请求参数**:
  ```json
  {
    "command": "string",
    "name": "string",
    "description": "string",
    "content": "string",
    "access_control": {}
  }
  ```

### 删除提示词
- **路由**: `DELETE /prompts/command/{command}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定命令的提示词
- **响应参数**:
  ```json
  true
  ```

## 工具 API

### 获取工具列表
- **路由**: `GET /tools/`
- **响应模型**: `list[ToolUserResponse]`
- **描述**: 获取工具列表
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "user_id": "string",
      "name": "string",
      "meta": {
        "description": "string"
      },
      "access_control": {},
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

### 创建工具
- **路由**: `POST /tools/create`
- **请求模型**: `ToolForm`
- **响应模型**: `ToolResponse`
- **描述**: 创建新工具
- **请求参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string"
    },
    "access_control": {}
  }
  ```

### 获取工具详情
- **路由**: `GET /tools/id/{id}`
- **响应模型**: `ToolModel`
- **描述**: 获取工具详情
- **响应参数**:
  ```json
  {
    "id": "string",
    "user_id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string",
      "manifest": {}
    },
    "access_control": {},
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 删除工具
- **路由**: `DELETE /tools/id/{id}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定 ID 的工具（需要管理员权限或工具所有者权限）
- **响应参数**:
  ```json
  true
  ```

## 音频 API

### 配置管理

#### 获取音频配置
- **路由**: `GET /audio/config`
- **响应模型**: `dict`
- **描述**: 获取音频配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "tts": {
      "OPENAI_API_BASE_URL": "string",
      "OPENAI_API_KEY": "string",
      "API_KEY": "string",
      "ENGINE": "string",
      "MODEL": "string",
      "VOICE": "string",
      "SPLIT_ON": "string",
      "AZURE_SPEECH_REGION": "string",
      "AZURE_SPEECH_OUTPUT_FORMAT": "string"
    },
    "stt": {
      "OPENAI_API_BASE_URL": "string",
      "OPENAI_API_KEY": "string",
      "ENGINE": "string",
      "MODEL": "string",
      "WHISPER_MODEL": "string",
      "DEEPGRAM_API_KEY": "string",
      "AZURE_API_KEY": "string",
      "AZURE_REGION": "string",
      "AZURE_LOCALES": "string"
    }
  }
  ```

#### 更新音频配置
- **路由**: `POST /audio/config/update`
- **请求模型**: `AudioConfigUpdateForm`
- **响应模型**: `dict`
- **描述**: 更新音频配置（需要管理员权限）
- **请求参数**:
  ```json
  {
    "tts": {
      "OPENAI_API_BASE_URL": "string",
      "OPENAI_API_KEY": "string",
      "API_KEY": "string",
      "ENGINE": "string",
      "MODEL": "string",
      "VOICE": "string",
      "SPLIT_ON": "string",
      "AZURE_SPEECH_REGION": "string",
      "AZURE_SPEECH_OUTPUT_FORMAT": "string"
    },
    "stt": {
      "OPENAI_API_BASE_URL": "string",
      "OPENAI_API_KEY": "string",
      "ENGINE": "string",
      "MODEL": "string",
      "WHISPER_MODEL": "string",
      "DEEPGRAM_API_KEY": "string",
      "AZURE_API_KEY": "string",
      "AZURE_REGION": "string",
      "AZURE_LOCALES": "string"
    }
  }
  ```

### 语音合成（TTS）

#### 文本转语音
- **路由**: `POST /audio/speech`
- **描述**: 将文本转换为语音
- **请求参数**:
  ```json
  {
    "input": "string",
    "voice": "string" // 可选，语音 ID
  }
  ```
- **响应**: 音频文件（MP3 格式）
- **限制**: 
  - 最大文件大小：25MB
  - 支持的音频格式：MP3, WAV, OGG, WebM
  - Azure 语音服务最大文件大小：200MB

#### 获取可用模型
- **路由**: `GET /audio/models`
- **响应模型**: `dict`
- **描述**: 获取可用的语音合成模型
- **响应参数**:
  ```json
  {
    "models": [
      {
        "id": "string",
        "name": "string" // 可选
      }
    ]
  }
  ```

#### 获取可用语音
- **路由**: `GET /audio/voices`
- **响应模型**: `dict`
- **描述**: 获取可用的语音列表
- **响应参数**:
  ```json
  {
    "voices": [
      {
        "id": "string",
        "name": "string"
      }
    ]
  }
  ```

### 语音识别（STT）

#### 语音转文本
- **路由**: `POST /audio/transcriptions`
- **描述**: 将语音文件转换为文本
- **请求参数**:
  - `file`: 音频文件（支持格式：audio/mpeg, audio/wav, audio/ogg, audio/x-m4a）
- **响应参数**:
  ```json
  {
    "text": "string",
    "filename": "string"
  }
  ```
- **支持的引擎**:
  - Whisper
  - OpenAI
  - Azure
  - Deepgram

## 函数管理 API

### 获取函数列表
- **路由**: `GET /functions/`
- **响应模型**: `list[FunctionResponse]`
- **描述**: 获取所有函数列表
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "content": "string",
      "meta": {
        "description": "string",
        "manifest": {}
      },
      "is_active": "boolean",
      "is_global": "boolean",
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

### 导出函数
- **路由**: `GET /functions/export`
- **响应模型**: `list[FunctionModel]`
- **描述**: 导出所有函数（需要管理员权限）
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "content": "string",
      "meta": {
        "description": "string",
        "manifest": {}
      },
      "is_active": "boolean",
      "is_global": "boolean",
      "updated_at": "integer",
      "created_at": "integer"
    }
  ]
  ```

### 获取函数详情
- **路由**: `GET /functions/id/{id}`
- **响应模型**: `FunctionModel`
- **描述**: 获取指定 ID 的函数详情（需要管理员权限）
- **响应参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string",
      "manifest": {}
    },
    "is_active": "boolean",
    "is_global": "boolean",
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 切换函数状态
- **路由**: `POST /functions/id/{id}/toggle`
- **响应模型**: `FunctionModel`
- **描述**: 切换指定函数的激活状态（需要管理员权限）
- **响应参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string",
      "manifest": {}
    },
    "is_active": "boolean",
    "is_global": "boolean",
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 切换全局函数状态
- **路由**: `POST /functions/id/{id}/toggle/global`
- **响应模型**: `FunctionModel`
- **描述**: 切换指定函数的全局状态（需要管理员权限）
- **响应参数**:
  ```json
  {
    "id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string",
      "manifest": {}
    },
    "is_active": "boolean",
    "is_global": "boolean",
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 删除函数
- **路由**: `DELETE /functions/id/{id}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定函数（需要管理员权限）
- **响应参数**:
  ```json
  true
  ```

## 评估反馈 API

### 创建反馈
- **路由**: `POST /evaluations/feedback`
- **请求模型**: `FeedbackForm`
- **响应模型**: `FeedbackModel`
- **描述**: 创建新的反馈
- **请求参数**:
  ```json
  {
    "content": "string",
    "rating": "integer",
    "meta": {}
  }
  ```
- **响应参数**:
  ```json
  {
    "id": "string",
    "user_id": "string",
    "content": "string",
    "rating": "integer",
    "meta": {},
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 更新反馈
- **路由**: `POST /evaluations/feedback/{id}`
- **请求模型**: `FeedbackForm`
- **响应模型**: `FeedbackModel`
- **描述**: 更新指定 ID 的反馈
- **请求参数**:
  ```json
  {
    "content": "string",
    "rating": "integer",
    "meta": {}
  }
  ```
- **响应参数**:
  ```json
  {
    "id": "string",
    "user_id": "string",
    "content": "string",
    "rating": "integer",
    "meta": {},
    "updated_at": "integer",
    "created_at": "integer"
  }
  ```

### 删除反馈
- **路由**: `DELETE /evaluations/feedback/{id}`
- **描述**: 删除指定 ID 的反馈（管理员可以删除任何反馈，普通用户只能删除自己的反馈）
- **响应参数**:
  ```json
  true
  ```

### 删除所有反馈
- **路由**: `DELETE /evaluations/feedbacks/all`
- **描述**: 删除所有反馈（需要管理员权限）
- **响应参数**:
  ```json
  true
  ```

## Ollama API

### 推送模型
- **路由**: `DELETE /ollama/api/push` 或 `DELETE /ollama/api/push/{url_idx}`
- **请求模型**: `PushModelForm`
- **描述**: 推送模型到 Ollama 服务器（需要管理员权限）
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```
- **响应参数**:
  ```json
  {
    "status": "string"
  }
  ```

### 删除模型
- **路由**: `DELETE /ollama/api/delete` 或 `DELETE /ollama/api/delete/{url_idx}`
- **请求模型**: `ModelNameForm`
- **描述**: 删除 Ollama 模型（需要管理员权限）
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```
- **响应参数**:
  ```json
  true
  ```

## 检索 API

### 获取检索状态
- **路由**: `GET /retrieval/`
- **描述**: 获取检索系统状态
- **响应参数**:
  ```json
  {
    "status": "boolean",
    "chunk_size": "integer",
    "chunk_overlap": "integer",
    "template": "string",
    "embedding_engine": "string",
    "embedding_model": "string",
    "reranking_model": "string",
    "embedding_batch_size": "integer"
  }
  ```

### 获取嵌入配置
- **路由**: `GET /retrieval/embedding`
- **描述**: 获取嵌入配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "status": "boolean",
    "embedding_engine": "string",
    "embedding_model": "string",
    "embedding_batch_size": "integer",
    "openai_config": {
      "url": "string",
      "key": "string"
    },
    "ollama_config": {
      "url": "string",
      "key": "string"
    }
  }
  ```

### 更新嵌入配置
- **路由**: `POST /retrieval/embedding/update`
- **请求模型**: `EmbeddingModelUpdateForm`
- **描述**: 更新嵌入配置（需要管理员权限）
- **请求参数**:
  ```json
  {
    "openai_config": {
      "url": "string",
      "key": "string"
    },
    "ollama_config": {
      "url": "string",
      "key": "string"
    },
    "embedding_engine": "string",
    "embedding_model": "string",
    "embedding_batch_size": "integer"
  }
  ```

### 获取重排序配置
- **路由**: `GET /retrieval/reranking`
- **描述**: 获取重排序配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "status": "boolean",
    "reranking_model": "string"
  }
  ```

### 更新重排序配置
- **路由**: `POST /retrieval/reranking/update`
- **请求模型**: `RerankingModelUpdateForm`
- **描述**: 更新重排序配置（需要管理员权限）
- **请求参数**:
  ```json
  {
    "reranking_model": "string"
  }
  ```

### 处理文件
- **路由**: `POST /retrieval/process/file`
- **请求模型**: `ProcessFileForm`
- **描述**: 处理文件并保存到向量数据库
- **请求参数**:
  ```json
  {
    "file_id": "string",
    "content": "string",
    "collection_name": "string"
  }
  ```

### 处理文本
- **路由**: `POST /retrieval/process/text`
- **请求模型**: `ProcessTextForm`
- **描述**: 处理文本并保存到向量数据库
- **请求参数**:
  ```json
  {
    "name": "string",
    "content": "string",
    "collection_name": "string"
  }
  ```

### 处理 YouTube 视频
- **路由**: `POST /retrieval/process/youtube`
- **请求模型**: `ProcessUrlForm`
- **描述**: 处理 YouTube 视频并保存到向量数据库
- **请求参数**:
  ```json
  {
    "url": "string",
    "collection_name": "string"
  }
  ```

### 处理网页
- **路由**: `POST /retrieval/process/web`
- **请求模型**: `ProcessUrlForm`
- **描述**: 处理网页并保存到向量数据库
- **请求参数**:
  ```json
  {
    "url": "string",
    "collection_name": "string"
  }
  ```

### 查询文档
- **路由**: `POST /retrieval/query/doc`
- **请求模型**: `QueryDocForm`
- **描述**: 查询文档
- **请求参数**:
  ```json
  {
    "collection_name": "string",
    "query": "string",
    "k": "integer",
    "k_reranker": "integer",
    "r": "float",
    "hybrid": "boolean"
  }
  ```

### 查询集合
- **路由**: `POST /retrieval/query/collection`
- **请求模型**: `QueryCollectionsForm`
- **描述**: 查询集合
- **请求参数**:
  ```json
  {
    "collection_names": ["string"],
    "query": "string",
    "k": "integer",
    "k_reranker": "integer",
    "r": "float",
    "hybrid": "boolean"
  }
  ```

### 删除集合条目
- **路由**: `POST /retrieval/delete`
- **请求模型**: `DeleteForm`
- **描述**: 从集合中删除条目
- **请求参数**:
  ```json
  {
    "collection_name": "string",
    "file_id": "string"
  }
  ```

### 重置向量数据库
- **路由**: `POST /retrieval/reset/db`
- **描述**: 重置向量数据库（需要管理员权限）

### 重置上传目录
- **路由**: `POST /retrieval/reset/uploads`
- **描述**: 重置上传目录（需要管理员权限）

## OpenAI 代理 API

### 代理请求
- **路由**: `/{path:path}`
- **方法**: `GET`, `POST`, `PUT`, `DELETE`
- **描述**: 代理所有请求到 OpenAI API（已弃用）
- **注意**: 此 API 会将请求转发到配置的 OpenAI API 端点，并添加用户信息头（如果启用）
- **请求头**:
  ```
  Authorization: Bearer {key}
  Content-Type: application/json
  X-OpenWebUI-User-Name: {user_name}
  X-OpenWebUI-User-Id: {user_id}
  X-OpenWebUI-User-Email: {user_email}
  X-OpenWebUI-User-Role: {user_role}
  ```

## 频道 API

### 获取频道列表
- **路由**: `GET /channels`
- **响应模型**: `list[ChannelModel]`
- **描述**: 获取用户可访问的所有频道列表。管理员可以看到所有频道，普通用户只能看到有访问权限的频道。

### 创建新频道
- **路由**: `POST /channels/create`
- **请求模型**: `ChannelForm`
- **响应模型**: `ChannelModel`
- **描述**: 创建一个新的频道（需要管理员权限）
- **请求参数**:
  ```json
  {
    "name": "string",
    "description": "string",
    "access_control": {}
  }
  ```

### 获取频道详情
- **路由**: `GET /channels/{id}`
- **响应模型**: `ChannelModel`
- **描述**: 获取指定ID的频道详情

### 更新频道
- **路由**: `POST /channels/{id}/update`
- **请求模型**: `ChannelForm`
- **响应模型**: `ChannelModel`
- **描述**: 更新指定ID的频道信息（需要管理员权限）
- **请求参数**: 同创建新频道

### 删除频道
- **路由**: `DELETE /channels/{id}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定ID的频道（需要管理员权限）
- **响应参数**:
  ```json
  true
  ```

### 获取频道消息
- **路由**: `GET /channels/{id}/messages`
- **响应模型**: `list[MessageUserResponse]`
- **描述**: 获取指定频道的消息列表
- **查询参数**:
  - `skip`: 跳过的消息数量（默认0）
  - `limit`: 返回的最大消息数量（默认50）

### 发送新消息
- **路由**: `POST /channels/{id}/messages/post`
- **请求模型**: `MessageForm`
- **响应模型**: `MessageModel`
- **描述**: 在指定频道发送新消息
- **请求参数**:
  ```json
  {
    "content": "string",
    "type": "string"
  }
  ```

### 获取单条消息
- **路由**: `GET /channels/{id}/messages/{message_id}`
- **响应模型**: `MessageUserResponse`
- **描述**: 获取指定频道中的特定消息

### 获取消息回复
- **路由**: `GET /channels/{id}/messages/{message_id}/thread`
- **响应模型**: `list[MessageUserResponse]`
- **描述**: 获取指定消息的回复列表
- **查询参数**:
  - `skip`: 跳过的回复数量（默认0）
  - `limit`: 返回的最大回复数量（默认50）

### 更新消息
- **路由**: `POST /channels/{id}/messages/{message_id}/update`
- **请求模型**: `MessageForm`
- **响应模型**: `MessageModel`
- **描述**: 更新指定消息的内容
- **请求参数**: 同发送新消息

### 添加消息反应
- **路由**: `POST /channels/{id}/messages/{message_id}/reactions/add`
- **请求模型**: `ReactionForm`
- **响应模型**: `bool`
- **描述**: 为指定消息添加反应（如表情）
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```

### 移除消息反应
- **路由**: `POST /channels/{id}/messages/{message_id}/reactions/remove`
- **请求模型**: `ReactionForm`
- **响应模型**: `bool`
- **描述**: 移除指定消息的特定反应
- **请求参数**: 同添加消息反应

### 删除消息
- **路由**: `DELETE /channels/{id}/messages/{message_id}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定的消息

## 组管理 API

### 删除组
- **路由**: `DELETE /groups/id/{id}/delete`
- **响应模型**: `bool`
- **描述**: 删除指定 ID 的组（需要管理员权限）
- **响应参数**:
  ```json
  true
  ```

## 配置管理 API

### 基本配置

#### 导出配置
- **路由**: `GET /configs/export`
- **响应模型**: `dict`
- **描述**: 导出系统配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "config_key1": "value1",
    "config_key2": "value2"
  }
  ```

#### 导入配置
- **路由**: `POST /configs/import`
- **请求模型**: `ImportConfigForm`
- **响应模型**: `dict`
- **描述**: 导入系统配置（需要管理员权限）
- **请求参数**:
  ```json
  {
    "config": {}
  }
  ```

### 工具服务器配置

#### 获取工具服务器配置
- **路由**: `GET /configs/tool_servers`
- **响应模型**: `ToolServersConfigForm`
- **描述**: 获取工具服务器配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "TOOL_SERVER_CONNECTIONS": [
      {
        "url": "string",
        "path": "string",
        "auth_type": "string",
        "key": "string",
        "config": {}
      }
    ]
  }
  ```

#### 更新工具服务器配置
- **路由**: `POST /configs/tool_servers`
- **请求模型**: `ToolServersConfigForm`
- **响应模型**: `ToolServersConfigForm`
- **描述**: 更新工具服务器配置（需要管理员权限）
- **请求参数**: 同上

#### 验证工具服务器配置
- **路由**: `POST /configs/tool_servers/verify`
- **请求模型**: `ToolServerConnection`
- **描述**: 验证工具服务器连接（需要管理员权限）
- **请求参数**:
  ```json
  {
    "url": "string",
    "path": "string",
    "auth_type": "string",
    "key": "string",
    "config": {}
  }
  ```

### 代码执行器配置

#### 获取代码执行器配置
- **路由**: `GET /configs/code_execution`
- **响应模型**: `CodeInterpreterConfigForm`
- **描述**: 获取代码执行器配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "ENABLE_CODE_EXECUTION": "boolean",
    "CODE_EXECUTION_ENGINE": "string",
    "CODE_EXECUTION_JUPYTER_URL": "string",
    "CODE_EXECUTION_JUPYTER_AUTH": "string",
    "CODE_EXECUTION_JUPYTER_AUTH_TOKEN": "string",
    "CODE_EXECUTION_JUPYTER_AUTH_PASSWORD": "string",
    "CODE_EXECUTION_JUPYTER_TIMEOUT": "integer",
    "ENABLE_CODE_INTERPRETER": "boolean",
    "CODE_INTERPRETER_ENGINE": "string",
    "CODE_INTERPRETER_PROMPT_TEMPLATE": "string",
    "CODE_INTERPRETER_JUPYTER_URL": "string",
    "CODE_INTERPRETER_JUPYTER_AUTH": "string",
    "CODE_INTERPRETER_JUPYTER_AUTH_TOKEN": "string",
    "CODE_INTERPRETER_JUPYTER_AUTH_PASSWORD": "string",
    "CODE_INTERPRETER_JUPYTER_TIMEOUT": "integer"
  }
  ```

#### 更新代码执行器配置
- **路由**: `POST /configs/code_execution`
- **请求模型**: `CodeInterpreterConfigForm`
- **响应模型**: `CodeInterpreterConfigForm`
- **描述**: 更新代码执行器配置（需要管理员权限）
- **请求参数**: 同上

### 直接连接配置

#### 获取直接连接配置
- **路由**: `GET /configs/direct_connections`
- **响应模型**: `DirectConnectionsConfigForm`
- **描述**: 获取直接连接配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "ENABLE_DIRECT_CONNECTIONS": "boolean"
  }
  ```

#### 更新直接连接配置
- **路由**: `POST /configs/direct_connections`
- **请求模型**: `DirectConnectionsConfigForm`
- **响应模型**: `DirectConnectionsConfigForm`
- **描述**: 更新直接连接配置（需要管理员权限）
- **请求参数**: 同上

### 模型配置

#### 获取模型配置
- **路由**: `GET /configs/models`
- **响应模型**: `ModelsConfigForm`
- **描述**: 获取模型配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "DEFAULT_MODELS": "string",
    "MODEL_ORDER_LIST": ["string"]
  }
  ```

#### 更新模型配置
- **路由**: `POST /configs/models`
- **请求模型**: `ModelsConfigForm`
- **响应模型**: `ModelsConfigForm`
- **描述**: 更新模型配置（需要管理员权限）
- **请求参数**: 同上

### 提示建议配置

#### 更新默认提示建议
- **路由**: `POST /configs/suggestions`
- **请求模型**: `SetDefaultSuggestionsForm`
- **响应模型**: `list[PromptSuggestion]`
- **描述**: 更新默认提示建议（需要管理员权限）
- **请求参数**:
  ```json
  {
    "suggestions": [
      {
        "title": ["string"],
        "content": "string"
      }
    ]
  }
  ```

### 横幅配置

#### 获取横幅
- **路由**: `GET /configs/banners`
- **响应模型**: `list[BannerModel]`
- **描述**: 获取横幅列表

#### 更新横幅
- **路由**: `POST /configs/banners`
- **请求模型**: `SetBannersForm`
- **响应模型**: `list[BannerModel]`
- **描述**: 更新横幅列表（需要管理员权限）
- **请求参数**:
  ```json
  {
    "banners": [
      {
        "id": "string",
        "content": "string",
        "type": "string",
        "dismissible": "boolean"
      }
    ]
  }
  ```

## 管道 API

### 删除管道
- **路由**: `DELETE /pipelines/delete`
- **请求模型**: `DeletePipelineForm`
- **描述**: 删除指定的管道（需要管理员权限）
- **请求参数**:
  ```json
  {
    "id": "string",
    "urlIdx": "integer"
  }
  ```
- **响应参数**:
  ```json
  {
    "status": "string"
  }
  ```

## 文件夹 API

### 获取文件夹列表
- **路由**: `GET /folders/`
- **响应模型**: `list[FolderModel]`
- **描述**: 获取当前用户的文件夹列表
- **响应参数**:
  ```json
  [
    {
      "id": "string",
      "name": "string",
      "parent_id": "string",
      "user_id": "string",
      "is_expanded": "boolean",
      "items": {
        "chats": [
          {
            "title": "string",
            "id": "string"
          }
        ]
      }
    }
  ]
  ```

### 创建文件夹
- **路由**: `POST /folders/`
- **请求模型**: `FolderForm`
- **描述**: 创建新文件夹
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```

### 获取文件夹详情
- **路由**: `GET /folders/{id}`
- **响应模型**: `FolderModel`
- **描述**: 获取指定 ID 的文件夹详情

### 更新文件夹名称
- **路由**: `POST /folders/{id}/update`
- **请求模型**: `FolderForm`
- **描述**: 更新指定 ID 的文件夹名称
- **请求参数**:
  ```json
  {
    "name": "string"
  }
  ```

### 更新文件夹父级
- **路由**: `POST /folders/{id}/update/parent`
- **请求模型**: `FolderParentIdForm`
- **描述**: 更新指定 ID 的文件夹父级
- **请求参数**:
  ```json
  {
    "parent_id": "string"
  }
  ```

### 更新文件夹展开状态
- **路由**: `POST /folders/{id}/update/expanded`
- **请求模型**: `FolderIsExpandedForm`
- **描述**: 更新指定 ID 的文件夹展开状态
- **请求参数**:
  ```json
  {
    "is_expanded": "boolean"
  }
  ```

### 删除文件夹
- **路由**: `DELETE /folders/{id}`
- **描述**: 删除指定 ID 的文件夹

## 图片生成 API

### 获取图片配置
- **路由**: `GET /images/config`
- **描述**: 获取图片生成配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "enabled": "boolean",
    "engine": "string",
    "prompt_generation": "boolean",
    "openai": {
      "OPENAI_API_BASE_URL": "string",
      "OPENAI_API_KEY": "string"
    },
    "automatic1111": {
      "AUTOMATIC1111_BASE_URL": "string",
      "AUTOMATIC1111_API_AUTH": "string",
      "AUTOMATIC1111_CFG_SCALE": "number",
      "AUTOMATIC1111_SAMPLER": "string",
      "AUTOMATIC1111_SCHEDULER": "string"
    },
    "comfyui": {
      "COMFYUI_BASE_URL": "string",
      "COMFYUI_API_KEY": "string",
      "COMFYUI_WORKFLOW": "string",
      "COMFYUI_WORKFLOW_NODES": ["dict"]
    },
    "gemini": {
      "GEMINI_API_BASE_URL": "string",
      "GEMINI_API_KEY": "string"
    }
  }
  ```

### 更新图片配置
- **路由**: `POST /images/config/update`
- **请求模型**: `ConfigForm`
- **描述**: 更新图片生成配置（需要管理员权限）
- **请求参数**: 同上

### 验证服务器 URL
- **路由**: `GET /images/config/url/verify`
- **描述**: 验证图片生成服务器 URL（需要管理员权限）

### 获取可用模型
- **路由**: `GET /images/models`
- **描述**: 获取可用的图片生成模型

### 生成图片
- **路由**: `POST /images/generations`
- **请求模型**: `GenerateImageForm`
- **描述**: 生成图片
- **请求参数**:
  ```json
  {
    "model": "string",
    "prompt": "string",
    "size": "string",
    "n": "integer",
    "negative_prompt": "string"
  }
  ```

## 记忆 API

### 获取记忆列表
- **路由**: `GET /memories/`
- **响应模型**: `list[MemoryModel]`
- **描述**: 获取当前用户的记忆列表

### 添加记忆
- **路由**: `POST /memories/add`
- **请求模型**: `AddMemoryForm`
- **响应模型**: `MemoryModel`
- **描述**: 添加新记忆
- **请求参数**:
  ```json
  {
    "content": "string"
  }
  ```

### 查询记忆
- **路由**: `POST /memories/query`
- **请求模型**: `QueryMemoryForm`
- **描述**: 查询记忆
- **请求参数**:
  ```json
  {
    "content": "string",
    "k": "integer"
  }
  ```

### 重置记忆向量数据库
- **路由**: `POST /memories/reset`
- **描述**: 重置记忆向量数据库

### 删除用户记忆
- **路由**: `DELETE /memories/delete/user`
- **描述**: 删除当前用户的所有记忆

### 更新记忆
- **路由**: `POST /memories/{memory_id}/update`
- **请求模型**: `MemoryUpdateModel`
- **响应模型**: `MemoryModel`
- **描述**: 更新指定 ID 的记忆
- **请求参数**:
  ```json
  {
    "content": "string"
  }
  ```

### 删除记忆
- **路由**: `DELETE /memories/{memory_id}`
- **描述**: 删除指定 ID 的记忆

## 工具 API

### 获取 Gravatar 头像
- **路由**: `GET /utils/gravatar`
- **描述**: 获取指定邮箱的 Gravatar 头像 URL
- **参数**:
  - `email`: 邮箱地址

### 格式化代码
- **路由**: `POST /utils/code/format`
- **请求模型**: `CodeForm`
- **描述**: 格式化代码
- **请求参数**:
  ```json
  {
    "code": "string"
  }
  ```

### 执行代码
- **路由**: `POST /utils/code/execute`
- **请求模型**: `CodeForm`
- **描述**: 执行代码
- **请求参数**:
  ```json
  {
    "code": "string"
  }
  ```

### Markdown 转 HTML
- **路由**: `POST /utils/markdown`
- **请求模型**: `MarkdownForm`
- **描述**: 将 Markdown 转换为 HTML
- **请求参数**:
  ```json
  {
    "md": "string"
  }
  ```

### 导出聊天为 PDF
- **路由**: `POST /utils/pdf`
- **请求模型**: `ChatTitleMessagesForm`
- **描述**: 将聊天导出为 PDF 文件

### 下载数据库
- **路由**: `GET /utils/db/download`
- **描述**: 下载数据库文件（需要管理员权限）

### 下载 LiteLLM 配置
- **路由**: `GET /utils/litellm/config`
- **描述**: 下载 LiteLLM 配置文件（需要管理员权限）

## 评估配置 API

### 获取评估配置
- **路由**: `GET /evaluations/config`
- **响应模型**: `dict`
- **描述**: 获取评估配置（需要管理员权限）
- **响应参数**:
  ```json
  {
    "ENABLE_EVALUATION_ARENA_MODELS": "boolean",
    "EVALUATION_ARENA_MODELS": ["dict"]
  }
  ```

- **路由**: `POST /evaluations/config`
- **请求模型**: `dict`
- **响应模型**: `dict`
- **描述**: 更新评估配置（需要管理员权限）
- **请求参数**: 同上

### 反馈管理
- **路由**: `GET /evaluations/feedbacks/all`
- **响应模型**: `list[FeedbackUserResponse]`
- **描述**: 获取所有反馈（需要管理员权限）

- **路由**: `GET /evaluations/feedbacks/all/export`
- **响应模型**: `list[FeedbackModel]`
- **描述**: 导出所有反馈（需要管理员权限）

- **路由**: `GET /evaluations/feedbacks/user`
- **响应模型**: `list[FeedbackUserResponse]`
- **描述**: 获取当前用户的反馈列表

- **路由**: `DELETE /evaluations/feedbacks`
- **响应模型**: `bool`
- **描述**: 删除当前用户的所有反馈

- **路由**: `POST /evaluations/feedback`
- **请求模型**: `FeedbackForm`
- **响应模型**: `FeedbackModel`
- **描述**: 创建新的反馈
- **请求参数**:
  ```json
  {
    "content": "string",
    "rating": "integer",
    "meta": {}
  }
  ```

- **路由**: `POST /evaluations/feedback/{id}`
- **请求模型**: `FeedbackForm`
- **响应模型**: `FeedbackModel`
- **描述**: 更新指定 ID 的反馈
- **请求参数**: 同上

- **路由**: `DELETE /evaluations/feedback/{id}`
- **描述**: 删除指定 ID 的反馈（管理员可以删除任何反馈，普通用户只能删除自己的反馈）

## 函数管理 API

### 创建新函数
- **路由**: `POST /functions/create`
- **描述**: 创建新的函数（需要管理员权限）
- **请求体**:
  ```json
  {
    "id": "string",
    "name": "string",
    "content": "string",
    "meta": {
      "description": "string",
      "manifest": {}
    }
  }
  ```
- **响应**: 返回创建的 `FunctionResponse` 对象

### 更新函数
- **路由**: `POST /functions/id/{id}/update`
- **描述**: 更新指定ID的函数（需要管理员权限）
- **请求体**: 同创建新函数
- **响应**: 返回更新后的 `FunctionModel` 对象

### 函数阀门管理

#### 获取函数阀门
- **路由**: `GET /functions/id/{id}/valves`
- **描述**: 获取指定函数的阀门配置（需要管理员权限）
- **响应**: 返回阀门配置对象

#### 获取函数阀门规范
- **路由**: `GET /functions/id/{id}/valves/spec`
- **描述**: 获取指定函数的阀门规范（需要管理员权限）
- **响应**: 返回阀门规范对象

#### 更新函数阀门
- **路由**: `POST /functions/id/{id}/valves/update`
- **描述**: 更新指定函数的阀门配置（需要管理员权限）
- **请求体**: 阀门配置对象
- **响应**: 返回更新后的阀门配置

#### 获取用户函数阀门
- **路由**: `GET /functions/id/{id}/valves/user`
- **描述**: 获取指定函数的用户阀门配置
- **响应**: 返回用户阀门配置对象

#### 获取用户函数阀门规范
- **路由**: `GET /functions/id/{id}/valves/user/spec`
- **描述**: 获取指定函数的用户阀门规范
- **响应**: 返回用户阀门规范对象

#### 更新用户函数阀门
- **路由**: `POST /functions/id/{id}/valves/user/update`
- **描述**: 更新指定函数的用户阀门配置
- **请求体**: 用户阀门配置对象
- **响应**: 返回更新后的用户阀门配置

## 组管理 API

### 获取组列表
- **路由**: `GET /groups`
- **描述**: 获取组列表（管理员可以看到所有组，普通用户只能看到自己所在的组）
- **响应**: 返回 `GroupResponse` 对象列表

### 创建新组
- **路由**: `POST /groups/create`
- **描述**: 创建新的组（需要管理员权限）
- **请求体**:
  ```json
  {
    "name": "string",
    "description": "string",
    "user_ids": ["string"]
  }
  ```
- **响应**: 返回创建的 `GroupResponse` 对象

### 获取组详情
- **路由**: `GET /groups/id/{id}`
- **描述**: 获取指定ID的组详情（需要管理员权限）
- **响应**: 返回 `GroupResponse` 对象

### 更新组信息
- **路由**: `POST /groups/id/{id}/update`
- **描述**: 更新指定ID的组信息（需要管理员权限）
- **请求体**:
  ```json
  {
    "name": "string",
    "description": "string",
    "user_ids": ["string"]
  }
  ```
- **响应**: 返回更新后的 `GroupResponse` 对象
