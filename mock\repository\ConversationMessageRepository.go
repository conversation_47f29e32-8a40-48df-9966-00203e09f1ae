// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// ConversationMessageRepository is an autogenerated mock type for the ConversationMessageRepository type
type ConversationMessageRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, message
func (_m *ConversationMessageRepository) Create(ctx context.Context, message *repository.ConversationMessage) error {
	ret := _m.Called(ctx, message)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.ConversationMessage) error); ok {
		r0 = rf(ctx, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByConversationID provides a mock function with given fields: ctx, conversationID
func (_m *ConversationMessageRepository) GetByConversationID(ctx context.Context, conversationID uint) ([]*repository.ConversationMessage, error) {
	ret := _m.Called(ctx, conversationID)

	if len(ret) == 0 {
		panic("no return value specified for GetByConversationID")
	}

	var r0 []*repository.ConversationMessage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.ConversationMessage, error)); ok {
		return rf(ctx, conversationID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.ConversationMessage); ok {
		r0 = rf(ctx, conversationID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.ConversationMessage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, conversationID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByConversationIDAndUUID provides a mock function with given fields: ctx, conversationID, uuid
func (_m *ConversationMessageRepository) GetByConversationIDAndUUID(ctx context.Context, conversationID uint, uuid string) (*repository.ConversationMessage, error) {
	ret := _m.Called(ctx, conversationID, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByConversationIDAndUUID")
	}

	var r0 *repository.ConversationMessage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.ConversationMessage, error)); ok {
		return rf(ctx, conversationID, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.ConversationMessage); ok {
		r0 = rf(ctx, conversationID, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.ConversationMessage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, conversationID, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *ConversationMessageRepository) GetByID(ctx context.Context, id uint) (*repository.ConversationMessage, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.ConversationMessage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.ConversationMessage, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.ConversationMessage); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.ConversationMessage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, message
func (_m *ConversationMessageRepository) Update(ctx context.Context, message *repository.ConversationMessage) error {
	ret := _m.Called(ctx, message)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.ConversationMessage) error); ok {
		r0 = rf(ctx, message)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateContentAndDoneById provides a mock function with given fields: ctx, id, content, fileUUIDs
func (_m *ConversationMessageRepository) UpdateContentAndDoneById(ctx context.Context, id uint, content string, fileUUIDs string) error {
	ret := _m.Called(ctx, id, content, fileUUIDs)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContentAndDoneById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string, string) error); ok {
		r0 = rf(ctx, id, content, fileUUIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewConversationMessageRepository creates a new instance of ConversationMessageRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewConversationMessageRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ConversationMessageRepository {
	mock := &ConversationMessageRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
