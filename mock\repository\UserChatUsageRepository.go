// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserChatUsageRepository is an autogenerated mock type for the UserChatUsageRepository type
type UserChatUsageRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, usage
func (_m *UserChatUsageRepository) Create(ctx context.Context, usage *repository.UserChatUsage) error {
	ret := _m.Called(ctx, usage)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatUsage) error); ok {
		r0 = rf(ctx, usage)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByCreatedAtPeriod provides a mock function with given fields: ctx, start, end
func (_m *UserChatUsageRepository) GetByCreatedAtPeriod(ctx context.Context, start string, end string) ([]*repository.UserChatUsage, error) {
	ret := _m.Called(ctx, start, end)

	if len(ret) == 0 {
		panic("no return value specified for GetByCreatedAtPeriod")
	}

	var r0 []*repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]*repository.UserChatUsage, error)); ok {
		return rf(ctx, start, end)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []*repository.UserChatUsage); ok {
		r0 = rf(ctx, start, end)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, start, end)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *UserChatUsageRepository) GetByID(ctx context.Context, id uint) (*repository.UserChatUsage, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.UserChatUsage, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.UserChatUsage); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUUID provides a mock function with given fields: ctx, uuid
func (_m *UserChatUsageRepository) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatUsage, error) {
	ret := _m.Called(ctx, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByUUID")
	}

	var r0 *repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.UserChatUsage, error)); ok {
		return rf(ctx, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.UserChatUsage); ok {
		r0 = rf(ctx, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConfirmed provides a mock function with given fields: ctx
func (_m *UserChatUsageRepository) GetConfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetConfirmed")
	}

	var r0 []*repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.UserChatUsage, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.UserChatUsage); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSettled provides a mock function with given fields: ctx
func (_m *UserChatUsageRepository) GetSettled(ctx context.Context) ([]*repository.UserChatUsage, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetSettled")
	}

	var r0 []*repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.UserChatUsage, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.UserChatUsage); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUnconfirmed provides a mock function with given fields: ctx
func (_m *UserChatUsageRepository) GetUnconfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUnconfirmed")
	}

	var r0 []*repository.UserChatUsage
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.UserChatUsage, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.UserChatUsage); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatUsage)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, usage
func (_m *UserChatUsageRepository) Update(ctx context.Context, usage *repository.UserChatUsage) error {
	ret := _m.Called(ctx, usage)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatUsage) error); ok {
		r0 = rf(ctx, usage)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCompletedByIDs provides a mock function with given fields: ctx, ids, userReportCostRecordID
func (_m *UserChatUsageRepository) UpdateCompletedByIDs(ctx context.Context, ids []uint, userReportCostRecordID uint) error {
	ret := _m.Called(ctx, ids, userReportCostRecordID)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCompletedByIDs")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint, uint) error); ok {
		r0 = rf(ctx, ids, userReportCostRecordID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateSettledByID provides a mock function with given fields: ctx, id
func (_m *UserChatUsageRepository) UpdateSettledByID(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSettledByID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateSettledByIDs provides a mock function with given fields: ctx, ids
func (_m *UserChatUsageRepository) UpdateSettledByIDs(ctx context.Context, ids []uint) error {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSettledByIDs")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) error); ok {
		r0 = rf(ctx, ids)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserChatUsageRepository creates a new instance of UserChatUsageRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserChatUsageRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserChatUsageRepository {
	mock := &UserChatUsageRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
