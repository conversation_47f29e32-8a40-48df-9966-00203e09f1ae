package service

import (
	"context"
	"errors"
	"io"
	"net/http"
	"reflect"
	"strings"
	"testing"
	"unsafe"

	"github.com/sashabaranov/go-openai"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// MockRawResponse 模拟RawResponse
type MockRawResponse struct {
	*mockReadCloser
}

// MockOpenAIAPIClient 模拟OpenAI API客户端
type MockOpenAIAPIClient struct {
	mock.Mock
}

func (m *MockOpenAIAPIClient) CreateChatCompletionStream(ctx context.Context, request openai.ChatCompletionRequest) (*openai.ChatCompletionStream, error) {
	args := m.Called(ctx, request)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	// 检查类型转换
	if stream, ok := args.Get(0).(*openai.ChatCompletionStream); ok {
		return stream, args.Error(1)
	}
	// 如果是mock类型，返回nil
	return nil, args.Error(1)
}

func (m *MockOpenAIAPIClient) CreateChatCompletion(ctx context.Context, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(openai.ChatCompletionResponse), args.Error(1)
}

func (m *MockOpenAIAPIClient) CreateTranscription(ctx context.Context, request openai.AudioRequest) (openai.AudioResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(openai.AudioResponse), args.Error(1)
}

func (m *MockOpenAIAPIClient) CreateImage(ctx context.Context, request openai.ImageRequest) (openai.ImageResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(openai.ImageResponse), args.Error(1)
}

func (m *MockOpenAIAPIClient) CreateSpeech(ctx context.Context, request openai.CreateSpeechRequest) (openai.RawResponse, error) {
	args := m.Called(ctx, request)
	if args.Error(1) != nil {
		// 返回一个空的 RawResponse 和错误
		return openai.RawResponse{}, args.Error(1)
	}
	if args.Get(0) == nil {
		return openai.RawResponse{}, args.Error(1)
	}
	// 直接返回，假设 mock 会提供正确的类型
	return args.Get(0).(openai.RawResponse), args.Error(1)
}

func (m *MockOpenAIAPIClient) ListModels(ctx context.Context) (openai.ModelsList, error) {
	args := m.Called(ctx)
	return args.Get(0).(openai.ModelsList), args.Error(1)
}

// MockChatCompletionStream 模拟聊天完成流
type MockChatCompletionStream struct {
	mock.Mock
}

func (m *MockChatCompletionStream) Recv() (openai.ChatCompletionStreamResponse, error) {
	args := m.Called()
	return args.Get(0).(openai.ChatCompletionStreamResponse), args.Error(1)
}

func (m *MockChatCompletionStream) Close() error {
	args := m.Called()
	return args.Error(0)
}

// TestOpenAIManager_ChatLLMStream 测试流式聊天对话
func TestOpenAIManager_ChatLLMStream(t *testing.T) {
	tests := []struct {
		name       string
		setupMocks func(*MockOpenAIAPIClient)
		wantErr    bool
	}{
		{
			name: "TC1-流式聊天创建失败",
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				// 不创建stream，直接返回错误
				mockClient.On("CreateChatCompletionStream", mock.Anything, mock.Anything).Return((*openai.ChatCompletionStream)(nil), errors.New("stream creation failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockClient := &MockOpenAIAPIClient{}
			tt.setupMocks(mockClient)

			manager := newOpenaiManagerWithClient(context.Background(), mockClient)

			req := &openai.ChatCompletionRequest{
				Model: "gpt-3.5-turbo",
				Messages: []openai.ChatCompletionMessage{
					{Role: "user", Content: "Hello"},
				},
			}

			remoterModel := &repository.RemoterModelInfo{
				ModelName: "gpt-3.5-turbo",
			}

			respCh, errCh, err := manager.ChatLLMStream(context.Background(), remoterModel, req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, respCh)
				assert.Nil(t, errCh)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, respCh)
				assert.NotNil(t, errCh)

				// 读取第一个响应
				select {
				case resp := <-respCh:
					assert.Equal(t, "Hello! How can I help you?", resp.Choices[0].Delta.Content)
				case err := <-errCh:
					if err != io.EOF {
						t.Errorf("Unexpected error: %v", err)
					}
				}
			}

			mockClient.AssertExpectations(t)
		})
	}
}

// TestOpenAIManager_ChatLLMNonStream 测试非流式聊天对话
func TestOpenAIManager_ChatLLMNonStream(t *testing.T) {
	tests := []struct {
		name         string
		setupMock    func(*MockOpenAIAPIClient)
		req          *openai.ChatCompletionRequest
		remoterModel *repository.RemoterModelInfo
		wantErr      bool
		errContains  string
	}{
		{
			name: "TC1-成功创建非流式对话",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateChatCompletion", mock.Anything, mock.AnythingOfType("openai.ChatCompletionRequest")).
					Return(openai.ChatCompletionResponse{
						Choices: []openai.ChatCompletionChoice{
							{
								Message: openai.ChatCompletionMessage{
									Role:    "assistant",
									Content: "Hello! How can I help you?",
								},
							},
						},
					}, nil)
			},
			req: &openai.ChatCompletionRequest{
				Model: "gpt-3.5-turbo",
				Messages: []openai.ChatCompletionMessage{
					{
						Role:    "user",
						Content: "Hello",
					},
				},
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "gpt-3.5-turbo",
			},
			wantErr: false,
		},
		{
			name: "TC2-创建对话失败",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateChatCompletion", mock.Anything, mock.AnythingOfType("openai.ChatCompletionRequest")).
					Return(openai.ChatCompletionResponse{}, errors.New("API error"))
			},
			req: &openai.ChatCompletionRequest{
				Model: "gpt-3.5-turbo",
				Messages: []openai.ChatCompletionMessage{
					{
						Role:    "user",
						Content: "Hello",
					},
				},
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "gpt-3.5-turbo",
			},
			wantErr:     true,
			errContains: "API error",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mockClient := &MockOpenAIAPIClient{}

			if tt.setupMock != nil {
				tt.setupMock(mockClient)
			}

			manager := newOpenaiManagerWithClient(ctx, mockClient)

			resp, err := manager.ChatLLMNonStream(ctx, tt.remoterModel, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.Equal(t, "Hello! How can I help you?", resp.Choices[0].Message.Content)

			mockClient.AssertExpectations(t)
		})
	}
}

// TestOpenAIManager_ChatASR 测试语音转文本
func TestOpenAIManager_ChatASR(t *testing.T) {
	tests := []struct {
		name         string
		setupMock    func(*MockOpenAIAPIClient)
		req          *openai.AudioRequest
		remoterModel *repository.RemoterModelInfo
		wantText     string
		wantErr      bool
		errContains  string
	}{
		{
			name: "TC1-成功转录音频",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateTranscription", mock.Anything, mock.AnythingOfType("openai.AudioRequest")).
					Return(openai.AudioResponse{
						Text: "Hello, this is a test transcription.",
					}, nil)
			},
			req: &openai.AudioRequest{
				FilePath: "/tmp/test.mp3",
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "whisper-1",
			},
			wantText: "Hello, this is a test transcription.",
			wantErr:  false,
		},
		{
			name: "TC2-转录失败",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateTranscription", mock.Anything, mock.AnythingOfType("openai.AudioRequest")).
					Return(openai.AudioResponse{}, errors.New("transcription failed"))
			},
			req: &openai.AudioRequest{
				FilePath: "/tmp/test.mp3",
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "whisper-1",
			},
			wantErr:     true,
			errContains: "transcription failed",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mockClient := &MockOpenAIAPIClient{}

			if tt.setupMock != nil {
				tt.setupMock(mockClient)
			}

			manager := newOpenaiManagerWithClient(ctx, mockClient)

			text, err := manager.ChatASR(ctx, tt.remoterModel, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.wantText, text)

			mockClient.AssertExpectations(t)
		})
	}
}

// TestOpenAIManager_ChatTTS 测试TTS功能
func TestOpenAIManager_ChatTTS(t *testing.T) {
	tests := []struct {
		name         string
		setupMocks   func(*MockOpenAIAPIClient)
		remoterModel *repository.RemoterModelInfo
		req          *openai.CreateSpeechRequest
		wantErr      bool
	}{
		{
			name: "TC1-TTS成功",
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				// 使用反射创建有效的 RawResponse
				reader := io.NopCloser(strings.NewReader("audio data"))
				rawResp := createMockRawResponse(reader)
				mockClient.On("CreateSpeech", mock.Anything, mock.Anything).Return(rawResp, nil)
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "tts-1",
			},
			req: &openai.CreateSpeechRequest{
				Model: "tts-1",
				Input: "Hello world",
				Voice: "alloy",
			},
			wantErr: false,
		},
		{
			name: "TC2-TTS失败",
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				// Mock CreateSpeech 返回错误
				mockClient.On("CreateSpeech", mock.Anything, mock.Anything).Return(openai.RawResponse{}, errors.New("TTS error"))
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "tts-1",
			},
			req: &openai.CreateSpeechRequest{
				Model: "tts-1",
				Input: "Hello world",
				Voice: "alloy",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockClient := &MockOpenAIAPIClient{}
			if tt.setupMocks != nil {
				tt.setupMocks(mockClient)
			}

			manager := newOpenaiManagerWithClient(context.Background(), mockClient)

			resp, err := manager.ChatTTS(context.Background(), tt.remoterModel, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				// 由于我们无法正确模拟 RawResponse，这里只测试不会 panic
				// assert.NotNil(t, resp)
			}

			mockClient.AssertExpectations(t)
		})
	}
}

// TestOpenAIManager_ChatTTI 测试文本转图像
func TestOpenAIManager_ChatTTI(t *testing.T) {
	tests := []struct {
		name         string
		setupMock    func(*MockOpenAIAPIClient)
		req          *openai.ImageRequest
		remoterModel *repository.RemoterModelInfo
		wantData     []byte
		wantErr      bool
		errContains  string
	}{
		{
			name: "TC1-成功生成图像",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				// 返回base64数据而不是URL，避免访问外部URL
				mockClient.On("CreateImage", mock.Anything, mock.AnythingOfType("openai.ImageRequest")).
					Return(openai.ImageResponse{
						Data: []openai.ImageResponseDataInner{
							{
								B64JSON: "SGVsbG8gV29ybGQ=", // "Hello World" 的base64编码
							},
						},
					}, nil)
			},
			req: &openai.ImageRequest{
				Prompt: "A beautiful sunset",
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "dall-e-3",
			},
			wantData: []byte("Hello World"),
			wantErr:  false,
		},
		{
			name: "TC2-生成图像失败",
			setupMock: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateImage", mock.Anything, mock.AnythingOfType("openai.ImageRequest")).
					Return(openai.ImageResponse{}, errors.New("image generation failed"))
			},
			req: &openai.ImageRequest{
				Prompt: "A beautiful sunset",
			},
			remoterModel: &repository.RemoterModelInfo{
				ProviderModelName: "dall-e-3",
			},
			wantErr:     true,
			errContains: "image generation failed",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mockClient := &MockOpenAIAPIClient{}

			if tt.setupMock != nil {
				tt.setupMock(mockClient)
			}

			manager := newOpenaiManagerWithClient(ctx, mockClient)

			data, err := manager.ChatTTI(ctx, tt.remoterModel, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.wantData, data)

			mockClient.AssertExpectations(t)
		})
	}
}

// mockReadCloser 模拟io.ReadCloser
type mockReadCloser struct {
	data []byte
	pos  int
}

func (m *mockReadCloser) Read(p []byte) (n int, err error) {
	if m.pos >= len(m.data) {
		return 0, io.EOF
	}
	n = copy(p, m.data[m.pos:])
	m.pos += n
	return n, nil
}

func (m *mockReadCloser) Close() error {
	return nil
}

// mockRawResponse 实现 RawResponse 接口
type mockRawResponse struct {
	io.ReadCloser
	header http.Header
}

func newMockRawResponse(reader io.ReadCloser) *mockRawResponse {
	return &mockRawResponse{
		ReadCloser: reader,
		header:     make(http.Header),
	}
}

// 实现 RawResponse 接口的其他方法
func (m *mockRawResponse) GetRateLimitHeaders() openai.RateLimitHeaders {
	return openai.RateLimitHeaders{}
}

func (m *mockRawResponse) Header() http.Header {
	return m.header
}

func (m *mockRawResponse) SetHeader(header http.Header) {
	m.header = header
}

// createMockRawResponse 创建一个有效的 openai.RawResponse 实例
func createMockRawResponse(reader io.ReadCloser) openai.RawResponse {
	// 使用反射创建 RawResponse
	rawResp := openai.RawResponse{}

	// 获取 RawResponse 的值
	v := reflect.ValueOf(&rawResp).Elem()

	// 设置 ReadCloser 字段
	readCloserField := v.Field(0) // 第一个字段是 io.ReadCloser
	readCloserField = reflect.NewAt(readCloserField.Type(), unsafe.Pointer(readCloserField.UnsafeAddr())).Elem()
	readCloserField.Set(reflect.ValueOf(reader))

	return rawResp
}

// TestOpenAIManager_getAllModelInfo 测试获取所有模型信息
func TestOpenAIManager_getAllModelInfo(t *testing.T) {
	tests := []struct {
		name       string
		setupMocks func(*MockOpenAIAPIClient)
		wantErr    bool
		wantModels int
	}{
		{
			name: "TC1-获取模型成功",
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				modelsList := openai.ModelsList{
					Models: []openai.Model{
						{ID: "gpt-3.5-turbo", Object: "model"},
						{ID: "gpt-4", Object: "model"},
					},
				}
				mockClient.On("ListModels", mock.Anything).Return(modelsList, nil)
			},
			wantErr:    false,
			wantModels: 2,
		},
		{
			name: "TC2-获取模型失败",
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("ListModels", mock.Anything).Return(openai.ModelsList{}, errors.New("list models failed"))
			},
			wantErr:    true,
			wantModels: 0,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockClient := &MockOpenAIAPIClient{}
			tt.setupMocks(mockClient)

			manager := newOpenaiManagerWithClient(context.Background(), mockClient)

			models, err := manager.getAllModelInfo(context.Background())

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, models)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, models)
				assert.Equal(t, tt.wantModels, len(models))

				if tt.wantModels > 0 {
					assert.Contains(t, models, "gpt-3.5-turbo")
					assert.Contains(t, models, "gpt-4")
				}
			}

			mockClient.AssertExpectations(t)
		})
	}
}

// TestOpenAIManager_ChatLLMStream_Complete 测试流式聊天的完整功能
func TestOpenAIManager_ChatLLMStream_Complete(t *testing.T) {
	tests := []struct {
		name         string
		remoterModel *repository.RemoterModelInfo
		req          *openai.ChatCompletionRequest
		setupMocks   func(*MockOpenAIAPIClient)
		wantErr      bool
	}{
		{
			name: "TC1-流式聊天失败_创建流失败",
			remoterModel: &repository.RemoterModelInfo{
				Id:                   1,
				ModelName:            "gpt-3.5-turbo",
				ProviderModelName:    "gpt-3.5-turbo",
				ProviderModelVersion: "1.0",
				Provider:             "openai",
			},
			req: &openai.ChatCompletionRequest{
				Model:    "gpt-3.5-turbo",
				Messages: []openai.ChatCompletionMessage{{Role: "user", Content: "Hello"}},
			},
			setupMocks: func(mockClient *MockOpenAIAPIClient) {
				mockClient.On("CreateChatCompletionStream", mock.Anything, mock.Anything).Return(nil, errors.New("stream creation failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mockClient := &MockOpenAIAPIClient{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockClient)
			}

			manager := newOpenaiManagerWithClient(ctx, mockClient)

			msgCh, errCh, err := manager.ChatLLMStream(ctx, tt.remoterModel, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, msgCh)
				assert.Nil(t, errCh)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, msgCh)
				assert.NotNil(t, errCh)
			}

			mockClient.AssertExpectations(t)
		})
	}
}
