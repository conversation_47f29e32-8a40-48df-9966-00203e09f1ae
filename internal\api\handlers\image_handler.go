package handlers

import (
	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type ImageHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewImageHandler(service *service.Api) *ImageHandler {
	return &ImageHandler{
		service: service,
		log:     logger.GetLogger("image-handler"),
	}
}

// GetImageConfig 获取图像配置
// @Summary 获取图像配置
// @Description 图像管理
// @Accept json
// @Produce json
// @Success 200 {object} service.ImageConfig
// @Failure 500 {object} string
// @Router /api/v1/images/config [get]
func (h *ImageHandler) GetImageConfig(ctx *gin.Context) {
	config, err := h.service.Image.GetImageConfig(ctx)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Get image config failed", err)
		return
	}
	api.Success(ctx, config)
}

// GetImageBaseModels 获取图像基础模型
// @Summary 获取图像基础模型
// @Description 图像管理
// @Accept json
// @Produce json
// @Success 200 {object} []service.ImageModel
// @Failure 500 {object} string
// @Router /api/v1/images/models [get]
func (h *ImageHandler) GetImageBaseModels(ctx *gin.Context) {
	models, err := h.service.Image.GetImageBaseModels(ctx)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Get image base models failed", err)
		return
	}
	api.Success(ctx, models)
}

// UpdateImageConfig 更新图像配置
// @Summary 更新图像配置
// @Description 图像管理
// @Accept json
// @Produce json
// @Param config body service.ImageConfig true "图像配置"
// @Success 200 {object} service.ImageConfig
// @Failure 500 {object} string
// @Router /api/v1/images/config [post]
func (h *ImageHandler) UpdateImageConfig(ctx *gin.Context) {
	var config service.ImageConfig
	if err := ctx.ShouldBindJSON(&config); err != nil {
		api.Fail(ctx, api.CodeServerError, "Update image config failed", err)
		return
	}
	err := h.service.Image.UpdateImageConfig(ctx, &config)
	if err != nil {
		api.Fail(ctx, api.CodeServerError, "Update image config failed", err)
		return
	}
	api.Success(ctx, config)
}

func (h *ImageHandler) Close() {

}
