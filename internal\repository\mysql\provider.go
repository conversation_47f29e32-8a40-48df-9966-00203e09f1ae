package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelProvider 相关实现
type topaiModelProviderRepository struct {
	db *gorm.DB
}

func NewTopaiModelProviderRepository(db *gorm.DB) repository.TopaiModelProviderRepository {
	return &topaiModelProviderRepository{db: db}
}

func (r *topaiModelProviderRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelProvider, error) {
	var providers []*repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Find(&providers).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return providers, nil
}

func (r *topaiModelProviderRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelProvider, error) {
	var providers []*repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&providers).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return providers, nil
}

func (r *topaiModelProviderRepository) GetByUuids(ctx context.Context, uuids []string) ([]*repository.TopaiModelProvider, error) {
	var providers []*repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Where("uuid IN ?", uuids).Find(&providers).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return providers, nil
}

func (r *topaiModelProviderRepository) GetByWalletAddress(ctx context.Context, walletAddress string) (*repository.TopaiModelProvider, error) {
	var provider repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Where("wallet_address = ?", walletAddress).First(&provider).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &provider, nil
}

func (r *topaiModelProviderRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModelProvider, error) {

	var provider repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&provider).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &provider, nil
}

func (r *topaiModelProviderRepository) GetByNames(ctx context.Context, names []string) ([]*repository.TopaiModelProvider, error) {
	var providers []*repository.TopaiModelProvider
	if err := r.db.WithContext(ctx).Where("name IN ?", names).Find(&providers).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return providers, nil
}

func (r *topaiModelProviderRepository) Update(ctx context.Context, provider *repository.TopaiModelProvider) error {
	if err := r.db.WithContext(ctx).Save(provider).Error; err != nil {
		return err
	}
	return nil
}

func (r *topaiModelProviderRepository) Create(ctx context.Context, provider *repository.TopaiModelProvider) error {
	if err := r.db.WithContext(ctx).Create(provider).Error; err != nil {
		return err
	}
	return nil
}
