package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type providerTokenRepository struct {
	db *gorm.DB
}

func NewProviderTokenRepository(db *gorm.DB) repository.ProviderTokenRepository {
	return &providerTokenRepository{db: db}
}

func (r *providerTokenRepository) Create(ctx context.Context, token *repository.ProviderToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

func (r *providerTokenRepository) GetByToken(ctx context.Context, token string) (*repository.ProviderToken, error) {
	var providerToken repository.ProviderToken
	if err := r.db.WithContext(ctx).Where("token = ? AND deleted_at = 0", token).First(&providerToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &providerToken, nil
}

func (r *providerTokenRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.ProviderToken{}).
		Where("id = ? AND deleted_at = 0", id).
		Update("deleted_at", gorm.Expr("UNIX_TIMESTAMP()")).
		Error
}

func (r *providerTokenRepository) DeleteByProviderID(ctx context.Context, providerID uint) error {
	return r.db.WithContext(ctx).Model(&repository.ProviderToken{}).
		Where("provider_id = ? AND deleted_at = 0", providerID).
		Update("deleted_at", gorm.Expr("UNIX_TIMESTAMP()")).
		Error
}
