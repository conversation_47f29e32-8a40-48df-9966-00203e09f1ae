package service

import (
	"context"

	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// NewService 创建新的服务实例
func NewService(ctx context.Context, db *repository.DB) *Api {
	conf := config.GetConfig()

	// 创建各种服务
	userService := NewUserService(ctx, db)
	usageIdCh := make(chan uint, 1024)
	shadowAccountManage := NewShadowAccountManage(db, &conf.Service, &DefaultUtilChainFunc)
	userBalanceService := NewUserBalanceService(ctx, db, usageIdCh, conf.Service.CompanyUserID, shadowAccountManage)
	fileService := NewFileService(db, conf.Service.File)
	modelService := NewModelService(ctx, db, &conf.Service, usageIdCh, fileService)
	conversationService := NewConversationService(ctx, db)
	imageService := NewImageService(ctx, db)
	configService := NewConfigService(ctx, db)
	audioService := NewAudioService(ctx, db)
	// 创建文件服务
	topaiService := NewTopaiService(db)

	userAppService := NewUserAppService(db)
	providerService := NewProviderService(db)

	return &Api{
		User:         userService,
		UserBalance:  userBalanceService,
		Model:        modelService,
		Conversation: conversationService,
		File:         fileService,
		Image:        imageService,
		Config:       configService,
		Audio:        audioService,
		Topai:        topaiService,
		UserApp:      userAppService,
		Provider:     providerService,
		start:        []func(ctx context.Context) error{modelService.start, userBalanceService.start, shadowAccountManage.Start},
		close:        []func(){modelService.Close, userBalanceService.Close},
	}
}
