---
description: 
globs: 
alwaysApply: false
---
---
description: Core Architecture Design Rules for Kubernetes-style Projects
globs: **/*.go
alwaysApply: false
---

When designing the core architecture:

1. Layer Separation
- Maintain strict separation between cmd, pkg, and internal layers
- Ensure unidirectional dependencies (cmd -> pkg -> internal)
- Keep cross-layer communication through well-defined interfaces
- Avoid circular dependencies between packages

2. Component Structure
- Each component must have a clear single responsibility
- Implement standard lifecycle methods (Init, Start, Stop)
- Provide health check mechanisms
- Include proper resource cleanup

Required component structure:
```go
type Component interface {
    Init(ctx context.Context) error
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
    Health() Health
}

type Health struct {
    Status    HealthStatus
    Message   string
    Timestamp time.Time
}
```

3. Controller Pattern
- Implement the standard Kubernetes controller pattern
- Use informers for watching resources
- Implement proper reconciliation loops
- Handle edge cases and error conditions

Required controller structure:
```go
type Controller struct {
    client    kubernetes.Interface
    informer  cache.SharedIndexInformer
    queue     workqueue.RateLimitingInterface
    recorder  record.EventRecorder
}

func (c *Controller) Run(workers int, stopCh <-chan struct{}) error {
    defer c.queue.ShutDown()
    
    if !cache.WaitForCacheSync(stopCh, c.informer.HasSynced) {
        return fmt.Errorf("failed to sync caches")
    }
    
    for i := 0; i < workers; i++ {
        go wait.Until(c.runWorker, time.Second, stopCh)
    }
    
    <-stopCh
    return nil
}
```

4. Resource Management
- Implement proper resource cleanup
- Use finalizers for asynchronous cleanup
- Handle cascading deletions
- Maintain owner references

5. Error Handling
- Define clear error types
- Implement proper error wrapping
- Include context in errors
- Handle transient vs permanent failures

6. Configuration
- Use structured configuration
- Implement validation
- Support dynamic reloading
- Secure sensitive data
