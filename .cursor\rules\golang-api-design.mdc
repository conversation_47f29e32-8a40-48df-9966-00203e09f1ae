---
description: 
globs: 
alwaysApply: false
---
---
description: API Design Standards
globs: handlers/*.go
alwaysApply: false
---

# API Design Specification

## 1. Framework and Package Usage

```go
// Example using project's logger package
type testHandlers struct {
    service   Service
    validator Validator
    metrics   MetricsCollector
    log       *logger.ModuleLogger // Use project-root logger package
}

// Initialization example
func NewTestHandlers(serviceApi Services.Api, /* another params */) *testHandlers {
    return &testHandlers{
        log: logger.New("test_handlers"), // Initialize with project logger
        service: serviceApi,
        // Other dependencies...
    }
}

// Stop example
func (*th testHandlers) Stop() {

}
```

2. Unified Response Format

```go
// Response status codes
const (
    CodeSuccess      = 0     // Success
    CodeUnauthorized = 401   // Unauthorized
    CodeClientError  = 400   // User-facing error
    CodeServerError  = 500   // Internal error
)

// Unified response structure
type Response struct {
    Code    int         `json:"code"`           // Business status code
    Data    interface{} `json:"data"`           // Response payload
    Msg     string      `json:"msg"`            // User-facing message
    Error   string      `json:"error,omitempty"`// Debug info (development only)
}

// Success response
func Success(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, Response{
        Code: CodeSuccess,
        Data: data,
        Msg:  "Operation succeeded",
    })
}

// Error response
func Fail(c *gin.Context, code int, userMsg string, errorMsg string) {
    resp := Response{
        Code: code,
        Msg:  userMsg,
    }
    
    // Show error details in non-production environments
    if gin.Mode() != gin.ReleaseMode {
        resp.Error = errorMsg
    }
    
    c.JSON(httpStatusCode(code), resp)
}

// Map business code to HTTP status code
func httpStatusCode(bizCode int) int {
    switch bizCode {
    case CodeUnauthorized:
        return http.StatusUnauthorized
    case CodeClientError:
        return http.StatusBadRequest
    default:
        return http.StatusOK
    }
}
```

3. Swagger Documentation Example

```go
// @Summary      Create test item
// @Description  Create new test entry
// @Tags         Test Management
// @Accept       json
// @Produce      json
// @Param        X-Auth-Token  header    string         true  "Authentication token"
// @Param        request       body      CreateRequest  true  "Create request"
// @Success      200           {object}  Response{data=Result} 
// @Failure      400           {object}  Response
// @Failure      401           {object}  Response
// @Failure      500           {object}  Response
// @Router       /api/v1/tests [post]
func (th *testHandlers) Create(c *gin.Context) {
    ctx := c.Request.Context()
    th.log.Info("Handling create request") // Using project logger

    // Parse request
    var req CreateRequest
    if err := c.BindJSON(&req); err != nil {
        Fail(c, CodeClientError, "Invalid request format", err.Error())
        return
    }

    // Business logic
    result, err := th.service.Create(ctx, req)
    if err != nil {
        handleServiceError(c, err) // Unified error handling
        return
    }

    Success(c, result)
}

// Unified error handling
func handleServiceError(c *gin.Context, err error) {
    switch e := err.(type) {
    case *UnauthorizedError:
        Fail(c, CodeUnauthorized, "Please login first", e.Detail)
    case *ValidationError:
        Fail(c, CodeClientError, "Validation failed", e.Detail)
    case *BusinessError:
        Fail(c, CodeClientError, e.Message, e.Detail)
    default:
        Fail(c, CodeServerError, "System busy, please try later", err.Error())
    }
}
```

4. Error Handling Standards
- Client Errors (4xx):
  - Validation failures
  - Business rule violations
- Server Errors (5xx):
  - Database errors
  - Third-party service failures
- Authentication Errors (401):
  - Invalid tokens
  - Session expiration

5. Core Principles
- RESTful Endpoints
  - Proper HTTP methods (GET/POST/PUT/DELETE)
  - Standard HTTP status codes
  - Content negotiation support
  - Pagination support
- Request Validation
  - Input validation
  - Content-type handling
  - Header validation
  - Permission checks
- API Versioning
  - URL-based versioning (/api/v1/...)
  - Multiple version support
  - Deprecation handling
  - Change documentation
- Rate Limiting
  - Per-client rate limiting
  - Token bucket algorithm
  - X-RateLimit headers
  - Burst traffic handling
- Documentation
  - OpenAPI 3.0 compliance
  - Example payloads
  - Error response documentation
  - Changelog maintenance

6. Middleware Example

```go
// Global recovery middleware
func RecoveryMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        defer func() {
            if err := recover(); err != nil {
                logger.Get(c).Errorf("Panic recovered: %v", err)
                Fail(c, CodeServerError, "System error", fmt.Sprintf("%v", err))
                c.Abort()
            }
        }()
        c.Next()
    }
}
```
7. Best Practices
- Keep handlers focused on flow control
- Move business logic to service layer
- Use structured logging
- Validate all external inputs
- Provide meaningful error messages
- Support request tracing (X-Request-ID)
- Implement proper CORS policies
- Add security headers (HSTS, CSP, etc.)

This specification ensures consistent API implementation across the project while maintaining flexibility for different use cases. All handlers should follow these standards unless explicitly approved for exceptions.


