package service

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestMain 测试主函数，用于初始化测试环境
func TestMain(m *testing.M) {
	// 初始化日志系统
	logConfig := &config.LogConfig{
		Level:   "debug",
		Console: true,
	}
	logger.InitLogger(logConfig)

	// 运行测试
	code := m.Run()

	// 退出
	os.Exit(code)
}

// TestConversationService_NewConversationService 测试创建对话服务
func TestConversationService_NewConversationService(t *testing.T) {
	ctx := context.Background()
	db := &repository.DB{}
	service := NewConversationService(ctx, db)

	assert.NotNil(t, service)
	assert.Equal(t, db, service.db)
}

// TestConversationService_CreateConversation 测试创建对话
func TestConversationService_CreateConversation(t *testing.T) {
	tests := []struct {
		name    string
		user    *UserBaseOP
		req     *CreateConversationRequest
		setup   func(*testutil.MockDependencies)
		want    *ConversationOP
		wantErr bool
	}{
		{
			name: "TC1-成功创建对话",
			user: &UserBaseOP{ID: 1, IdentityID: "user-1"},
			req: &CreateConversationRequest{
				Chat: &Conversation{
					UUID:      "chat-1",
					Title:     "Test Chat",
					Timestamp: 1753180462000, // 固定时间戳
					System:    "You are a helpful assistant",
					Models:    []string{"test-model"},
					History: &ConversationHistory{
						Messages:  make(map[string]*ConversationMessage),
						CurrentId: "msg-1",
					},
					Messages: []*ConversationMessage{
						{
							UUID:      "msg-1",
							Content:   "Hello",
							Role:      "user",
							Timestamp: 1753180462, // 固定时间戳
						},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock RemoterModelInfo.GetByModelName for model validation
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, mock.AnythingOfType("string")).Return(&repository.RemoterModelInfo{
					Id:        1,
					ModelName: "test-model",
					ModelType: repository.RemoterModelTypeLLM,
				}, nil)

				mocks.DB.Conversation.On("Create", mock.Anything, mock.AnythingOfType("*repository.Conversation")).
					Run(func(args mock.Arguments) {
						arg := args.Get(1).(*repository.Conversation)
						arg.ID = 1
					}).Return(nil)
				mocks.DB.ConversationMessage.On("Create", mock.Anything, mock.AnythingOfType("*repository.ConversationMessage")).Return(nil)
			},
			want: &ConversationOP{
				Chat: &Conversation{
					UUID:      "chat-1",
					Title:     "Test Chat",
					Timestamp: 1753180462000, // 固定时间戳
					System:    "You are a helpful assistant",
					Models:    []string{"test-model"},
					History: &ConversationHistory{
						Messages:  make(map[string]*ConversationMessage),
						CurrentId: "msg-1",
					},
					Messages: []*ConversationMessage{
						{
							UUID:      "msg-1",
							Content:   "Hello",
							Role:      "user",
							Timestamp: 1753180462, // 固定时间戳除以1000
						},
					},
				},
				Archived:  false,
				CreatedAt: 1753180462 / 1000, // 固定时间戳除以1000
				FolderId:  nil,
				UUID:      "", // 将在测试中动态设置
				Meta:      nil,
				Pinned:    false,
				Title:     "Test Chat",
				UpdatedAt: 1753180462 / 1000, // 固定时间戳除以1000
				UserId:    "user-1",
			},
			wantErr: false,
		},
		{
			name: "TC2-创建对话失败",
			user: &UserBaseOP{ID: 1, IdentityID: "user-1"},
			req: &CreateConversationRequest{
				Chat: &Conversation{
					UUID:      "chat-1",
					Title:     "Test Chat",
					Timestamp: time.Now().UnixMilli(),
					Models:    []string{"test-model"},
					History: &ConversationHistory{
						Messages:  make(map[string]*ConversationMessage),
						CurrentId: "msg-1",
					},
					Messages: []*ConversationMessage{
						{
							UUID:      "msg-1",
							Content:   "Hello",
							Role:      "user",
							Timestamp: time.Now().Unix(),
						},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock RemoterModelInfo.GetByModelName for model validation
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, mock.AnythingOfType("string")).Return(&repository.RemoterModelInfo{
					Id:        1,
					ModelName: "test-model",
					ModelType: repository.RemoterModelTypeLLM,
				}, nil)

				mocks.DB.Conversation.On("Create", mock.Anything, mock.AnythingOfType("*repository.Conversation")).
					Return(errors.New("database error"))
				mocks.DB.ConversationMessage.On("Create", mock.Anything, mock.AnythingOfType("*repository.ConversationMessage")).Return(nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			}
			service := NewConversationService(ctx, db)

			got, err := service.CreateConversation(ctx, tt.user, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, got)

			// 更新期望的UUID为实际生成的UUID
			tt.want.UUID = got.UUID
			assert.Equal(t, tt.want, got)

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_GetConversation 测试获取对话
func TestConversationService_GetConversation(t *testing.T) {
	tests := []struct {
		name             string
		user             *UserBaseOP
		conversationUUID string
		setup            func(*testutil.MockDependencies)
		want             *ConversationOP
		wantErr          bool
	}{
		{
			name:             "TC1-成功获取对话",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			setup: func(mocks *testutil.MockDependencies) {
				createTime := time.Unix(1753180462, 0)
				updateTime := time.Unix(1753180462, 0)
				conv := &repository.Conversation{
					ID:         1,
					UserID:     1,
					IdentityID: "user-1",
					UUID:       "chat-1",
					Title:      "Test Chat",
					System:     "You are a helpful assistant",
					CreatedAt:  &createTime,
					UpdatedAt:  &updateTime,
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conv, nil)

				// Mock GetByConversationID for conversation messages
				messageCreateTime := time.Unix(1753180462, 0)
				messages := []*repository.ConversationMessage{
					{
						ID:               1,
						UUID:             "msg-1",
						ConversationID:   1,
						ConversationUUID: "chat-1",
						Role:             "user",
						Content:          "Hello",
						Models:           "test-model",
						IsDone:           1,
						CreatedAt:        &messageCreateTime,
					},
				}
				mocks.DB.ConversationMessage.On("GetByConversationID", mock.Anything, uint(1)).Return(messages, nil)

				// No need to mock File.FindByUserIDAndUUIDs since fileUUIDs will be empty
			},
			want: &ConversationOP{
				Chat: &Conversation{
					UUID:   "chat-1",
					Title:  "Test Chat",
					System: "You are a helpful assistant",
				},
				UUID: "chat-1",
			},
			wantErr: false,
		},
		{
			name:             "TC2-对话不存在",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "non-existent").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:             "TC3-对话不属于用户",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			setup: func(mocks *testutil.MockDependencies) {
				conv := &repository.Conversation{
					ID:         1,
					UserID:     2, // 不同的用户ID
					IdentityID: "user-2",
					UUID:       "chat-1",
					Title:      "Test Chat",
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conv, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				File:                mocks.DB.File,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			got, err := service.GetConversation(ctx, tt.user, tt.conversationUUID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.UUID, got.UUID)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_GetConversationList 测试获取对话列表
func TestConversationService_GetConversationList(t *testing.T) {
	tests := []struct {
		name    string
		user    *UserBaseOP
		page    int
		limit   int
		setup   func(*testutil.MockDependencies)
		want    []*ConversationListOP
		wantErr bool
	}{
		{
			name:  "TC1-成功获取对话列表",
			user:  &UserBaseOP{ID: 1, IdentityID: "user-1"},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				createTime := time.Unix(1753180462, 0)
				updateTime := time.Unix(1753180462, 0)
				convs := []*repository.Conversation{
					{
						ID:        1,
						UserID:    1,
						UUID:      "chat-1",
						Title:     "Chat 1",
						CreatedAt: &createTime,
						UpdatedAt: &updateTime,
					},
					{
						ID:        2,
						UserID:    1,
						UUID:      "chat-2",
						Title:     "Chat 2",
						CreatedAt: &createTime,
						UpdatedAt: &updateTime,
					},
				}
				mocks.DB.Conversation.On("FindByUserIDOrderByUpdatedAtDesc", mock.Anything, uint(1), 10, 0).Return(convs, nil)
			},
			want: []*ConversationListOP{
				{
					UUID:  "chat-1",
					Title: "Chat 1",
				},
				{
					UUID:  "chat-2",
					Title: "Chat 2",
				},
			},
			wantErr: false,
		},
		{
			name:  "TC2-获取对话列表失败",
			user:  &UserBaseOP{ID: 1, IdentityID: "user-1"},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.Conversation.On("FindByUserIDOrderByUpdatedAtDesc", mock.Anything, uint(1), 10, 0).Return(nil, errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			got, err := service.GetConversationList(ctx, tt.user, tt.page, tt.limit)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Len(t, got, len(tt.want))
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_DeleteConversation 测试删除对话
func TestConversationService_DeleteConversation(t *testing.T) {
	tests := []struct {
		name             string
		user             *UserBaseOP
		conversationUUID string
		setup            func(*testutil.MockDependencies)
		wantErr          bool
	}{
		{
			name:             "TC1-成功删除对话",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			setup: func(mocks *testutil.MockDependencies) {
				conv := &repository.Conversation{
					ID:         1,
					UserID:     1,
					IdentityID: "user-1",
					UUID:       "chat-1",
					Title:      "Test Chat",
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conv, nil)
				mocks.DB.Conversation.On("Delete", mock.Anything, uint(1)).Return(nil)
			},
			wantErr: false,
		},
		{
			name:             "TC2-对话不存在",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "non-existent").Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name:             "TC3-对话不属于用户",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			setup: func(mocks *testutil.MockDependencies) {
				conv := &repository.Conversation{
					ID:         1,
					UserID:     2, // 不同的用户ID
					IdentityID: "user-2",
					UUID:       "chat-1",
					Title:      "Test Chat",
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conv, nil)
			},
			wantErr: true,
		},
		{
			name:             "TC4-删除失败",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			setup: func(mocks *testutil.MockDependencies) {
				conv := &repository.Conversation{
					ID:         1,
					UserID:     1,
					IdentityID: "user-1",
					UUID:       "chat-1",
					Title:      "Test Chat",
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conv, nil)
				mocks.DB.Conversation.On("Delete", mock.Anything, uint(1)).Return(errors.New("delete failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			err := service.DeleteConversation(ctx, tt.user, tt.conversationUUID)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_UpdateTTIChatShare 测试更新TTI聊天分享状态
func TestConversationService_UpdateTTIChatShare(t *testing.T) {
	tests := []struct {
		name        string
		user        *UserBaseOP
		ttiChatUUID string
		isShare     bool
		setup       func(*testutil.MockDependencies)
		wantErr     bool
	}{
		{
			name:        "TC1-成功更新分享状态",
			user:        &UserBaseOP{ID: 1, IdentityID: "user-1"},
			ttiChatUUID: "tti-1",
			isShare:     true,
			setup: func(mocks *testutil.MockDependencies) {
				ttiRecord := &repository.UserChatTTIRecord{
					ID:     1,
					UUID:   "tti-1",
					UserID: 1,
				}
				mocks.DB.UserChatTTIRecord.On("GetByUUID", mock.Anything, "tti-1").Return(ttiRecord, nil)
				mocks.DB.UserChatTTIRecord.On("UpdateIsSharedByUUID", mock.Anything, "tti-1", uint8(1)).Return(nil)
			},
			wantErr: false,
		},
		{
			name:        "TC2-TTI记录不存在",
			user:        &UserBaseOP{ID: 1, IdentityID: "user-1"},
			ttiChatUUID: "non-existent",
			isShare:     true,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserChatTTIRecord.On("GetByUUID", mock.Anything, "non-existent").Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name:        "TC3-TTI记录不属于用户",
			user:        &UserBaseOP{ID: 1, IdentityID: "user-1"},
			ttiChatUUID: "tti-1",
			isShare:     true,
			setup: func(mocks *testutil.MockDependencies) {
				ttiRecord := &repository.UserChatTTIRecord{
					ID:     1,
					UUID:   "tti-1",
					UserID: 2, // 不同的用户ID
				}
				mocks.DB.UserChatTTIRecord.On("GetByUUID", mock.Anything, "tti-1").Return(ttiRecord, nil)
			},
			wantErr: true,
		},
		{
			name:        "TC4-更新失败",
			user:        &UserBaseOP{ID: 1, IdentityID: "user-1"},
			ttiChatUUID: "tti-1",
			isShare:     true,
			setup: func(mocks *testutil.MockDependencies) {
				ttiRecord := &repository.UserChatTTIRecord{
					ID:     1,
					UUID:   "tti-1",
					UserID: 1,
				}
				mocks.DB.UserChatTTIRecord.On("GetByUUID", mock.Anything, "tti-1").Return(ttiRecord, nil)
				mocks.DB.UserChatTTIRecord.On("UpdateIsSharedByUUID", mock.Anything, "tti-1", uint8(1)).Return(errors.New("update failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				UserChatTTIRecord:   mocks.DB.UserChatTTIRecord,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			err := service.UpdateTTIChatShare(ctx, tt.user, tt.ttiChatUUID, tt.isShare)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_GetUserTTIChatShared 测试获取用户TTI聊天分享列表
func TestConversationService_GetUserTTIChatShared(t *testing.T) {
	tests := []struct {
		name    string
		user    *UserBaseOP
		page    int
		limit   int
		setup   func(*testutil.MockDependencies)
		want    *TTIChatSharedListOP
		wantErr bool
	}{
		{
			name:  "TC1-成功获取用户TTI分享列表",
			user:  &UserBaseOP{ID: 1, IdentityID: "user-1"},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				createTime := time.Now()
				ttiRecords := []*repository.UserChatTTIRecord{
					{
						ID:        1,
						UUID:      "tti-1",
						UserID:    1,
						ModelID:   "model-1",
						Content:   "Test content 1",
						IsShared:  1,
						FileUUIDs: "", // 空文件UUIDs
						CreatedAt: &createTime,
					},
					{
						ID:        2,
						UUID:      "tti-2",
						UserID:    1,
						ModelID:   "model-2",
						Content:   "Test content 2",
						IsShared:  1,
						FileUUIDs: "", // 空文件UUIDs
						CreatedAt: &createTime,
					},
				}
				mocks.DB.UserChatTTIRecord.On("GetSharedByUserID", mock.Anything, uint(1), 10, 0, "id DESC").Return(ttiRecords, nil)
				mocks.DB.UserChatTTIRecord.On("GetSharedCountByUserID", mock.Anything, uint(1)).Return(int64(2), nil)
				// Mock File.GetByUUID for empty fileUUID
				mocks.DB.File.On("GetByUUID", mock.Anything, "").Return(nil, nil)
			},
			want: &TTIChatSharedListOP{
				Count: 2,
				List: []*TTIChatSharedOP{
					{
						UUID:     "tti-1",
						ModelID:  "model-1",
						Content:  "Test content 1",
						IsShared: true,
					},
					{
						UUID:     "tti-2",
						ModelID:  "model-2",
						Content:  "Test content 2",
						IsShared: true,
					},
				},
			},
			wantErr: false,
		},
		{
			name:  "TC2-获取失败",
			user:  &UserBaseOP{ID: 1, IdentityID: "user-1"},
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserChatTTIRecord.On("GetSharedCountByUserID", mock.Anything, uint(1)).Return(int64(0), errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				UserChatTTIRecord:   mocks.DB.UserChatTTIRecord,
				File:                mocks.DB.File,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			got, err := service.GetUserTTIChatShared(ctx, tt.user, tt.page, tt.limit)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.Count, got.Count)
				assert.Len(t, got.List, len(tt.want.List))
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConversationService_GetAllTTIChatShared 测试获取所有TTI聊天分享列表
func TestConversationService_GetAllTTIChatShared(t *testing.T) {
	tests := []struct {
		name    string
		page    int
		limit   int
		setup   func(*testutil.MockDependencies)
		want    *TTIChatSharedListOP
		wantErr bool
	}{
		{
			name:  "TC1-成功获取所有TTI分享列表",
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				createTime := time.Now()
				ttiRecords := []*repository.UserChatTTIRecord{
					{
						ID:        1,
						UUID:      "tti-1",
						UserID:    1,
						ModelID:   "model-1",
						Content:   "Test content 1",
						IsShared:  1,
						FileUUIDs: "", // 空文件UUIDs
						CreatedAt: &createTime,
					},
					{
						ID:        2,
						UUID:      "tti-2",
						UserID:    2,
						ModelID:   "model-2",
						Content:   "Test content 2",
						IsShared:  1,
						FileUUIDs: "", // 空文件UUIDs
						CreatedAt: &createTime,
					},
				}
				mocks.DB.UserChatTTIRecord.On("GetShared", mock.Anything, 10, 0, "id DESC").Return(ttiRecords, nil)
				mocks.DB.UserChatTTIRecord.On("GetSharedCount", mock.Anything).Return(int64(2), nil)
				// Mock File.GetByUUID for empty fileUUID
				mocks.DB.File.On("GetByUUID", mock.Anything, "").Return(nil, nil)
			},
			want: &TTIChatSharedListOP{
				Count: 2,
				List: []*TTIChatSharedOP{
					{
						UUID:     "tti-1",
						ModelID:  "model-1",
						Content:  "Test content 1",
						IsShared: true,
					},
					{
						UUID:     "tti-2",
						ModelID:  "model-2",
						Content:  "Test content 2",
						IsShared: true,
					},
				},
			},
			wantErr: false,
		},
		{
			name:  "TC2-获取失败",
			page:  1,
			limit: 10,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserChatTTIRecord.On("GetSharedCount", mock.Anything).Return(int64(0), errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				UserChatTTIRecord:   mocks.DB.UserChatTTIRecord,
				File:                mocks.DB.File,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			got, err := service.GetAllTTIChatShared(ctx, tt.page, tt.limit)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.Count, got.Count)
				assert.Len(t, got.List, len(tt.want.List))
			}

			mocks.AssertExpectations(t)
		})
	}
}

// BenchmarkConversationService_NewService 创建对话服务性能基准测试
func BenchmarkConversationService_NewService(b *testing.B) {
	ctx := context.Background()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewConversationService(ctx, &repository.DB{})
		_ = service
	}
}

// TestConversationService_ValidateConversationAccess 测试验证对话访问权限
func TestConversationService_ValidateConversationAccess(t *testing.T) {
	tests := []struct {
		name             string
		conversationUUID string
		userID           uint
		setup            func(*testutil.MockDependencies)
		wantErr          bool
		errType          string
	}{
		{
			name:             "TC1-有访问权限",
			conversationUUID: "conv-123",
			userID:           1,
			setup: func(mocks *testutil.MockDependencies) {
				now := time.Now()
				conversation := &repository.Conversation{
					UUID:      "conv-123",
					UserID:    1,
					CreatedAt: &now,
					UpdatedAt: &now,
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "conv-123").Return(conversation, nil)
				// Mock消息列表
				mocks.DB.ConversationMessage.On("GetByConversationID", mock.Anything, mock.AnythingOfType("uint")).Return([]*repository.ConversationMessage{}, nil)
			},
			wantErr: false,
		},
		{
			name:             "TC2-无访问权限",
			conversationUUID: "conv-123",
			userID:           2,
			setup: func(mocks *testutil.MockDependencies) {
				conversation := &repository.Conversation{
					UUID:   "conv-123",
					UserID: 1, // 属于用户1
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "conv-123").Return(conversation, nil)
			},
			wantErr: true,
			errType: "conversation not found",
		},
		{
			name:             "TC3-对话不存在",
			conversationUUID: "invalid-conv",
			userID:           1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "invalid-conv").Return(nil, nil)
			},
			wantErr: true,
			errType: "conversation not found",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			user := &UserBaseOP{ID: tt.userID, IdentityID: "uid-1"}

			// 通过GetConversation来测试访问权限
			result, err := service.GetConversation(ctx, user, tt.conversationUUID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// TestConversationService_ProcessMessageFiles 测试处理消息文件
func TestConversationService_ProcessMessageFiles(t *testing.T) {
	tests := []struct {
		name    string
		files   []*ConversationMessageFile
		setup   func(*testutil.MockDependencies)
		wantErr bool
		errType string
	}{
		{
			name:  "TC1-处理文件成功",
			files: []*ConversationMessageFile{}, // 空文件列表，因为文件不被支持
			setup: func(mocks *testutil.MockDependencies) {
				// Mock模型信息
				modelInfo := &repository.RemoterModelInfo{
					ModelName: "gpt-4",
					ModelType: repository.RemoterModelTypeLLM,
				}
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "gpt-4").Return(modelInfo, nil)

				// Mock对话创建
				mocks.DB.Conversation.On("Create", mock.Anything, mock.Anything).Return(nil)
				mocks.DB.ConversationMessage.On("Create", mock.Anything, mock.AnythingOfType("*repository.ConversationMessage")).Return(nil)

				// Mock事务 - 直接调用函数，不需要mock
			},
			wantErr: false,
		},
		{
			name: "TC2-文件不被支持",
			files: []*ConversationMessageFile{
				{
					FileID: "file-123",
					Name:   "test.txt",
					Size:   1024,
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock模型信息
				modelInfo := &repository.RemoterModelInfo{
					ModelName: "gpt-4",
					ModelType: repository.RemoterModelTypeLLM,
				}
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "gpt-4").Return(modelInfo, nil)
			},
			wantErr: true,
			errType: "file is not supported",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 创建一个mock的DB，包含RunWithTx函数
			mockDB := &repository.DB{
				File:                mocks.DB.File,
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
			}

			// 设置RunWithTx函数
			mockDB.RunWithTx = func(ctx context.Context, fn func(ctx context.Context, db *repository.DB) error) error {
				return fn(ctx, mockDB)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx: func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
					return mocks.DB.RunWithTx(ctx, fn)
				},
			})

			// 通过CreateConversation来测试文件处理
			user := &UserBaseOP{ID: 1, IdentityID: "uid-1"}
			req := &CreateConversationRequest{
				Chat: &Conversation{
					Title:     "Test Conversation",
					Models:    []string{"gpt-4"},
					Timestamp: time.Now().UnixMilli(),
					History: &ConversationHistory{
						CurrentId: "",
					},
					Messages: []*ConversationMessage{
						{
							UUID:      "msg-1",
							Content:   "Hello",
							Role:      "user",
							Timestamp: time.Now().Unix(),
							Models:    []string{"gpt-4"},
							Files:     tt.files,
						},
					},
				},
			}

			result, err := service.CreateConversation(ctx, user, req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				// 由于文件处理可能涉及复杂逻辑，这里主要确保不panic
				assert.NotNil(t, result)
			}
		})
	}
}

// TestConversationService_UpdateConversation 测试更新对话
func TestConversationService_UpdateConversation(t *testing.T) {
	tests := []struct {
		name             string
		user             *UserBaseOP
		conversationUUID string
		req              *UpdateConversationRequest
		setup            func(*testutil.MockDependencies)
		want             *ConversationOP
		wantErr          bool
	}{
		{
			name:             "TC1-成功更新对话",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			req: &UpdateConversationRequest{
				Chat: &Conversation{
					UUID: "chat-1",
					History: &ConversationHistory{
						CurrentId: "msg-2",
						Messages: map[string]*ConversationMessage{
							"msg-1": {
								UUID:      "msg-1",
								Content:   "Hello",
								Role:      "user",
								Model:     "gpt-3.5-turbo",
								Timestamp: 1753180462,
								Done:      true,
							},
							"msg-2": {
								UUID:      "msg-2",
								Content:   "Hi there!",
								Role:      "assistant",
								Model:     "gpt-3.5-turbo",
								Timestamp: 1753180463,
								Done:      true,
							},
						},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock conversation
				now := time.Now()
				conversation := &repository.Conversation{
					ID:        1,
					UUID:      "chat-1",
					UserID:    1,
					CreatedAt: &now,
					UpdatedAt: &now,
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conversation, nil)

				// Mock existing messages
				existingMessages := []*repository.ConversationMessage{
					{
						ID:             1,
						UUID:           "msg-1",
						ConversationID: 1,
						Content:        "Hello",
						Role:           "user",
						Models:         "gpt-3.5-turbo",
						IsDone:         0,
						CreatedAt:      &now,
					},
				}
				mocks.DB.ConversationMessage.On("GetByConversationID", mock.Anything, uint(1)).Return(existingMessages, nil)

				// Mock model validation
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "gpt-3.5-turbo").Return(&repository.RemoterModelInfo{
					Id:        1,
					ModelName: "gpt-3.5-turbo",
					ModelType: repository.RemoterModelTypeLLM,
				}, nil)
				// Mock empty model string case - return a valid model to avoid "model not found" error
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "").Return(&repository.RemoterModelInfo{
					Id:        2,
					ModelName: "",
					ModelType: repository.RemoterModelTypeLLM,
				}, nil)

				// Mock transaction - RunWithTx 会自动调用传入的函数

				// Mock message updates
				mocks.DB.ConversationMessage.On("Update", mock.Anything, mock.AnythingOfType("*repository.ConversationMessage")).Return(nil)
				mocks.DB.ConversationMessage.On("Create", mock.Anything, mock.AnythingOfType("*repository.ConversationMessage")).Return(nil)
				mocks.DB.Conversation.On("UpdateCurrentMsgUUIDByUUID", mock.Anything, "chat-1", "msg-2").Return(nil)

				// Mock GetConversation for return value - GetConversation calls GetByConversationID, not GetByConversationUUID
			},
			want:    &ConversationOP{},
			wantErr: false,
		},
		{
			name:             "TC2-对话不存在",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-999",
			req: &UpdateConversationRequest{
				Chat: &Conversation{
					UUID: "chat-999",
					History: &ConversationHistory{
						CurrentId: "msg-1",
						Messages:  map[string]*ConversationMessage{},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-999").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:             "TC3-对话不属于用户",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			req: &UpdateConversationRequest{
				Chat: &Conversation{
					UUID: "chat-1",
					History: &ConversationHistory{
						CurrentId: "msg-1",
						Messages:  map[string]*ConversationMessage{},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				conversation := &repository.Conversation{
					ID:     1,
					UUID:   "chat-1",
					UserID: 2, // 不同的用户ID
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conversation, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:             "TC4-模型不存在",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			req: &UpdateConversationRequest{
				Chat: &Conversation{
					UUID: "chat-1",
					History: &ConversationHistory{
						CurrentId: "msg-1",
						Messages: map[string]*ConversationMessage{
							"msg-1": {
								UUID:      "msg-1",
								Content:   "Hello",
								Role:      "assistant",
								Model:     "invalid-model",
								Timestamp: 1753180462,
							},
						},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				conversation := &repository.Conversation{
					ID:     1,
					UUID:   "chat-1",
					UserID: 1,
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conversation, nil)
				mocks.DB.ConversationMessage.On("GetByConversationID", mock.Anything, uint(1)).Return([]*repository.ConversationMessage{}, nil)
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "invalid-model").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:             "TC5-模型类型不支持",
			user:             &UserBaseOP{ID: 1, IdentityID: "user-1"},
			conversationUUID: "chat-1",
			req: &UpdateConversationRequest{
				Chat: &Conversation{
					UUID: "chat-1",
					History: &ConversationHistory{
						CurrentId: "msg-1",
						Messages: map[string]*ConversationMessage{
							"msg-1": {
								UUID:      "msg-1",
								Content:   "Hello",
								Role:      "assistant",
								Model:     "tts-model",
								Timestamp: 1753180462,
							},
						},
					},
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				conversation := &repository.Conversation{
					ID:     1,
					UUID:   "chat-1",
					UserID: 1,
				}
				mocks.DB.Conversation.On("GetByUUID", mock.Anything, "chat-1").Return(conversation, nil)
				mocks.DB.ConversationMessage.On("GetByConversationID", mock.Anything, uint(1)).Return([]*repository.ConversationMessage{}, nil)
				mocks.DB.RemoterModelInfo.On("GetByModelName", mock.Anything, "tts-model").Return(&repository.RemoterModelInfo{
					Id:        1,
					ModelName: "tts-model",
					ModelType: repository.RemoterModelTypeTTS, // 不支持的模型类型
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConversationService(ctx, &repository.DB{
				Conversation:        mocks.DB.Conversation,
				ConversationMessage: mocks.DB.ConversationMessage,
				RemoterModelInfo:    mocks.DB.RemoterModelInfo,
				RunWithTx:           mocks.DB.RunWithTx,
			})

			got, err := service.UpdateConversation(ctx, tt.user, tt.conversationUUID, tt.req)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}
