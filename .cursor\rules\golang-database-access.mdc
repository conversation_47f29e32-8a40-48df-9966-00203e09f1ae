---
description: 
globs: 
alwaysApply: false
---
---
description: Database Access and Query Patterns
globs: **/repository/**/*.go, **/store/**/*.go
---

1. Core Principles
- Interface-Centric Design
  ```go
    // Generic database operations interface
    type Database interface {
        Users() UserRepository
        Orders() OrderRepository
        RunTransaction(ctx context.Context, fn func(Database) error) error
        // Add other repository accessors...
    }

    // Example repository interface
    type UserRepository interface {
        GetByID(ctx context.Context, id string) (*User, error)
        Create(ctx context.Context, user *User) error
        Update(ctx context.Context, user *User) error
        Delete(ctx context.Context, id string) error
    }
    ```
- Implementation Neutrality
  - Database-specific code MUST be isolated in implementation packages
  - Core business logic MUST NOT contain any SQL/dialect-specific code

2. Configuration Standards
- Connection Pooling (All Databases)
  ```yaml
  # Standard connection pool configuration (applies to all SQL databases)
    database:
        max_open_conns: 25       # <= 2 * CPU cores
        max_idle_conns: 5        # >= average concurrent idle connections
        conn_max_lifetime: 30m   # < database server's wait_timeout
        conn_max_idletime: 10m   # < database server's interactive_timeout
  ```
- Timeout Configuration
  ```go
  type Timeouts struct {
      Connect    time.Duration `json:"connect"`    // 3s
      Query      time.Duration `json:"query"`      // 10s
      Transaction time.Duration `json:"transaction"` // 30s
  }
  ```
- Health Monitoring
  ```go
  // Mandatory health checks
  type HealthChecker interface {
      Ping(ctx context.Context) error
      ReportMetrics(collector metrics.Collector)
  } 
  ```

3. Transaction Management
- Transaction Template
  ```go
  // Generic transaction handler
  func WithTransaction(ctx context.Context, db Database, fn func(Database) error) error {
      txDB, err := db.BeginTx(ctx)
      if err != nil {
          return err
      }
      
      defer func() {
          if p := recover(); p != nil {
              txDB.Rollback()
              panic(p)
          }
      }()
      
      if err := fn(txDB); err != nil {
          txDB.Rollback()
          return err
      }
      
      return txDB.Commit()
  }
  ```
- Cross-DB Transactions (if supported)
  ```go
  // Distributed transaction pattern
  func XATransaction(ctx context.Context, dbs ...Database) (XATx, error) {
      // Implementation varies by database
  }
  ```

4. MySQL-Specific Implementation (Example)
- MySQL Configuration
  ```go
  type MySQLConfig struct {
      Host            string        `json:"host"`
      Port            int           `json:"port"`          // Default: 3306
      Schema          string        `json:"schema"`
      ParseTime       bool          `json:"parse_time"`    // true
      Collation       string        `json:"collation"`     // utf8mb4_unicode_ci
      Timeouts        Timeouts      `json:"timeouts"`
      Pool            PoolConfig    `json:"pool"`
  }
  ```
- MySQL Implementation Package
  ```text
  /repository
    /mysql
      client.go      # Implements Database interface
      repositories/  # MySQL-specific repository implementations
      migrations/    # Database schema management
  ```

5. Error Handling Rules
- Standard Error Types
  ```go
  var (
      ErrDatabaseUnavailable = errors.New("database unavailable")
      ErrTimeout            = errors.New("database operation timeout")
      ErrConstraintViolation= errors.New("constraint violation")
      ErrDeadlock           = errors.New("deadlock detected")
  )
  ```
- Error Wrapping
  ```go
  // All database errors MUST be wrapped with context
  func (r *userRepo) GetByID(ctx context.Context, id string) (*User, error) {
      user, err := r.db.Get(id)
      if err != nil {
          return nil, fmt.Errorf("userRepo.GetByID(%s): %w", id, err)
      }
      return user, nil
  }
  ```

6. Observability Requirements
- Mandatory Metrics
  ```go
  type DatabaseMetrics interface {
      RecordQueryDuration(query string, duration time.Duration)
      RecordConnectionState(open, idle int)
      RecordError(errorType string)
  }
  ```
- Tracing Integration
  ```go
  // All database operations MUST propagate tracing context
  func (r *userRepo) GetByID(ctx context.Context, id string) (*User, error) {
      span := trace.SpanFromContext(ctx)
      defer span.End()
      
      // Database operation here...
  }
  ```

7. Security Rules
- Credential Management
  ```text
  - NEVER commit credentials to source control
  - Use secret management system (Vault/AWS Secrets Manager)
  - Credentials MUST be rotated every 90 days
  ```
- Query Security
  ```go
  // PROHIBITED: Raw SQL with string concatenation
  func UnsafeGetUser(name string) {
      db.Exec(fmt.Sprintf("SELECT * FROM users WHERE name='%s'", name))
  }
  
  // REQUIRED: Parameterized queries
  func SafeGetUser(name string) {
      db.Exec("SELECT * FROM users WHERE name=?", name)
  }
  ```

8. Compliance & Best Practices
- Requirement	Implementation Guide
- Database Versioning	MySQL 8.0+/PostgreSQL 12+
- TLS Encryption	REQUIRED for production environments
- Backup Retention	30-day minimum for production data
- Query Complexity	Reject queries with >3 JOINs without review
- Indexing Standards	All foreign keys MUST have indexes

This specification defines database-agnostic configuration rules while allowing database-specific implementations (like MySQL) to exist as compliant options. The core principles ensure:
- Portability between database technologies
- Observability through standardized metrics
- Security via strict access controls
- Reliability with connection/resource management

MySQL implementations should follow both the generic rules and MySQL-specific additions in Section 4.


