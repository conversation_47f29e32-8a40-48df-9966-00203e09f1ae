# TopAI Chat Server 系统架构文档

## 1. 系统架构概览

```mermaid
graph TB
    subgraph "客户端层"
        A[Web前端]
        B[移动应用]
        C[第三方应用]
    end
    
    subgraph "API网关层"
        D[HTTP API Gateway]
        E[WebSocket Gateway]
        F[OpenAI兼容API]
    end
    
    subgraph "中间件层"
        G[认证中间件]
        H[CORS中间件]
        I[限流中间件]
        J[并发控制中间件]
        K[日志中间件]
    end
    
    subgraph "处理器层 (Handlers)"
        L[AuthHandler]
        M[UserHandler]
        N[ConversationHandler]
        O[ModelHandler]
        P[FileHandler]
        Q[AudioHandler]
        R[ImageHandler]
        S[ConfigHandler]
        T[SystemHandler]
        U[WebSocketHandler]
        V[ApiHandler]
        W[TopaiModelHandler]
        X[UserAppHandler]
    end
    
    subgraph "服务层 (Services)"
        Y[UserService]
        Z[ModelService]
        AA[ConversationService]
        BB[FileService]
        CC[AudioService]
        DD[ImageService]
        EE[ConfigService]
        FF[TopaiService]
        GG[UserBalanceService]
        HH[UserAppService]
        II[ShadowAccountManage]
    end
    
    subgraph "数据访问层 (Repository)"
        JJ[UserRepository]
        KK[ConversationRepository]
        LL[ModelRepository]
        MM[FileRepository]
        NN[ConfigRepository]
        OO[BalanceRepository]
        PP[TopaiRepository]
        QQ[AppRepository]
    end
    
    subgraph "数据存储层"
        RR[(MySQL数据库)]
        SS[文件存储]
        TT[配置文件]
    end
    
    subgraph "外部服务"
        UU[OpenAI API]
        VV[TopAI节点网络]
        WW[区块链网络]
        XX[第三方AI服务]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> G
    F --> G
    
    G --> H
    H --> I
    I --> J
    J --> K
    
    K --> L
    K --> M
    K --> N
    K --> O
    K --> P
    K --> Q
    K --> R
    K --> S
    K --> T
    K --> U
    K --> V
    K --> W
    K --> X
    
    L --> Y
    M --> Y
    N --> AA
    O --> Z
    P --> BB
    Q --> CC
    R --> DD
    S --> EE
    T --> EE
    U --> AA
    V --> Z
    W --> FF
    X --> HH
    
    Y --> JJ
    Z --> LL
    AA --> KK
    BB --> MM
    CC --> NN
    DD --> NN
    EE --> NN
    FF --> PP
    GG --> OO
    HH --> QQ
    II --> OO
    
    JJ --> RR
    KK --> RR
    LL --> RR
    MM --> RR
    NN --> RR
    OO --> RR
    PP --> RR
    QQ --> RR
    
    BB --> SS
    EE --> TT
    
    Z --> UU
    Z --> VV
    FF --> VV
    II --> WW
    Z --> XX
```

## 2. 核心模块详述

### 2.1 API网关层

#### 2.1.1 HTTP API Gateway
- **标准REST API**: 提供标准的HTTP RESTful接口
- **路由管理**: 统一的路由注册和管理
- **版本控制**: 支持API版本管理 (`/api/v1`)

#### 2.1.2 WebSocket Gateway
- **实时通信**: 支持双向实时通信
- **Socket.IO集成**: 基于Socket.IO的WebSocket实现
- **会话管理**: WebSocket连接会话管理

#### 2.1.3 OpenAI兼容API
- **标准兼容**: 完全兼容OpenAI API标准
- **多模型支持**: 支持LLM、TTS、ASR、TTI等多种模型
- **流式响应**: 支持流式和非流式响应

### 2.2 中间件层

#### 2.2.1 认证中间件 (Auth Middleware)
```go
// JWT认证
- Bearer Token验证
- 用户权限检查
- 管理员权限验证
- API Key认证
```

#### 2.2.2 CORS中间件
```go
// 跨域处理
- Origin验证
- 预检请求处理
- WebSocket升级支持
```

#### 2.2.3 限流中间件 (Rate Limiting)
```go
// 多层限流
- IP级别限流
- 用户级别限流
- API Key级别限流
- 令牌桶算法
```

#### 2.2.4 并发控制中间件
```go
// 精细化并发控制
- 全局并发限制
- 用户级并发限制
- 用户+模型级并发限制
- 实时统计监控
```

#### 2.2.5 日志中间件
```go
// 结构化日志
- 请求/响应日志
- 敏感数据过滤
- 性能指标记录
```

### 2.3 服务层 (Business Logic)

#### 2.3.1 用户服务 (UserService)
```go
// 用户管理
- 用户注册/登录
- 密码管理
- 用户设置
- 权限管理
- Token管理
```

#### 2.3.2 模型服务 (ModelService)
```go
// AI模型管理
- LLM对话模型
- TTS文本转语音
- ASR语音转文本
- TTI文本转图像
- 模型配置管理
- 多供应商支持 (OpenAI, TopAI, 自定义)
```

#### 2.3.3 对话服务 (ConversationService)
```go
// 对话管理
- 对话创建/删除
- 消息管理
- 对话历史
- 多模态对话支持
```

#### 2.3.4 文件服务 (FileService)
```go
// 文件管理
- 文件上传/下载
- 图片处理
- 音频处理
- 文件存储管理
```

#### 2.3.5 用户余额服务 (UserBalanceService)
```go
// 余额管理
- 用户余额查询
- 消费记录
- 自动结算
- 余额转账
```

#### 2.3.6 TopAI服务 (TopaiService)
```go
// TopAI模型市场
- 模型列表管理
- 分类/系列管理
- 供应商管理
- 链上同步
- 成本报告
```

#### 2.3.7 影子钱包管理 (ShadowAccountManage)
```go
// 区块链集成
- 影子钱包生成
- 私钥加密存储
- 链上交易管理
- 节点注册
- 费用结算
```

### 2.4 数据访问层 (Repository)

#### 2.4.1 统一事务管理
```go
// 事务处理
func (db *DB) RunWithTx(ctx context.Context, fn func(ctx context.Context, db *DB) error) error
```

#### 2.4.2 错误处理统一化
```go
// 标准化错误处理
- ErrNotFound
- ErrDuplicateEntry
- ErrInvalidInput
- ErrDatabaseUnavailable
```

## 3. 数据库设计

### 3.1 核心数据表

#### 3.1.1 用户相关表
```sql
-- 用户基础信息
users
user_tokens
user_settings
user_balance
user_balance_records
user_shadow_wallets
user_shadow_wallet_balance
user_shadow_wallet_balance_records
user_chat_requests
user_chat_usage
```

#### 3.1.2 对话相关表
```sql
-- 对话和消息
conversations
conversation_messages
```

#### 3.1.3 模型相关表
```sql
-- 模型配置
llm_model_configs
remoter_model_infos
tts_model_voices
topai_models
topai_model_categories
topai_model_series
topai_model_providers
topai_model_costs
topai_model_tasks
```

#### 3.1.4 应用相关表
```sql
-- 开发者应用
dev_app_infos
dev_app_keys
```

#### 3.1.5 系统配置表
```sql
-- 系统配置
system_configs
files
```

### 3.2 数据关系图

```mermaid
erDiagram
    users ||--o{ user_tokens : has
    users ||--o{ conversations : creates
    users ||--o{ user_balance : has
    users ||--o{ user_shadow_wallets : owns
    conversations ||--o{ conversation_messages : contains
    users ||--o{ dev_app_infos : develops
    dev_app_infos ||--o{ dev_app_keys : has
    users ||--o{ user_chat_usage : generates
    topai_models ||--o{ topai_model_costs : incurs
```

## 4. API接口设计

### 4.1 认证接口
```
POST /api/v1/auths/signin      # 用户登录
POST /api/v1/auths/signup      # 用户注册
GET  /api/v1/auths             # 获取用户信息
GET  /api/v1/auths/signout     # 用户登出
POST /api/v1/auths/update/profile   # 更新用户资料
POST /api/v1/auths/update/password  # 更新密码
```

### 4.2 对话接口
```
GET    /api/v1/chats           # 获取对话列表
POST   /api/v1/chats/new       # 创建新对话
GET    /api/v1/chats/:chat_id  # 获取对话详情
POST   /api/v1/chats/:chat_id  # 更新对话
DELETE /api/v1/chats/:chat_id  # 删除对话
POST   /api/v1/chats/nonllm    # 非LLM对话
```

### 4.3 模型接口
```
GET  /api/models/base          # 获取基础模型列表
GET  /api/models               # 获取可用模型列表
POST /api/chat/completions     # 聊天完成
```

### 4.4 OpenAI兼容接口
```
POST /api/openai/chat/completions    # 聊天完成
POST /api/openai/audio/transcriptions # 语音识别
POST /api/openai/audio/speech        # 语音合成
POST /api/openai/images/generations   # 图像生成
```

### 4.5 TopAI模型市场接口
```
POST /api/v1/topai/models            # 获取TopAI模型列表
GET  /api/v1/topai/models/categories # 获取模型分类
GET  /api/v1/topai/models/series     # 获取模型系列
GET  /api/v1/topai/models/providers  # 获取供应商列表
GET  /api/v1/topai/models/:model_id  # 获取模型详情
```

### 4.6 用户应用接口
```
GET    /api/v1/user/app/list         # 获取应用列表
POST   /api/v1/user/app/create       # 创建应用
GET    /api/v1/user/app/info/:app_id # 获取应用详情
POST   /api/v1/user/app/update/:app_id # 更新应用
DELETE /api/v1/user/app/delete/:app_id # 删除应用
GET    /api/v1/user/app/key/list     # 获取API密钥列表
POST   /api/v1/user/app/key/:app_id/create # 创建API密钥
```

### 4.7 文件接口
```
POST   /api/v1/files              # 上传文件
POST   /api/v1/files/image        # 上传图片
DELETE /api/v1/files/:id          # 删除文件
GET    /api/v1/files/image/:file_id/content # 获取图片
GET    /api/v1/files/audio/:file_id/content # 获取音频
```

## 5. WebSocket实时通信

### 5.1 连接管理
```javascript
// 客户端连接
const socket = io('/ws/socket.io/');

// 用户认证
socket.emit('user-join', {
    auth: { token: 'Bearer_token' }
});
```

### 5.2 事件处理
```javascript
// 聊天事件
socket.on('chat-completion', (data) => {
    // 处理聊天响应
});

// 任务状态
socket.on('task-status', (data) => {
    // 处理任务状态更新
});

// 用户列表
socket.on('user-list', (data) => {
    // 处理在线用户列表
});
```

## 6. 配置管理

### 6.1 配置结构
```yaml
# config.yaml
version: "1.0.0"

server:
  port: 8080
  timeout: 30s
  tls:
    enabled: false

database:
  host: localhost
  port: 3306
  username: root
  password: ""
  database: chat_server
  pool:
    max_open_conns: 100
    max_idle_conns: 10
    conn_max_lifetime: 1h
    conn_max_idletime: 30m

auth:
  secret: "jwt-secret-key"
  tokenTTL: 24h

log:
  level: info
  path: logs/app.log
  console: true

service:
  file:
    max_size: 10485760  # 10MB
    path: uploads
  
  openai:
    openai:
      base_url: "https://api.openai.com/v1"
      api_key: "sk-..."
  
  topai:
    wallet_addr: "0x..."
    ip_map_path: "ip_map.json"
  
  concurrency_limit:
    global_limit: 100
    user_limit: 10
    user_model_limit: 3
  
  top_chain:
    chain_url: "https://rpc.topnetwork.org"
    chain_id: 980
    # 合约地址配置...
```

### 6.2 热重载支持
```go
// 配置变更通知
type ConfigChangeNotifier interface {
    NotifyChange(old, new *Config)
}

// 注册配置变更监听器
config.RegisterNotifier(notifier)
```

## 7. 安全设计

### 7.1 认证与授权
- **JWT Token**: 基于JWT的无状态认证
- **API Key**: 支持应用级API密钥认证  
- **角色权限**: Admin/User角色权限控制
- **会话管理**: 支持会话失效和强制登出

### 7.2 数据安全
- **密码加密**: bcrypt密码哈希
- **敏感数据**: 私钥AES加密存储
- **SQL注入防护**: GORM参数化查询
- **XSS防护**: 输入验证和输出编码

### 7.3 网络安全
- **HTTPS**: 支持TLS加密传输
- **CORS**: 跨域请求控制
- **限流**: 多层级限流防护
- **并发控制**: 防止资源耗尽攻击

## 8. 监控与运维

### 8.1 日志系统
```go
// 结构化日志
- 模块化日志记录
- 敏感数据自动过滤
- 多输出支持(文件+控制台)
- 日志轮转和压缩
```

### 8.2 性能监控
```go
// 性能指标
- API响应时间监控
- 并发连接数统计
- 数据库查询性能
- 内存和CPU使用率
```

### 8.3 健康检查
```go
// 健康检查接口
GET /health              # 基础健康检查
GET /api/v1/system/concurrency/stats # 并发统计
```

## 9. 部署架构

### 9.1 开发环境
```bash
# 本地开发
go run cmd/server/main.go -config config.yaml
```

### 9.2 生产环境
```docker
# Docker容器化部署
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o chat-server cmd/server/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/chat-server .
CMD ["./chat-server"]
```

### 9.3 Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chat-server
  template:
    metadata:
      labels:
        app: chat-server
    spec:
      containers:
      - name: chat-server
        image: chat-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: CONFIG_PATH
          value: "/etc/config/config.yaml"
```

## 10. 扩展性设计

### 10.1 水平扩展
- **无状态设计**: 服务器无状态，支持水平扩展
- **负载均衡**: 支持多实例负载均衡
- **会话共享**: WebSocket会话状态外部化

### 10.2 模块扩展
- **插件架构**: 支持自定义AI模型提供商
- **中间件系统**: 可插拔的中间件架构
- **事件系统**: 基于事件的异步处理

### 10.3 数据库扩展
- **读写分离**: 支持MySQL主从复制
- **分库分表**: 支持大数据量分片
- **缓存层**: Redis缓存层集成

## 11. 技术栈总结

### 11.1 后端技术栈
- **语言**: Go 1.23+
- **Web框架**: Gin
- **数据库**: MySQL + GORM
- **实时通信**: Socket.IO
- **认证**: JWT
- **日志**: Zap
- **配置**: YAML
- **测试**: Testify

### 11.2 外部依赖
- **AI服务**: OpenAI API, TopAI Network
- **区块链**: Ethereum兼容链
- **存储**: 本地文件系统
- **缓存**: 内存缓存(可扩展Redis)

### 11.3 开发工具
- **代码检查**: golangci-lint
- **API文档**: Swaggo
- **容器化**: Docker
- **编排**: Kubernetes
- **监控**: Prometheus + Grafana

---

**文档版本**: v2.0  
**更新时间**: 2024-12-28  
**维护人员**: TopAI开发团队 