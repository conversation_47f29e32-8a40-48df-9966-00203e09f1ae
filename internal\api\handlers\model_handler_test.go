package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// ModelHandlerTestEnv 模型处理器测试环境
type ModelHandlerTestEnv struct {
	T       *testing.T
	Router  *gin.Engine
	Handler *ModelHandler
	Service *MockServiceAPI
}

// NewModelHandlerTestEnv 创建模型处理器测试环境
func NewModelHandlerTestEnv(t *testing.T) *ModelHandlerTestEnv {
	router := gin.New()
	svc := &MockServiceAPI{}
	handler := &ModelHandler{
		// 根据实际ModelHandler结构设置字段
		// service: svc,
	}

	env := &ModelHandlerTestEnv{
		T:       t,
		Router:  router,
		Handler: handler,
		Service: svc,
	}

	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *ModelHandlerTestEnv) Cleanup() {
	e.Service.AssertExpectations(e.T)
}

// TestModelHandler_GetModels 测试获取模型列表
func TestModelHandler_GetModels(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ModelHandlerTestEnv)
		queryParams    map[string]string
		headers        map[string]string
		expectedStatus int
		verify         func(*ModelHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取所有模型",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望获取模型列表成功
				env.Service.On("GetModels", mock.AnythingOfType("*gin.Context")).
					Return([]interface{}{
						map[string]interface{}{
							"id":       "gpt-3.5-turbo",
							"name":     "GPT-3.5 Turbo",
							"provider": "openai",
							"enabled":  true,
						},
						map[string]interface{}{
							"id":       "gpt-4",
							"name":     "GPT-4",
							"provider": "openai",
							"enabled":  true,
						},
					}, nil)
			},
			queryParams: map[string]string{},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
				assert.NotNil(t, response["data"])

				data := response["data"].([]interface{})
				assert.Len(t, data, 2)
			},
		},
		{
			name: "TC2-按提供商筛选",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望按提供商筛选
				env.Service.On("GetModelsByProvider", mock.AnythingOfType("*gin.Context"), "openai").
					Return([]interface{}{
						map[string]interface{}{
							"id":       "gpt-3.5-turbo",
							"name":     "GPT-3.5 Turbo",
							"provider": "openai",
							"enabled":  true,
						},
					}, nil)
			},
			queryParams: map[string]string{
				"provider": "openai",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])

				data := response["data"].([]interface{})
				assert.Len(t, data, 1)

				model := data[0].(map[string]interface{})
				assert.Equal(t, "openai", model["provider"])
			},
		},
		{
			name: "TC3-只显示启用的模型",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望只返回启用的模型
				env.Service.On("GetEnabledModels", mock.AnythingOfType("*gin.Context")).
					Return([]interface{}{
						map[string]interface{}{
							"id":       "gpt-3.5-turbo",
							"name":     "GPT-3.5 Turbo",
							"provider": "openai",
							"enabled":  true,
						},
					}, nil)
			},
			queryParams: map[string]string{
				"enabled": "true",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])

				data := response["data"].([]interface{})
				for _, item := range data {
					model := item.(map[string]interface{})
					assert.Equal(t, true, model["enabled"])
				}
			},
		},
		{
			name: "TC4-服务错误",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望服务错误
				env.Service.On("GetModels", mock.AnythingOfType("*gin.Context")).
					Return(nil, fmt.Errorf("database connection failed"))
			},
			queryParams: map[string]string{},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusInternalServerError,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusInternalServerError, w.Code)
			},
		},
		{
			name: "TC5-未授权访问",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为中间件会处理
			},
			queryParams:    map[string]string{},
			headers:        map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewModelHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/models", env.Handler.GetModels)

			// 执行setup
			tt.setup(env)

			// 构建URL和查询参数
			url := "/models"
			if len(tt.queryParams) > 0 {
				url += "?"
				first := true
				for key, value := range tt.queryParams {
					if !first {
						url += "&"
					}
					url += fmt.Sprintf("%s=%s", key, value)
					first = false
				}
			}

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestModelHandler_GetModel 测试获取单个模型
func TestModelHandler_GetModel(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ModelHandlerTestEnv)
		modelID        string
		headers        map[string]string
		expectedStatus int
		verify         func(*ModelHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取模型",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望获取模型成功
				env.Service.On("GetModelByID", mock.AnythingOfType("*gin.Context"), "gpt-3.5-turbo").
					Return(map[string]interface{}{
						"id":           "gpt-3.5-turbo",
						"name":         "GPT-3.5 Turbo",
						"provider":     "openai",
						"description":  "Fast and efficient model",
						"max_tokens":   4096,
						"price_input":  0.0015,
						"price_output": 0.002,
						"enabled":      true,
					}, nil)
			},
			modelID: "gpt-3.5-turbo",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])

				data := response["data"].(map[string]interface{})
				assert.Equal(t, "gpt-3.5-turbo", data["id"])
				assert.Equal(t, "GPT-3.5 Turbo", data["name"])
				assert.Equal(t, "openai", data["provider"])
				assert.Equal(t, true, data["enabled"])
			},
		},
		{
			name: "TC2-模型不存在",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望模型不存在错误
				env.Service.On("GetModelByID", mock.AnythingOfType("*gin.Context"), "nonexistent-model").
					Return(nil, fmt.Errorf("model not found"))
			},
			modelID: "nonexistent-model",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusNotFound,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusNotFound, w.Code)
			},
		},
		{
			name: "TC3-空的模型ID",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为参数验证会失败
			},
			modelID: "",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-模型已禁用",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望获取到禁用的模型
				env.Service.On("GetModelByID", mock.AnythingOfType("*gin.Context"), "disabled-model").
					Return(map[string]interface{}{
						"id":      "disabled-model",
						"name":    "Disabled Model",
						"enabled": false,
					}, nil)
			},
			modelID: "disabled-model",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])

				data := response["data"].(map[string]interface{})
				assert.Equal(t, false, data["enabled"])
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewModelHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/models/:id", env.Handler.GetModel)

			// 执行setup
			tt.setup(env)

			// 创建请求
			url := fmt.Sprintf("/models/%s", tt.modelID)
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestModelHandler_UpdateModel 测试更新模型 (仅管理员)
func TestModelHandler_UpdateModel(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ModelHandlerTestEnv)
		modelID        string
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		verify         func(*ModelHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-管理员成功更新模型",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望更新成功
				env.Service.On("UpdateModel", mock.AnythingOfType("*gin.Context"), "gpt-3.5-turbo", mock.AnythingOfType("map[string]interface {}")).
					Return(nil)
			},
			modelID: "gpt-3.5-turbo",
			requestBody: map[string]interface{}{
				"enabled":      false,
				"price_input":  0.002,
				"price_output": 0.003,
				"description":  "Updated description",
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-普通用户无权限更新",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为权限验证会失败
			},
			modelID: "gpt-3.5-turbo",
			requestBody: map[string]interface{}{
				"enabled": false,
			},
			headers: map[string]string{
				"Authorization": "Bearer user_token",
				"X-User-Role":   "user",
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC3-更新不存在的模型",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望模型不存在错误
				env.Service.On("UpdateModel", mock.AnythingOfType("*gin.Context"), "nonexistent-model", mock.AnythingOfType("map[string]interface {}")).
					Return(fmt.Errorf("model not found"))
			},
			modelID: "nonexistent-model",
			requestBody: map[string]interface{}{
				"enabled": false,
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusNotFound,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusNotFound, w.Code)
			},
		},
		{
			name: "TC4-无效的更新数据",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			modelID: "gpt-3.5-turbo",
			requestBody: map[string]interface{}{
				"price_input": -1.0, // 负数价格
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC5-空的更新内容",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为没有更新内容
			},
			modelID:     "gpt-3.5-turbo",
			requestBody: map[string]interface{}{},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewModelHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.PUT("/models/:id", env.Handler.UpdateModel)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			url := fmt.Sprintf("/models/%s", tt.modelID)
			req := httptest.NewRequest(http.MethodPut, url, bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestModelHandler_GetModelStats 测试获取模型统计信息
func TestModelHandler_GetModelStats(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ModelHandlerTestEnv)
		headers        map[string]string
		expectedStatus int
		verify         func(*ModelHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取模型统计",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望获取统计信息成功
				env.Service.On("GetModelStats", mock.AnythingOfType("*gin.Context")).
					Return(map[string]interface{}{
						"total_models":    10,
						"enabled_models":  8,
						"disabled_models": 2,
						"providers": map[string]int{
							"openai": 5,
							"topai":  3,
							"claude": 2,
						},
						"usage_stats": map[string]interface{}{
							"most_used":   "gpt-3.5-turbo",
							"total_calls": 12345,
						},
					}, nil)
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])

				data := response["data"].(map[string]interface{})
				assert.Equal(t, float64(10), data["total_models"])
				assert.Equal(t, float64(8), data["enabled_models"])
				assert.NotNil(t, data["providers"])
				assert.NotNil(t, data["usage_stats"])
			},
		},
		{
			name: "TC2-普通用户无权限访问统计",
			setup: func(env *ModelHandlerTestEnv) {
				// 不需要Mock，因为权限验证会失败
			},
			headers: map[string]string{
				"Authorization": "Bearer user_token",
				"X-User-Role":   "user",
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC3-服务错误",
			setup: func(env *ModelHandlerTestEnv) {
				// Mock期望服务错误
				env.Service.On("GetModelStats", mock.AnythingOfType("*gin.Context")).
					Return(nil, fmt.Errorf("database connection failed"))
			},
			headers: map[string]string{
				"Authorization": "Bearer admin_token",
				"X-User-Role":   "admin",
			},
			expectedStatus: http.StatusInternalServerError,
			verify: func(env *ModelHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusInternalServerError, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewModelHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/models/stats", env.Handler.GetModelStats)

			// 执行setup
			tt.setup(env)

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, "/models/stats", nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// BenchmarkModelHandler_GetModels 获取模型列表性能基准测试
func BenchmarkModelHandler_GetModels(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewModelHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.GET("/models", env.Handler.GetModels)

	// Mock返回固定数据
	env.Service.On("GetModels", mock.AnythingOfType("*gin.Context")).
		Return([]interface{}{
			map[string]interface{}{"id": "gpt-3.5-turbo", "name": "GPT-3.5"},
			map[string]interface{}{"id": "gpt-4", "name": "GPT-4"},
		}, nil)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/models", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}

// BenchmarkModelHandler_GetModel 获取单个模型性能基准测试
func BenchmarkModelHandler_GetModel(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewModelHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.GET("/models/:id", env.Handler.GetModel)

	// Mock返回固定数据
	env.Service.On("GetModelByID", mock.AnythingOfType("*gin.Context"), "gpt-3.5-turbo").
		Return(map[string]interface{}{
			"id":      "gpt-3.5-turbo",
			"name":    "GPT-3.5 Turbo",
			"enabled": true,
		}, nil)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/models/gpt-3.5-turbo", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}
