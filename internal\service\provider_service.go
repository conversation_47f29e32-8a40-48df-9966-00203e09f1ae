package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/google/uuid"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/utils"
)

const centralizedProviderPrefix = "self-"

// ProviderAuthRequest 模型提供者登录请求
// @Description 模型提供者登录请求参数
type ProviderAuthRequest struct {
	WalletAddr string `json:"wallet_addr" binding:"required" example:"0x1234567890abcdef"` // 钱包地址
	Signature  string `json:"signature" binding:"required" example:"0xabcdef1234567890"`   // 钱包签名
	Nonce      string `json:"nonce" binding:"required" example:"a1b2c3d4e5f6"`             // 随机字符串
	Timestamp  int64  `json:"timestamp" binding:"required" example:"**********"`           // 时间戳
}

// ProviderAuthResponse 模型提供者登录响应
// @Description 模型提供者登录响应
type ProviderAuthResponse struct {
	Token     string `json:"token" example:"abc123def456"`    // 访问令牌
	ExpiresAt int64  `json:"expires_at" example:"**********"` // 过期时间戳
}

// ProviderInfo 模型提供者信息
// @Description 模型提供者基本信息
type ProviderInfo struct {
	ID            uint   `json:"id" example:"1"`                                      // 提供者ID
	UUID          string `json:"uuid" example:"550e8400-e29b-41d4-a716-************"` // 提供者UUID
	WalletAddress string `json:"wallet_address" example:"0x1234567890abcdef"`         // 钱包地址
	Name          string `json:"name" example:"AI模型提供者"`                              // 提供者名称
	CreatedAt     int64  `json:"created_at" example:"**********"`                     // 创建时间戳
	Token         string `-`                                                          // 访问令牌
}

// ProviderModelInfo 提供者的模型信息
// @Description 提供者的模型详细信息
type ProviderModelInfo struct {
	ModelID      string `json:"model_id" example:"1"`                       // 模型ID
	ChainModelID uint   `json:"chain_model_id" example:"100"`               // 链上模型ID
	OwnerAddress string `json:"owner_address" example:"0x1234567890abcdef"` // 所有者地址
	ModelName    string `json:"model_name" example:"GPT-4"`                 // 模型名称
	ModelVersion string `json:"model_version" example:"1.0"`                // 模型版本
	ModelType    string `json:"model_type" example:"text-generation"`       // 模型类型
	InputPrice   string `json:"input_price" example:"0.001"`                // 输入价格
	OutputPrice  string `json:"output_price" example:"0.002"`               // 输出价格
	SeriesID     uint   `json:"series_id" example:"1"`                      // 系列ID
	// 扩展信息
	Description string `json:"description" example:"强大的语言模型"`                      // 模型描述
	ShowPicture string `json:"show_picture" example:"https://example.com/pic.jpg"` // 展示图片
	ShowVideo   string `json:"show_video" example:"https://example.com/video.mp4"` // 展示视频
	Status      int    `json:"status" example:"1"`                                 // 状态
	// 分类
	CategoryIds []uint   `json:"category_ids"` // 分类ID
	TTSVoices   []string `json:"tts_voices"`   // TTS音色列表，只有模型为TTS时才会被使用
}

type ProviderModelList struct {
	Models []*ProviderModelInfo `json:"models"`
	Count  int                  `json:"count"`
}

// UpdateProviderInfoRequest 更新提供者信息请求
// @Description 更新提供者信息请求参数
type UpdateProviderInfoRequest struct {
	Name string `json:"name" binding:"required" example:"AI模型提供者"` // 提供者名称
}

// UpdateModelExtRequest 更新模型扩展信息请求
// @Description 更新模型扩展信息请求参数
type UpdateModelExtRequest struct {
	ChainModelID uint     `json:"chain_model_id" example:"100"`                       // 链上模型ID
	Description  string   `json:"description" example:"强大的语言模型"`                      // 模型描述
	SampleCode   string   `json:"sample_code" example:"print('Hello World')"`         // 示例代码
	ShowPicture  string   `json:"show_picture" example:"https://example.com/pic.jpg"` // 展示图片
	ShowVideo    string   `json:"show_video" example:"https://example.com/video.mp4"` // 展示视频
	SeriesId     uint     `json:"series_id" example:"1"`                              // 系列ID
	CategoryIds  []uint   `json:"category_ids" example:"[1,2,3]"`                     // 分类ID
	TTSVoices    []string `json:"tts_voices" example:"[1,2,3]"`                       // TTS音色列表，只有模型为TTS时才会被使用
}

// ProviderApi 模型提供者API接口
// @Description 模型提供者相关业务逻辑接口
type ProviderApi interface {
	// GenerateLoginNonce 生成登录随机字符串
	GenerateLoginNonce(ctx context.Context) (string, error)
	// Login 钱包签名登录
	Login(ctx context.Context, req *ProviderAuthRequest) (*ProviderAuthResponse, error)
	// GetProviderInfo 获取提供者信息
	GetProviderInfoByToken(ctx context.Context, token string) (*ProviderInfo, error)
	// UpdateProviderInfo 更新提供者信息
	UpdateProviderInfo(ctx context.Context, providerInfo *ProviderInfo, req *UpdateProviderInfoRequest) error
	// GetProviderModels 获取提供者的模型列表
	GetProviderModels(ctx context.Context, providerInfo *ProviderInfo, page int, limit int) (*ProviderModelList, error)
	// UpdateModelExt 更新模型扩展信息
	UpdateModelExt(ctx context.Context, providerInfo *ProviderInfo, chainModelId uint, req *UpdateModelExtRequest) error
	// GetProviderModelInfo 获取提供者的模型信息
	GetProviderModelInfo(ctx context.Context, providerInfo *ProviderInfo, chainModelId uint) (*ProviderModelInfo, error)
	// GetAllCategories 获取所有分类
	GetAllCategories(ctx context.Context) ([]*TopaiModelCategoryOP, error)
	// 管理员供应商管理接口
	AdminGetProviderList(ctx context.Context, page, pageSize int, isCentralized *bool) (*AdminProviderListResponse, error)
	AdminCreateProvider(ctx context.Context, name string) (*AdminProviderListItem, error)
	// 管理员分类/系列
	AdminCreateCategory(ctx context.Context, name string, belongTo string) (*TopaiModelCategoryOP, error)
	AdminCreateSeries(ctx context.Context, name string) (*TopaiModelSeriesOP, error)

	// 管理员新增中心化模型（同时关联分类与系列）
	AdminCreateCentralModel(ctx context.Context, req *AdminCreateCentralModelRequest) (*AdminModelDetailResponse, error)
}

type providerService struct {
	db *repository.DB
}

func NewProviderService(db *repository.DB) ProviderApi {
	return &providerService{db: db}
}

// GenerateLoginNonce 生成登录随机字符串
func (s *providerService) GenerateLoginNonce(ctx context.Context) (string, error) {
	return utils.GenerateNonce(), nil
}

// Login 钱包签名登录
func (s *providerService) Login(ctx context.Context, req *ProviderAuthRequest) (*ProviderAuthResponse, error) {
	// 验证签名（nonce验证已在handler层处理）
	valid, err := utils.VerifyProviderSignature(req.Nonce, req.WalletAddr, req.Signature)
	if err != nil {
		return nil, fmt.Errorf("signature verification failed: %w", err)
	}
	if !valid {
		return nil, fmt.Errorf("invalid signature")
	}

	// 查找提供者，如果不存在则自动注册
	provider, err := s.db.TopaiModelProvider.GetByWalletAddress(ctx, req.WalletAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider: %w", err)
	}
	if provider == nil {
		// 自动注册新provider，只设置钱包地址
		provider = &repository.TopaiModelProvider{
			WalletAddress: req.WalletAddr,
			Uuid:          uuid.New().String(),
		}
		if err := s.db.TopaiModelProvider.Create(ctx, provider); err != nil {
			return nil, fmt.Errorf("failed to auto-register provider: %w", err)
		}
	} else {
		// 删除旧的token
		err = s.db.ProviderToken.DeleteByProviderID(ctx, provider.Id)
		if err != nil {
			return nil, fmt.Errorf("failed to delete old tokens: %w", err)
		}
	}

	// 生成新token
	tokenBytes := make([]byte, 16)
	_, err = rand.Read(tokenBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}
	token := hex.EncodeToString(tokenBytes)

	// 设置过期时间（7天）
	expiresAt := time.Now().Add(7 * 24 * time.Hour)

	// 保存token
	providerToken := &repository.ProviderToken{
		ProviderID: provider.Id,
		Token:      token,
		ExpiresAt:  expiresAt,
		DeletedAt:  0,
	}

	err = s.db.ProviderToken.Create(ctx, providerToken)
	if err != nil {
		return nil, fmt.Errorf("failed to create token: %w", err)
	}

	return &ProviderAuthResponse{
		Token:     token,
		ExpiresAt: expiresAt.Unix(),
	}, nil
}

func (s *providerService) GetProviderInfoByToken(ctx context.Context, token string) (*ProviderInfo, error) {
	providerToken, err := s.db.ProviderToken.GetByToken(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get token: %w", err)
	}
	if providerToken == nil {
		return nil, fmt.Errorf("invalid token")
	}

	provider, err := s.db.TopaiModelProvider.GetByID(ctx, providerToken.ProviderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider: %w", err)
	}
	if provider == nil {
		return nil, fmt.Errorf("provider not found")
	}

	return &ProviderInfo{
		ID:            provider.Id,
		UUID:          provider.Uuid,
		WalletAddress: provider.WalletAddress,
		Name:          provider.Name,
		CreatedAt:     provider.CreatedAt.Unix(),
		Token:         token,
	}, nil
}

// UpdateProviderInfo 更新提供者信息
func (s *providerService) UpdateProviderInfo(ctx context.Context, providerInfo *ProviderInfo, req *UpdateProviderInfoRequest) error {
	// 获取提供者信息
	provider, err := s.db.TopaiModelProvider.GetByID(ctx, providerInfo.ID)
	if err != nil {
		return fmt.Errorf("failed to get provider: %w", err)
	}
	if provider == nil {
		return fmt.Errorf("provider not found")
	}

	// 更新信息
	provider.Name = req.Name

	err = s.db.TopaiModelProvider.Update(ctx, provider)
	if err != nil {
		return fmt.Errorf("failed to update provider: %w", err)
	}

	return nil
}

// GetProviderModels 获取提供者的模型列表
func (s *providerService) GetProviderModels(ctx context.Context, providerInfo *ProviderInfo, page int, limit int) (*ProviderModelList, error) {
	// 获取提供者信息
	provider, err := s.db.TopaiModelProvider.GetByID(ctx, providerInfo.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get provider: %w", err)
	}
	if provider == nil {
		return nil, fmt.Errorf("provider not found")
	}

	count, err := s.db.TopaiModel.GetCountByOwnerAddress(ctx, provider.WalletAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get count: %w", err)
	}

	offset := (page - 1) * limit
	if offset > int(count) {
		return &ProviderModelList{
			Models: make([]*ProviderModelInfo, 0),
			Count:  int(count),
		}, nil
	}

	// 获取提供者的模型列表
	models, err := s.db.TopaiModel.GetByOwnerAddress(ctx, provider.WalletAddress, offset, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get models: %w", err)
	}

	var result []*ProviderModelInfo
	for _, model := range models {
		// 获取模型扩展信息
		ext, err := s.db.TopaiModelExt.GetByTopaiModelId(ctx, model.Id)
		if err != nil {
			return nil, fmt.Errorf("failed to get model ext: %w", err)
		}

		modelInfo := &ProviderModelInfo{
			ChainModelID: model.ChainModelId,
			OwnerAddress: model.OwnerAddress,
			ModelName:    model.ModelName,
			ModelVersion: model.ModelVersion,
			ModelType:    model.ModelType,
			InputPrice:   model.InputPrice,
			OutputPrice:  model.OutputPrice,
		}

		// 添加扩展信息
		if ext != nil {
			modelInfo.ModelID = ext.ModelId
			modelInfo.Status = ext.Status
		}

		result = append(result, modelInfo)
	}

	return &ProviderModelList{
		Models: result,
		Count:  int(count),
	}, nil
}

func (s *providerService) GetProviderModelInfo(ctx context.Context, providerInfo *ProviderInfo, chainModelId uint) (*ProviderModelInfo, error) {
	model, err := s.db.TopaiModel.GetByChainModelId(ctx, chainModelId)
	if err != nil {
		return nil, fmt.Errorf("failed to get model: %w", err)
	}
	if model == nil {
		return nil, api.NewClientError("model not found")
	}
	if !strings.EqualFold(model.OwnerAddress, providerInfo.WalletAddress) {
		return nil, api.NewClientError("model not found")
	}

	ext, err := s.db.TopaiModelExt.GetByTopaiModelId(ctx, model.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get model ext: %w", err)
	}
	modelInfo := &ProviderModelInfo{
		ChainModelID: model.ChainModelId,
		OwnerAddress: model.OwnerAddress,
		ModelName:    model.ModelName,
		ModelVersion: model.ModelVersion,
		ModelType:    model.ModelType,
		InputPrice:   model.InputPrice,
		OutputPrice:  model.OutputPrice,
		SeriesID:     model.SeriesId,
	}

	// 添加扩展信息
	if ext != nil {
		modelInfo.ModelID = ext.ModelId
		modelInfo.Description = ext.Description
		modelInfo.Status = ext.Status
		if ext.ShowPicture != "" {
			file, err := s.db.File.GetByUUID(ctx, ext.ShowPicture)
			if err != nil {
				return nil, fmt.Errorf("failed to get file: %w", err)
			}
			if file != nil {
				modelInfo.ShowPicture = utils.GetFileRouter(file)
			}
		}
		if ext.ShowVideo != "" {
			file, err := s.db.File.GetByUUID(ctx, ext.ShowVideo)
			if err != nil {
				return nil, fmt.Errorf("failed to get file: %w", err)
			}
			if file != nil {
				modelInfo.ShowVideo = utils.GetFileRouter(file)
			}
		}
		modelInfo.SeriesID = ext.SeriesId
		// 获取分类信息
		categoryRelations, err := s.db.TopaiModelCategoryRelation.GetByTopaiModelId(ctx, model.Id)
		if err != nil {
			return nil, fmt.Errorf("failed to get category ids: %w", err)
		}
		for _, categoryRelation := range categoryRelations {
			modelInfo.CategoryIds = append(modelInfo.CategoryIds, categoryRelation.CategoryId)
		}
		// 获取TTS音色列表
		if model.ModelType == repository.RemoterModelTypeTTS.String() {
			voices, err := s.db.TTSModelVoice.GetByModelName(ctx, model.ModelName)
			if err != nil {
				return nil, fmt.Errorf("failed to get tts invoice: %w", err)
			}
			if voices != nil {
				modelInfo.TTSVoices = make([]string, len(voices))
				for i, voice := range voices {
					modelInfo.TTSVoices[i] = voice.Name
				}
			}
		}
	}
	return modelInfo, nil
}

// UpdateModelExt 更新模型扩展信息
func (s *providerService) UpdateModelExt(ctx context.Context, providerInfo *ProviderInfo, chainModelId uint, req *UpdateModelExtRequest) error {
	// 获取提供者信息
	provider, err := s.db.TopaiModelProvider.GetByID(ctx, providerInfo.ID)
	if err != nil {
		return fmt.Errorf("failed to get provider: %w", err)
	}
	if provider == nil {
		return api.NewClientError("provider not found")
	}

	// 获取模型信息
	model, err := s.db.TopaiModel.GetByChainModelId(ctx, chainModelId)
	if err != nil {
		return fmt.Errorf("failed to get model: %w", err)
	}
	if model == nil {
		return api.NewClientError("model not found")
	}

	// 验证模型是否属于该提供者
	if !strings.EqualFold(model.OwnerAddress, provider.WalletAddress) {
		return api.NewClientError("model not found")
	}

	// 获取模型扩展信息
	ext, err := s.db.TopaiModelExt.GetByTopaiModelId(ctx, model.Id)
	if err != nil {
		return fmt.Errorf("failed to get model ext: %w", err)
	}
	if ext == nil {
		inputPrice, ok := big.NewInt(0).SetString(model.InputPrice, 10)
		if !ok {
			inputPrice = big.NewInt(0)
		}
		outputPrice, ok := big.NewInt(0).SetString(model.OutputPrice, 10)
		if !ok {
			outputPrice = big.NewInt(0)
		}

		ext = &repository.TopaiModelExt{
			TopaiModelProviderId: provider.Id,
			TopaiModelId:         model.Id,
			ModelId:              fmt.Sprintf("%s-%s", model.ModelName, model.ModelVersion),
			Status:               0,
			Price:                inputPrice.Add(inputPrice, outputPrice).String(),
		}
	}

	// 更新扩展信息
	if req.Description != "" {
		ext.Description = req.Description
	}
	if req.ShowPicture != "" {
		ext.ShowPicture = utils.GetFileUUIDByUrl(req.ShowPicture)
	}
	if req.ShowVideo != "" {
		ext.ShowVideo = utils.GetFileUUIDByUrl(req.ShowVideo)
	}
	ext.SeriesId = req.SeriesId

	addCategoryRelations := make([]*repository.TopaiModelCategoryRelation, 0)
	for _, categoryId := range req.CategoryIds {
		addCategoryRelations = append(addCategoryRelations, &repository.TopaiModelCategoryRelation{
			TopaiModelId: model.Id,
			CategoryId:   categoryId,
		})
	}

	addTTSVoices := make([]*repository.TTSModelVoice, len(req.TTSVoices))
	if model.ModelType == repository.RemoterModelTypeTTS.String() {
		for i, voice := range req.TTSVoices {
			addTTSVoices[i] = &repository.TTSModelVoice{
				ModelName:    model.ModelName,
				Name:         voice,
				ProviderName: voice,
			}
		}
	}

	err = s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		err := db.TopaiModelCategoryRelation.DeleteByTopaiModelId(ctx, model.Id)
		if err != nil {
			return fmt.Errorf("failed to delete category relations: %w", err)
		}
		err = db.TopaiModelCategoryRelation.CreateBatch(ctx, addCategoryRelations)
		if err != nil {
			return fmt.Errorf("failed to add category relations: %w", err)
		}

		if model.ModelType == repository.RemoterModelTypeTTS.String() {
			err = db.TTSModelVoice.DeleteByModelName(ctx, model.ModelName)
			if err != nil {
				return fmt.Errorf("failed to delete tts voices: %w", err)
			}
			err = db.TTSModelVoice.CreateBatch(ctx, addTTSVoices)
			if err != nil {
				return fmt.Errorf("failed to add tts voices: %w", err)
			}
		}
		if ext.Id > 0 {
			return db.TopaiModelExt.Update(ctx, ext)
		}
		return db.TopaiModelExt.Create(ctx, ext)
	})

	if err != nil {
		return fmt.Errorf("failed to update model ext: %w", err)
	}

	return nil
}

func (s *providerService) GetAllCategories(ctx context.Context) ([]*TopaiModelCategoryOP, error) {
	categories, err := s.db.TopaiModelCategory.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get categories: %w", err)
	}
	result := make([]*TopaiModelCategoryOP, len(categories))
	for i, category := range categories {
		result[i] = &TopaiModelCategoryOP{
			CategoryId:   category.Id,
			CategoryName: category.Name,
			BelongTo:     category.BelongTo,
		}
	}
	return result, nil
}

// AdminCreateCategory 创建分类
func (s *providerService) AdminCreateCategory(ctx context.Context, name string, belongTo string) (*TopaiModelCategoryOP, error) {
	if strings.TrimSpace(name) == "" {
		return nil, api.NewClientError("category name required")
	}
	category := &repository.TopaiModelCategory{
		Name:      name,
		BelongTo:  belongTo,
		IsDeleted: 0,
	}
	if err := s.db.TopaiModelCategory.Create(ctx, category); err != nil {
		return nil, err
	}
	return &TopaiModelCategoryOP{CategoryId: category.Id, CategoryName: category.Name, BelongTo: category.BelongTo}, nil
}

// AdminCreateSeries 创建系列
func (s *providerService) AdminCreateSeries(ctx context.Context, name string) (*TopaiModelSeriesOP, error) {
	if strings.TrimSpace(name) == "" {
		return nil, api.NewClientError("series name required")
	}
	series := &repository.TopaiModelSeries{Name: name}
	if err := s.db.TopaiModelSeries.Create(ctx, series); err != nil {
		return nil, err
	}
	return &TopaiModelSeriesOP{SeriesId: series.Id, SeriesName: series.Name}, nil
}

// AdminCreateCentralModel 创建中心化模型并关联分类与系列
func (s *providerService) AdminCreateCentralModel(ctx context.Context, req *AdminCreateCentralModelRequest) (*AdminModelDetailResponse, error) {
	if req == nil {
		return nil, api.NewClientError("invalid request")
	}
	if strings.TrimSpace(req.ModelName) == "" || strings.TrimSpace(req.InputPrice) == "" || strings.TrimSpace(req.OutputPrice) == "" {
		return nil, api.NewClientError("model_name, input_price, output_price required")
	}
	// 校验provider
	provider, err := s.db.TopaiModelProvider.GetByID(ctx, req.ProviderID)
	if err != nil {
		return nil, err
	}
	if provider == nil {
		return nil, api.NewClientError("provider not found")
	}
	// 创建topai_model
	model := &repository.TopaiModel{
		ChainModelId:      0,
		OwnerAddress:      provider.WalletAddress,
		ModelName:         req.ModelName,
		ModelVersion:      req.ModelVersion,
		ModelType:         req.ModelType,
		InputPrice:        req.InputPrice,
		OutputPrice:       req.OutputPrice,
		SeriesId:          req.SeriesID,
		SupportImageLevel: "",
	}
	if err := s.db.TopaiModel.Create(ctx, model); err != nil {
		return nil, err
	}
	// 创建ext
	modelId := generateModelId(req.ModelName, req.ModelVersion)
	ext := &repository.TopaiModelExt{
		ModelId:              modelId,
		TopaiModelProviderId: provider.Id,
		TopaiModelId:         model.Id,
		Description:          req.Description,
		ContextLength:        req.ContextLength,
		MaxOutput:            req.MaxOutput,
		Price:                "", // 已拆分为输入输出价
		Status:               1,
		Weight:               req.Weight,
	}
	if err := s.db.TopaiModelExt.Create(ctx, ext); err != nil {
		return nil, err
	}
	// 关联分类
	if len(req.CategoryIDs) > 0 {
		relations := make([]*repository.TopaiModelCategoryRelation, 0, len(req.CategoryIDs))
		for _, cid := range req.CategoryIDs {
			relations = append(relations, &repository.TopaiModelCategoryRelation{
				TopaiModelId: model.Id,
				CategoryId:   cid,
				IsDeleted:    0,
			})
		}
		if err := s.db.TopaiModelCategoryRelation.CreateBatch(ctx, relations); err != nil {
			return nil, err
		}
	}
	// 系列使用 ext.SeriesId 字段，不再写入 TopaiModelSeriesRelation

	// 返回详情
	return &AdminModelDetailResponse{
		AdminModelListItem: AdminModelListItem{
			ID:               ext.Id,
			ModelID:          ext.ModelId,
			ModelName:        model.ModelName,
			ModelVersion:     model.ModelVersion,
			InputPrice:       model.InputPrice,
			OutputPrice:      model.OutputPrice,
			TokenConsumption: "0",
			Status:           ext.Status,
			IsOnChain:        false,
			ProviderName:     provider.Name,
			Description:      ext.Description,
			CreatedAt:        time.Now().Unix(),
			UpdatedAt:        time.Now().Unix(),
		},
		ContextLength: ext.ContextLength,
		MaxOutput:     ext.MaxOutput,
		Latency:       ext.Latency,
		Throughput:    ext.Throughput,
		SampleCode:    ext.SampleCode,
		ShowPicture:   ext.ShowPicture,
		ShowVideo:     ext.ShowVideo,
		Weight:        ext.Weight,
		SeriesID:      req.SeriesID,
	}, nil
}

// AdminGetProviderList 管理员获取供应商列表
func (s *providerService) AdminGetProviderList(ctx context.Context, page, pageSize int, isCentralized *bool) (*AdminProviderListResponse, error) {
	offset := (page - 1) * pageSize

	// 获取所有供应商
	allProviders, err := s.db.TopaiModelProvider.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	// 过滤供应商
	var filteredProviders []*repository.TopaiModelProvider
	for _, provider := range allProviders {
		// 判断是否中心化供应商（wallet_address 以 centralizedProviderPrefix 开头）
		providerIsCentralized := strings.HasPrefix(provider.WalletAddress, centralizedProviderPrefix)

		// 根据筛选条件过滤
		if isCentralized != nil && providerIsCentralized != *isCentralized {
			continue
		}

		filteredProviders = append(filteredProviders, provider)
	}

	total := int64(len(filteredProviders))

	// 分页
	start := offset
	end := offset + pageSize
	if start > len(filteredProviders) {
		start = len(filteredProviders)
	}
	if end > len(filteredProviders) {
		end = len(filteredProviders)
	}

	pagedProviders := filteredProviders[start:end]

	// 转换为响应格式
	providers := make([]*AdminProviderListItem, len(pagedProviders))
	for i, provider := range pagedProviders {
		providers[i] = &AdminProviderListItem{
			ID:            provider.Id,
			UUID:          provider.Uuid,
			Name:          provider.Name,
			WalletAddress: provider.WalletAddress,
			IsCentralized: strings.HasPrefix(provider.WalletAddress, centralizedProviderPrefix),
			CreatedAt:     provider.CreatedAt.Unix(),
		}
	}

	return &AdminProviderListResponse{
		Total:     total,
		Providers: providers,
	}, nil
}

// AdminCreateProvider 管理员创建中心化供应商
func (s *providerService) AdminCreateProvider(ctx context.Context, name string) (*AdminProviderListItem, error) {
	// 检查供应商名称是否已存在
	existingProviders, err := s.db.TopaiModelProvider.GetByNames(ctx, []string{name})
	if err != nil {
		return nil, err
	}
	if len(existingProviders) > 0 {
		return nil, fmt.Errorf("provider name already exists")
	}

	// 生成UUID
	uuidStr := uuid.New().String()

	// 创建中心化供应商（wallet_address 使用唯一前缀，避免唯一索引冲突）
	provider := &repository.TopaiModelProvider{
		Uuid:          uuidStr,
		Name:          name,
		WalletAddress: centralizedProviderPrefix + uuidStr,
	}

	err = s.db.TopaiModelProvider.Create(ctx, provider)
	if err != nil {
		return nil, err
	}

	return &AdminProviderListItem{
		ID:            provider.Id,
		UUID:          provider.Uuid,
		Name:          provider.Name,
		WalletAddress: provider.WalletAddress,
		IsCentralized: true,
		CreatedAt:     time.Now().Unix(),
	}, nil
}
