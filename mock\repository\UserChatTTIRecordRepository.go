// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserChatTTIRecordRepository is an autogenerated mock type for the UserChatTTIRecordRepository type
type UserChatTTIRecordRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, record
func (_m *UserChatTTIRecordRepository) Create(ctx context.Context, record *repository.UserChatTTIRecord) error {
	ret := _m.Called(ctx, record)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserChatTTIRecord) error); ok {
		r0 = rf(ctx, record)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByUUID provides a mock function with given fields: ctx, uuid
func (_m *UserChatTTIRecordRepository) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatTTIRecord, error) {
	ret := _m.Called(ctx, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByUUID")
	}

	var r0 *repository.UserChatTTIRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.UserChatTTIRecord, error)); ok {
		return rf(ctx, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.UserChatTTIRecord); ok {
		r0 = rf(ctx, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserChatTTIRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetShared provides a mock function with given fields: ctx, limit, offset, order
func (_m *UserChatTTIRecordRepository) GetShared(ctx context.Context, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	ret := _m.Called(ctx, limit, offset, order)

	if len(ret) == 0 {
		panic("no return value specified for GetShared")
	}

	var r0 []*repository.UserChatTTIRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string) ([]*repository.UserChatTTIRecord, error)); ok {
		return rf(ctx, limit, offset, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int, string) []*repository.UserChatTTIRecord); ok {
		r0 = rf(ctx, limit, offset, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatTTIRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int, string) error); ok {
		r1 = rf(ctx, limit, offset, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSharedByUserID provides a mock function with given fields: ctx, userID, limit, offset, order
func (_m *UserChatTTIRecordRepository) GetSharedByUserID(ctx context.Context, userID uint, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	ret := _m.Called(ctx, userID, limit, offset, order)

	if len(ret) == 0 {
		panic("no return value specified for GetSharedByUserID")
	}

	var r0 []*repository.UserChatTTIRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, int, int, string) ([]*repository.UserChatTTIRecord, error)); ok {
		return rf(ctx, userID, limit, offset, order)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, int, int, string) []*repository.UserChatTTIRecord); ok {
		r0 = rf(ctx, userID, limit, offset, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserChatTTIRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, int, int, string) error); ok {
		r1 = rf(ctx, userID, limit, offset, order)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSharedCount provides a mock function with given fields: ctx
func (_m *UserChatTTIRecordRepository) GetSharedCount(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetSharedCount")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSharedCountByUserID provides a mock function with given fields: ctx, userID
func (_m *UserChatTTIRecordRepository) GetSharedCountByUserID(ctx context.Context, userID uint) (int64, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetSharedCountByUserID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (int64, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) int64); ok {
		r0 = rf(ctx, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateIsSharedByUUID provides a mock function with given fields: ctx, uuid, isShared
func (_m *UserChatTTIRecordRepository) UpdateIsSharedByUUID(ctx context.Context, uuid string, isShared uint8) error {
	ret := _m.Called(ctx, uuid, isShared)

	if len(ret) == 0 {
		panic("no return value specified for UpdateIsSharedByUUID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, uint8) error); ok {
		r0 = rf(ctx, uuid, isShared)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserChatTTIRecordRepository creates a new instance of UserChatTTIRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserChatTTIRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserChatTTIRecordRepository {
	mock := &UserChatTTIRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
