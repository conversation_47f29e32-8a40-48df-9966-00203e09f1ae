package handlers

import (
	"errors"
	"path/filepath"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type FileHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewFileHandler(service *service.Api) *FileHandler {
	return &FileHandler{
		service: service,
		log:     logger.GetLogger("file_handler"),
	}
}

// @Summary      上传文件
// @Description  上传新文件
// @Tags         文件管理
// @Accept       multipart/form-data
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        file          formData  file    true  "文件"
// @Success      200           {object}  service.FileInfoOP
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/files/upload [post]
func (h *FileHandler) UploadFile(c *gin.Context) {
	user, _ := c.Get("user")

	userOP := user.(*service.UserBaseOP)

	file, err := c.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	ext := filepath.Ext(file.Filename)
	if strings.ToLower(ext) != ".wav" {
		h.log.Error("invalid file type", zap.String("file_name", file.Filename), zap.String("ext", ext))
		api.Fail(c, api.CodeClientError, "invalid file type", errors.New("invalid file type"))
		return
	}

	reader, err := file.Open()
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	defer reader.Close()

	fileInfo, err := h.service.File.UploadFile(c.Request.Context(), userOP, &service.FileUploadIP{
		Name:     file.Filename,
		Size:     file.Size,
		MimeType: file.Header.Get("Content-Type"),
	}, reader, "audio", false)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to upload file", err)
		return
	}

	// 处理模型处理语音文件
	data, err := h.service.Model.ProcessAudioFile(c.Request.Context(), userOP, fileInfo.Meta)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to process audio file", err)
		return
	}

	fileInfo.Data = data

	// 更新文件内容
	err = h.service.File.UpdateFileContent(c.Request.Context(), userOP, fileInfo.ID, data.Content)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to update file content", err)
		return
	}

	api.Success(c, fileInfo)
}

// @Summary      上传图片文件
// @Description  上传新图片文件
// @Tags         文件管理
// @Accept       multipart/form-data
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        file          formData  file    true  "文件"
// @Success      200           {object}  service.FileInfoOP
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/files/image [post]
func (h *FileHandler) UploadImageFile(c *gin.Context) {
	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	file, err := c.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	ext := filepath.Ext(file.Filename)
	// 只支持 png,jpg,jpeg,gif,webp,svg
	if !slices.Contains([]string{".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg"}, strings.ToLower(ext)) {
		h.log.Error("invalid file type", zap.String("file_name", file.Filename), zap.String("ext", ext))
		api.Fail(c, api.CodeClientError, "invalid file type, only support png,jpg,jpeg,gif,webp,svg", errors.New("invalid file type"))
		return
	}

	// 如果文件大小大于 10MB，则不支持上传
	if file.Size > 10*1024*1024 {
		h.log.Error("file size too large", zap.String("file_name", file.Filename), zap.Int64("size", file.Size))
		api.Fail(c, api.CodeClientError, "file size too large, only support 10MB", errors.New("file size too large"))
		return
	}

	reader, err := file.Open()
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	defer reader.Close()

	fileInfo, err := h.service.File.UploadFile(c.Request.Context(), userOP, &service.FileUploadIP{
		Name:     file.Filename,
		Size:     file.Size,
		MimeType: file.Header.Get("Content-Type"),
	}, reader, "image", false)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to upload image file", err)
		return
	}

	api.Success(c, fileInfo)
}

// @Summary      删除文件
// @Description  删除指定文件
// @Tags         文件管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        id            path      string  true  "文件ID"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/files/{id} [delete]
func (h *FileHandler) DeleteFile(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("file id is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)
	if err := h.service.File.DeleteFile(c.Request.Context(), userOP, id); err != nil {
		api.Fail(c, api.CodeServerError, "failed to delete file", err)
		return
	}
	api.Success(c, nil)
}

// GetImageFile 获取图片文件
// @Summary 获取图片文件
// @Description 获取指定图片文件
// @Tags 文件管理
// @Accept json
// @Produce octet-stream
// @Param Authorization header string true "Bearer token"
// @Param dir path string true "目录"
// @Param image_name path string true "图片名称"
// @Success 200 {file} binary "图片文件"
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/files/image/{file_id}/content [get]
func (h *FileHandler) GetImageFile(c *gin.Context) {
	fileID := c.Param("file_id")
	if fileID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("file id is required"))
		return
	}
	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	imageBytes, err := h.service.File.GetUserFileBytes(c.Request.Context(), userOP, fileID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get image file", err)
		return
	}

	c.Data(200, "image/jpeg", imageBytes)
}

// GetPublicImageFile 获取公共图片文件
// @Summary 获取公共图片文件
// @Description 获取指定公共图片文件
// @Tags 文件管理
// @Accept json
// @Produce octet-stream
// @Param file_id path string true "文件ID"
// @Success 200 {file} binary "图片文件"
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/public/files/image/{file_id}/content [get]
func (h *FileHandler) GetPublicImageFile(c *gin.Context) {
	fileID := c.Param("file_id")
	if fileID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("file id is required"))
		return
	}
	imageBytes, err := h.service.File.GetUserFileBytes(c.Request.Context(), nil, fileID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get image file", err)
		return
	}
	c.Data(200, "image/jpeg", imageBytes)
}

// GetAudioFile 获取音频文件
// @Summary 获取音频文件
// @Description 获取指定音频文件
// @Tags 文件管理
// @Accept json
// @Produce octet-stream
// @Param Authorization header string true "Bearer token"
// @Param file_id path string true "文件ID"
// @Success 200 {file} binary "音频文件"
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/files/audio/{file_id}/content [get]
func (h *FileHandler) GetAudioFile(c *gin.Context) {
	fileID := c.Param("file_id")
	if fileID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("file id is required"))
		return
	}

	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	audioBytes, err := h.service.File.GetUserFileBytes(c.Request.Context(), userOP, fileID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get audio file", err)
		return
	}
	c.Data(200, "audio/wav", audioBytes)
}

// GetPublicAudioFile 获取公共音频文件
// @Summary 获取公共音频文件
// @Description 获取指定公共音频文件
// @Tags 文件管理
// @Accept json
// @Produce octet-stream
// @Param file_id path string true "文件ID"
// @Success 200 {file} binary "音频文件"
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/public/files/audio/{file_id}/content [get]
func (h *FileHandler) GetPublicAudioFile(c *gin.Context) {
	fileID := c.Param("file_id")
	if fileID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("file id is required"))
		return
	}
	audioBytes, err := h.service.File.GetUserFileBytes(c.Request.Context(), nil, fileID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get audio file", err)
		return
	}
	c.Data(200, "audio/wav", audioBytes)
}

// UploadUserAppAvatar 上传用户应用头像
// @Summary 上传用户应用头像
// @Description 上传用户应用头像
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param file formData file true "文件"
// @Success 200 {object} service.UserAppInfoOP "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/avatar [post]
func (h *FileHandler) UploadUserAppAvatar(c *gin.Context) {
	user, _ := c.Get("user")
	userOP := user.(*service.UserBaseOP)

	file, err := c.FormFile("file")
	if err != nil {
		api.Fail(c, api.CodeClientError, "file is required", err)
		return
	}

	ext := filepath.Ext(file.Filename)
	// 只支持 png,jpg,jpeg,gif,webp,svg
	if !slices.Contains([]string{".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg"}, strings.ToLower(ext)) {
		h.log.Error("invalid file type", zap.String("file_name", file.Filename), zap.String("ext", ext))
		api.Fail(c, api.CodeClientError, "invalid file type, only support png,jpg,jpeg,gif,webp,svg", errors.New("invalid file type"))
		return
	}

	// 如果文件大小大于 10MB，则不支持上传
	if file.Size > 10*1024*1024 {
		h.log.Error("file size too large", zap.String("file_name", file.Filename), zap.Int64("size", file.Size))
		api.Fail(c, api.CodeClientError, "file size too large, only support 10MB", errors.New("file size too large"))
		return
	}

	reader, err := file.Open()
	if err != nil {
		api.Fail(c, api.CodeClientError, "failed to open file", err)
		return
	}
	defer reader.Close()

	fileInfo, err := h.service.File.UploadFile(c.Request.Context(), userOP, &service.FileUploadIP{
		Name:     file.Filename,
		Size:     file.Size,
		MimeType: file.Header.Get("Content-Type"),
	}, reader, "image", true)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to upload image file", err)
		return
	}

	api.Success(c, fileInfo)
}

func (h *FileHandler) Close() {
	// 清理资源
}
