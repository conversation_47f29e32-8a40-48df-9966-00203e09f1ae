package repository

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// 定义错误类型
var (
	// 通用错误
	ErrNotFound            = errors.New("记录未找到")
	ErrDuplicateEntry      = errors.New("记录已存在")
	ErrInvalidInput        = errors.New("无效的输入")
	ErrDatabaseUnavailable = errors.New("数据库不可用")
	ErrTimeout             = errors.New("操作超时")
	ErrConstraintViolation = errors.New("约束违反")
	ErrDeadlock            = errors.New("死锁检测")

	// 用户相关错误
	ErrUserNotFound      = errors.New("用户未找到")
	ErrUserAlreadyExists = errors.New("用户已存在")
	ErrInvalidPassword   = errors.New("密码无效")

	// 对话相关错误
	ErrConversationNotFound = errors.New("对话未找到")
	ErrInvalidConversation  = errors.New("无效的对话")

	// 消息相关错误
	ErrMessageNotFound = errors.New("消息未找到")
	ErrInvalidMessage  = errors.New("无效的消息")

	// 模型相关错误
	ErrModelNotFound      = errors.New("模型未找到")
	ErrModelAlreadyExists = errors.New("模型已存在")
	ErrInvalidModel       = errors.New("无效的模型")
)

// Error 自定义错误类型
type Error struct {
	Code    string
	Message string
	Err     error
}

// NewError 创建新的错误
func NewError(code string, message string, err error) *Error {
	return &Error{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// Error 实现 error 接口
func (e *Error) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Err.Error())
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 实现 errors.Unwrap
func (e *Error) Unwrap() error {
	return e.Err
}

// Is 检查错误类型
func (e *Error) Is(target error) bool {
	if target == nil {
		return false
	}
	if err, ok := target.(*Error); ok {
		return e.Code == err.Code
	}
	return errors.Is(e.Err, target)
}

// HandleError 处理数据库错误
func HandleError(err error) error {
	if err == nil {
		return nil
	}

	// 处理 GORM 错误
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return ErrNotFound
	case errors.Is(err, gorm.ErrDuplicatedKey):
		return ErrDuplicateEntry
	case errors.Is(err, gorm.ErrInvalidData):
		return ErrInvalidInput
	case errors.Is(err, gorm.ErrInvalidTransaction):
		return ErrDatabaseUnavailable
	case errors.Is(err, context.DeadlineExceeded):
		return ErrTimeout
	}

	// 处理数据库错误
	errStr := err.Error()
	switch {
	case errStr == "Error 1062: Duplicate entry":
		return ErrDuplicateEntry
	case errStr == "Error 1048: Column cannot be null":
		return ErrInvalidInput
	case errStr == "Error 1205: Lock wait timeout exceeded":
		return ErrTimeout
	case errStr == "Error 1213: Deadlock found":
		return ErrDatabaseUnavailable
	}

	return err
}
