package handlers

import (
	"errors"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// ModelHandler 处理模型相关的请求
type ModelHandler struct {
	log     *logger.ModuleLogger
	service *service.Api
}

// NewModelHandler 创建新的模型处理器
func NewModelHandler(service *service.Api) *ModelHandler {
	log := logger.GetLogger("model_handler")
	return &ModelHandler{
		log:     log,
		service: service,
	}
}

// GetAllBaseLLMModels 获取所有基础模型
// @Summary 获取所有基础模型
// @Description 获取所有基础模型
// @Tags model
// @Accept json
// @Produce json
// @Success 200 {object} []service.BaseModel
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/models/base [get]
func (h *ModelHandler) GetAllBaseLLMModels(c *gin.Context) {
	models, err := h.service.Model.GetAllBaseLLMModels(c.Request.Context())
	if err != nil {
		api.Fail(c, api.CodeServerError, "get base models failed", err)
		return
	}
	api.Success(c, map[string]interface{}{
		"data": models,
	})
}

// GetAllBaseLLMModelsConfig 获取所有基础模型配置
// @Summary 获取所有基础模型配置
// @Description 获取所有基础模型配置
// @Tags model
// @Accept json
// @Produce json
// @Success 200 {object} []service.ModelConfigIO
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/models/base [get]
func (h *ModelHandler) GetAllBaseLLMModelsConfig(c *gin.Context) {
	models, err := h.service.Model.GetAllBaseLLMModelsConfig(c.Request.Context())
	if err != nil {
		api.Fail(c, api.CodeServerError, "get base models failed", err)
		return
	}
	api.Success(c, models)
}

// CreateLLMModelConfig 创建模型配置
// @Summary 创建模型配置
// @Description 创建新的模型配置
// @Tags model
// @Accept json
// @Produce json
// @Param request body service.ModelConfigIO true "模型配置信息"
// @Success 200 {object} service.ModelConfigIO
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/models/create [post]
func (h *ModelHandler) CreateLLMModelConfig(c *gin.Context) {
	var req service.ModelConfigIO
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	model, err := h.service.Model.CreateLLMModelConfig(c.Request.Context(), &req)
	if err != nil {
		api.Fail(c, api.CodeServerError, "create model config failed", err)
		return
	}

	api.Success(c, model)
}

// UpdateLLMModelConfig 更新模型配置
// @Summary 更新模型配置
// @Description 更新指定模型的配置信息
// @Tags model
// @Accept json
// @Produce json
// @Param model_id path string true "模型ID"
// @Param config body service.ModelConfigIO true "配置信息"
// @Success 200 {object} service.ModelConfigIO
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 404 {object} string
// @Failure 500 {object} string
// @Router /model/{model_id}/config [post]
func (h *ModelHandler) UpdateLLMModelConfig(c *gin.Context) {
	modelID := c.Param("id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "id is required", errors.New("id is required"))
		return
	}

	var req service.ModelConfigIO
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	req.ModelID = modelID

	model, err := h.service.Model.UpdateLLMModelConfig(c.Request.Context(), &req)
	if err != nil {
		api.Fail(c, api.CodeServerError, "update model config failed", err)
		return
	}

	api.Success(c, model)
}

// ToggleLLMModelConfig 修改模型启用状态
// @Summary 修改模型启用状态
// @Description 修改指定模型的启用状态
// @Tags model
// @Accept json
// @Produce json
// @Param id query string true "模型ID"
// @Success 200 {object} service.ModelConfigIO
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 404 {object} string
// @Failure 500 {object} string
// @Router /api/v1/models/toggle [post]
func (h *ModelHandler) ToggleLLMModelConfig(c *gin.Context) {
	modelID := c.Query("id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "id is required", errors.New("id is required"))
		return
	}

	model, err := h.service.Model.ToggleLLMModelConfig(c.Request.Context(), modelID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "toggle model config failed", err)
		return
	}

	api.Success(c, model)
}

// GetAvailableLLMModels 获取可以使用的模型列表
// @Summary 获取可以使用的模型列表
// @Description 获取可以使用的模型列表
// @Tags model
// @Accept json
// @Produce json
// @Success 200 {object} []service.AvailableModel
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/models [get]
func (h *ModelHandler) GetAvailableLLMModels(c *gin.Context) {
	models, err := h.service.Model.GetAvailableLLMModels(c.Request.Context())
	if err != nil {
		api.Fail(c, api.CodeServerError, "get available models failed", err)
		return
	}
	api.Success(c, map[string]interface{}{
		"data": models,
	})
}

func (h *ModelHandler) Close() {

}
