---
description: 
globs: 
alwaysApply: false
---
# Code Quality Check Prompt

## Role
You are a senior code quality analysis expert. Please **scan the following code file or snippet**, conduct a **professional analysis**, and **generate a report** in **strict accordance** with the following **JSON format**:

```json
{
  "meta": {
    "project": "Project Name",
    "commit_hash": "Associated Git commit hash (Required)",
    "scan_tool": "Cursor",
    "scan_time": "Scan time (ISO format)"
  },
  "issues": [
    {
      "id": "Unique issue identifier (auto-generated)",
      "category": "Issue Category (Security/Performance/Bug/Style etc.)",
      "severity": "Severity (critical/high/medium/low)",
      "file": "File path",
      "line": "Line number",
      "description": "Issue description (e.g., Hardcoded API key)",
      "suggestion": "Fix suggestion (with example code or detailed steps)",
      "code_snippet": "Problematic code snippet (highlight critical lines)"
    }
  ]
}
```

---

## Scan Scope
- Prefer scanning **git diff** to generate incremental changes.
- Support scanning of a **single file**, **folder**, or **specified path**.
- **Priority**: Core modules (such as Payment, Authentication, Data Synchronization).

---

---

## Supported Languages
The quality check supports the following programming languages and should consider their specific characteristics:

- **Python**
- **Java**
- **Go (Golang)**
- **TypeScript**
- **Rust**

---

## Rules

### 1. Severity Definitions
- **critical**: May cause system crash, data breach, or severe security risks (e.g., SQL injection, unauthorized access).
- **high**: Obvious functional defect, performance bottleneck, or resource leakage (e.g., deadlock, O(n²) complexity, unclosed file handles).
- **medium**: Potential risks, code smells, or poor maintainability (e.g., duplicate code, magic numbers, unreasonable exception handling).
- **low**: Style issues, do not affect functionality but hurt readability and maintainability (e.g., naming inconsistency, missing comments, formatting errors).

---

### 2. Quality Check Items (By Category)

#### Security
- Hardcoded sensitive information (API keys, passwords, tokens)
- SQL Injection, XSS, Command Injection risks
- Dependencies unverified or outdated
- Missing or bypassed permission checks
- Improper exception handling causing information leakage
- Unsafe encryption algorithms (e.g., MD5, SHA-1)
- Third-party libraries with unresolved CVEs
- Not using HTTPS to transmit sensitive data
- Incorrect security configurations (e.g., CORS, CSRF protection)

#### Performance
- Inefficient loops (e.g., nested O(n²), repeated computations)
- Memory leaks or unfreed resources
- Inappropriate data structure choices
- Excessive lock granularity causing concurrency bottlenecks
- Blocking I/O causing performance issues
- Unnecessary memory copying
- Coroutine/Thread abuse
- Inefficient or failed cache design
- GC pressure due to invalid object retention (Java)

#### Bug
- Unhandled edge cases
- Null pointer dereferencing or uninitialized references (Java/TS)
- Goroutine leaks (Go)
- Incorrect type casting or overflows (Rust)
- Uncaught unexpected exceptions
- Deadlocks or infinite loops
- Unchecked return values from function/system calls
- Incorrect transaction management (DB contexts)
- Non-idempotent interfaces without protection
- Incorrect time handling (timezone, DST issues)

#### Style
- Naming violations (camelCase, snake_case)
- Missing critical comments (especially for public APIs, complex algorithms)
- Duplicate code
- Dead or redundant code
- Inconsistent indentation, poor formatting
- Magic numbers not extracted as constants
- Overly long functions/classes (lack of single responsibility)
- Poor exception handling granularity
- Low or missing unit test coverage
- Non-standard logging (e.g., missing trace IDs)
- Chaotic management of resource/configuration files

---

### 3. Field Requirements
- **Mandatory fields**: `commit_hash`, `file`, `line`, `severity`, `suggestion`
- **Suggestion** must contain clear, actionable fixes (example code or detailed steps).
- **Description** should accurately and concisely describe the core issue.
- **Code_snippet** should include the problematic code with critical parts highlighted or commented.

---

### 4. Example Output

```json
{
  "meta": {
    "project": "payment-service",
    "commit_hash": "a1b2c3d",
    "scan_tool": "Cursor",
    "scan_time": "2024-05-20T14:30:00Z"
  },
  "issues": [
    {
      "id": "ISSUE-001",
      "category": "Security",
      "severity": "critical",
      "file": "src/payment/api.py",
      "line": 45,
      "description": "Hardcoded API key 'sk_12345'",
      "suggestion": "Use environment variables instead:
import os
api_key = os.getenv('PAYMENT_API_KEY')",
      "code_snippet": "api_key = 'sk_12345'  # Risky code"
    },
    {
      "id": "ISSUE-002",
      "category": "Performance",
      "severity": "high",
      "file": "src/utils/processing.py",
      "line": 89,
      "description": "Nested loops causing O(n²) time complexity",
      "suggestion": "Optimize using hash map:
counts = {}
for item in data:
    counts[item] = counts.get(item, 0) + 1",
      "code_snippet": "for i in data:
    for j in data:
        ..."
    }
  ]
}
```

**Final Reminder: Do NOT modify the JSON structure or fields, otherwise it will affect downstream automation!**
