package service

import (
	"context"
	"strconv"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type ConfigService struct {
	log *logger.ModuleLogger
	db  *repository.DB
}

var _ ConfigApi = &ConfigService{}

func NewConfigService(ctx context.Context, db *repository.DB) *ConfigService {
	return &ConfigService{
		log: logger.GetLogger("config"),
		db:  db,
	}
}

var (
	systemConfigCategory   = "system"
	audioTTSConfigCategory = "audio_tts"
	audioASRConfigCategory = "audio_asr"
	imageConfigCategory    = "image"
)

func (s *ConfigService) GetSystemCommonConfig(ctx context.Context) (*SystemCommonConfig, error) {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, systemConfigCategory)
	if err != nil {
		return nil, err
	}

	config := &SystemCommonConfig{}

	for _, c := range configList {
		switch c.Key {
		case SystemCommonConfigAPI_KEY_ALLOWED_ENDPOINTS:
			config.API_KEY_ALLOWED_ENDPOINTS = c.Value
		case SystemCommonConfigDEFAULT_USER_ROLE:
			config.DEFAULT_USER_ROLE = c.Value
		case SystemCommonConfigENABLE_API_KEY:
			config.ENABLE_API_KEY = c.Value == "true"
		case SystemCommonConfigENABLE_API_KEY_ENDPOINT_RESTRICTIONS:
			config.ENABLE_API_KEY_ENDPOINT_RESTRICTIONS = c.Value == "true"
		case SystemCommonConfigENABLE_CHANNELS:
			config.ENABLE_CHANNELS = c.Value == "true"
		case SystemCommonConfigENABLE_COMMUNITY_SHARING:
			config.ENABLE_COMMUNITY_SHARING = c.Value == "true"
		case SystemCommonConfigENABLE_MESSAGE_RATING:
			config.ENABLE_MESSAGE_RATING = c.Value == "true"
		case SystemCommonConfigENABLE_SIGNUP:
			config.ENABLE_SIGNUP = c.Value == "true"
		case SystemCommonConfigJWT_EXPIRES_IN:
			config.JWT_EXPIRES_IN = c.Value
		case SystemCommonConfigSHOW_ADMIN_DETAILS:
			config.SHOW_ADMIN_DETAILS = c.Value == "true"
		case SystemCommonConfigWEBUI_URL:
			config.WEBUI_URL = c.Value
		}
	}

	return config, nil
}

func (s *ConfigService) UpdateSystemCommonConfig(ctx context.Context, config *SystemCommonConfig) error {
	configList, err := s.db.SystemConfig.GetByCategory(ctx, systemConfigCategory)
	if err != nil {
		return err
	}

	configMap := make(map[string]*repository.SystemConfig)
	for _, c := range configList {
		configMap[c.Key] = c
	}

	updateList := []*repository.SystemConfig{}
	if conf, ok := configMap[SystemCommonConfigAPI_KEY_ALLOWED_ENDPOINTS]; ok {
		if config.API_KEY_ALLOWED_ENDPOINTS != conf.Value {
			conf.Value = config.API_KEY_ALLOWED_ENDPOINTS
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigAPI_KEY_ALLOWED_ENDPOINTS,
			Value:    config.API_KEY_ALLOWED_ENDPOINTS,
		})
	}

	if conf, ok := configMap[SystemCommonConfigDEFAULT_USER_ROLE]; ok {
		if config.DEFAULT_USER_ROLE != conf.Value {
			conf.Value = config.DEFAULT_USER_ROLE
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigDEFAULT_USER_ROLE,
			Value:    config.DEFAULT_USER_ROLE,
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_API_KEY]; ok {
		if config.ENABLE_API_KEY != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_API_KEY)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_API_KEY,
			Value:    strconv.FormatBool(config.ENABLE_API_KEY),
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_API_KEY_ENDPOINT_RESTRICTIONS]; ok {
		if config.ENABLE_API_KEY_ENDPOINT_RESTRICTIONS != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_API_KEY_ENDPOINT_RESTRICTIONS)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_API_KEY_ENDPOINT_RESTRICTIONS,
			Value:    strconv.FormatBool(config.ENABLE_API_KEY_ENDPOINT_RESTRICTIONS),
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_CHANNELS]; ok {
		if config.ENABLE_CHANNELS != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_CHANNELS)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_CHANNELS,
			Value:    strconv.FormatBool(config.ENABLE_CHANNELS),
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_COMMUNITY_SHARING]; ok {
		if config.ENABLE_COMMUNITY_SHARING != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_COMMUNITY_SHARING)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_COMMUNITY_SHARING,
			Value:    strconv.FormatBool(config.ENABLE_COMMUNITY_SHARING),
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_MESSAGE_RATING]; ok {
		if config.ENABLE_MESSAGE_RATING != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_MESSAGE_RATING)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_MESSAGE_RATING,
			Value:    strconv.FormatBool(config.ENABLE_MESSAGE_RATING),
		})
	}

	if conf, ok := configMap[SystemCommonConfigENABLE_SIGNUP]; ok {
		if config.ENABLE_SIGNUP != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.ENABLE_SIGNUP)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigENABLE_SIGNUP,
			Value:    strconv.FormatBool(config.ENABLE_SIGNUP),
		})
	}

	if conf, ok := configMap[SystemCommonConfigJWT_EXPIRES_IN]; ok {
		if config.JWT_EXPIRES_IN != conf.Value {
			conf.Value = config.JWT_EXPIRES_IN
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigJWT_EXPIRES_IN,
			Value:    config.JWT_EXPIRES_IN,
		})
	}

	if conf, ok := configMap[SystemCommonConfigSHOW_ADMIN_DETAILS]; ok {
		if config.SHOW_ADMIN_DETAILS != (conf.Value == "true") {
			conf.Value = strconv.FormatBool(config.SHOW_ADMIN_DETAILS)
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigSHOW_ADMIN_DETAILS,
			Value:    strconv.FormatBool(config.SHOW_ADMIN_DETAILS),
		})
	}

	if conf, ok := configMap[SystemCommonConfigWEBUI_URL]; ok {
		if config.WEBUI_URL != conf.Value {
			conf.Value = config.WEBUI_URL
			updateList = append(updateList, conf)
		}
	} else {
		updateList = append(updateList, &repository.SystemConfig{
			Category: systemConfigCategory,
			Key:      SystemCommonConfigWEBUI_URL,
			Value:    config.WEBUI_URL,
		})
	}

	if len(updateList) == 0 {
		return nil
	}

	err = s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		for _, conf := range updateList {
			if conf.ID == 0 {
				err = db.SystemConfig.Create(ctx, conf)

			} else {
				err = db.SystemConfig.UpdateByCategoryAndKey(ctx, conf.Category, conf.Key, conf.Value)
			}
			if err != nil {
				return err
			}
		}
		return nil
	})

	return err
}

func (s *ConfigService) GetBaseConfig(ctx context.Context) (*BaseConfig, error) {
	config := &BaseConfig{
		Status:        true,
		Name:          "TopAI",
		Version:       "0.0.1",
		DefaultLocale: "",
		Oauth: struct {
			Providers map[string]interface{} `json:"providers"`
		}{
			Providers: map[string]interface{}{},
		},
		Features: struct {
			Auth                  bool `json:"auth"`
			AuthTrustedHeader     bool `json:"auth_trusted_header"`
			EnableLdap            bool `json:"enable_ldap"`
			EnableApiKey          bool `json:"enable_api_key"`
			EnableSignup          bool `json:"enable_signup"`
			EnableLoginForm       bool `json:"enable_login_form"`
			EnableWebsocket       bool `json:"enable_websocket"`
			EnableImageGeneration bool `json:"enable_image_generation"`
		}{
			Auth:                  true,
			AuthTrustedHeader:     false,
			EnableLdap:            false,
			EnableApiKey:          false,
			EnableSignup:          true,
			EnableLoginForm:       true,
			EnableWebsocket:       true,
			EnableImageGeneration: false,
		},
	}

	enableSinup, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, systemConfigCategory, SystemCommonConfigENABLE_SIGNUP)
	if err != nil {
		return nil, err
	}
	if enableSinup != nil {
		config.Features.EnableSignup = enableSinup.Value == "true"
	}
	enableImageGeneration, err := s.db.SystemConfig.GetByCategoryAndKey(ctx, imageConfigCategory, ImageConfigEnable)
	if err != nil {
		return nil, err
	}
	if enableImageGeneration != nil {
		config.Features.EnableImageGeneration = enableImageGeneration.Value == "true"
	}

	return config, nil
}
