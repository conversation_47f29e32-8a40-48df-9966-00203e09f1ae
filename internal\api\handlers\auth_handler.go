package handlers

import (
	"errors"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

// AuthHandler 处理用户认证相关请求
type AuthHandler struct {
	log          *logger.ModuleLogger
	service      *service.Api
	signMessages []signMessage
	lock         *sync.RWMutex
}
type signMessage struct {
	Message   string
	CreatedAt time.Time
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(service *service.Api) *AuthHandler {
	log := logger.GetLogger("auth_handler")
	return &AuthHandler{
		log:          log,
		service:      service,
		signMessages: make([]signMessage, 0),
		lock:         &sync.RWMutex{},
	}
}

// SigninRequest 登录请求
type SigninRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// SignupRequest 注册请求
type SignupRequest struct {
	Name           string `json:"name" binding:"required,min=3,max=50"`
	Email          string `json:"email" binding:"required,email"`
	Password       string `json:"password" binding:"required,min=6"`
	ProfileImageID string `json:"profile_image_id"`
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	Name           string `json:"name"`
	Email          string `json:"email"`
	ProfileImageID string `json:"profile_image_id"`
}

// UpdatePasswordRequest 更新密码请求
type UpdatePasswordRequest struct {
	OldPassword string `json:"password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// Signin 处理登录请求
// @Summary 用户登录
// @Description 用户登录接口
// @Tags auth
// @Accept json
// @Produce json
// @Param login body SigninRequest true "登录信息"
// @Success 200 {object} service.UserAuthResponse
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/signin [post]
func (h *AuthHandler) Signin(c *gin.Context) {
	ctx := c.Request.Context()
	var req SigninRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	// 验证邮箱格式
	if !utils.IsEmailValid(req.Email) {
		api.Fail(c, api.CodeClientError, "invalid email format", errors.New("invalid email format"))
		return
	}

	// 登录逻辑
	loginResponse, err := h.service.User.Login(ctx, req.Email, req.Password)
	if err != nil {
		h.log.Error("登录失败", zap.Error(err))
		api.Fail(c, api.CodeUnauthorized, "login failed", err)
		return
	}

	// 设置只读的token cookie
	c.SetCookie("token", loginResponse.Token, 0, "/", "", false, true)

	api.Success(c, loginResponse)
}

// Signup 处理注册请求
// @Summary 用户注册
// @Description 用户注册接口
// @Tags auth
// @Accept json
// @Produce json
// @Param register body SignupRequest true "注册信息"
// @Success 200 {object} service.UserAuthResponse
// @Failure 400 {object} string
// @Failure 500 {object} string
// @Router /auth/signup [post]
func (h *AuthHandler) Signup(c *gin.Context) {
	ctx := c.Request.Context()
	var req SignupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	// 验证邮箱格式
	if !utils.IsEmailValid(req.Email) {
		api.Fail(c, api.CodeClientError, "invalid email format", errors.New("invalid email format"))
		return
	}

	// 验证密码格式
	if !utils.IsPasswordValid(req.Password) {
		api.Fail(c, api.CodeClientError, "invalid password format", errors.New("password should be at least 6 characters"))
		return
	}

	// 验证用户名格式
	if !utils.IsUsernameValid(req.Name) {
		api.Fail(c, api.CodeClientError, "invalid username format", errors.New("username should be 3-50 characters"))
		return
	}

	// 注册逻辑
	registerResponse, err := h.service.User.RegisterAndGenerateToken(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		h.log.Error("注册失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "register failed", err)
		return
	}

	// 设置只读的token cookie
	c.SetCookie("token", registerResponse.Token, 0, "/", "", false, true)

	api.Success(c, registerResponse)
}

// Signout 处理登出请求
// @Summary 用户登出
// @Description 用户登出接口
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/signout [get]
func (h *AuthHandler) Signout(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")

	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}

	// 登出逻辑
	err := h.service.User.Logout(ctx, userOP.ID)
	if err != nil {
		h.log.Error("登出失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "logout failed", err)
		return
	}

	// 清理cookie
	c.SetCookie("token", "", -1, "/", "", false, true)

	api.Success(c, nil)
}

// Me 获取当前用户信息
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的信息
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} service.UserBaseOP
// @Failure 401 {object} string
// @Router /auth/ [get]
func (h *AuthHandler) Me(c *gin.Context) {
	user, _ := c.Get("user")

	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}

	api.Success(c, userOP)
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新当前登录用户的资料
// @Tags auth
// @Accept json
// @Produce json
// @Param profile body UpdateProfileRequest true "用户资料"
// @Success 200 {object} service.UserBaseOP
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/update/profile [post]
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")

	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	// 验证邮箱格式
	if req.Email != "" && !utils.IsEmailValid(req.Email) {
		api.Fail(c, api.CodeClientError, "invalid email format", errors.New("invalid email format"))
		return
	}

	// 验证用户名格式
	if req.Name != "" && !utils.IsUsernameValid(req.Name) {
		api.Fail(c, api.CodeClientError, "invalid username format", errors.New("username should be 3-50 characters"))
		return
	}

	// 更新用户资料
	updatedUser, err := h.service.User.AdminUpdateUser(ctx, userOP.IdentityID, req.Email, req.Name, "", "")
	if err != nil {
		h.log.Error("更新用户资料失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "update user profile failed", err)
		return
	}

	api.Success(c, updatedUser)
}

// UpdatePassword 更新密码
// @Summary 更新用户密码
// @Description 更新当前登录用户的密码
// @Tags auth
// @Accept json
// @Produce json
// @Param password body UpdatePasswordRequest true "密码信息"
// @Success 200 {object} string
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/update/password [post]
func (h *AuthHandler) UpdatePassword(c *gin.Context) {
	// 从上下文获取用户信息
	user, _ := c.Get("user")

	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}

	var req UpdatePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request format", err)
		return
	}

	// 验证密码格式
	if !utils.IsPasswordValid(req.NewPassword) {
		api.Fail(c, api.CodeClientError, "invalid password format", errors.New("password should be at least 6 characters"))
		return
	}

	if req.NewPassword == req.OldPassword {
		api.Fail(c, api.CodeClientError, "new password is the same as old password", errors.New("new password is the same as old password"))
		return
	}

	// TODO: 实现密码更新逻辑
	err := h.service.User.UpdatePassword(c.Request.Context(), userOP, req.OldPassword, req.NewPassword)
	if err != nil {
		h.log.Error("更新密码失败", zap.Error(err))
		api.Fail(c, api.CodeServerError, "update password failed", err)
		return
	}

	// 这里需要验证旧密码，然后更新新密码
	api.Fail(c, api.CodeServerError, "function not implemented", errors.New("function not implemented"))
}

// Close 关闭处理器，释放资源
func (h *AuthHandler) Close() {
	// 清理资源
}
