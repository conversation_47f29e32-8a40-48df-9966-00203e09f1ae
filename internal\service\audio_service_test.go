package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestAudioService_NewAudioService 测试创建音频服务
func TestAudioService_NewAudioService(t *testing.T) {
	tests := []struct {
		name       string
		wantNotNil bool
	}{
		{
			name:       "TC1-成功创建音频服务",
			wantNotNil: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			db := &repository.DB{
				SystemConfig:  mocks.DB.SystemConfig,
				TTSModelVoice: mocks.DB.TTSModelVoice,
			}
			service := NewAudioService(ctx, db)

			if tt.wantNotNil {
				assert.NotNil(t, service)
				assert.Equal(t, db, service.db)
			} else {
				assert.Nil(t, service)
			}
		})
	}
}

// TestAudioService_GetAudioConfig 测试获取音频配置
func TestAudioService_GetAudioConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-获取音频配置成功",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock TTS配置
				ttsConfigs := []*repository.SystemConfig{
					{Category: "audio_tts", Key: AudioConfigTTS_MODEL, Value: "tts-1"},
					{Category: "audio_tts", Key: AudioConfigTTS_VOICE, Value: "alloy"},
					{Category: "audio_tts", Key: AudioConfigTTS_ENABLE, Value: "true"},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "audio_tts").Return(ttsConfigs, nil)

				// Mock ASR配置
				asrConfigs := []*repository.SystemConfig{
					{Category: "audio_asr", Key: AudioConfigASR_MODEL, Value: "whisper-1"},
					{Category: "audio_asr", Key: AudioConfigASR_ENABLE, Value: "true"},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "audio_asr").Return(asrConfigs, nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				SystemConfig:  mocks.DB.SystemConfig,
				TopaiModelExt: mocks.DB.TopaiModelExt,
				TopaiModel:    mocks.DB.TopaiModel,
				TTSModelVoice: mocks.DB.TTSModelVoice,
				RunWithTx:     mocks.DB.RunWithTx,
			}
			service := NewAudioService(ctx, db)

			result, err := service.GetAudioConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotNil(t, result.TTS)
				assert.NotNil(t, result.ASR)
			}
		})
	}
}

// TestAudioService_UpdateAudioConfig 测试更新音频配置
func TestAudioService_UpdateAudioConfig(t *testing.T) {
	tests := []struct {
		name    string
		config  *AudioConfig
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-更新音频配置成功",
			config: &AudioConfig{
				TTS: &AudioConfigTTS{
					ENABLE: true,
					MODEL:  "tts-1",
					VOICE:  "alloy",
				},
				ASR: &AudioConfigASR{
					ENABLE: true,
					MODEL:  "whisper-1",
				},
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock获取现有配置
				ttsConfigs := []*repository.SystemConfig{
					{Category: "audio_tts", Key: AudioConfigTTS_MODEL, Value: "old-tts"},
					{Category: "audio_tts", Key: AudioConfigTTS_VOICE, Value: "old-voice"},
					{Category: "audio_tts", Key: AudioConfigTTS_ENABLE, Value: "false"},
				}
				asrConfigs := []*repository.SystemConfig{
					{Category: "audio_asr", Key: AudioConfigASR_MODEL, Value: "old-asr"},
					{Category: "audio_asr", Key: AudioConfigASR_ENABLE, Value: "false"},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "audio_tts").Return(ttsConfigs, nil)
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "audio_asr").Return(asrConfigs, nil)

				// Mock更新操作
				mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
				mocks.DB.SystemConfig.On("Create", mock.Anything, mock.Anything).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			config: &AudioConfig{
				TTS: &AudioConfigTTS{ENABLE: true, MODEL: "tts-1"},
				ASR: &AudioConfigASR{ENABLE: true, MODEL: "whisper-1"},
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, mock.Anything).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				SystemConfig:  mocks.DB.SystemConfig,
				TopaiModelExt: mocks.DB.TopaiModelExt,
				TopaiModel:    mocks.DB.TopaiModel,
				TTSModelVoice: mocks.DB.TTSModelVoice,
				RunWithTx:     mocks.DB.RunWithTx,
			}
			service := NewAudioService(ctx, db)

			err := service.UpdateAudioConfig(ctx, tt.config)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestAudioService_GetAudioTTSConfig 测试获取TTS配置
func TestAudioService_GetAudioTTSConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    *AudioConfigTTS
		wantErr bool
	}{
		{
			name: "TC1-获取TTS配置成功",
			setup: func(mocks *testutil.MockDependencies) {
				configs := []*repository.SystemConfig{
					{
						Category: audioTTSConfigCategory,
						Key:      AudioConfigTTS_MODEL,
						Value:    "tts-model-1",
					},
					{
						Category: audioTTSConfigCategory,
						Key:      AudioConfigTTS_VOICE,
						Value:    "voice-1",
					},
					{
						Category: audioTTSConfigCategory,
						Key:      AudioConfigTTS_ENABLE,
						Value:    "true",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioTTSConfigCategory).Return(configs, nil)
			},
			want: &AudioConfigTTS{
				MODEL:  "tts-model-1",
				VOICE:  "voice-1",
				ENABLE: true,
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioTTSConfigCategory).Return(nil, assert.AnError)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-配置为空，使用默认值",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioTTSConfigCategory).Return([]*repository.SystemConfig{}, nil)
			},
			want: &AudioConfigTTS{
				MODEL:  "",
				VOICE:  "",
				ENABLE: false,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			}
			service := NewAudioService(ctx, db)

			got, err := service.GetAudioTTSConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestAudioService_GetAudioASRConfig 测试获取ASR配置
func TestAudioService_GetAudioASRConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    *AudioConfigASR
		wantErr bool
	}{
		{
			name: "TC1-获取ASR配置成功",
			setup: func(mocks *testutil.MockDependencies) {
				configs := []*repository.SystemConfig{
					{
						Category: audioASRConfigCategory,
						Key:      AudioConfigASR_MODEL,
						Value:    "asr-model-1",
					},
					{
						Category: audioASRConfigCategory,
						Key:      AudioConfigASR_ENABLE,
						Value:    "true",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioASRConfigCategory).Return(configs, nil)
			},
			want: &AudioConfigASR{
				MODEL:  "asr-model-1",
				ENABLE: true,
			},
			wantErr: false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioASRConfigCategory).Return(nil, assert.AnError)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-配置为空，使用默认值",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, audioASRConfigCategory).Return([]*repository.SystemConfig{}, nil)
			},
			want: &AudioConfigASR{
				MODEL:  "",
				ENABLE: false,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			}
			service := NewAudioService(ctx, db)

			got, err := service.GetAudioASRConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestAudioService_GetAudioVoices 测试获取音频音色列表
func TestAudioService_GetAudioVoices(t *testing.T) {
	ctx := context.Background()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	db := &repository.DB{}
	service := NewAudioService(ctx, db)

	got, err := service.GetAudioVoices(ctx)

	assert.NoError(t, err)
	assert.Len(t, got, 6)

	// 验证返回的音色列表
	expectedVoices := []string{"alloy", "echo", "fable", "onyx", "nova", "shimmer"}
	for i, voice := range got {
		assert.Equal(t, expectedVoices[i], voice.ID)
		assert.Equal(t, expectedVoices[i], voice.Name)
	}
}

// TestAudioService_GetAudioBaseModels 测试获取音频基础模型
func TestAudioService_GetAudioBaseModels(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    *AudioBaseModel
		wantErr bool
	}{
		{
			name: "TC1-成功获取音频基础模型",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock model extensions
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "tts-model-1",
					},
					{
						Id:           2,
						TopaiModelId: 2,
						ModelId:      "asr-model-1",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)

				// Mock models
				models := []*repository.TopaiModel{
					{
						Id:        1,
						ModelType: repository.RemoterModelTypeTTS.String(),
					},
					{
						Id:        2,
						ModelType: repository.RemoterModelTypeASR.String(),
					},
				}
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1, 2}).Return(models, nil)

				// Mock TTS voices
				voices := []*repository.TTSModelVoice{
					{
						Name: "voice1",
					},
					{
						Name: "voice2",
					},
				}
				mocks.DB.TTSModelVoice.On("GetByModelName", mock.Anything, "tts-model-1").Return(voices, nil)
			},
			want: &AudioBaseModel{
				TTS: []*AudioModel{
					{
						ID:      1,
						ModelId: "tts-model-1",
						Voices: []*AudioVoice{
							{ID: "voice1", Name: "voice1"},
							{ID: "voice2", Name: "voice2"},
						},
					},
				},
				ASR: []*AudioModel{
					{
						ID:      2,
						ModelId: "asr-model-1",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "TC2-获取模型扩展信息失败",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-获取模型信息失败",
			setup: func(mocks *testutil.MockDependencies) {
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "tts-model-1",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC4-TTS模型没有音色",
			setup: func(mocks *testutil.MockDependencies) {
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "tts-model-1",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)

				models := []*repository.TopaiModel{
					{
						Id:        1,
						ModelType: repository.RemoterModelTypeTTS.String(),
					},
				}
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(models, nil)

				// Mock empty voices
				mocks.DB.TTSModelVoice.On("GetByModelName", mock.Anything, "tts-model-1").Return([]*repository.TTSModelVoice{}, nil)
			},
			want: &AudioBaseModel{
				TTS: []*AudioModel{},
				ASR: []*AudioModel{},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewAudioService(ctx, &repository.DB{
				TopaiModelExt: mocks.DB.TopaiModelExt,
				TopaiModel:    mocks.DB.TopaiModel,
				TTSModelVoice: mocks.DB.TTSModelVoice,
			})

			got, err := service.GetAudioBaseModels(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// BenchmarkAudioService_NewService 创建音频服务性能基准测试
func BenchmarkAudioService_NewService(b *testing.B) {
	ctx := context.Background()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		db := &repository.DB{
			SystemConfig:  mocks.DB.SystemConfig,
			TopaiModelExt: mocks.DB.TopaiModelExt,
			TopaiModel:    mocks.DB.TopaiModel,
			TTSModelVoice: mocks.DB.TTSModelVoice,
			RunWithTx:     mocks.DB.RunWithTx,
		}
		service := NewAudioService(ctx, db)
		_ = service
	}
}

// BenchmarkAudioService_GetAudioConfig 获取音频配置性能基准测试
func BenchmarkAudioService_GetAudioConfig(b *testing.B) {
	ctx := context.Background()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()
	db := &repository.DB{
		SystemConfig:  mocks.DB.SystemConfig,
		TopaiModelExt: mocks.DB.TopaiModelExt,
		TopaiModel:    mocks.DB.TopaiModel,
		TTSModelVoice: mocks.DB.TTSModelVoice,
		RunWithTx:     mocks.DB.RunWithTx,
	}
	service := NewAudioService(ctx, db)
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.GetAudioConfig(ctx)
	}
}
