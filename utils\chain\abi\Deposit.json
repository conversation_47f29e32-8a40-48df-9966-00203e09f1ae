{"_format": "hh-sol-artifact-1", "contractName": "<PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/AIPay/Deposit.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_usdtAddress", "type": "address"}, {"internalType": "address", "name": "_bankAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "oldBank", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newBank", "type": "address"}], "name": "BankAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "currentBalance", "type": "uint256"}], "name": "DepositMade", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "newBalance", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "directory", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "currentBalance", "type": "uint256"}], "name": "UserBalanceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "newBalance", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "directory", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "currentBalance", "type": "uint256"}], "name": "WorkerTopBalanceUpdated", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "IMO_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bankAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "deposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getUserBalance", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}, {"internalType": "uint256", "name": "current", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newBank", "type": "address"}], "name": "updateBankAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "uint256", "name": "_newBalance", "type": "uint256"}], "name": "updateUserBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}, {"internalType": "uint256", "name": "_addTop", "type": "uint256"}, {"internalType": "bool", "name": "direct", "type": "bool"}], "name": "updateWorkerBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdt", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userBalances", "outputs": [{"internalType": "uint256", "name": "totalDeposited", "type": "uint256"}, {"internalType": "uint256", "name": "currentBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "workerBalances", "outputs": [{"internalType": "uint256", "name": "totalBalance", "type": "uint256"}, {"internalType": "uint256", "name": "currentBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}