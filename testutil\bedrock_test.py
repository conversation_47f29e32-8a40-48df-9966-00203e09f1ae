import boto3
import json
import time
import statistics
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
import random
import string

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BedrockLatencyTester:
    """AWS Bedrock Claude模型首包时延测试器"""
    
    # 支持的Claude模型配置
    CLAUDE_MODELS = {
        'claude-3-haiku': {
            'model_id': 'anthropic.claude-3-haiku-20240307-v1:0',
            'name': 'Claude 3 Haiku',
            'max_tokens': 4096
        },
        'claude-3-sonnet': {
            'model_id': 'anthropic.claude-3-sonnet-20240229-v1:0', 
            'name': 'Claude 3 Sonnet',
            'max_tokens': 4096
        },
        'claude-3-opus': {
            'model_id': 'anthropic.claude-3-opus-20240229-v1:0',
            'name': '<PERSON> 3 Opus', 
            'max_tokens': 4096
        },
        'claude-3.5-sonnet': {
            'model_id': 'anthropic.claude-3-5-sonnet-20240620-v1:0',
            'name': 'Claude 3.5 Sonnet',
            'max_tokens': 8192
        },
        'claude-3.5-haiku': {
            'model_id': 'anthropic.claude-3-5-haiku-20241022-v1:0',
            'name': 'Claude 3.5 Haiku',
            'max_tokens': 8192
        }
    }
    
    # Prompt模板和长度配置
    PROMPT_TEMPLATES = {
        'short': [
            "Hello!",
            "What is AI?",
            "Explain quantum physics.",
            "Write a poem.",
            "Tell me a joke.",
            "How are you?",
            "What's the weather like?",
            "Define machine learning."
        ],
        'medium': [
            "Please explain the concept of artificial intelligence and its applications in modern technology. Include examples of how AI is used in different industries.",
            "Write a detailed summary about climate change, its causes, effects, and potential solutions. Make sure to cover both scientific and policy aspects.",
            "Describe the process of photosynthesis in plants, including the chemical reactions involved and the importance of this process for life on Earth.",
            "Explain the history and development of the internet, from its early beginnings to the modern web we use today. Include key milestones and technologies.",
            "Discuss the principles of sustainable development and how they can be applied to create environmentally friendly business practices.",
            "Analyze the impact of social media on modern communication and society, including both positive and negative aspects.",
            "Explain the basics of cryptocurrency and blockchain technology, including how they work and their potential applications.",
            "Describe the human immune system and how it protects the body from diseases and infections."
        ],
        'long': [
            """Please provide a comprehensive analysis of the global economic impact of artificial intelligence over the next decade. 
            Consider the following aspects in your response:
            1. Job displacement and creation across different sectors
            2. Productivity gains and economic growth potential
            3. Regional differences in AI adoption and economic benefits
            4. Policy recommendations for governments to manage the transition
            5. The role of education and reskilling programs
            6. Potential risks and mitigation strategies
            7. Impact on developing vs developed economies
            8. Specific industry case studies (healthcare, finance, manufacturing, transportation)
            Please provide detailed explanations, data-driven insights where possible, and cite relevant examples.""",
            
            """Write a detailed research paper outline on the topic of renewable energy transition and its environmental, economic, and social implications.
            The outline should include:
            1. Executive summary of current global energy landscape
            2. Detailed analysis of different renewable energy technologies (solar, wind, hydro, geothermal, etc.)
            3. Economic analysis including cost comparisons, job creation, and investment requirements
            4. Environmental impact assessment and carbon reduction potential
            5. Social implications including energy justice and community impacts
            6. Policy frameworks and government incentives across different countries
            7. Technological challenges and breakthrough innovations needed
            8. Grid integration and energy storage solutions
            9. Case studies from leading countries in renewable energy adoption
            10. Timeline and roadmap for global transition to 100% renewable energy
            Please provide detailed subsections for each main point and suggest relevant data sources and research methodologies.""",
            
            """Analyze the evolution of human communication from ancient times to the digital age, focusing on how technology has transformed the way we connect and share information.
            Your analysis should cover:
            1. Historical overview: From cave paintings to written language
            2. The printing press revolution and mass communication
            3. Telegraph, telephone, and early electronic communication
            4. Radio and television: The broadcast era
            5. The internet revolution and email communication
            6. Mobile technology and instant messaging
            7. Social media platforms and their societal impact
            8. Video conferencing and remote collaboration tools
            9. Artificial intelligence in communication (chatbots, translation, etc.)
            10. Future trends: VR/AR, brain-computer interfaces, and beyond
            
            For each era, discuss:
            - Key technological innovations
            - Social and cultural impacts
            - Changes in human behavior and relationships
            - Economic implications
            - Challenges and limitations
            - Transition to the next phase
            
            Conclude with predictions about the future of human communication and potential implications for society."""
        ]
    }
    
    def __init__(self, region_name: str = 'us-west-2', models: Optional[List[str]] = None):
        """
        初始化测试器
        
        Args:
            region_name: AWS区域名称
            models: 要测试的模型列表，如果为None则测试所有支持的模型
        """
        self.region_name = region_name
        self.bedrock_runtime = boto3.client(
            service_name='bedrock-runtime',
            region_name=region_name
        )
        
        # 设置要测试的模型
        if models is None:
            self.models_to_test = list(self.CLAUDE_MODELS.keys())
        else:
            self.models_to_test = [m for m in models if m in self.CLAUDE_MODELS]
            if not self.models_to_test:
                raise ValueError(f"无效的模型列表。支持的模型: {list(self.CLAUDE_MODELS.keys())}")
        
        logger.info(f"初始化完成，将测试模型: {[self.CLAUDE_MODELS[m]['name'] for m in self.models_to_test]}")
    
    def generate_random_prompt(self) -> Tuple[str, str]:
        """
        生成随机长度的prompt
        
        Returns:
            (prompt内容, prompt长度类别)
        """
        # 随机选择prompt长度类别
        length_category = random.choice(['short', 'medium', 'long'])
        
        # 从对应类别中随机选择一个prompt
        base_prompt = random.choice(self.PROMPT_TEMPLATES[length_category])
        
        # 对于short类别，有时添加随机扩展
        if length_category == 'short' and random.random() < 0.3:
            extensions = [
                " Please provide a detailed explanation.",
                " Include examples in your response.",
                " Make it comprehensive and informative.",
                " Add relevant context and background information.",
                " Explain this in simple terms with examples."
            ]
            base_prompt += random.choice(extensions)
        
        # 对于medium类别，有时添加额外要求
        elif length_category == 'medium' and random.random() < 0.4:
            additions = [
                " Please also include recent developments and future trends.",
                " Add practical examples and case studies to support your explanation.",
                " Consider different perspectives and potential counterarguments.",
                " Include relevant statistics and data where appropriate.",
                " Discuss both advantages and disadvantages of the topic."
            ]
            base_prompt += random.choice(additions)
        
        # 对于long类别，有时添加格式要求
        elif length_category == 'long' and random.random() < 0.2:
            format_reqs = [
                "\n\nPlease format your response with clear headings and bullet points where appropriate.",
                "\n\nOrganize your response in a structured manner with numbered sections.",
                "\n\nUse markdown formatting to improve readability.",
                "\n\nInclude a summary at the end of your response."
            ]
            base_prompt += random.choice(format_reqs)
        
        return base_prompt, length_category
    
    def prepare_request_body(self, model_key: str, prompt: str) -> str:
        """
        准备请求体
        
        Args:
            model_key: 模型键名
            prompt: 测试prompt
            
        Returns:
            JSON格式的请求体
        """
        model_info = self.CLAUDE_MODELS[model_key]
        
        body = {
            "anthropic_version": "bedrock-2023-05-31",
            "max_tokens": min(200, model_info['max_tokens']),  # 限制输出长度以加快测试
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,  # 降低随机性，提高一致性
            "top_p": 0.9
        }
        return json.dumps(body)
    
    def test_non_streaming_latency(self, model_key: str, prompt: str) -> Tuple[float, str]:
        """
        测试非流式请求的首包时延
        
        Args:
            model_key: 模型键名
            prompt: 测试prompt
            
        Returns:
            (首包时延毫秒, prompt长度类别)
        """
        model_info = self.CLAUDE_MODELS[model_key]
        body = self.prepare_request_body(model_key, prompt)
        
        start_time = time.perf_counter()
        
        try:
            response = self.bedrock_runtime.invoke_model(
                body=body,
                modelId=model_info['model_id'],
                accept='application/json',
                contentType='application/json'
            )
            
            # 首包时延
            first_packet_time = time.perf_counter()
            latency_ms = (first_packet_time - start_time) * 1000
            
            # 验证响应
            response_body = json.loads(response.get('body').read())
            if 'content' not in response_body:
                logger.warning(f"模型 {model_info['name']} 响应格式异常")
                
            return latency_ms, ""
            
        except Exception as e:
            logger.error(f"模型 {model_info['name']} 非流式请求失败: {e}")
            return float('inf'), ""
    
    def test_streaming_latency(self, model_key: str, prompt: str) -> Tuple[float, str]:
        """
        测试流式请求的首包时延
        
        Args:
            model_key: 模型键名
            prompt: 测试prompt
            
        Returns:
            (首包时延毫秒, prompt长度类别)
        """
        model_info = self.CLAUDE_MODELS[model_key]
        body = self.prepare_request_body(model_key, prompt)
        
        start_time = time.perf_counter()
        
        try:
            response = self.bedrock_runtime.invoke_model_with_response_stream(
                body=body,
                modelId=model_info['model_id'],
                accept='application/json',
                contentType='application/json'
            )
            
            stream = response.get('body')
            
            for event in stream:
                first_packet_time = time.perf_counter()
                latency_ms = (first_packet_time - start_time) * 1000
                
                chunk = event.get('chunk')
                if chunk:
                    chunk_data = json.loads(chunk.get('bytes').decode())
                    if 'type' in chunk_data:
                        logger.debug(f"模型 {model_info['name']} 收到首包，类型: {chunk_data.get('type')}")
                
                return latency_ms, ""
                
        except Exception as e:
            logger.error(f"模型 {model_info['name']} 流式请求失败: {e}")
            return float('inf'), ""
        
        logger.warning(f"模型 {model_info['name']} 流式请求未收到任何数据包")
        return float('inf'), ""
    
    def run_single_test(self, model_key: str, test_type: str) -> Dict:
        """
        运行单次测试
        
        Args:
            model_key: 模型键名
            test_type: 'streaming' 或 'non_streaming'
            
        Returns:
            测试结果字典
        """
        # 生成随机prompt
        prompt, prompt_category = self.generate_random_prompt()
        
        if test_type == 'streaming':
            latency, _ = self.test_streaming_latency(model_key, prompt)
        elif test_type == 'non_streaming':
            latency, _ = self.test_non_streaming_latency(model_key, prompt)
        else:
            raise ValueError("test_type必须是'streaming'或'non_streaming'")
        
        return {
            'latency': latency,
            'prompt_category': prompt_category,
            'prompt_length': len(prompt),
            'model': model_key
        }
    
    def run_batch_test(self, model_key: str, test_type: str, sample_count: int = 100, 
                      max_workers: int = 5) -> List[Dict]:
        """
        批量运行测试
        
        Args:
            model_key: 模型键名
            test_type: 'streaming' 或 'non_streaming'
            sample_count: 采样次数
            max_workers: 最大并发数
            
        Returns:
            测试结果列表
        """
        results = []
        failed_count = 0
        model_name = self.CLAUDE_MODELS[model_key]['name']
        
        logger.info(f"开始测试 {model_name} - {test_type}，采样{sample_count}次，并发数{max_workers}")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            futures = [executor.submit(self.run_single_test, model_key, test_type) 
                      for _ in range(sample_count)]
            
            # 收集结果
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    if result['latency'] != float('inf'):
                        results.append(result)
                        logger.info(f"{model_name} 完成第{len(results)}次测试，时延: {result['latency']:.2f}ms，"
                                  f"prompt类别: {result['prompt_category']}")
                    else:
                        failed_count += 1
                        logger.warning(f"{model_name} 第{i+1}次测试失败")
                except Exception as e:
                    failed_count += 1
                    logger.error(f"{model_name} 第{i+1}次测试异常: {e}")
        
        logger.info(f"{model_name} - {test_type}测试完成，成功{len(results)}次，失败{failed_count}次")
        return results
    
    def calculate_statistics(self, results: List[Dict]) -> Dict[str, any]:
        """
        计算统计数据
        
        Args:
            results: 测试结果列表
            
        Returns:
            统计结果字典
        """
        if not results:
            return {}
        
        latencies = [r['latency'] for r in results]
        prompt_categories = [r['prompt_category'] for r in results]
        prompt_lengths = [r['prompt_length'] for r in results]
        
        # 基础统计
        sorted_latencies = sorted(latencies)
        stats = {
            'count': len(latencies),
            'mean': statistics.mean(latencies),
            'median': statistics.median(latencies),
            'std_dev': statistics.stdev(latencies) if len(latencies) > 1 else 0,
            'min': min(latencies),
            'max': max(latencies),
            'p99': np.percentile(sorted_latencies, 99),
            'p95': np.percentile(sorted_latencies, 95),
            'p90': np.percentile(sorted_latencies, 90),
            'p50': np.percentile(sorted_latencies, 50)
        }
        
        # 按prompt长度分类的统计
        category_stats = {}
        for category in ['short', 'medium', 'long']:
            category_latencies = [r['latency'] for r in results if r['prompt_category'] == category]
            if category_latencies:
                category_stats[category] = {
                    'count': len(category_latencies),
                    'mean': statistics.mean(category_latencies),
                    'p99': np.percentile(sorted(category_latencies), 99)
                }
        
        stats['by_prompt_category'] = category_stats
        
        # Prompt长度统计
        stats['prompt_length'] = {
            'mean': statistics.mean(prompt_lengths),
            'min': min(prompt_lengths),
            'max': max(prompt_lengths),
            'median': statistics.median(prompt_lengths)
        }
        
        return stats
    
    def print_statistics(self, stats: Dict[str, any], model_key: str, test_type: str):
        """打印统计结果"""
        model_name = self.CLAUDE_MODELS[model_key]['name']
        print(f"\n{'='*70}")
        print(f"{model_name} - {test_type.upper()} 首包时延统计结果")
        print(f"{'='*70}")
        print(f"采样次数: {stats.get('count', 0)}")
        print(f"平均时延: {stats.get('mean', 0):.2f} ms")
        print(f"中位时延: {stats.get('median', 0):.2f} ms")
        print(f"标准差: {stats.get('std_dev', 0):.2f} ms")
        print(f"最小时延: {stats.get('min', 0):.2f} ms")
        print(f"最大时延: {stats.get('max', 0):.2f} ms")
        print(f"P50时延: {stats.get('p50', 0):.2f} ms")
        print(f"P90时延: {stats.get('p90', 0):.2f} ms")
        print(f"P95时延: {stats.get('p95', 0):.2f} ms")
        print(f"P99时延: {stats.get('p99', 0):.2f} ms")
        
        # Prompt长度统计
        if 'prompt_length' in stats:
            print(f"\nPrompt长度统计:")
            print(f"平均长度: {stats['prompt_length']['mean']:.0f} 字符")
            print(f"长度范围: {stats['prompt_length']['min']} - {stats['prompt_length']['max']} 字符")
        
        # 按prompt类别统计
        if 'by_prompt_category' in stats:
            print(f"\n按Prompt长度类别统计:")
            for category, cat_stats in stats['by_prompt_category'].items():
                print(f"  {category.upper()}:")
                print(f"    采样数: {cat_stats['count']}")
                print(f"    平均时延: {cat_stats['mean']:.2f} ms")
                print(f"    P99时延: {cat_stats['p99']:.2f} ms")
        
        print(f"{'='*70}")
    
    def run_comprehensive_test(self, sample_count: int = 100, max_workers: int = 5):
        """
        运行综合测试，测试所有模型的流式和非流式场景
        
        Args:
            sample_count: 每个模型每种场景的采样次数
            max_workers: 最大并发数
        """
        all_results = {}
        
        print("AWS Bedrock Claude模型首包时延综合测试")
        print(f"测试模型: {[self.CLAUDE_MODELS[m]['name'] for m in self.models_to_test]}")
        print(f"AWS区域: {self.region_name}")
        print(f"每模型每场景采样次数: {sample_count}")
        print(f"最大并发: {max_workers}")
        print(f"支持随机长度Prompt: 短/中/长三种类别")
        
        for model_key in self.models_to_test:
            model_name = self.CLAUDE_MODELS[model_key]['name']
            all_results[model_key] = {}
            
            print(f"\n开始测试模型: {model_name}")
            
            # 非流式测试
            print(f"\n[{model_name}] 开始非流式测试...")
            non_streaming_results = self.run_batch_test(
                model_key, 'non_streaming', sample_count, max_workers)
            non_streaming_stats = self.calculate_statistics(non_streaming_results)
            all_results[model_key]['non_streaming'] = {
                'results': non_streaming_results,
                'statistics': non_streaming_stats
            }
            self.print_statistics(non_streaming_stats, model_key, 'non_streaming')
            
            # 等待间隔
            time.sleep(2)
            
            # 流式测试
            print(f"\n[{model_name}] 开始流式测试...")
            streaming_results = self.run_batch_test(
                model_key, 'streaming', sample_count, max_workers)
            streaming_stats = self.calculate_statistics(streaming_results)
            all_results[model_key]['streaming'] = {
                'results': streaming_results,
                'statistics': streaming_stats
            }
            self.print_statistics(streaming_stats, model_key, 'streaming')
            
            # 等待间隔
            time.sleep(3)
        
        # 生成对比报告
        self.generate_comparison_report(all_results)
        
        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        result_file = f"bedrock_comprehensive_latency_test_{timestamp}.json"
        
        final_results = {
            'timestamp': timestamp,
            'region': self.region_name,
            'sample_count': sample_count,
            'models_tested': [self.CLAUDE_MODELS[m]['name'] for m in self.models_to_test],
            'results': all_results
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            # 由于results包含大量原始数据，我们只保存统计信息
            save_data = {
                'timestamp': timestamp,
                'region': self.region_name,
                'sample_count': sample_count,
                'models_tested': final_results['models_tested'],
                'statistics': {
                    model_key: {
                        'non_streaming': data['non_streaming']['statistics'],
                        'streaming': data['streaming']['statistics']
                    }
                    for model_key, data in all_results.items()
                }
            }
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n测试完成！详细结果已保存到: {result_file}")
        return all_results
    
    def generate_comparison_report(self, all_results: Dict):
        """生成模型对比报告"""
        print(f"\n{'='*80}")
        print("模型性能对比报告")
        print(f"{'='*80}")
        
        # 收集所有模型的关键指标
        comparison_data = []
        for model_key, results in all_results.items():
            model_name = self.CLAUDE_MODELS[model_key]['name']
            
            non_streaming_stats = results['non_streaming']['statistics']
            streaming_stats = results['streaming']['statistics']
            
            if non_streaming_stats and streaming_stats:
                comparison_data.append({
                    'model': model_name,
                    'non_streaming_mean': non_streaming_stats['mean'],
                    'non_streaming_p99': non_streaming_stats['p99'],
                    'streaming_mean': streaming_stats['mean'],
                    'streaming_p99': streaming_stats['p99'],
                    'streaming_advantage': non_streaming_stats['mean'] - streaming_stats['mean']
                })
        
        if not comparison_data:
            print("无足够数据生成对比报告")
            return
        
        # 按平均时延排序
        comparison_data.sort(key=lambda x: x['streaming_mean'])
        
        print(f"{'模型':<20} {'非流式均值':<12} {'流式均值':<12} {'非流式P99':<12} {'流式P99':<12} {'流式优势':<10}")
        print("-" * 80)
        
        for data in comparison_data:
            advantage_str = f"+{data['streaming_advantage']:.1f}ms" if data['streaming_advantage'] > 0 else f"{data['streaming_advantage']:.1f}ms"
            print(f"{data['model']:<20} {data['non_streaming_mean']:<12.2f} {data['streaming_mean']:<12.2f} "
                  f"{data['non_streaming_p99']:<12.2f} {data['streaming_p99']:<12.2f} {advantage_str:<10}")
        
        # 找出最佳表现者
        best_streaming = min(comparison_data, key=lambda x: x['streaming_mean'])
        best_non_streaming = min(comparison_data, key=lambda x: x['non_streaming_mean'])
        
        print(f"\n 性能冠军:")
        print(f"流式场景最快: {best_streaming['model']} (平均 {best_streaming['streaming_mean']:.2f}ms)")
        print(f"非流式场景最快: {best_non_streaming['model']} (平均 {best_non_streaming['non_streaming_mean']:.2f}ms)")
        
        print(f"{'='*80}")

def main():
    """主函数"""
    # 可以指定要测试的模型，如果不指定则测试所有支持的模型
    models_to_test = [
        'claude-3-haiku',
        'claude-3.5-haiku', 
        'claude-3-sonnet',
        'claude-3.5-sonnet'
        # 'claude-3-opus'  # Opus通常较慢且成本较高，可以选择性测试
    ]
    
    # 创建测试器
    tester = BedrockLatencyTester(
        region_name='us-west-2',  # 根据需要修改区域
        models=models_to_test
    )
    
    # 测试参数
    sample_count = 50  # 由于要测试多个模型，可以适当减少单模型采样数
    max_workers = 3   # 降低并发以避免限流
    
    # 运行综合测试
    tester.run_comprehensive_test(sample_count, max_workers)

if __name__ == "__main__":
    # 检查依赖
    try:
        import boto3
        import numpy as np
    except ImportError as e:
        print(f"缺少依赖包: {e}")
        print("请安装依赖: pip install boto3 numpy")
        exit(1)
    
    # 运行测试
    main()