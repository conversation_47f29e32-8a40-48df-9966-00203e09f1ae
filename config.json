{"database": {"host": "localhost", "port": 5432, "user": "default_user", "password": "default_password", "dbname": "mydb"}, "ethereum": {"rpc_url": "http://localhost:8545", "chain_id": 1, "contract_address": "0x...", "keystoredir": "/Users/<USER>/GolangProjects/worker/topainode.go/~/.topaiagent"}, "server": {"host": "localhost", "port": 8080, "env_mode": "development"}, "contractaddress": {"ai_models": "******************************************", "ai_workerload": "******************************************", "node_register": "******************************************", "nodes_governance": "******************************************"}, "keystoredir": "~/.topaiagent"}