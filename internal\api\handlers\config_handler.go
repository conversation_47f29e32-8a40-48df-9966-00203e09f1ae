package handlers

import (
	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

type ConfigHandler struct {
	service *service.Api
}

func NewConfigHandler(service *service.Api) *ConfigHandler {
	return &ConfigHandler{
		service: service,
	}
}

// GetAdminConfig 获取管理员配置
// @Summary 获取管理员配置
// @Description 获取系统管理员配置(需要管理员权限)
// @Tags auth
// @Accept json
// @Produce json
// @Success 200 {object} service.SystemCommonConfig
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/admin/config [get]
func (h *ConfigHandler) GetSystemCommonConfig(c *gin.Context) {
	config, err := h.service.Config.GetSystemCommonConfig(c.Request.Context())
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get config", err)
		return
	}
	api.Success(c, config)
}

// UpdateAdminConfig 更新管理员配置
// @Summary 更新管理员配置
// @Description 更新系统管理员配置(需要管理员权限)
// @Tags auth
// @Accept json
// @Produce json
// @Param config body service.SystemCommonConfig true "管理员配置"
// @Success 200 {object} service.SystemCommonConfig
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /auth/admin/config [post]
func (h *ConfigHandler) UpdateSystemCommonConfig(c *gin.Context) {
	req := &service.SystemCommonConfig{}
	if err := c.ShouldBindJSON(req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	if err := h.service.Config.UpdateSystemCommonConfig(c.Request.Context(), req); err != nil {
		api.Fail(c, api.CodeServerError, "failed to update config", err)
		return
	}
	api.Success(c, req)
}

func (h *ConfigHandler) Close() {
	// 清理资源
}
