// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// FileRepository is an autogenerated mock type for the FileRepository type
type FileRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, file
func (_m *FileRepository) Create(ctx context.Context, file *repository.File) error {
	ret := _m.Called(ctx, file)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.File) error); ok {
		r0 = rf(ctx, file)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, id
func (_m *FileRepository) Delete(ctx context.Context, id uint) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByUserIDAndUUIDs provides a mock function with given fields: ctx, userID, uuids
func (_m *FileRepository) FindByUserIDAndUUIDs(ctx context.Context, userID uint, uuids []string) ([]*repository.File, error) {
	ret := _m.Called(ctx, userID, uuids)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDAndUUIDs")
	}

	var r0 []*repository.File
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, []string) ([]*repository.File, error)); ok {
		return rf(ctx, userID, uuids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, []string) []*repository.File); ok {
		r0 = rf(ctx, userID, uuids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.File)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, []string) error); ok {
		r1 = rf(ctx, userID, uuids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *FileRepository) GetByID(ctx context.Context, id uint) (*repository.File, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.File
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.File, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.File); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.File)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUUID provides a mock function with given fields: ctx, uuid
func (_m *FileRepository) GetByUUID(ctx context.Context, uuid string) (*repository.File, error) {
	ret := _m.Called(ctx, uuid)

	if len(ret) == 0 {
		panic("no return value specified for GetByUUID")
	}

	var r0 *repository.File
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.File, error)); ok {
		return rf(ctx, uuid)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.File); ok {
		r0 = rf(ctx, uuid)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.File)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, uuid)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateContentById provides a mock function with given fields: ctx, id, content
func (_m *FileRepository) UpdateContentById(ctx context.Context, id uint, content string) error {
	ret := _m.Called(ctx, id, content)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContentById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) error); ok {
		r0 = rf(ctx, id, content)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewFileRepository creates a new instance of FileRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFileRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *FileRepository {
	mock := &FileRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
