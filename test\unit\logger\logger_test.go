package logger_test

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/logger"
)

// TestEnv 测试环境结构
type TestEnv struct {
	t       *testing.T
	tempDir string
	logFile string
	config  *config.LogConfig
	logger  *logger.ModuleLogger
}

// NewTestEnv 创建新的测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	logConfig := &config.LogConfig{
		Level:      "debug",
		Path:       logFile,
		MaxSize:    1,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
		Console:    true,
	}

	logger.InitLogger(logConfig)

	return &TestEnv{
		t:       t,
		tempDir: tempDir,
		logFile: logFile,
		config:  logConfig,
		logger:  logger.GetLogger("test"),
	}
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	os.RemoveAll(e.tempDir)
}

func TestLoggerInitialization(t *testing.T) {
	tests := []struct {
		name     string
		setup    func(*testing.T) *TestEnv
		wantErr  bool
		validate func(*testing.T, *TestEnv)
	}{
		{
			name: "有效配置初始化",
			setup: func(t *testing.T) *TestEnv {
				return NewTestEnv(t)
			},
			wantErr: false,
			validate: func(t *testing.T, env *TestEnv) {
				assert.NotNil(t, env.logger)
				assert.FileExists(t, env.logFile)
			},
		},
		{
			name: "无效的日志级别",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				env.config.Level = "invalid_level"
				return env
			},
			wantErr: true,
		},
		{
			name: "无效的日志路径",
			setup: func(t *testing.T) *TestEnv {
				env := NewTestEnv(t)
				env.config.Path = "/invalid/path/test.log"
				return env
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := tt.setup(t)
			defer env.Cleanup()

			if tt.wantErr {
				assert.Panics(t, func() {
					logger.InitLogger(env.config)
				})
			} else {
				assert.NotPanics(t, func() {
					logger.InitLogger(env.config)
				})
				if tt.validate != nil {
					tt.validate(t, env)
				}
			}
		})
	}
}

func TestLoggerLevels(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	tests := []struct {
		name     string
		level    string
		message  string
		fields   []zap.Field
		validate func(*testing.T, string)
	}{
		{
			name:    "Debug级别日志",
			level:   "debug",
			message: "debug message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "debug message")
				assert.Contains(t, content, "key")
				assert.Contains(t, content, "value")
			},
		},
		{
			name:    "Info级别日志",
			level:   "info",
			message: "info message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "info message")
			},
		},
		{
			name:    "Warn级别日志",
			level:   "warn",
			message: "warn message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "warn message")
			},
		},
		{
			name:    "Error级别日志",
			level:   "error",
			message: "error message",
			fields: []zap.Field{
				zap.String("key", "value"),
			},
			validate: func(t *testing.T, content string) {
				assert.Contains(t, content, "error message")
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// 设置日志级别
			env.config.Level = tt.level
			logger.InitLogger(env.config)
			log := logger.GetLogger("test")

			// 写入日志
			switch tt.level {
			case "debug":
				log.Debug(tt.message, tt.fields...)
			case "info":
				log.Info(tt.message, tt.fields...)
			case "warn":
				log.Warn(tt.message, tt.fields...)
			case "error":
				log.Error(tt.message, tt.fields...)
			}

			// 等待日志写入
			time.Sleep(100 * time.Millisecond)

			// 读取日志文件
			content, err := os.ReadFile(env.logFile)
			require.NoError(t, err)

			// 验证日志内容
			if tt.validate != nil {
				tt.validate(t, string(content))
			}
		})
	}
}

func TestLoggerRotation(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 设置较小的文件大小以触发轮转
	env.config.MaxSize = 1 // 1MB
	logger.InitLogger(env.config)
	log := logger.GetLogger("test")

	// 写入大量日志以触发轮转
	for i := 0; i < 1000; i++ {
		log.Info("test message", zap.Int("index", i))
	}

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 检查是否生成了备份文件
	files, err := filepath.Glob(filepath.Join(env.tempDir, "test.log*"))
	require.NoError(t, err)
	assert.Greater(t, len(files), 1, "应该生成了多个日志文件")
}

func TestLoggerWithFields(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	log := logger.GetLogger("test")
	fields := []zap.Field{
		zap.String("string_field", "value"),
		zap.Int("int_field", 123),
		zap.Bool("bool_field", true),
		zap.Time("time_field", time.Now()),
	}

	log.Info("test message", fields...)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证字段是否正确记录
	assert.Contains(t, string(content), "string_field")
	assert.Contains(t, string(content), "value")
	assert.Contains(t, string(content), "int_field")
	assert.Contains(t, string(content), "123")
	assert.Contains(t, string(content), "bool_field")
	assert.Contains(t, string(content), "true")
}

func TestLoggerWithContext(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	log := logger.GetLogger("test")
	ctx := []zap.Field{
		zap.String("request_id", "12345"),
		zap.String("user_id", "67890"),
	}

	log.WithContext(ctx...).Info("test message")

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证上下文字段是否正确记录
	assert.Contains(t, string(content), "request_id")
	assert.Contains(t, string(content), "12345")
	assert.Contains(t, string(content), "user_id")
	assert.Contains(t, string(content), "67890")
}

func TestLoggerWithSensitiveData(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	log := logger.GetLogger("test")
	fields := []zap.Field{
		zap.String("password", "secret123"),
		zap.String("token", "sensitive_token"),
		zap.String("normal", "visible"),
	}

	// 添加敏感字段
	log.AddSensitiveField("password")
	log.AddSensitiveField("token")
	log.Info("test message", fields...)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 读取日志文件
	content, err := os.ReadFile(env.logFile)
	require.NoError(t, err)

	// 验证敏感数据是否被屏蔽
	assert.Contains(t, string(content), "password")
	assert.NotContains(t, string(content), "secret123")
	assert.Contains(t, string(content), "token")
	assert.NotContains(t, string(content), "sensitive_token")
	assert.Contains(t, string(content), "normal")
	assert.Contains(t, string(content), "visible")
}
