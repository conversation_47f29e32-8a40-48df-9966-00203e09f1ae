package testutil

import (
	"context"
	"testing"

	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// MockDependencies Mock依赖
type MockDependencies struct {
	DB     *MockDB     `reset:"always"`
	Logger *MockLogger `reset:"always"`
	Config *MockConfig `reset:"always"`
}

func NewMockDependencies() *MockDependencies {
	return &MockDependencies{
		DB:     NewMockDB(),
		Logger: NewMockLogger(),
		Config: NewMockConfig(),
	}
}

func (m *MockDependencies) Reset() {
	m.DB.Reset()
	m.Logger.Reset()
	m.Config.Reset()
}

func (m *MockDependencies) Cleanup() {
	m.Reset()
}

func (m *MockDependencies) AssertExpectations(t *testing.T) {
	m.DB.AssertExpectations(t)
	m.Logger.AssertExpectations(t)
	m.Config.AssertExpectations(t)
}

// MockDB Mock数据库
type MockDB struct {
	// 用户基础信息
	User        *MockUser        `reset:"always"`
	UserToken   *MockUserToken   `reset:"always"`
	UserSetting *MockUserSetting `reset:"always"`
	// 用户余额
	UserBalance                   *MockUserBalance                   `reset:"always"`
	UserBalanceRecord             *MockUserBalanceRecord             `reset:"always"`
	UserBalanceSnapshot           *MockUserBalanceSnapshot           `reset:"always"`
	UserRechargeRecord            *MockUserRechargeRecord            `reset:"always"`
	UserShadowWallet              *MockUserShadowWallet              `reset:"always"`
	UserShadowWalletBalance       *MockUserShadowWalletBalance       `reset:"always"`
	UserShadowWalletBalanceRecord *MockUserShadowWalletBalanceRecord `reset:"always"`
	WalletTransferRecord          *MockWalletTransferRecord          `reset:"always"`
	WalletReportCostRecord        *MockWalletReportCostRecord        `reset:"always"`
	WalletDepositRecord           *MockWalletDepositRecord           `reset:"always"`
	// 对话相关
	Conversation        *MockConversation        `reset:"always"`
	ConversationMessage *MockConversationMessage `reset:"always"`
	UserChatASRRecord   *MockUserChatASRRecord   `reset:"always"`
	UserChatTTSRecord   *MockUserChatTTSRecord   `reset:"always"`
	UserChatTTIRecord   *MockUserChatTTIRecord   `reset:"always"`
	// 文件
	File *MockFile `reset:"always"`
	// 系统配置
	SystemConfig *MockSystemConfig `reset:"always"`
	// 模型相关
	LLMModelConfig             *MockLLMModelConfig             `reset:"always"`
	RemoterModelInfo           *MockRemoterModelInfo           `reset:"always"`
	TTSModelVoice              *MockTTSModelVoice              `reset:"always"`
	TopaiModel                 *MockTopaiModel                 `reset:"always"`
	TopaiModelProvider         *MockTopaiModelProvider         `reset:"always"`
	TopaiModelCategory         *MockTopaiModelCategory         `reset:"always"`
	TopaiModelCategoryRelation *MockTopaiModelCategoryRelation `reset:"always"`
	TopaiModelSeries           *MockTopaiModelSeries           `reset:"always"`
	TopaiModelSeriesRelation   *MockTopaiModelSeriesRelation   `reset:"always"`
	TopaiModelExt              *MockTopaiModelExt              `reset:"always"`
	TopaiModelSupportParam     *MockTopaiModelSupportParam     `reset:"always"`
	TopaiModelCost             *MockTopaiModelCost             `reset:"always"`
	UserUsageDayRecord         *MockUserUsageDayRecord         `reset:"always"`
	// 用户应用
	DevAppInfo      *MockDevAppInfo      `reset:"always"`
	DevAppKey       *MockDevAppKey       `reset:"always"`
	UserChatRequest *MockUserChatRequest `reset:"always"`
	UserChatUsage   *MockUserChatUsage   `reset:"always"`
	// 模型提供者
	ProviderToken *MockProviderToken `reset:"always"`
}

func NewMockDB() *MockDB {
	return &MockDB{
		// 用户基础信息
		User:        NewMockUser(),
		UserToken:   NewMockUserToken(),
		UserSetting: NewMockUserSetting(),
		// 用户余额
		UserBalance:                   NewMockUserBalance(),
		UserBalanceRecord:             NewMockUserBalanceRecord(),
		UserBalanceSnapshot:           NewMockUserBalanceSnapshot(),
		UserRechargeRecord:            NewMockUserRechargeRecord(),
		UserShadowWallet:              NewMockUserShadowWallet(),
		UserShadowWalletBalance:       NewMockUserShadowWalletBalance(),
		UserShadowWalletBalanceRecord: NewMockUserShadowWalletBalanceRecord(),
		WalletTransferRecord:          NewMockWalletTransferRecord(),
		WalletReportCostRecord:        NewMockWalletReportCostRecord(),
		WalletDepositRecord:           NewMockWalletDepositRecord(),
		// 对话相关
		Conversation:        NewMockConversation(),
		ConversationMessage: NewMockConversationMessage(),
		UserChatASRRecord:   NewMockUserChatASRRecord(),
		UserChatTTSRecord:   NewMockUserChatTTSRecord(),
		UserChatTTIRecord:   NewMockUserChatTTIRecord(),
		// 文件
		File: NewMockFile(),
		// 系统配置
		SystemConfig: NewMockSystemConfig(),
		// 模型相关
		LLMModelConfig:             NewMockLLMModelConfig(),
		RemoterModelInfo:           NewMockRemoterModelInfo(),
		TTSModelVoice:              NewMockTTSModelVoice(),
		TopaiModel:                 NewMockTopaiModel(),
		TopaiModelProvider:         NewMockTopaiModelProvider(),
		TopaiModelCategory:         NewMockTopaiModelCategory(),
		TopaiModelCategoryRelation: NewMockTopaiModelCategoryRelation(),
		TopaiModelSeries:           NewMockTopaiModelSeries(),
		TopaiModelSeriesRelation:   NewMockTopaiModelSeriesRelation(),
		TopaiModelExt:              NewMockTopaiModelExt(),
		TopaiModelSupportParam:     NewMockTopaiModelSupportParam(),
		TopaiModelCost:             NewMockTopaiModelCost(),
		UserUsageDayRecord:         NewMockUserUsageDayRecord(),
		// 用户应用
		DevAppInfo:      NewMockDevAppInfo(),
		DevAppKey:       NewMockDevAppKey(),
		UserChatRequest: NewMockUserChatRequest(),
		UserChatUsage:   NewMockUserChatUsage(),
		// 模型提供者
		ProviderToken: NewMockProviderToken(),
	}
}

func (m *MockDB) Reset() {
	// 用户基础信息
	m.User.Reset()
	m.UserToken.Reset()
	m.UserSetting.Reset()
	// 用户余额
	m.UserBalance.Reset()
	m.UserBalanceRecord.Reset()
	m.UserBalanceSnapshot.Reset()
	m.UserRechargeRecord.Reset()
	m.UserShadowWallet.Reset()
	m.UserShadowWalletBalance.Reset()
	m.UserShadowWalletBalanceRecord.Reset()
	m.WalletTransferRecord.Reset()
	m.WalletReportCostRecord.Reset()
	m.WalletDepositRecord.Reset()
	// 对话相关
	m.Conversation.Reset()
	m.ConversationMessage.Reset()
	m.UserChatASRRecord.Reset()
	m.UserChatTTSRecord.Reset()
	m.UserChatTTIRecord.Reset()
	// 文件
	m.File.Reset()
	// 系统配置
	m.SystemConfig.Reset()
	// 模型相关
	m.LLMModelConfig.Reset()
	m.RemoterModelInfo.Reset()
	m.TTSModelVoice.Reset()
	m.TopaiModel.Reset()
	m.TopaiModelProvider.Reset()
	m.TopaiModelCategory.Reset()
	m.TopaiModelCategoryRelation.Reset()
	m.TopaiModelSeries.Reset()
	m.TopaiModelSeriesRelation.Reset()
	m.TopaiModelExt.Reset()
	m.TopaiModelSupportParam.Reset()
	m.TopaiModelCost.Reset()
	m.UserUsageDayRecord.Reset()
	// 用户应用
	m.DevAppInfo.Reset()
	m.DevAppKey.Reset()
	m.UserChatRequest.Reset()
	m.UserChatUsage.Reset()
	// 模型提供者
	m.ProviderToken.Reset()
}

func (m *MockDB) AssertExpectations(t *testing.T) {
	// 用户基础信息
	m.User.AssertExpectations(t)
	m.UserToken.AssertExpectations(t)
	m.UserSetting.AssertExpectations(t)
	// 用户余额
	m.UserBalance.AssertExpectations(t)
	m.UserBalanceRecord.AssertExpectations(t)
	m.UserBalanceSnapshot.AssertExpectations(t)
	m.UserRechargeRecord.AssertExpectations(t)
	m.UserShadowWallet.AssertExpectations(t)
	m.UserShadowWalletBalance.AssertExpectations(t)
	m.UserShadowWalletBalanceRecord.AssertExpectations(t)
	m.WalletTransferRecord.AssertExpectations(t)
	m.WalletReportCostRecord.AssertExpectations(t)
	m.WalletDepositRecord.AssertExpectations(t)
	// 对话相关
	m.Conversation.AssertExpectations(t)
	m.ConversationMessage.AssertExpectations(t)
	m.UserChatASRRecord.AssertExpectations(t)
	m.UserChatTTSRecord.AssertExpectations(t)
	m.UserChatTTIRecord.AssertExpectations(t)
	// 文件
	m.File.AssertExpectations(t)
	// 系统配置
	m.SystemConfig.AssertExpectations(t)
	// 模型相关
	m.LLMModelConfig.AssertExpectations(t)
	m.RemoterModelInfo.AssertExpectations(t)
	m.TTSModelVoice.AssertExpectations(t)
	m.TopaiModel.AssertExpectations(t)
	m.TopaiModelProvider.AssertExpectations(t)
	m.TopaiModelCategory.AssertExpectations(t)
	m.TopaiModelCategoryRelation.AssertExpectations(t)
	m.TopaiModelSeries.AssertExpectations(t)
	m.TopaiModelSeriesRelation.AssertExpectations(t)
	m.TopaiModelExt.AssertExpectations(t)
	m.TopaiModelSupportParam.AssertExpectations(t)
	m.TopaiModelCost.AssertExpectations(t)
	m.UserUsageDayRecord.AssertExpectations(t)
	// 用户应用
	m.DevAppInfo.AssertExpectations(t)
	m.DevAppKey.AssertExpectations(t)
	m.UserChatRequest.AssertExpectations(t)
	m.UserChatUsage.AssertExpectations(t)
	// 模型提供者
	m.ProviderToken.AssertExpectations(t)
}

// RunWithTx Mock事务处理
func (m *MockDB) RunWithTx(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
	// 对于mock来说，我们只需要调用原始函数，不需要实际的事务
	return fn(ctx, &repository.DB{
		// 用户基础信息
		User:        m.User,
		UserToken:   m.UserToken,
		UserSetting: m.UserSetting,
		// 用户余额
		UserBalance:                   m.UserBalance,
		UserBalanceRecord:             m.UserBalanceRecord,
		UserBalanceSnapshot:           m.UserBalanceSnapshot,
		UserRechargeRecord:            m.UserRechargeRecord,
		UserShadowWallet:              m.UserShadowWallet,
		UserShadowWalletBalance:       m.UserShadowWalletBalance,
		UserShadowWalletBalanceRecord: m.UserShadowWalletBalanceRecord,
		WalletTransferRecord:          m.WalletTransferRecord,
		WalletReportCostRecord:        m.WalletReportCostRecord,
		WalletDepositRecord:           m.WalletDepositRecord,
		// 对话相关
		Conversation:        m.Conversation,
		ConversationMessage: m.ConversationMessage,
		UserChatASRRecord:   m.UserChatASRRecord,
		UserChatTTSRecord:   m.UserChatTTSRecord,
		UserChatTTIRecord:   m.UserChatTTIRecord,
		// 文件
		File: m.File,
		// 系统配置
		SystemConfig: m.SystemConfig,
		// 模型相关
		LLMModelConfig:             m.LLMModelConfig,
		RemoterModelInfo:           m.RemoterModelInfo,
		TTSModelVoice:              m.TTSModelVoice,
		TopaiModel:                 m.TopaiModel,
		TopaiModelProvider:         m.TopaiModelProvider,
		TopaiModelCategory:         m.TopaiModelCategory,
		TopaiModelCategoryRelation: m.TopaiModelCategoryRelation,
		TopaiModelSeries:           m.TopaiModelSeries,
		TopaiModelSeriesRelation:   m.TopaiModelSeriesRelation,
		TopaiModelExt:              m.TopaiModelExt,
		TopaiModelSupportParam:     m.TopaiModelSupportParam,
		TopaiModelCost:             m.TopaiModelCost,
		UserUsageDayRecord:         m.UserUsageDayRecord,
		// 用户应用
		DevAppInfo:      m.DevAppInfo,
		DevAppKey:       m.DevAppKey,
		UserChatRequest: m.UserChatRequest,
		UserChatUsage:   m.UserChatUsage,
	})
}

// MockUser Mock用户接口
type MockUser struct {
	mock.Mock
}

func NewMockUser() *MockUser {
	return &MockUser{}
}

func (m *MockUser) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUser) GetByEmail(ctx context.Context, email string) (*repository.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

func (m *MockUser) GetByID(ctx context.Context, id uint) (*repository.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

func (m *MockUser) GetByIdentityID(ctx context.Context, identityID string) (*repository.User, error) {
	args := m.Called(ctx, identityID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

func (m *MockUser) Create(ctx context.Context, user *repository.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUser) Update(ctx context.Context, user *repository.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUser) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUser) GetAllUsers(ctx context.Context, limit int, offset int, orderBy string, orderDir string) ([]*repository.User, error) {
	args := m.Called(ctx, limit, offset, orderBy, orderDir)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.User), args.Error(1)
}

func (m *MockUser) List(ctx context.Context, offset, limit int, order, direction string) ([]*repository.User, error) {
	args := m.Called(ctx, offset, limit, order, direction)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.User), args.Error(1)
}

func (m *MockUser) GetAllUsersCount(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUser) UpdatePassword(ctx context.Context, id uint, passwordHash string) error {
	args := m.Called(ctx, id, passwordHash)
	return args.Error(0)
}

func (m *MockUser) UpdateProfileImageURL(ctx context.Context, id uint, url string) error {
	args := m.Called(ctx, id, url)
	return args.Error(0)
}

func (m *MockUser) UpdateRole(ctx context.Context, id uint, role repository.UserRole) error {
	args := m.Called(ctx, id, role)
	return args.Error(0)
}

func (m *MockUser) Count(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUser) GetAll(ctx context.Context) ([]*repository.User, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.User), args.Error(1)
}

// MockUserToken Mock用户Token接口
type MockUserToken struct {
	mock.Mock
}

func NewMockUserToken() *MockUserToken {
	return &MockUserToken{}
}

func (m *MockUserToken) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserToken) Create(ctx context.Context, token *repository.UserToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockUserToken) GetByToken(ctx context.Context, token string) (*repository.UserToken, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserToken), args.Error(1)
}

func (m *MockUserToken) DeleteByUserID(ctx context.Context, userID uint) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserToken) DeleteByToken(ctx context.Context, token string) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockUserToken) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MockSystemConfig Mock系统配置接口
type MockSystemConfig struct {
	mock.Mock
}

func NewMockSystemConfig() *MockSystemConfig {
	return &MockSystemConfig{}
}

func (m *MockSystemConfig) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockSystemConfig) Get(ctx context.Context, category, key string) (*repository.SystemConfig, error) {
	args := m.Called(ctx, category, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.SystemConfig), args.Error(1)
}

func (m *MockSystemConfig) UpdateByCategoryAndKey(ctx context.Context, category, key, value string) error {
	args := m.Called(ctx, category, key, value)
	return args.Error(0)
}

func (m *MockSystemConfig) GetByCategory(ctx context.Context, category string) ([]*repository.SystemConfig, error) {
	args := m.Called(ctx, category)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.SystemConfig), args.Error(1)
}

func (m *MockSystemConfig) Create(ctx context.Context, config *repository.SystemConfig) error {
	args := m.Called(ctx, config)
	return args.Error(0)
}

func (m *MockSystemConfig) GetByCategoryAndKey(ctx context.Context, category, key string) (*repository.SystemConfig, error) {
	args := m.Called(ctx, category, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.SystemConfig), args.Error(1)
}

// MockUserSetting Mock用户设置接口
type MockUserSetting struct {
	mock.Mock
}

func NewMockUserSetting() *MockUserSetting {
	return &MockUserSetting{}
}

func (m *MockUserSetting) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserSetting) GetByUserID(ctx context.Context, userID uint) (*repository.UserSetting, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserSetting), args.Error(1)
}

func (m *MockUserSetting) CreateOrUpdate(ctx context.Context, setting *repository.UserSetting) error {
	args := m.Called(ctx, setting)
	return args.Error(0)
}

func (m *MockUserSetting) Create(ctx context.Context, setting *repository.UserSetting) error {
	args := m.Called(ctx, setting)
	return args.Error(0)
}

func (m *MockUserSetting) FindByUserID(ctx context.Context, userID uint) ([]*repository.UserSetting, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserSetting), args.Error(1)
}

func (m *MockUserSetting) GetByUserIDAndKey(ctx context.Context, userID uint, key string) (*repository.UserSetting, error) {
	args := m.Called(ctx, userID, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserSetting), args.Error(1)
}

func (m *MockUserSetting) Update(ctx context.Context, setting *repository.UserSetting) error {
	args := m.Called(ctx, setting)
	return args.Error(0)
}

// MockUserChatRequest Mock用户聊天请求接口
type MockUserChatRequest struct {
	mock.Mock
}

func NewMockUserChatRequest() *MockUserChatRequest {
	return &MockUserChatRequest{}
}

func (m *MockUserChatRequest) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserChatRequest) Create(ctx context.Context, request *repository.UserChatRequest) error {
	args := m.Called(ctx, request)
	return args.Error(0)
}

func (m *MockUserChatRequest) GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*repository.UserChatRequest, error) {
	args := m.Called(ctx, start, end)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatRequest), args.Error(1)
}

// MockRemoterModelInfo Mock远程模型信息接口
type MockRemoterModelInfo struct {
	mock.Mock
}

func NewMockRemoterModelInfo() *MockRemoterModelInfo {
	return &MockRemoterModelInfo{}
}

func (m *MockRemoterModelInfo) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockRemoterModelInfo) GetAll(ctx context.Context) ([]*repository.RemoterModelInfo, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.RemoterModelInfo), args.Error(1)
}

func (m *MockRemoterModelInfo) GetByModelName(ctx context.Context, modelName string) (*repository.RemoterModelInfo, error) {
	args := m.Called(ctx, modelName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.RemoterModelInfo), args.Error(1)
}

// MockConversation Mock对话接口
type MockConversation struct {
	mock.Mock
}

func NewMockConversation() *MockConversation {
	return &MockConversation{}
}

func (m *MockConversation) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockConversation) FindByUserIDOrderByUpdatedAtDesc(ctx context.Context, userID uint, limit int, offset int) ([]*repository.Conversation, error) {
	args := m.Called(ctx, userID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.Conversation), args.Error(1)
}

func (m *MockConversation) GetByID(ctx context.Context, id uint) (*repository.Conversation, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.Conversation), args.Error(1)
}

func (m *MockConversation) GetByUUID(ctx context.Context, uuid string) (*repository.Conversation, error) {
	args := m.Called(ctx, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.Conversation), args.Error(1)
}

func (m *MockConversation) Create(ctx context.Context, conversation *repository.Conversation) error {
	args := m.Called(ctx, conversation)
	return args.Error(0)
}

func (m *MockConversation) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockConversation) UpdateTitleByUUID(ctx context.Context, uuid string, title string) error {
	args := m.Called(ctx, uuid, title)
	return args.Error(0)
}

func (m *MockConversation) UpdateCurrentMsgUUIDByUUID(ctx context.Context, uuid string, currentMsgUUID string) error {
	args := m.Called(ctx, uuid, currentMsgUUID)
	return args.Error(0)
}

// MockConversationMessage Mock对话消息接口
type MockConversationMessage struct {
	mock.Mock
}

func NewMockConversationMessage() *MockConversationMessage {
	return &MockConversationMessage{}
}

func (m *MockConversationMessage) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockConversationMessage) GetByID(ctx context.Context, id uint) (*repository.ConversationMessage, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.ConversationMessage), args.Error(1)
}

func (m *MockConversationMessage) GetByConversationID(ctx context.Context, conversationID uint) ([]*repository.ConversationMessage, error) {
	args := m.Called(ctx, conversationID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.ConversationMessage), args.Error(1)
}

func (m *MockConversationMessage) GetByConversationIDAndUUID(ctx context.Context, conversationID uint, uuid string) (*repository.ConversationMessage, error) {
	args := m.Called(ctx, conversationID, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.ConversationMessage), args.Error(1)
}

func (m *MockConversationMessage) UpdateContentAndDoneById(ctx context.Context, id uint, content, fileUUIDs string) error {
	args := m.Called(ctx, id, content, fileUUIDs)
	return args.Error(0)
}

func (m *MockConversationMessage) Create(ctx context.Context, message *repository.ConversationMessage) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockConversationMessage) Update(ctx context.Context, message *repository.ConversationMessage) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

// MockUserChatTTIRecord Mock用户聊天TTI记录接口
type MockUserChatTTIRecord struct {
	mock.Mock
}

func NewMockUserChatTTIRecord() *MockUserChatTTIRecord {
	return &MockUserChatTTIRecord{}
}

func (m *MockUserChatTTIRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserChatTTIRecord) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatTTIRecord, error) {
	args := m.Called(ctx, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserChatTTIRecord), args.Error(1)
}

func (m *MockUserChatTTIRecord) UpdateShareByUUID(ctx context.Context, uuid string, isShared bool) error {
	args := m.Called(ctx, uuid, isShared)
	return args.Error(0)
}

func (m *MockUserChatTTIRecord) GetUserTTISharedList(ctx context.Context, userID uint, limit int, offset int) ([]*repository.UserChatTTIRecord, error) {
	args := m.Called(ctx, userID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatTTIRecord), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetUserTTISharedCount(ctx context.Context, userID uint) (int64, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetAllTTISharedList(ctx context.Context, limit int, offset int) ([]*repository.UserChatTTIRecord, error) {
	args := m.Called(ctx, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatTTIRecord), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetAllTTISharedCount(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserChatTTIRecord) Create(ctx context.Context, record *repository.UserChatTTIRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockUserChatTTIRecord) UpdateIsSharedByUUID(ctx context.Context, uuid string, isShared uint8) error {
	args := m.Called(ctx, uuid, isShared)
	return args.Error(0)
}

func (m *MockUserChatTTIRecord) GetSharedByUserID(ctx context.Context, userID uint, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	args := m.Called(ctx, userID, limit, offset, order)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatTTIRecord), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetSharedCountByUserID(ctx context.Context, userID uint) (int64, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetSharedCount(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserChatTTIRecord) GetShared(ctx context.Context, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	args := m.Called(ctx, limit, offset, order)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatTTIRecord), args.Error(1)
}

// MockFile Mock文件接口
type MockFile struct {
	mock.Mock
}

func NewMockFile() *MockFile {
	return &MockFile{}
}

func (m *MockFile) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockFile) FindByUserIDAndUUIDs(ctx context.Context, userID uint, uuids []string) ([]*repository.File, error) {
	args := m.Called(ctx, userID, uuids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.File), args.Error(1)
}

func (m *MockFile) GetByUUID(ctx context.Context, uuid string) (*repository.File, error) {
	args := m.Called(ctx, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.File), args.Error(1)
}

func (m *MockFile) GetByID(ctx context.Context, id uint) (*repository.File, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.File), args.Error(1)
}

func (m *MockFile) Create(ctx context.Context, file *repository.File) error {
	args := m.Called(ctx, file)
	return args.Error(0)
}

func (m *MockFile) Update(ctx context.Context, file *repository.File) error {
	args := m.Called(ctx, file)
	return args.Error(0)
}

func (m *MockFile) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockFile) UpdateContentById(ctx context.Context, id uint, content string) error {
	args := m.Called(ctx, id, content)
	return args.Error(0)
}

// MockUserBalance Mock用户余额接口
type MockUserBalance struct {
	mock.Mock
}

func NewMockUserBalance() *MockUserBalance {
	return &MockUserBalance{}
}

func (m *MockUserBalance) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserBalance) GetByUserID(ctx context.Context, userID uint) (*repository.UserBalance, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserBalance), args.Error(1)
}

func (m *MockUserBalance) Create(ctx context.Context, balance *repository.UserBalance) error {
	args := m.Called(ctx, balance)
	return args.Error(0)
}

func (m *MockUserBalance) Update(ctx context.Context, balance *repository.UserBalance) error {
	args := m.Called(ctx, balance)
	return args.Error(0)
}

func (m *MockUserBalance) UpdateBalance(ctx context.Context, userID uint, balance string) error {
	args := m.Called(ctx, userID, balance)
	return args.Error(0)
}

func (m *MockUserBalance) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserBalance, error) {
	args := m.Called(ctx, userID, currency)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserBalance), args.Error(1)
}

func (m *MockUserBalance) UpdateBalanceByID(ctx context.Context, id uint, balance string, accumulated string) error {
	args := m.Called(ctx, id, balance, accumulated)
	return args.Error(0)
}

// MockUserBalanceRecord Mock用户余额记录接口
type MockUserBalanceRecord struct {
	mock.Mock
}

func NewMockUserBalanceRecord() *MockUserBalanceRecord {
	return &MockUserBalanceRecord{}
}

func (m *MockUserBalanceRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserBalanceRecord) Create(ctx context.Context, record *repository.UserBalanceRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockUserBalanceRecord) GetByUserID(ctx context.Context, userID uint, limit int, offset int) ([]*repository.UserBalanceRecord, error) {
	args := m.Called(ctx, userID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserBalanceRecord), args.Error(1)
}

func (m *MockUserBalanceRecord) BatchCreate(ctx context.Context, records []*repository.UserBalanceRecord) error {
	args := m.Called(ctx, records)
	return args.Error(0)
}

func (m *MockUserBalanceRecord) CountByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (int64, error) {
	args := m.Called(ctx, userID, currency)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserBalanceRecord) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string, offset, pageSize int, orderBy, orderDirection string) ([]*repository.UserBalanceRecord, error) {
	args := m.Called(ctx, userID, currency, offset, pageSize, orderBy, orderDirection)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserBalanceRecord), args.Error(1)
}

// MockRepository Mock仓库接口
type MockRepository struct {
	mock.Mock
}

func NewMockRepository() *MockRepository {
	return &MockRepository{}
}

func (m *MockRepository) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockRepository) GetUser(ctx context.Context, id uint) (*repository.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

func (m *MockRepository) CreateUser(ctx context.Context, user *repository.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockRepository) UpdateUser(ctx context.Context, user *repository.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockRepository) DeleteUser(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRepository) GetUserByEmail(ctx context.Context, email string) (*repository.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

func (m *MockRepository) GetUserByIdentityID(ctx context.Context, identityID string) (*repository.User, error) {
	args := m.Called(ctx, identityID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.User), args.Error(1)
}

// MockLogger Mock日志接口
type MockLogger struct {
	mock.Mock
}

func NewMockLogger() *MockLogger {
	return &MockLogger{}
}

func (m *MockLogger) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockLogger) Info(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Debug(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

// MockConfig Mock配置接口
type MockConfig struct {
	mock.Mock
}

func NewMockConfig() *MockConfig {
	return &MockConfig{}
}

func (m *MockConfig) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockConfig) GetString(key string) string {
	args := m.Called(key)
	return args.String(0)
}

func (m *MockConfig) GetInt(key string) int {
	args := m.Called(key)
	return args.Int(0)
}

func (m *MockConfig) GetBool(key string) bool {
	args := m.Called(key)
	return args.Bool(0)
}

// ========== 用户余额相关 Mock ==========

// MockUserBalanceSnapshot Mock用户余额快照接口
type MockUserBalanceSnapshot struct {
	mock.Mock
}

func NewMockUserBalanceSnapshot() *MockUserBalanceSnapshot {
	return &MockUserBalanceSnapshot{}
}

func (m *MockUserBalanceSnapshot) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserBalanceSnapshot) Create(ctx context.Context, snapshot *repository.UserBalanceSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

var _ repository.UserBalanceSnapshotRepository = (*MockUserBalanceSnapshot)(nil)

// MockUserRechargeRecord Mock用户充值记录接口
type MockUserRechargeRecord struct {
	mock.Mock
}

func NewMockUserRechargeRecord() *MockUserRechargeRecord {
	return &MockUserRechargeRecord{}
}

func (m *MockUserRechargeRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserRechargeRecord) Create(ctx context.Context, record *repository.UserRechargeRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockUserRechargeRecord) GetUnconfirmed(ctx context.Context) ([]*repository.UserRechargeRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserRechargeRecord), args.Error(1)
}

func (m *MockUserRechargeRecord) UpdateConfirmedByID(ctx context.Context, id uint, userBalanceRecordId uint) error {
	args := m.Called(ctx, id, userBalanceRecordId)
	return args.Error(0)
}

func (m *MockUserRechargeRecord) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	args := m.Called(ctx, id, blockNumber)
	return args.Error(0)
}

var _ repository.UserRechargeRecordRepository = (*MockUserRechargeRecord)(nil)

// MockUserShadowWallet Mock用户影子钱包接口
type MockUserShadowWallet struct {
	mock.Mock
}

func NewMockUserShadowWallet() *MockUserShadowWallet {
	return &MockUserShadowWallet{}
}

func (m *MockUserShadowWallet) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserShadowWallet) Create(ctx context.Context, wallet *repository.UserShadowWallet) error {
	args := m.Called(ctx, wallet)
	return args.Error(0)
}

func (m *MockUserShadowWallet) GetByUserID(ctx context.Context, userID uint) (*repository.UserShadowWallet, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserShadowWallet), args.Error(1)
}

func (m *MockUserShadowWallet) GetByPublicAddress(ctx context.Context, publicAddress string) (*repository.UserShadowWallet, error) {
	args := m.Called(ctx, publicAddress)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserShadowWallet), args.Error(1)
}

var _ repository.UserShadowWalletRepository = (*MockUserShadowWallet)(nil)

// MockUserShadowWalletBalance Mock用户影子钱包余额接口
type MockUserShadowWalletBalance struct {
	mock.Mock
}

func NewMockUserShadowWalletBalance() *MockUserShadowWalletBalance {
	return &MockUserShadowWalletBalance{}
}

func (m *MockUserShadowWalletBalance) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserShadowWalletBalance) Create(ctx context.Context, balance *repository.UserShadowWalletBalance) error {
	args := m.Called(ctx, balance)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalance) GetByUserIDAndCurrency(ctx context.Context, userID uint, currency string) (*repository.UserShadowWalletBalance, error) {
	args := m.Called(ctx, userID, currency)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserShadowWalletBalance), args.Error(1)
}

func (m *MockUserShadowWalletBalance) UpdateBalanceByUserIDAndCurrency(ctx context.Context, userID uint, currency string, balance, accumulated string, lastBlockNumber uint64) error {
	args := m.Called(ctx, userID, currency, balance, accumulated, lastBlockNumber)
	return args.Error(0)
}

var _ repository.UserShadowWalletBalanceRepository = (*MockUserShadowWalletBalance)(nil)

// MockUserShadowWalletBalanceRecord Mock用户影子钱包余额记录接口
type MockUserShadowWalletBalanceRecord struct {
	mock.Mock
}

func NewMockUserShadowWalletBalanceRecord() *MockUserShadowWalletBalanceRecord {
	return &MockUserShadowWalletBalanceRecord{}
}

func (m *MockUserShadowWalletBalanceRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserShadowWalletBalanceRecord) Create(ctx context.Context, record *repository.UserShadowWalletBalanceRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalanceRecord) BatchCreate(ctx context.Context, records []*repository.UserShadowWalletBalanceRecord) error {
	args := m.Called(ctx, records)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalanceRecord) GetUnconfirmed(ctx context.Context) ([]*repository.UserShadowWalletBalanceRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserShadowWalletBalanceRecord), args.Error(1)
}

func (m *MockUserShadowWalletBalanceRecord) UpdateConfirmedByID(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalanceRecord) UpdateConfirmedAmountByID(ctx context.Context, id uint, amount string) error {
	args := m.Called(ctx, id, amount)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalanceRecord) UpdateFailedByID(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserShadowWalletBalanceRecord) GetLastByCurrencys(ctx context.Context, currencies []string) (*repository.UserShadowWalletBalanceRecord, error) {
	args := m.Called(ctx, currencies)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserShadowWalletBalanceRecord), args.Error(1)
}

var _ repository.UserShadowWalletBalanceRecordRepository = (*MockUserShadowWalletBalanceRecord)(nil)

// MockWalletTransferRecord Mock钱包转账记录接口
type MockWalletTransferRecord struct {
	mock.Mock
}

func NewMockWalletTransferRecord() *MockWalletTransferRecord {
	return &MockWalletTransferRecord{}
}

func (m *MockWalletTransferRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockWalletTransferRecord) Create(ctx context.Context, record *repository.WalletTransferRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockWalletTransferRecord) GetUnconfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	// 仅当未为 GetUnconfirmed 明确设置期望时，返回默认空列表，保证测试隔离
	hasSpecificExpectation := false
	for _, c := range m.ExpectedCalls {
		if c.Method == "GetUnconfirmed" {
			hasSpecificExpectation = true
			break
		}
	}
	if !hasSpecificExpectation {
		return []*repository.WalletTransferRecord{}, nil
	}
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.WalletTransferRecord), args.Error(1)
}

func (m *MockWalletTransferRecord) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	args := m.Called(ctx, id, blockNumber, walletBalanceRecordId)
	return args.Error(0)
}

func (m *MockWalletTransferRecord) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	args := m.Called(ctx, id, blockNumber)
	return args.Error(0)
}

func (m *MockWalletTransferRecord) GetConfirmed(ctx context.Context) ([]*repository.WalletTransferRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.WalletTransferRecord), args.Error(1)
}

func (m *MockWalletTransferRecord) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletTransferRecord, error) {
	args := m.Called(ctx, txHash)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.WalletTransferRecord), args.Error(1)
}

var _ repository.WalletTransferRecordRepository = (*MockWalletTransferRecord)(nil)

// MockWalletReportCostRecord Mock钱包报告成本记录接口
type MockWalletReportCostRecord struct {
	mock.Mock
}

func NewMockWalletReportCostRecord() *MockWalletReportCostRecord {
	return &MockWalletReportCostRecord{}
}

func (m *MockWalletReportCostRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockWalletReportCostRecord) Create(ctx context.Context, record *repository.WalletReportCostRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockWalletReportCostRecord) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletReportCostRecord, error) {
	args := m.Called(ctx, txHash)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.WalletReportCostRecord), args.Error(1)
}

func (m *MockWalletReportCostRecord) GetUnconfirmed(ctx context.Context) ([]*repository.WalletReportCostRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.WalletReportCostRecord), args.Error(1)
}

func (m *MockWalletReportCostRecord) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	args := m.Called(ctx, id, blockNumber, walletBalanceRecordId)
	return args.Error(0)
}

func (m *MockWalletReportCostRecord) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	args := m.Called(ctx, id, blockNumber)
	return args.Error(0)
}

var _ repository.WalletReportCostRecordRepository = (*MockWalletReportCostRecord)(nil)

// MockWalletDepositRecord Mock钱包存款记录接口
type MockWalletDepositRecord struct {
	mock.Mock
}

func NewMockWalletDepositRecord() *MockWalletDepositRecord {
	return &MockWalletDepositRecord{}
}

func (m *MockWalletDepositRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockWalletDepositRecord) Create(ctx context.Context, record *repository.WalletDepositRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

func (m *MockWalletDepositRecord) GetUnconfirmed(ctx context.Context) ([]*repository.WalletDepositRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.WalletDepositRecord), args.Error(1)
}

func (m *MockWalletDepositRecord) UpdateConfirmedByID(ctx context.Context, id uint, blockNumber uint64, walletBalanceRecordId uint) error {
	args := m.Called(ctx, id, blockNumber, walletBalanceRecordId)
	return args.Error(0)
}

func (m *MockWalletDepositRecord) UpdateFailedByID(ctx context.Context, id uint, blockNumber uint64) error {
	args := m.Called(ctx, id, blockNumber)
	return args.Error(0)
}

func (m *MockWalletDepositRecord) GetByTxHash(ctx context.Context, txHash string) (*repository.WalletDepositRecord, error) {
	args := m.Called(ctx, txHash)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.WalletDepositRecord), args.Error(1)
}

var _ repository.WalletDepositRecordRepository = (*MockWalletDepositRecord)(nil)

// ========== 对话相关 Mock ==========

// MockUserChatASRRecord Mock用户聊天ASR记录接口
type MockUserChatASRRecord struct {
	mock.Mock
}

func NewMockUserChatASRRecord() *MockUserChatASRRecord {
	return &MockUserChatASRRecord{}
}

func (m *MockUserChatASRRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserChatASRRecord) Create(ctx context.Context, record *repository.UserChatASRRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

var _ repository.UserChatASRRecordRepository = (*MockUserChatASRRecord)(nil)

// MockUserChatTTSRecord Mock用户聊天TTS记录接口
type MockUserChatTTSRecord struct {
	mock.Mock
}

func NewMockUserChatTTSRecord() *MockUserChatTTSRecord {
	return &MockUserChatTTSRecord{}
}

func (m *MockUserChatTTSRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserChatTTSRecord) Create(ctx context.Context, record *repository.UserChatTTSRecord) error {
	args := m.Called(ctx, record)
	return args.Error(0)
}

var _ repository.UserChatTTSRecordRepository = (*MockUserChatTTSRecord)(nil)

// ========== 模型相关 Mock ==========

// MockLLMModelConfig MockLLM模型配置接口
type MockLLMModelConfig struct {
	mock.Mock
}

func NewMockLLMModelConfig() *MockLLMModelConfig {
	return &MockLLMModelConfig{}
}

func (m *MockLLMModelConfig) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockLLMModelConfig) GetAll(ctx context.Context) ([]*repository.LLMModelConfig, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.LLMModelConfig), args.Error(1)
}

func (m *MockLLMModelConfig) GetByID(ctx context.Context, id uint) (*repository.LLMModelConfig, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.LLMModelConfig), args.Error(1)
}

func (m *MockLLMModelConfig) GetByModelID(ctx context.Context, modelID string) (*repository.LLMModelConfig, error) {
	args := m.Called(ctx, modelID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.LLMModelConfig), args.Error(1)
}

func (m *MockLLMModelConfig) Create(ctx context.Context, model *repository.LLMModelConfig) error {
	args := m.Called(ctx, model)
	return args.Error(0)
}

func (m *MockLLMModelConfig) Update(ctx context.Context, model *repository.LLMModelConfig) error {
	args := m.Called(ctx, model)
	return args.Error(0)
}

var _ repository.LLMModelConfigRepository = (*MockLLMModelConfig)(nil)

// MockTTSModelVoice MockTTS模型语音接口
type MockTTSModelVoice struct {
	mock.Mock
}

func NewMockTTSModelVoice() *MockTTSModelVoice {
	return &MockTTSModelVoice{}
}

func (m *MockTTSModelVoice) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTTSModelVoice) GetByModelName(ctx context.Context, modelName string) ([]*repository.TTSModelVoice, error) {
	args := m.Called(ctx, modelName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TTSModelVoice), args.Error(1)
}

func (m *MockTTSModelVoice) GetByModelNameAndName(ctx context.Context, modelName, name string) (*repository.TTSModelVoice, error) {
	args := m.Called(ctx, modelName, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TTSModelVoice), args.Error(1)
}

func (m *MockTTSModelVoice) CreateBatch(ctx context.Context, voices []*repository.TTSModelVoice) error {
	args := m.Called(ctx, voices)
	return args.Error(0)
}

func (m *MockTTSModelVoice) DeleteByModelName(ctx context.Context, modelName string) error {
	args := m.Called(ctx, modelName)
	return args.Error(0)
}

var _ repository.TTSModelVoiceRepository = (*MockTTSModelVoice)(nil)

// MockTopaiModel MockTopai模型接口
type MockTopaiModel struct {
	mock.Mock
}

func NewMockTopaiModel() *MockTopaiModel {
	return &MockTopaiModel{}
}

func (m *MockTopaiModel) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModel) Create(ctx context.Context, model *repository.TopaiModel) error {
	args := m.Called(ctx, model)
	return args.Error(0)
}

func (m *MockTopaiModel) GetAll(ctx context.Context) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByModelNameAndVersion(ctx context.Context, modelName, modelVersion string) (*repository.TopaiModel, error) {
	args := m.Called(ctx, modelName, modelVersion)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByModelName(ctx context.Context, modelName string) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, modelName)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByID(ctx context.Context, id uint) (*repository.TopaiModel, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetBySeriesIds(ctx context.Context, seriesIds []uint) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, seriesIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByModelTypes(ctx context.Context, modelTypes []string) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, modelTypes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetByOwnerAddress(ctx context.Context, ownerAddress string, offset int, limit int) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, ownerAddress, offset, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

func (m *MockTopaiModel) GetCountByOwnerAddress(ctx context.Context, ownerAddress string) (int64, error) {
	args := m.Called(ctx, ownerAddress)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockTopaiModel) GetByChainModelId(ctx context.Context, chainModelId uint) (*repository.TopaiModel, error) {
	args := m.Called(ctx, chainModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModel), args.Error(1)
}

var _ repository.TopaiModelRepository = (*MockTopaiModel)(nil)

// 兼容新增的方法
func (m *MockTopaiModel) GetByIsOnChain(ctx context.Context, isOnChain bool) ([]*repository.TopaiModel, error) {
	args := m.Called(ctx, isOnChain)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModel), args.Error(1)
}

// 兼容接口包含的 Update 方法
func (m *MockTopaiModel) Update(ctx context.Context, model *repository.TopaiModel) error {
	args := m.Called(ctx, model)
	return args.Error(0)
}

// MockTopaiModelProvider MockTopai模型提供商接口
type MockTopaiModelProvider struct {
	mock.Mock
}

func NewMockTopaiModelProvider() *MockTopaiModelProvider {
	return &MockTopaiModelProvider{}
}

func (m *MockTopaiModelProvider) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelProvider) GetAll(ctx context.Context) ([]*repository.TopaiModelProvider, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) GetByWalletAddress(ctx context.Context, walletAddress string) (*repository.TopaiModelProvider, error) {
	args := m.Called(ctx, walletAddress)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) GetByUuids(ctx context.Context, uuids []string) ([]*repository.TopaiModelProvider, error) {
	args := m.Called(ctx, uuids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) GetByNames(ctx context.Context, names []string) ([]*repository.TopaiModelProvider, error) {
	args := m.Called(ctx, names)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelProvider, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) GetByID(ctx context.Context, id uint) (*repository.TopaiModelProvider, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelProvider), args.Error(1)
}

func (m *MockTopaiModelProvider) Update(ctx context.Context, provider *repository.TopaiModelProvider) error {
	args := m.Called(ctx, provider)
	return args.Error(0)
}

func (m *MockTopaiModelProvider) Create(ctx context.Context, provider *repository.TopaiModelProvider) error {
	args := m.Called(ctx, provider)
	return args.Error(0)
}

var _ repository.TopaiModelProviderRepository = (*MockTopaiModelProvider)(nil)

// MockTopaiModelCategory MockTopai模型分类接口
type MockTopaiModelCategory struct {
	mock.Mock
}

func NewMockTopaiModelCategory() *MockTopaiModelCategory {
	return &MockTopaiModelCategory{}
}

func (m *MockTopaiModelCategory) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelCategory) GetAll(ctx context.Context) ([]*repository.TopaiModelCategory, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategory), args.Error(1)
}

func (m *MockTopaiModelCategory) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategory, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategory), args.Error(1)
}

var _ repository.TopaiModelCategoryRepository = (*MockTopaiModelCategory)(nil)

// 兼容接口新增 Create 方法
func (m *MockTopaiModelCategory) Create(ctx context.Context, category *repository.TopaiModelCategory) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

// MockTopaiModelCategoryRelation MockTopai模型分类关系接口
type MockTopaiModelCategoryRelation struct {
	mock.Mock
}

func NewMockTopaiModelCategoryRelation() *MockTopaiModelCategoryRelation {
	return &MockTopaiModelCategoryRelation{}
}

func (m *MockTopaiModelCategoryRelation) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelCategoryRelation) GetAll(ctx context.Context) ([]*repository.TopaiModelCategoryRelation, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategoryRelation), args.Error(1)
}

func (m *MockTopaiModelCategoryRelation) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelCategoryRelation, error) {
	args := m.Called(ctx, topaiModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategoryRelation), args.Error(1)
}

func (m *MockTopaiModelCategoryRelation) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	args := m.Called(ctx, topaiModelIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategoryRelation), args.Error(1)
}

func (m *MockTopaiModelCategoryRelation) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategoryRelation), args.Error(1)
}

func (m *MockTopaiModelCategoryRelation) GetByCategoryIds(ctx context.Context, categoryIds []uint) ([]*repository.TopaiModelCategoryRelation, error) {
	args := m.Called(ctx, categoryIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCategoryRelation), args.Error(1)
}

func (m *MockTopaiModelCategoryRelation) DeleteByTopaiModelId(ctx context.Context, topaiModelId uint) error {
	args := m.Called(ctx, topaiModelId)
	return args.Error(0)
}

func (m *MockTopaiModelCategoryRelation) CreateBatch(ctx context.Context, relations []*repository.TopaiModelCategoryRelation) error {
	args := m.Called(ctx, relations)
	return args.Error(0)
}

var _ repository.TopaiModelCategoryRelationRepository = (*MockTopaiModelCategoryRelation)(nil)

// MockTopaiModelSeries MockTopai模型系列接口
type MockTopaiModelSeries struct {
	mock.Mock
}

func NewMockTopaiModelSeries() *MockTopaiModelSeries {
	return &MockTopaiModelSeries{}
}

func (m *MockTopaiModelSeries) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelSeries) GetAll(ctx context.Context) ([]*repository.TopaiModelSeries, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelSeries), args.Error(1)
}

func (m *MockTopaiModelSeries) GetByID(ctx context.Context, id uint) (*repository.TopaiModelSeries, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelSeries), args.Error(1)
}

var _ repository.TopaiModelSeriesRepository = (*MockTopaiModelSeries)(nil)

// 兼容接口新增 Create 方法
func (m *MockTopaiModelSeries) Create(ctx context.Context, series *repository.TopaiModelSeries) error {
	args := m.Called(ctx, series)
	return args.Error(0)
}

// MockTopaiModelSeriesRelation MockTopai模型系列关系接口
type MockTopaiModelSeriesRelation struct {
	mock.Mock
}

func NewMockTopaiModelSeriesRelation() *MockTopaiModelSeriesRelation {
	return &MockTopaiModelSeriesRelation{}
}

func (m *MockTopaiModelSeriesRelation) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelSeriesRelation) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelSeriesRelation, error) {
	args := m.Called(ctx, topaiModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelSeriesRelation), args.Error(1)
}

func (m *MockTopaiModelSeriesRelation) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelSeriesRelation, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelSeriesRelation), args.Error(1)
}

var _ repository.TopaiModelSeriesRelationRepository = (*MockTopaiModelSeriesRelation)(nil)

// MockTopaiModelExt MockTopai模型扩展接口
type MockTopaiModelExt struct {
	mock.Mock
}

func NewMockTopaiModelExt() *MockTopaiModelExt {
	return &MockTopaiModelExt{}
}

func (m *MockTopaiModelExt) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelExt) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelExt, error) {
	args := m.Called(ctx, topaiModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx, topaiModelIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetOnlineList(ctx context.Context, offset, pageSize int, sortBy, sortOrder string, modelIds []uint, providerIds []uint, isfree bool, searchModelId string) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx, offset, pageSize, sortBy, sortOrder, modelIds, providerIds, isfree, searchModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetOnlineAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetAll(ctx context.Context) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelExt, error) {
	args := m.Called(ctx, modelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx, modelIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) GetBySearchOnlineModelId(ctx context.Context, modelId string) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx, modelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

func (m *MockTopaiModelExt) Create(ctx context.Context, ext *repository.TopaiModelExt) error {
	args := m.Called(ctx, ext)
	return args.Error(0)
}

func (m *MockTopaiModelExt) Update(ctx context.Context, ext *repository.TopaiModelExt) error {
	args := m.Called(ctx, ext)
	return args.Error(0)
}

var _ repository.TopaiModelExtRepository = (*MockTopaiModelExt)(nil)

// 兼容接口新增 GetByTopaiModelIdsAndStatus 方法
func (m *MockTopaiModelExt) GetByTopaiModelIdsAndStatus(ctx context.Context, topaiModelIds []uint, status []int, offset, limit int) ([]*repository.TopaiModelExt, error) {
	args := m.Called(ctx, topaiModelIds, status, offset, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelExt), args.Error(1)
}

// MockTopaiModelSupportParam MockTopai模型支持参数接口
type MockTopaiModelSupportParam struct {
	mock.Mock
}

func NewMockTopaiModelSupportParam() *MockTopaiModelSupportParam {
	return &MockTopaiModelSupportParam{}
}

func (m *MockTopaiModelSupportParam) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelSupportParam) GetByTopaiModelId(ctx context.Context, topaiModelId uint) ([]*repository.TopaiModelSupportParam, error) {
	args := m.Called(ctx, topaiModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelSupportParam), args.Error(1)
}

func (m *MockTopaiModelSupportParam) GetByParams(ctx context.Context, params []string) ([]*repository.TopaiModelSupportParam, error) {
	args := m.Called(ctx, params)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelSupportParam), args.Error(1)
}

func (m *MockTopaiModelSupportParam) GetAll(ctx context.Context) ([]*repository.TopaiModelSupportParam, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelSupportParam), args.Error(1)
}

var _ repository.TopaiModelSupportParamRepository = (*MockTopaiModelSupportParam)(nil)

// MockTopaiModelCost MockTopai模型成本接口
type MockTopaiModelCost struct {
	mock.Mock
}

func NewMockTopaiModelCost() *MockTopaiModelCost {
	return &MockTopaiModelCost{}
}

func (m *MockTopaiModelCost) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockTopaiModelCost) GetByModelId(ctx context.Context, modelId string) (*repository.TopaiModelCost, error) {
	args := m.Called(ctx, modelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelCost), args.Error(1)
}

func (m *MockTopaiModelCost) GetByModelIds(ctx context.Context, modelIds []string) ([]*repository.TopaiModelCost, error) {
	args := m.Called(ctx, modelIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCost), args.Error(1)
}

func (m *MockTopaiModelCost) GetByTopaiModelIds(ctx context.Context, topaiModelIds []uint) ([]*repository.TopaiModelCost, error) {
	args := m.Called(ctx, topaiModelIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCost), args.Error(1)
}

func (m *MockTopaiModelCost) GetByTopaiModelId(ctx context.Context, topaiModelId uint) (*repository.TopaiModelCost, error) {
	args := m.Called(ctx, topaiModelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.TopaiModelCost), args.Error(1)
}

func (m *MockTopaiModelCost) Create(ctx context.Context, cost *repository.TopaiModelCost) error {
	args := m.Called(ctx, cost)
	return args.Error(0)
}

func (m *MockTopaiModelCost) Update(ctx context.Context, cost *repository.TopaiModelCost) error {
	args := m.Called(ctx, cost)
	return args.Error(0)
}

func (m *MockTopaiModelCost) GetAll(ctx context.Context, offset, limit int, sort string) ([]*repository.TopaiModelCost, error) {
	args := m.Called(ctx, offset, limit, sort)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.TopaiModelCost), args.Error(1)
}

var _ repository.TopaiModelCostRepository = (*MockTopaiModelCost)(nil)

// MockUserUsageDayRecord Mock用户使用日记录接口
type MockUserUsageDayRecord struct {
	mock.Mock
}

func NewMockUserUsageDayRecord() *MockUserUsageDayRecord {
	return &MockUserUsageDayRecord{}
}

func (m *MockUserUsageDayRecord) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserUsageDayRecord) CreateBatch(ctx context.Context, records []*repository.UserUsageDayRecord) error {
	args := m.Called(ctx, records)
	return args.Error(0)
}

func (m *MockUserUsageDayRecord) GetByModelId(ctx context.Context, modelId string) ([]*repository.UserUsageDayRecord, error) {
	args := m.Called(ctx, modelId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserUsageDayRecord), args.Error(1)
}

func (m *MockUserUsageDayRecord) GetLast(ctx context.Context) (*repository.UserUsageDayRecord, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserUsageDayRecord), args.Error(1)
}

var _ repository.UserUsageDayRecordRepository = (*MockUserUsageDayRecord)(nil)

// ========== 用户应用相关 Mock ==========

// MockDevAppInfo Mock开发者应用信息接口
type MockDevAppInfo struct {
	mock.Mock
}

func NewMockDevAppInfo() *MockDevAppInfo {
	return &MockDevAppInfo{}
}

func (m *MockDevAppInfo) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockDevAppInfo) Create(ctx context.Context, app *repository.DevAppInfo) error {
	args := m.Called(ctx, app)
	return args.Error(0)
}

func (m *MockDevAppInfo) Update(ctx context.Context, app *repository.DevAppInfo) error {
	args := m.Called(ctx, app)
	return args.Error(0)
}

func (m *MockDevAppInfo) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDevAppInfo) GetAll(ctx context.Context) ([]*repository.DevAppInfo, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.DevAppInfo), args.Error(1)
}

func (m *MockDevAppInfo) GetByUuid(ctx context.Context, uuid string) (*repository.DevAppInfo, error) {
	args := m.Called(ctx, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.DevAppInfo), args.Error(1)
}

func (m *MockDevAppInfo) GetByIds(ctx context.Context, ids []uint) ([]*repository.DevAppInfo, error) {
	args := m.Called(ctx, ids)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.DevAppInfo), args.Error(1)
}

func (m *MockDevAppInfo) GetByID(ctx context.Context, id uint) (*repository.DevAppInfo, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.DevAppInfo), args.Error(1)
}

func (m *MockDevAppInfo) GetAllByUserID(ctx context.Context, userID uint) ([]*repository.DevAppInfo, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.DevAppInfo), args.Error(1)
}

var _ repository.DevAppInfoRepository = (*MockDevAppInfo)(nil)

// MockDevAppKey Mock开发者应用密钥接口
type MockDevAppKey struct {
	mock.Mock
}

func NewMockDevAppKey() *MockDevAppKey {
	return &MockDevAppKey{}
}

func (m *MockDevAppKey) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockDevAppKey) GetByDevAppId(ctx context.Context, devAppId uint) ([]*repository.DevAppKey, error) {
	args := m.Called(ctx, devAppId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.DevAppKey), args.Error(1)
}

func (m *MockDevAppKey) GetByDevAppIds(ctx context.Context, devAppIds []uint) ([]*repository.DevAppKey, error) {
	args := m.Called(ctx, devAppIds)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.DevAppKey), args.Error(1)
}

func (m *MockDevAppKey) GetByKey(ctx context.Context, key string) (*repository.DevAppKey, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.DevAppKey), args.Error(1)
}

func (m *MockDevAppKey) Create(ctx context.Context, key *repository.DevAppKey) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

func (m *MockDevAppKey) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDevAppKey) GetByDevAppIdAndKey(ctx context.Context, devAppId uint, key string) (*repository.DevAppKey, error) {
	args := m.Called(ctx, devAppId, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.DevAppKey), args.Error(1)
}

func (m *MockDevAppKey) GetByDevAppIdAndName(ctx context.Context, devAppId uint, name string) (*repository.DevAppKey, error) {
	args := m.Called(ctx, devAppId, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.DevAppKey), args.Error(1)
}

var _ repository.DevAppKeyRepository = (*MockDevAppKey)(nil)

// MockUserChatUsage Mock用户聊天使用记录接口
type MockUserChatUsage struct {
	mock.Mock
}

func NewMockUserChatUsage() *MockUserChatUsage {
	return &MockUserChatUsage{}
}

func (m *MockUserChatUsage) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockUserChatUsage) Create(ctx context.Context, usage *repository.UserChatUsage) error {
	args := m.Called(ctx, usage)
	return args.Error(0)
}

func (m *MockUserChatUsage) GetByCreatedAtPeriod(ctx context.Context, start, end string) ([]*repository.UserChatUsage, error) {
	args := m.Called(ctx, start, end)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatUsage), args.Error(1)
}

func (m *MockUserChatUsage) Update(ctx context.Context, usage *repository.UserChatUsage) error {
	args := m.Called(ctx, usage)
	return args.Error(0)
}

func (m *MockUserChatUsage) GetByID(ctx context.Context, id uint) (*repository.UserChatUsage, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserChatUsage), args.Error(1)
}

func (m *MockUserChatUsage) UpdateSettledByID(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserChatUsage) UpdateSettledByIDs(ctx context.Context, ids []uint) error {
	args := m.Called(ctx, ids)
	return args.Error(0)
}

func (m *MockUserChatUsage) GetUnconfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatUsage), args.Error(1)
}

func (m *MockUserChatUsage) GetConfirmed(ctx context.Context) ([]*repository.UserChatUsage, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatUsage), args.Error(1)
}

func (m *MockUserChatUsage) GetSettled(ctx context.Context) ([]*repository.UserChatUsage, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*repository.UserChatUsage), args.Error(1)
}

func (m *MockUserChatUsage) UpdateCompletedByIDs(ctx context.Context, ids []uint, userReportCostRecordID uint) error {
	args := m.Called(ctx, ids, userReportCostRecordID)
	return args.Error(0)
}

func (m *MockUserChatUsage) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatUsage, error) {
	args := m.Called(ctx, uuid)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.UserChatUsage), args.Error(1)
}

var _ repository.UserChatUsageRepository = (*MockUserChatUsage)(nil)

// MockProviderToken Mock提供者Token
type MockProviderToken struct {
	mock.Mock
}

func NewMockProviderToken() *MockProviderToken {
	return &MockProviderToken{}
}

func (m *MockProviderToken) Reset() {
	m.Mock = mock.Mock{}
}

func (m *MockProviderToken) Create(ctx context.Context, token *repository.ProviderToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockProviderToken) GetByToken(ctx context.Context, token string) (*repository.ProviderToken, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*repository.ProviderToken), args.Error(1)
}

func (m *MockProviderToken) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockProviderToken) DeleteByProviderID(ctx context.Context, providerID uint) error {
	args := m.Called(ctx, providerID)
	return args.Error(0)
}

var _ repository.ProviderTokenRepository = (*MockProviderToken)(nil)
