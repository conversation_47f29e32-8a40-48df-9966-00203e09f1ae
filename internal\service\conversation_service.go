package service

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

type ConversationService struct {
	db  *repository.DB
	log *logger.ModuleLogger
}

var _ ConversationApi = &ConversationService{}

func NewConversationService(ctx context.Context, db *repository.DB) *ConversationService {
	return &ConversationService{
		db:  db,
		log: logger.GetLogger("conversation_service"),
	}
}

func (s *ConversationService) CreateConversation(ctx context.Context, user *UserBaseOP, Request *CreateConversationRequest) (*ConversationOP, error) {
	for _, model := range Request.Chat.Models {
		modelInfo, err := s.db.RemoterModelInfo.GetByModelName(ctx, model)
		if err != nil {
			return nil, err
		}
		if modelInfo == nil {
			return nil, api.NewClientError("model not found")
		}
		if modelInfo.ModelType != repository.RemoterModelTypeLLM {
			return nil, api.NewClientError("model type not supported")
		}
	}

	// 生成 conversation_id
	uuid := uuid.New().String()

	// 创建对话
	createTime := time.Unix(Request.Chat.Timestamp/1000, 0)
	conversation := &repository.Conversation{
		UUID:           uuid,
		UserID:         user.ID,
		IdentityID:     user.IdentityID,
		Title:          Request.Chat.Title,
		ModelID:        strings.Join(Request.Chat.Models, ","),
		System:         Request.Chat.System,
		CurrentMsgUUID: Request.Chat.History.CurrentId,
		CreatedAt:      &createTime,
		UpdatedAt:      &createTime,
	}

	// 创建消息
	messageReq := Request.Chat.Messages[0]
	fileUUIDs := make([]string, len(messageReq.Files))
	for i, file := range messageReq.Files {
		fileUUIDs[i] = file.FileID
	}

	message := &repository.ConversationMessage{
		UUID:             messageReq.UUID,
		ConversationUUID: conversation.UUID,
		Content:          messageReq.Content,
		Role:             messageReq.Role,
		ParentID:         0,
		ParentUUID:       "",
		Models:           strings.Join(messageReq.Models, ","),
		FileUUIDs:        strings.Join(fileUUIDs, ","),
		IsDone:           1,
		CreatedAt:        &createTime,
	}

	if message.FileUUIDs != "" {
		return nil, api.NewClientError("file is not supported")
	}
	if message.Content == "" {
		return nil, api.NewClientError("content must not be empty")
	}

	err := s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
		err := db.Conversation.Create(ctx, conversation)
		if err != nil {
			return err
		}
		message.ConversationID = conversation.ID
		err = db.ConversationMessage.Create(ctx, message)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	resp := &ConversationOP{
		Chat:      Request.Chat,
		Archived:  false,
		CreatedAt: messageReq.Timestamp / 1000,
		FolderId:  nil,
		UUID:      uuid,
		Meta:      nil,
		Pinned:    false,
		Title:     Request.Chat.Title,
		UpdatedAt: messageReq.Timestamp / 1000,
		UserId:    user.IdentityID,
	}

	return resp, nil
}

func (s *ConversationService) GetConversation(ctx context.Context, user *UserBaseOP, conversationUUID string) (*ConversationOP, error) {
	conversation, err := s.db.Conversation.GetByUUID(ctx, conversationUUID)
	if err != nil {
		return nil, err
	}
	if conversation == nil || conversation.UserID != user.ID {
		return nil, errors.New("conversation not found")
	}

	conversationMessages, err := s.db.ConversationMessage.GetByConversationID(ctx, conversation.ID)
	if err != nil {
		return nil, err
	}

	fileUUIDs := make([]string, 0)
	childrenMap := make(map[string][]string)

	messageMap := make(map[string]*repository.ConversationMessage)
	for _, message := range conversationMessages {
		messageMap[message.UUID] = message

		if message.FileUUIDs != "" {
			fileUUIDs = append(fileUUIDs, strings.Split(message.FileUUIDs, ",")...)
		}

		if message.ParentUUID != "" {
			childrenMap[message.ParentUUID] = append(childrenMap[message.ParentUUID], message.UUID)
		}
	}

	fileMap := make(map[string]*repository.File)
	if len(fileUUIDs) > 0 {
		files, err := s.db.File.FindByUserIDAndUUIDs(ctx, user.ID, fileUUIDs)
		if err != nil {
			return nil, err
		}
		for _, file := range files {
			fileMap[file.UUID] = file
		}
	}

	history := make(map[string]*ConversationMessage)
	totalFiles := make([]*ConversationMessageFile, 0)
	for _, message := range messageMap {
		msg := &ConversationMessage{
			UUID:         message.UUID,
			Content:      message.Content,
			Timestamp:    message.CreatedAt.Unix(),
			Role:         message.Role,
			Models:       strings.Split(message.Models, ","),
			ParentUUID:   message.ParentUUID,
			LastSentence: "",
			ChildrenIds:  make([]string, 0),
			Done:         message.IsDone == 1,
			UserContext:  nil,
		}
		if message.Role == "assistant" && len(msg.Models) > 0 {
			msg.Model = msg.Models[0]
		}
		if message.FileUUIDs != "" {
			msg.Files = make([]*ConversationMessageFile, len(strings.Split(message.FileUUIDs, ",")))
			for i, fileUUID := range strings.Split(message.FileUUIDs, ",") {
				repoFile := fileMap[fileUUID]
				if repoFile == nil {
					// Skip files that don't exist in the file map
					continue
				}
				msgFile := &ConversationMessageFile{
					FileID:         repoFile.UUID,
					CollectionName: repoFile.Name,
					Error:          "",
					ItemId:         "",
					Name:           repoFile.OriginalName,
					Size:           repoFile.Size,
					Status:         "uploaded",
					Type:           repoFile.Type,
					URL:            utils.GetFileRouter(repoFile),
					File: &FileInfoOP{
						ID:             repoFile.ID,
						FileID:         repoFile.UUID,
						FileName:       repoFile.OriginalName,
						URL:            repoFile.Path,
						CreatedAt:      repoFile.CreatedAt.Unix(),
						UpdatedAt:      repoFile.UpdatedAt.Unix(),
						Hash:           "",
						UserIdentityID: user.IdentityID,
						Data: &FileInfoData{
							Content: repoFile.Content,
						},
						Meta: &FileInfoMeta{
							CollectionName: repoFile.Name,
							ContentType:    repoFile.MimeType,
							Data:           nil,
							Name:           repoFile.OriginalName,
							Size:           repoFile.Size,
						},
					},
				}

				if repoFile.Type != "image" {
					msgFile.Type = "file"
					totalFiles = append(totalFiles, msgFile)
				}
				msg.Files[i] = msgFile
			}
		}
		if children, ok := childrenMap[message.UUID]; ok {
			msg.ChildrenIds = children
		}
		history[msg.UUID] = msg
	}

	currentMsgUUID := conversation.CurrentMsgUUID
	messageList := make([]*ConversationMessage, 0)
	userModels := make([]string, 0)
	for {
		if currentMsgUUID == "" {
			break
		}
		msg := history[currentMsgUUID]
		if msg == nil {
			break
		}
		messageList = append([]*ConversationMessage{msg}, messageList...)
		if msg.Role == "user" && len(userModels) == 0 {
			userModels = msg.Models
		}
		currentMsgUUID = msg.ParentUUID
	}

	return &ConversationOP{
		Chat: &Conversation{
			Files: totalFiles,
			History: &ConversationHistory{
				Messages:  history,
				CurrentId: conversation.CurrentMsgUUID,
			},
			Title:     conversation.Title,
			UUID:      conversation.UUID,
			Messages:  messageList,
			Params:    nil,
			System:    "",
			Models:    userModels,
			Tags:      nil,
			Timestamp: conversation.CreatedAt.Unix(),
		},
		Archived:  false,
		CreatedAt: conversation.CreatedAt.Unix(),
		FolderId:  nil,
		UUID:      conversation.UUID,
		Meta:      nil,
		Pinned:    false,
		ShareId:   nil,
		Title:     conversation.Title,
		UpdatedAt: conversation.UpdatedAt.Unix(),
		UserId:    user.IdentityID,
	}, nil
}

func (s *ConversationService) GetConversationList(ctx context.Context, user *UserBaseOP, page int, limit int) ([]*ConversationListOP, error) {
	conversations, err := s.db.Conversation.FindByUserIDOrderByUpdatedAtDesc(ctx, user.ID, limit, (page-1)*limit)
	if err != nil {
		return nil, err
	}

	resp := make([]*ConversationListOP, 0, len(conversations))
	for _, conversation := range conversations {
		resp = append(resp, &ConversationListOP{
			UUID:      conversation.UUID,
			Title:     conversation.Title,
			UpdatedAt: conversation.UpdatedAt.Unix(),
			CreatedAt: conversation.CreatedAt.Unix(),
		})
	}

	return resp, nil
}

func (s *ConversationService) UpdateConversation(ctx context.Context, user *UserBaseOP, conversationUUID string, req *UpdateConversationRequest) (*ConversationOP, error) {
	conversation, err := s.db.Conversation.GetByUUID(ctx, conversationUUID)
	if err != nil {
		return nil, err
	}

	if conversation == nil || conversation.UserID != user.ID {
		return nil, errors.New("conversation not found")
	}

	savedMessages, err := s.db.ConversationMessage.GetByConversationID(ctx, conversation.ID)
	if err != nil {
		return nil, err
	}
	savedMessagesMap := make(map[string]*repository.ConversationMessage)
	for _, msg := range savedMessages {
		savedMessagesMap[msg.UUID] = msg
	}

	needUpdateMsg := make([]*repository.ConversationMessage, 0)
	for _, currentMsg := range req.Chat.History.Messages {
		savedMsg, ok := savedMessagesMap[currentMsg.UUID]
		if ok && savedMsg.IsDone == 1 {
			continue
		}
		fileUUIDs := make([]string, len(currentMsg.Files))

		for i, file := range currentMsg.Files {
			if file.FileID != "" {
				fileUUIDs[i] = file.FileID
			} else if file.Type == "image" {
				fileUUID := utils.GetFileUUIDByUrl(file.URL)
				if fileUUID != "" {
					fileUUIDs[i] = fileUUID
				}
			}
		}
		modelStr := strings.Join(currentMsg.Models, ",")
		if currentMsg.Role == "assistant" {
			modelStr = currentMsg.Model
		}
		for _, model := range strings.Split(modelStr, ",") {
			modelInfo, err := s.db.RemoterModelInfo.GetByModelName(ctx, model)
			if err != nil {
				return nil, err
			}
			if modelInfo == nil {
				return nil, api.NewClientError("model not found")
			}
			if modelInfo.ModelType != repository.RemoterModelTypeLLM {
				return nil, api.NewClientError("model type not supported")
			}
		}
		if ok {
			savedMsg.Content = currentMsg.Content
			savedMsg.Role = currentMsg.Role
			savedMsg.Models = modelStr
			savedMsg.FileUUIDs = strings.Join(fileUUIDs, ",")
			if currentMsg.Done {
				savedMsg.IsDone = 1
			}
			needUpdateMsg = append(needUpdateMsg, savedMsg)
		} else {
			isDone := uint8(0)
			if currentMsg.Done || currentMsg.Role == "user" {
				isDone = 1
			}

			createTime := time.Unix(currentMsg.Timestamp, 0)
			needUpdateMsg = append(needUpdateMsg, &repository.ConversationMessage{
				UUID:             currentMsg.UUID,
				ConversationID:   conversation.ID,
				ConversationUUID: conversation.UUID,
				Content:          currentMsg.Content,
				Role:             currentMsg.Role,
				Models:           modelStr,
				FileUUIDs:        strings.Join(fileUUIDs, ","),
				CreatedAt:        &createTime,
				IsDone:           isDone,
				ParentUUID:       currentMsg.ParentUUID,
			})
		}
	}

	if len(needUpdateMsg) > 0 {
		err = s.db.RunWithTx(ctx, func(ctx context.Context, db *repository.DB) error {
			for i := len(needUpdateMsg) - 1; i >= 0; i-- {
				msg := needUpdateMsg[i]
				if msg.ID > 0 {
					err = db.ConversationMessage.Update(ctx, msg)
				} else {
					err = db.ConversationMessage.Create(ctx, msg)
				}
				if err != nil {
					return err
				}
			}

			return db.Conversation.UpdateCurrentMsgUUIDByUUID(ctx, conversation.UUID, req.Chat.History.CurrentId)
		})
	}

	if err != nil {
		return nil, err
	}

	op, err := s.GetConversation(ctx, user, conversationUUID)
	if err != nil {
		return nil, err
	}

	return op, nil
}

func (s *ConversationService) DeleteConversation(ctx context.Context, user *UserBaseOP, conversationUUID string) error {
	conversation, err := s.db.Conversation.GetByUUID(ctx, conversationUUID)
	if err != nil {
		return err
	}
	if conversation == nil || conversation.UserID != user.ID {
		return errors.New("conversation not found")
	}

	return s.db.Conversation.Delete(ctx, conversation.ID)
}

func (s *ConversationService) UpdateTTIChatShare(ctx context.Context, user *UserBaseOP, ttiChatUUID string, isShare bool) error {
	ttiRecord, err := s.db.UserChatTTIRecord.GetByUUID(ctx, ttiChatUUID)
	if err != nil {
		return err
	}
	if ttiRecord == nil || ttiRecord.UserID != user.ID {
		return api.NewClientError("tti record not found")
	}

	isShared := uint8(0)
	if isShare {
		isShared = 1
	}

	if isShared == ttiRecord.IsShared {
		return nil
	}

	err = s.db.UserChatTTIRecord.UpdateIsSharedByUUID(ctx, ttiChatUUID, isShared)
	if err != nil {
		return err
	}

	return nil
}

func (s *ConversationService) GetUserTTIChatShared(ctx context.Context, user *UserBaseOP, page int, limit int) (*TTIChatSharedListOP, error) {
	count, err := s.db.UserChatTTIRecord.GetSharedCountByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if count == 0 {
		return &TTIChatSharedListOP{
			Count: 0,
			List:  make([]*TTIChatSharedOP, 0),
		}, nil
	}

	records, err := s.db.UserChatTTIRecord.GetSharedByUserID(ctx, user.ID, limit, (page-1)*limit, "id DESC")
	if err != nil {
		return nil, err
	}

	resp := &TTIChatSharedListOP{
		Count: count,
		List:  make([]*TTIChatSharedOP, 0, len(records)),
	}

	for _, record := range records {
		fileUUIDs := strings.Split(record.FileUUIDs, ",")
		fileUrls := make([]string, 0, len(fileUUIDs))
		for _, fileUUID := range fileUUIDs {
			file, err := s.db.File.GetByUUID(ctx, fileUUID)
			if err != nil {
				return nil, err
			}
			if file == nil {
				continue
			}
			fileUrls = append(fileUrls, utils.GetFileRouter(file))
		}

		resp.List = append(resp.List, &TTIChatSharedOP{
			UUID:      record.UUID,
			ModelType: repository.RemoterModelTypeTTI.String(),
			ModelID:   record.ModelID,
			Content:   record.Content,
			FileUrls:  fileUrls,
			IsShared:  record.IsShared == 1,
			CreatedAt: record.CreatedAt.Unix(),
		})
	}

	return resp, nil
}

func (s *ConversationService) GetAllTTIChatShared(ctx context.Context, page int, limit int) (*TTIChatSharedListOP, error) {
	count, err := s.db.UserChatTTIRecord.GetSharedCount(ctx)
	if err != nil {
		return nil, err
	}

	if count == 0 {
		return &TTIChatSharedListOP{
			Count: 0,
			List:  make([]*TTIChatSharedOP, 0),
		}, nil
	}

	records, err := s.db.UserChatTTIRecord.GetShared(ctx, limit, (page-1)*limit, "id DESC")
	if err != nil {
		return nil, err
	}

	resp := &TTIChatSharedListOP{
		Count: count,
		List:  make([]*TTIChatSharedOP, 0, len(records)),
	}

	for _, record := range records {
		fileUUIDs := strings.Split(record.FileUUIDs, ",")
		fileUrls := make([]string, 0, len(fileUUIDs))
		for _, fileUUID := range fileUUIDs {
			file, err := s.db.File.GetByUUID(ctx, fileUUID)
			if err != nil {
				return nil, err
			}
			if file == nil {
				continue
			}
			fileUrls = append(fileUrls, utils.GetFileRouter(file))
		}

		resp.List = append(resp.List, &TTIChatSharedOP{
			UUID:      record.UUID,
			ModelType: repository.RemoterModelTypeTTI.String(),
			ModelID:   record.ModelID,
			Content:   record.Content,
			FileUrls:  fileUrls,
			IsShared:  record.IsShared == 1,
			CreatedAt: record.CreatedAt.Unix(),
		})
	}

	return resp, nil
}
