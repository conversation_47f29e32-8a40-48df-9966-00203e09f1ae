package handlers

import (
	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

type KnowledgeHandler struct {
	service *service.Api
}

func NewKnowledgeHandler(service *service.Api) *KnowledgeHandler {
	return &KnowledgeHandler{
		service: service,
	}
}

// @Summary      获取知识库列表
// @Description  获取所有知识库列表
// @Tags         知识库管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Success      200           {object}  []interface{}
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/knowledge [get]
func (h *KnowledgeHandler) GetKnowledgeList(c *gin.Context) {
	// todo 直接返回空，需要的时候再实现
	api.Success(c, []interface{}{})
}

func (h *KnowledgeHandler) Close() {
	// 清理资源
}
