package service

import (
	"context"
	"io"
	"math/big"

	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

type Api struct {
	User         UserApi
	UserBalance  UserBalanceApi
	Model        ModelApi
	File         FileApi
	Config       ConfigApi
	Audio        AudioApi
	Image        ImageApi
	Conversation ConversationApi
	Topai        TopaiApi
	UserApp      UserAppApi
	Provider     ProviderApi
	start        []func(ctx context.Context) error
	close        []func()
}

func (a *Api) Start(ctx context.Context) {
	for _, f := range a.start {
		err := f(ctx)
		if err != nil {
			panic(err)
		}
	}
}

func (a *Api) Close() {
	for _, f := range a.close {
		f()
	}
}

type UserAuthResponse struct {
	Token     string      `json:"token"`
	ExpiresAt uint64      `json:"expires_at"`
	User      *UserBaseOP `json:"user"`
}

type UserAdminOP struct {
	Total int64            `json:"total"`
	Users []*UserAdminUser `json:"users"`
}

type UserAdminUser struct {
	UserBaseOP
	CreatedAt    int64       `json:"created_at"`
	UpdatedAt    int64       `json:"updated_at"`
	LastActiveAt int64       `json:"last_active_at"`
	ApiKey       string      `json:"api_key"`
	Settings     interface{} `json:"settings"`
	Info         interface{} `json:"info"`
	OauthSub     interface{} `json:"oauth_sub"`
}

// AdminUserWithBalance 管理员查看用户列表响应结构
type AdminUserWithBalance struct {
	ID               uint   `json:"id"`
	IdentityID       string `json:"identity_id"`
	Email            string `json:"email"`
	Username         string `json:"username"`
	Role             string `json:"role"`
	CurrentBalance   string `json:"current_balance"`   // 当前余额
	AccumulatedTotal string `json:"accumulated_total"` // 累计充值
	CreatedAt        int64  `json:"created_at"`
	UpdatedAt        int64  `json:"updated_at"`
}

// AdminUsersListResponse 管理员用户列表响应
type AdminUsersListResponse struct {
	Total int64                   `json:"total"`
	Users []*AdminUserWithBalance `json:"users"`
}

type UserBaseOP struct {
	Email           string  `json:"email"`
	ID              uint    `json:"-"`
	IdentityID      string  `json:"id"`
	Name            string  `json:"name"`
	ProfileImageURL string  `json:"profile_image_url"`
	Role            string  `json:"role"`
	Token           string  `json:"token"`
	TokenType       string  `json:"token_type"`
	ExpiresAt       *uint64 `json:"expires_at"`
}

var (
	UserSettingsSystem = "system"
)

type UserSettings struct {
	SyStem string `json:"system"`
}

type UserBalanceRecordOP struct {
	Total   int64                `json:"total"`
	Records []*UserBalanceRecord `json:"records"`
}
type UserBalanceRecord struct {
	ID          uint   `json:"id"`
	Type        string `json:"type"`
	IsIn        bool   `json:"is_in"`
	Amount      string `json:"amount"`
	Description string `json:"description"`
	CreatedAt   int64  `json:"created_at"`
}

type UserChatRequest struct {
	UserID                  uint   `json:"user_id"`
	IP                      string `json:"ip"`
	ModelType               string `json:"model_type"`
	AssociatedUUID          string `json:"associated_uuid"` // 关联UUID：llm 为 conversation_uuid，tts 为 tti_uuid, asr 为 conversation_message_uuid
	ConversationMessageUUID string `json:"conversation_message_uuid"`
	ModelID                 string `json:"model_id"`
}

type UserApi interface {
	RegisterAndGenerateToken(ctx context.Context, username, email, password string) (*UserBaseOP, error)
	Login(ctx context.Context, email, password string) (*UserBaseOP, error)
	UpdatePassword(ctx context.Context, user *UserBaseOP, oldPassword, newPassword string) error
	GetUserByToken(ctx context.Context, token string) (*UserBaseOP, error)
	AdminGetAllUsers(ctx context.Context, page int, pageSize int, order string, direction string) (*UserAdminOP, error)
	AdminGetUsersWithBalance(ctx context.Context, page int, pageSize int, order string, direction string) (*AdminUsersListResponse, error)
	Logout(ctx context.Context, userID uint) error
	AdminCreateUser(ctx context.Context, email, password, name, role string) (*UserBaseOP, error)
	AdminUpdateUser(ctx context.Context, identityID string, email, name, password, profileImageURL string) (*UserAdminUser, error)
	AdminUpdateUserRole(ctx context.Context, identityID string, role string) (*UserAdminUser, error)
	AdminDeleteUser(ctx context.Context, identityID string) error
	GetUserSettings(ctx context.Context, userID uint) (*UserSettings, error)
	UpdateUserSettings(ctx context.Context, userID uint, settings *UserSettings) error
	UpdateUserChatRequest(ctx context.Context, user *UserBaseOP, ip string, req *UserChatRequest) error
	GetDayChatRequest(ctx context.Context, day string) ([]*repository.UserChatRequest, error)
}

type GetUsageResponse struct {
	Id               uint   `json:"-"`
	Uuid             string `json:"id"`
	Model            string `json:"model"`
	TokensPrompt     string `json:"tokens_prompt"`
	TokensCompletion string `json:"tokens_completion"`
	TotalCost        string `json:"total_cost"`
}

type UserBalanceApi interface {
	GetUserBalance(ctx context.Context, userID uint) (string, error)
	GetUserBalanceRecord(ctx context.Context, userID uint, page int, pageSize int) (*UserBalanceRecordOP, error)
	AdminGiftBalance(ctx context.Context, toUserIdentityID string, amount *big.Int, description string) error
	GetUsage(ctx context.Context, appKey *AppKeyOP, requestId string) (*GetUsageResponse, error)
	GetUserShadowAddress(ctx context.Context, userID uint) (string, error)
}

var (
	SystemCommonConfigAPI_KEY_ALLOWED_ENDPOINTS            = "API_KEY_ALLOWED_ENDPOINTS"
	SystemCommonConfigDEFAULT_USER_ROLE                    = "DEFAULT_USER_ROLE"
	SystemCommonConfigENABLE_API_KEY                       = "ENABLE_API_KEY"
	SystemCommonConfigENABLE_API_KEY_ENDPOINT_RESTRICTIONS = "ENABLE_API_KEY_ENDPOINT_RESTRICTIONS"
	SystemCommonConfigENABLE_CHANNELS                      = "ENABLE_CHANNELS"
	SystemCommonConfigENABLE_COMMUNITY_SHARING             = "ENABLE_COMMUNITY_SHARING"
	SystemCommonConfigENABLE_MESSAGE_RATING                = "ENABLE_MESSAGE_RATING"
	SystemCommonConfigENABLE_SIGNUP                        = "ENABLE_SIGNUP"
	SystemCommonConfigJWT_EXPIRES_IN                       = "JWT_EXPIRES_IN"
	SystemCommonConfigSHOW_ADMIN_DETAILS                   = "SHOW_ADMIN_DETAILS"
	SystemCommonConfigWEBUI_URL                            = "WEBUI_URL"
)

type SystemCommonConfig struct {
	// api key 允许的路由
	API_KEY_ALLOWED_ENDPOINTS string `json:"API_KEY_ALLOWED_ENDPOINTS"`
	// 默认用户角色
	DEFAULT_USER_ROLE string `json:"DEFAULT_USER_ROLE"`
	// 是否启用 api key
	ENABLE_API_KEY bool `json:"ENABLE_API_KEY"`
	// 是否启用 api key 限制路由
	ENABLE_API_KEY_ENDPOINT_RESTRICTIONS bool `json:"ENABLE_API_KEY_ENDPOINT_RESTRICTIONS"`
	// 是否启用频道
	ENABLE_CHANNELS bool `json:"ENABLE_CHANNELS"`
	// 是否启用社区分享
	ENABLE_COMMUNITY_SHARING bool `json:"ENABLE_COMMUNITY_SHARING"`
	// 是否启用消息评分
	ENABLE_MESSAGE_RATING bool `json:"ENABLE_MESSAGE_RATING"`
	// 是否启用注册
	ENABLE_SIGNUP bool `json:"ENABLE_SIGNUP"`
	// jwt 过期时间
	JWT_EXPIRES_IN string `json:"JWT_EXPIRES_IN"`
	// 是否显示管理员详情
	SHOW_ADMIN_DETAILS bool `json:"SHOW_ADMIN_DETAILS"`
	// 前端 url
	WEBUI_URL string `json:"WEBUI_URL"`
}

type BaseConfig struct {
	Status        bool   `json:"status"`
	Name          string `json:"name"`
	Version       string `json:"version"`
	DefaultLocale string `json:"default_locale"`
	Oauth         struct {
		Providers map[string]interface{} `json:"providers"`
	} `json:"oauth"`
	Features struct {
		Auth                  bool `json:"auth"`
		AuthTrustedHeader     bool `json:"auth_trusted_header"`
		EnableLdap            bool `json:"enable_ldap"`
		EnableApiKey          bool `json:"enable_api_key"`
		EnableSignup          bool `json:"enable_signup"`
		EnableLoginForm       bool `json:"enable_login_form"`
		EnableWebsocket       bool `json:"enable_websocket"`
		EnableImageGeneration bool `json:"enable_image_generation"`
	} `json:"features"`
}

type ConfigApi interface {
	GetSystemCommonConfig(ctx context.Context) (*SystemCommonConfig, error)
	UpdateSystemCommonConfig(ctx context.Context, config *SystemCommonConfig) error
	GetBaseConfig(ctx context.Context) (*BaseConfig, error)
}

type FileInfoData struct {
	Content string `json:"content"`
}

type FileInfoMeta struct {
	CollectionName string      `json:"collection_name"`
	ContentType    string      `json:"content_type"`
	Data           interface{} `json:"data"`
	Name           string      `json:"name"`
	Size           int64       `json:"size"`
	SavePath       string      `json:"-"`
}

type FileInfoOP struct {
	ID             uint          `json:"-"`
	FileID         string        `json:"id"`
	FileName       string        `json:"file_name"`
	URL            string        `json:"url"`
	CreatedAt      int64         `json:"created_at"`
	Hash           string        `json:"hash"`
	UpdatedAt      int64         `json:"updated_at"`
	Data           *FileInfoData `json:"data"`
	Meta           *FileInfoMeta `json:"meta"`
	UserIdentityID string        `json:"user_id"`
}

type FileUploadIP struct {
	Name     string `json:"name"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
}

type FileCommon interface {
	GetUserFileBytes(ctx context.Context, user *UserBaseOP, fileUUID string) ([]byte, error)
	GetUserFileRealPath(ctx context.Context, user *UserBaseOP, fileUUID string) (string, error)
	UploadFile(ctx context.Context, user *UserBaseOP, fileInfo *FileUploadIP, reader io.Reader, fileType string, isPublic bool) (*FileInfoOP, error)
}

type FileApi interface {
	FileCommon
	GetFile(ctx context.Context, user *UserBaseOP, fileID string) (*FileInfoOP, error)
	DeleteFile(ctx context.Context, user *UserBaseOP, fileID string) error
	UpdateFileContent(ctx context.Context, user *UserBaseOP, fileID uint, content string) error
	SaveImage(ctx context.Context, user *UserBaseOP, imageBytes []byte) (string, error)
}

type RemoteModel struct {
	Active              bool        `json:"active"`
	ContextWindow       int64       `json:"context_window"`
	Created             int64       `json:"created"`
	ModelID             string      `json:"id"`
	MaxCompletionTokens int64       `json:"max_completion_tokens"`
	Object              string      `json:"object"`
	OwnedBy             string      `json:"owned_by"`
	PublicApps          interface{} `json:"public_apps"`
}
type BaseModel struct {
	Active              bool         `json:"active"`
	ContextWindow       int64        `json:"context_window"`
	CreatedAt           int64        `json:"created_at"`
	ModelID             string       `json:"id"`
	MaxCompletionTokens int64        `json:"max_completion_tokens"`
	Name                string       `json:"name"`
	Object              string       `json:"object"`
	Remote              *RemoteModel `json:"remote"`
	OwnedBy             string       `json:"owned_by"`
	PublicApps          interface{}  `json:"public_apps"`
	URLIdx              int          `json:"urlIdx"`
	ModelType           string       `json:"model_type"`
}

type ModelConfigAccessControl struct {
	Read struct {
		GroupIDs []string `json:"group_ids"`
		UserIDs  []string `json:"user_ids"`
	} `json:"read"`
	Write struct {
		GroupIDs []string `json:"group_ids"`
		UserIDs  []string `json:"user_ids"`
	} `json:"write"`
}

type ModelConfigMeta struct {
	ProfileImageURL string `json:"profile_image_url"`
	Description     string `json:"description"`
	Capabilities    struct {
		Vision    bool `json:"vision"`
		Citations bool `json:"citations"`
	} `json:"capabilities"`
	SuggestionPrompts interface{} `json:"suggestion_prompts"`
	Tags              []string    `json:"tags"`
}

type ModelConfigIO struct {
	ID            uint                      `json:"-"`
	ModelID       string                    `json:"id"`
	BaseModelID   string                    `json:"base_model_id"`
	Name          string                    `json:"name"`
	Params        interface{}               `json:"params"`
	Meta          *ModelConfigMeta          `json:"meta"`
	AccessControl *ModelConfigAccessControl `json:"access_control"`
	IsActive      bool                      `json:"is_active"`
	CreatedAt     int64                     `json:"created_at"`
	UpdatedAt     int64                     `json:"updated_at"`
}

type AvailableModel struct {
	ModelID             string         `json:"id"`
	ModelType           string         `json:"model_type"`
	Object              string         `json:"object"`
	Created             int64          `json:"created"`
	OwnedBy             string         `json:"owned_by"`
	Active              bool           `json:"active"`
	ContextWindow       int64          `json:"context_window"`
	PublicApps          interface{}    `json:"public_apps"`
	MaxCompletionTokens int64          `json:"max_completion_tokens"`
	Name                string         `json:"name"`
	Remote              *RemoteModel   `json:"remote"`
	Info                *ModelConfigIO `json:"info"`
	Actions             []interface{}  `json:"actions"`
	Tags                []string       `json:"tags"`
	UrlIdx              int            `json:"urlIdx"`
	TotalTokens         string         `json:"total_tokens"`
}
type ChatCompletionMessage struct {
	Role     string `json:"role"`
	Content  string `json:"content"`
	FileUUID string `json:"file_uuid"`
}
type CreateChatCompletionRequest struct {
	ChatID   string `json:"chat_id"`
	Model    string `json:"model"`
	Features struct {
		CodeInterpreter bool `json:"code_interpreter"`
		ImageGeneration bool `json:"image_generation"`
		WebSearch       bool `json:"web_search"`
	} `json:"features"`
	Messages    []*ChatCompletionMessage   `json:"messages"`
	ModelItem   AvailableModel             `json:"model_item"`
	Params      interface{}                `json:"params"`
	SessionID   string                     `json:"session_id"`
	Stream      bool                       `json:"stream"`
	ToolServers []interface{}              `json:"tool_servers"`
	Variables   map[string]interface{}     `json:"variables"`
	TaskID      string                     `json:"-"`
	MsgID       string                     `json:"id"`
	Files       []*ConversationMessageFile `json:"files"`
	StopCh      chan struct{}              `json:"-"`
	User        *UserBaseOP                `json:"-"`
}

type ChatLLMByApiRequest struct {
	ModelId          string                     `json:"-"`
	Messages         []ChatLLMByApiMessage      `json:"messages"`
	Model            string                     `json:"model"`
	ResponseFormat   ChatLLMByApiResponseFormat `json:"response_format"`
	Stop             []string                   `json:"stop"`
	Stream           bool                       `json:"stream"`
	MaxTokens        uint64                     `json:"max_tokens"`
	Temperature      float32                    `json:"temperature"`
	TopP             float32                    `json:"top_p"`
	FrequencyPenalty float32                    `json:"frequency_penalty"`
	PresencePenalty  float32                    `json:"presence_penalty"`
	Seed             *int                       `json:"seed"`
	User             string                     `json:"user"`
}

type ChatLLMByApiResponseFormat struct {
	Type string `json:"type"`
}

type ChatLLMByApiMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatLLMNonStreamByApiResponse struct {
	ID      string                                 `json:"id"`
	Object  string                                 `json:"object"`
	Created int64                                  `json:"created"`
	Model   string                                 `json:"model"`
	Choices []*ChatLLMNonStreamByApiResponseChoice `json:"choices"`
	Usage   *ChatLLMByApiResponseUsage             `json:"usage"`
}

type ChatLLMNonStreamByApiResponseChoice struct {
	Index        int                 `json:"index"`
	Message      ChatLLMByApiMessage `json:"message"`
	FinishReason string              `json:"finish_reason"`
}

type ChatLLMByApiResponseUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type ChatLLMStreamByApiResponse struct {
	ID      string                              `json:"id"`
	Object  string                              `json:"object"`
	Created int64                               `json:"created"`
	Model   string                              `json:"model"`
	Choices []*ChatLLMStreamByApiResponseChoice `json:"choices"`
	Usage   *ChatLLMByApiResponseUsage          `json:"usage"`
}

type ChatLLMStreamByApiResponseChoice struct {
	Index        int                 `json:"index"`
	Delta        ChatLLMByApiMessage `json:"delta"`
	FinishReason string              `json:"finish_reason"`
}

type ChatASRByApiRequest struct {
	ModelId        string    `json:"-"`
	Model          string    `json:"model"`
	File           io.Reader `json:"-"`
	FilePath       string    `json:"-"`
	Language       string    `json:"language"`
	Prompt         string    `json:"prompt"`
	ResponseFormat string    `json:"response_format"`
	Temperature    float32   `json:"temperature"`
}

type ChatASRByApiResponse struct {
	Text string `json:"text"`
}

type ChatTTSByApiRequest struct {
	ModelId        string  `json:"-"`
	Model          string  `json:"model"`
	Input          string  `json:"input"`
	Voice          string  `json:"voice"`
	ResponseFormat string  `json:"response_format"`
	Speed          float64 `json:"speed"`
}

type ChatTTIByApiRequest struct {
	ModelId        string `json:"-"`
	Model          string `json:"model"`
	Prompt         string `json:"prompt"`
	ResponseFormat string `json:"response_format"`
	Size           string `json:"size"`
	User           string `json:"user"`
}

type ChatTTIByApiResponse struct {
	Created int64 `json:"created"`
	Data    struct {
		RevisedPrompt string `json:"revised_prompt"`
		B64Json       string `json:"b64_json"`
	} `json:"data"`
}

type StartChatASRRequest struct {
	File     io.Reader `json:"file"`
	FileName string    `json:"file_name"`
	FileSize int64     `json:"file_size"`
	ModelID  string    `json:"model_id"`
	MimeType string    `json:"mime_type"`
}

type StartChatASRResponse struct {
	UUID    string `json:"id"`
	Content string `json:"content"`
}

type StartChatTTSRequest struct {
	Content string  `json:"content"`
	ModelID string  `json:"model_id"`
	Voice   string  `json:"voice"`
	Speed   float64 `json:"speed"`
}

type StartChatTTSResponse struct {
	UUID string `json:"id"`
	URL  string `json:"url"`
}

type StartChatTTIRequest struct {
	Content        string `json:"content"`
	ModelID        string `json:"model_id"`
	ResponseFormat string `json:"response_format"`
	Size           string `json:"size"`
}

type StartChatTTIResponse struct {
	UUID string `json:"id"`
	URL  string `json:"url"`
}

type ModelApi interface {
	GetAllBaseLLMModels(ctx context.Context) ([]*BaseModel, error)
	GetAllBaseLLMModelsConfig(ctx context.Context) ([]*ModelConfigIO, error)
	CreateLLMModelConfig(ctx context.Context, req *ModelConfigIO) (*ModelConfigIO, error)
	UpdateLLMModelConfig(ctx context.Context, req *ModelConfigIO) (*ModelConfigIO, error)
	ToggleLLMModelConfig(ctx context.Context, modelID string) (*ModelConfigIO, error)
	GetAvailableLLMModels(ctx context.Context) ([]*AvailableModel, error)
	StartChatLLMStream(ctx context.Context, req *CreateChatCompletionRequest) (<-chan string, <-chan error, error)
	ProcessAudioFile(ctx context.Context, user *UserBaseOP, fileInfo *FileInfoMeta) (*FileInfoData, error)
	GenerateTitle(ctx context.Context, req *CreateChatCompletionRequest) (string, error)
	GenerateImage(ctx context.Context, req *CreateChatCompletionRequest) ([]byte, error)
	CreateSpeech(ctx context.Context, user *UserBaseOP, chatId, msgId string) ([]byte, error)
	UpdateConversationContent(ctx context.Context, user *UserBaseOP, conversationUUID, msgID, content, imageUrl string) error
	StartChatASR(ctx context.Context, user *UserBaseOP, req *StartChatASRRequest) (*StartChatASRResponse, error)
	StartChatTTS(ctx context.Context, user *UserBaseOP, req *StartChatTTSRequest) (*StartChatTTSResponse, error)
	StartChatTTI(ctx context.Context, user *UserBaseOP, req *StartChatTTIRequest) (*StartChatTTIResponse, error)
	// 以下用于模拟用户调用api
	MockUserChatUsage(ctx context.Context, key *AppKeyOP, model *TopaiModelsInfoOP, inputTokens, outputTokens, ttft, duration *big.Int) error
	// 以下用于api调用
	ChatLLMStreamByApi(ctx context.Context, userID uint, appKey string, req *ChatLLMByApiRequest) (<-chan ChatLLMStreamByApiResponse, <-chan error, error)
	ChatLLMNonStreamByApi(ctx context.Context, userID uint, appKey string, req *ChatLLMByApiRequest) (*ChatLLMNonStreamByApiResponse, error)
	ChatASRByApi(ctx context.Context, userID uint, appKey string, req *ChatASRByApiRequest) (*ChatASRByApiResponse, error)
	ChatTTSByApi(ctx context.Context, userID uint, appKey string, req *ChatTTSByApiRequest) ([]byte, error)
	ChatTTIByApi(ctx context.Context, userID uint, appKey string, req *ChatTTIByApiRequest) (*ChatTTIByApiResponse, error)
}

var (
	AudioConfigTTS_MODEL  = "MODEL"
	AudioConfigTTS_VOICE  = "VOICE"
	AudioConfigTTS_ENABLE = "ENABLE"
)

type AudioConfigTTS struct {
	// 模型，为空表示使用默认模型
	MODEL string `json:"MODEL"`
	// 音色，为空表示使用默认音色
	VOICE string `json:"VOICE"`
	// 是否启用图片生成
	ENABLE bool `json:"ENABLE"`
}

var (
	AudioConfigASR_MODEL  = "MODEL"
	AudioConfigASR_ENABLE = "ENABLE"
)

type AudioConfigASR struct {
	// 模型，为空表示使用默认模型
	MODEL string `json:"MODEL"`
	// 是否启用语音转文本
	ENABLE bool `json:"ENABLE"`
}

type AudioConfig struct {
	// 语音转文本
	ASR *AudioConfigASR `json:"asr"`
	// 文本转语音
	TTS *AudioConfigTTS `json:"tts"`
}

type AudioVoice struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type AudioBaseModel struct {
	ASR []*AudioModel `json:"asr"`
	TTS []*AudioModel `json:"tts"`
}

type AudioModel struct {
	ID      uint          `json:"-"`
	ModelId string        `json:"id"`
	Voices  []*AudioVoice `json:"voices"`
}

type AudioApi interface {
	GetAudioConfig(ctx context.Context) (*AudioConfig, error)
	UpdateAudioConfig(ctx context.Context, config *AudioConfig) error
	GetAudioVoices(ctx context.Context) ([]*AudioVoice, error)
	GetAudioBaseModels(ctx context.Context) (*AudioBaseModel, error)
	//GetAudioModels(ctx context.Context) ([]*AudioModel, error)
}

var (
	ImageConfigModel         = "MODEL"
	ImageConfigEngine        = "ENGINE"
	ImageConfigConfig        = "CONFIG"
	ImageConfigMaxTextLength = "MAX_TEXT_LENGTH"
	ImageConfigMaxImageSize  = "MAX_IMAGE_SIZE"
	ImageConfigEnable        = "ENABLE"
)

type ImageConfig struct {
	// 选择的模型id
	Model string `json:"MODEL"`
	// 模型分类，model/comfuui
	Engine string `json:"ENGINE"`
	// 模型配置
	Config interface{} `json:"CONFIG"`
	// 最大文本长度
	MaxTextLength int `json:"max_text_length"`
	// 最大图片大小
	MaxImageSize int `json:"max_image_size"`
	// 是否启用图片生成
	ENABLE bool `json:"ENABLE"`
}

type ImageModel struct {
	ID uint `json:"-"`
	// 模型id
	ModelID string `json:"model_id"`
	// 模型分类，model/comfuui
	Engine string `json:"engine"`
	// 模型配置参数
	Params interface{} `json:"params"`
}

type ImageApi interface {
	GetImageConfig(ctx context.Context) (*ImageConfig, error)
	GetImageBaseModels(ctx context.Context) ([]*ImageModel, error)
	UpdateImageConfig(ctx context.Context, config *ImageConfig) error
	//GetImageModels(ctx context.Context) ([]*ImageModel, error)
}

type CreateConversationRequest struct {
	Chat *Conversation `json:"chat"`
}

type UpdateConversationRequest struct {
	Chat *Conversation `json:"chat"`
}

type Conversation struct {
	UUID      string                     `json:"id"`
	Title     string                     `json:"title"`
	Timestamp int64                      `json:"timestamp"` // 注意，此处是毫秒
	Tags      []string                   `json:"tags"`
	History   *ConversationHistory       `json:"history"`
	Messages  []*ConversationMessage     `json:"messages"`
	Params    interface{}                `json:"params"`
	System    string                     `json:"system"`
	Models    []string                   `json:"models"`
	Files     []*ConversationMessageFile `json:"files"`
}

type ConversationMessage struct {
	UUID         string                     `json:"id"`
	ChildrenIds  []string                   `json:"childrenIds"`
	Models       []string                   `json:"models"`
	Model        string                     `json:"model"`
	ParentUUID   string                     `json:"parentId"`
	Role         string                     `json:"role"`
	Content      string                     `json:"content"`
	Done         bool                       `json:"done"`
	Timestamp    int64                      `json:"timestamp"` // 注意，此处是秒
	LastSentence string                     `json:"lastSentence"`
	UserContext  interface{}                `json:"userContext"`
	Files        []*ConversationMessageFile `json:"files"`
}

type ConversationMessageFile struct {
	CollectionName string      `json:"collection_name"`
	Error          string      `json:"error"`
	File           *FileInfoOP `json:"file"`
	FileID         string      `json:"id"`
	ItemId         string      `json:"item_id"`
	Name           string      `json:"name"`
	Size           int64       `json:"size"`
	Status         string      `json:"status"`
	Type           string      `json:"type"`
	URL            string      `json:"url"`
}

type ConversationHistory struct {
	Messages  map[string]*ConversationMessage `json:"messages"`
	CurrentId string                          `json:"currentId"`
}

type ConversationOP struct {
	Chat      *Conversation `json:"chat"`
	Archived  bool          `json:"archived"`
	CreatedAt int64         `json:"created_at"`
	FolderId  interface{}   `json:"folder_id"`
	UUID      string        `json:"id"`
	Meta      interface{}   `json:"meta"`
	Pinned    bool          `json:"pinned"`
	ShareId   interface{}   `json:"share_id"`
	Title     string        `json:"title"`
	UpdatedAt int64         `json:"updated_at"`
	UserId    string        `json:"user_id"`
}

type ConversationListOP struct {
	UUID      string `json:"id"`
	Title     string `json:"title"`
	UpdatedAt int64  `json:"updated_at"`
	CreatedAt int64  `json:"created_at"`
}

type TTIChatSharedOP struct {
	UUID      string   `json:"id"`
	ModelType string   `json:"model_type"`
	ModelID   string   `json:"model_id"`
	Content   string   `json:"content"`
	FileUrls  []string `json:"file_urls"`
	IsShared  bool     `json:"is_shared"`
	CreatedAt int64    `json:"created_at"`
}

type TTIChatSharedListOP struct {
	Count int64              `json:"count"`
	List  []*TTIChatSharedOP `json:"list"`
}

type ConversationApi interface {
	CreateConversation(ctx context.Context, user *UserBaseOP, req *CreateConversationRequest) (*ConversationOP, error)
	UpdateConversation(ctx context.Context, user *UserBaseOP, conversationUUID string, req *UpdateConversationRequest) (*ConversationOP, error)
	GetConversation(ctx context.Context, user *UserBaseOP, conversationUUID string) (*ConversationOP, error)
	GetConversationList(ctx context.Context, user *UserBaseOP, page int, limit int) ([]*ConversationListOP, error)
	DeleteConversation(ctx context.Context, user *UserBaseOP, conversationUUID string) error
	UpdateTTIChatShare(ctx context.Context, user *UserBaseOP, ttiChatUUID string, isShare bool) error
	GetUserTTIChatShared(ctx context.Context, user *UserBaseOP, page int, limit int) (*TTIChatSharedListOP, error)
	GetAllTTIChatShared(ctx context.Context, page int, limit int) (*TTIChatSharedListOP, error)
}

type GetTopaiModelListRequest struct {
	Search        string   `json:"search"`
	Page          int      `json:"page"`
	PageSize      int      `json:"page_size"`
	CategoryIds   []uint   `json:"category_ids"`
	SeriesIds     []uint   `json:"series_ids"`
	ModelTypes    []string `json:"model_types"`
	SupportParams []string `json:"support_params"`
	Providers     []string `json:"providers"`
	PriceFree     bool     `json:"price_free"`
	OrderBy       string   `json:"order_by"`
	OrderType     string   `json:"order_type"`
	ModelIds      []uint   `json:"model_ids"`
}

type TopaiModelCategoryOP struct {
	CategoryName string `json:"category_name"`
	CategoryId   uint   `json:"category_id"`
	BelongTo     string `json:"belong_to"`
}

type TopaiModelsInfoOP struct {
	ModelId       string                  `json:"model_id"`
	ProviderName  string                  `json:"provider_name"`
	ModelName     string                  `json:"model_name"`
	ModelType     string                  `json:"model_type"`
	InputPrice    string                  `json:"input_price"`
	OutputPrice   string                  `json:"output_price"`
	CategoryList  []*TopaiModelCategoryOP `json:"category_list"`
	TotalTokens   string                  `json:"total_tokens"`
	Description   string                  `json:"description"`
	ContextLength string                  `json:"context_length"`
	MaxOutput     string                  `json:"max_output"`
	IsFree        bool                    `json:"is_free"`
	Weight        int                     `json:"-"` // 权重，内部使用
	ShowPicture   string                  `json:"show_picture"`
}

type TopaiModelsInfoDetailOP struct {
	TopaiModelsInfoOP
	SeriesName string                    `json:"series_name"`
	SeriesId   uint                      `json:"series_id"`
	Throughput []interface{}             `json:"throughput"`
	AppUsed    []*TopaiModelAppUsedOP    `json:"app_used"`
	DayPutData []*TopaiModelDayPutDataOP `json:"day_put_data"`
	SampleCode map[string]string         `json:"sample_code"`
	Provider   []*TopaiModelsInfoOP      `json:"provider"`
}

type TopaiModelAppUsedOP struct {
	AppName           string `json:"app_name"`
	AppAvatar         string `json:"app_avatar"`
	AppId             uint   `json:"-"`
	AppUuid           string `json:"app_id"`
	Description       string `json:"description"`
	TotalInputTokens  string `json:"total_input_tokens"`
	TotalOutputTokens string `json:"total_output_tokens"`
	TotalTokens       string `json:"total_tokens"`
	Website           string `json:"website"`
}

type TopaiModelDayPutDataOP struct {
	Day               string `json:"day"`
	Count             int    `json:"count"`
	TotalInputTokens  string `json:"total_input_tokens"`
	TotalOutputTokens string `json:"total_output_tokens"`
	TotalTokens       string `json:"total_tokens"`
}

type TopaiModelSeriesOP struct {
	SeriesName string `json:"series_name"`
	SeriesId   uint   `json:"series_id"`
}

type TopaiModelProviderOP struct {
	ProviderName string `json:"provider_name"`
	ProviderId   uint   `json:"-"`
	ProviderUuid string `json:"provider_id"`
}

type AppKeyOP struct {
	UserId    uint   `json:"-"`
	AppId     uint   `json:"-"`
	AppUuid   string `json:"app_id"`
	AppName   string `json:"app_name"`
	Key       string `json:"key"`
	KeyName   string `json:"key_name"`
	CreatedAt int64  `json:"created_at"`
}

// AdminModelListItem 管理员模型列表项
type AdminModelListItem struct {
	ID               uint   `json:"id"`                // 模型ID
	ModelID          string `json:"model_id"`          // 模型标识符
	ModelName        string `json:"model_name"`        // 模型名称
	ModelVersion     string `json:"model_version"`     // 模型版本
	InputPrice       string `json:"input_price"`       // 输入价格
	OutputPrice      string `json:"output_price"`      // 输出价格
	TokenConsumption string `json:"token_consumption"` // token消耗量
	Status           int    `json:"status"`            // 上下架状态：1上线，0下线，-1删除
	IsOnChain        bool   `json:"is_on_chain"`       // 是否链上模型
	ProviderName     string `json:"provider_name"`     // 提供商名称
	Description      string `json:"description"`       // 描述
	CreatedAt        int64  `json:"created_at"`        // 创建时间
	UpdatedAt        int64  `json:"updated_at"`        // 更新时间
}

// AdminModelListResponse 管理员模型列表响应
type AdminModelListResponse struct {
	Total  int64                 `json:"total"`
	Models []*AdminModelListItem `json:"models"`
}

// AdminModelDetailResponse 管理员模型详情响应
type AdminModelDetailResponse struct {
	AdminModelListItem
	ContextLength uint64 `json:"context_length"` // 上下文长度
	MaxOutput     uint64 `json:"max_output"`     // 最大输出
	Latency       uint64 `json:"latency"`        // 延迟
	Throughput    uint64 `json:"throughput"`     // 吞吐量
	SampleCode    string `json:"sample_code"`    // 示例代码
	ShowPicture   string `json:"show_picture"`   // 展示图片
	ShowVideo     string `json:"show_video"`     // 展示视频
	Weight        uint   `json:"weight"`         // 权重
	SeriesID      uint   `json:"series_id"`      // 系列ID
}

// AdminUpdateModelRequest 管理员更新模型请求
type AdminUpdateModelRequest struct {
	Description   string `json:"description"`              // 描述（链上和非链上模型都可以编辑）
	InputPrice    string `json:"input_price,omitempty"`    // 输入价格（仅非链上模型可编辑）
	OutputPrice   string `json:"output_price,omitempty"`   // 输出价格（仅非链上模型可编辑）
	ContextLength uint64 `json:"context_length,omitempty"` // 上下文长度（仅非链上模型可编辑）
	MaxOutput     uint64 `json:"max_output,omitempty"`     // 最大输出（仅非链上模型可编辑）
	Weight        uint   `json:"weight,omitempty"`         // 权重
}

// AdminProviderListItem 管理员供应商列表项
type AdminProviderListItem struct {
	ID            uint   `json:"id"`             // 供应商ID
	UUID          string `json:"uuid"`           // 供应商UUID
	Name          string `json:"name"`           // 供应商名称
	WalletAddress string `json:"wallet_address"` // 钱包地址
	IsCentralized bool   `json:"is_centralized"` // 是否中心化供应商（wallet_address为空）
	CreatedAt     int64  `json:"created_at"`     // 创建时间
}

// AdminProviderListResponse 管理员供应商列表响应
type AdminProviderListResponse struct {
	Total     int64                    `json:"total"`
	Providers []*AdminProviderListItem `json:"providers"`
}

// AdminCreateCentralModelRequest 管理员创建中心化模型请求
type AdminCreateCentralModelRequest struct {
	ProviderID    uint   `json:"provider_id"`
	ModelName     string `json:"model_name"`
	ModelVersion  string `json:"model_version"`
	ModelType     string `json:"model_type"`
	InputPrice    string `json:"input_price"`
	OutputPrice   string `json:"output_price"`
	SeriesID      uint   `json:"series_id"`
	CategoryIDs   []uint `json:"category_ids"`
	Description   string `json:"description"`
	ContextLength uint64 `json:"context_length"`
	MaxOutput     uint64 `json:"max_output"`
	Weight        uint   `json:"weight"`
}

type TopaiApi interface {
	GetTopaiModelList(ctx context.Context, req *GetTopaiModelListRequest) ([]*TopaiModelsInfoOP, error)
	GetUsedCategoryList(ctx context.Context) ([]*TopaiModelCategoryOP, error)
	GetSeriesList(ctx context.Context) ([]*TopaiModelSeriesOP, error)
	GetProviderList(ctx context.Context) ([]*TopaiModelProviderOP, error)
	GetModelTypeList(ctx context.Context) ([]string, error)
	GetSupportParamList(ctx context.Context) ([]string, error)
	GetTopaiModelDetail(ctx context.Context, modelId string) (*TopaiModelsInfoDetailOP, error)
	GetAppKeyList(ctx context.Context) ([]*AppKeyOP, error)
	GetTopaiModelListByTokenSort(ctx context.Context, req *GetTopaiModelListRequest) ([]*TopaiModelsInfoOP, error)
	SyncOnchainModel(ctx context.Context, chainUrl, aiModelsAddress string) error
	AutoGenerateAppKey(ctx context.Context) error
	// 管理员模型管理接口
	AdminGetModelList(ctx context.Context, page, pageSize int, status []int, isOnChain int) (*AdminModelListResponse, error)
	AdminGetModelDetail(ctx context.Context, modelID string) (*AdminModelDetailResponse, error)
	AdminUpdateModel(ctx context.Context, modelID string, req *AdminUpdateModelRequest) error
	AdminUpdateModelStatus(ctx context.Context, modelID string, action string) error
}

type UserAppInfoOP struct {
	AppId          uint   `json:"-"`
	AppUuid        string `json:"app_id"`
	AppName        string `json:"app_name"`
	AppAccountId   string `json:"app_account_id"`
	AppLogo        string `json:"app_logo"`
	AppDescription string `json:"app_description"`
	AppWebsite     string `json:"app_website"`
	AppStatus      int    `json:"app_status"`
	AppCreatedAt   int64  `json:"app_created_at"`
}

type UserAppApi interface {
	GetUserAppList(ctx context.Context, userID uint) ([]*UserAppInfoOP, error)
	GetUserAppDetail(ctx context.Context, userID uint, AppUuid string) (*UserAppInfoOP, error)
	CreateUserApp(ctx context.Context, userID uint, appInfo *UserAppInfoOP) error
	UpdateUserApp(ctx context.Context, userID uint, appInfo *UserAppInfoOP) error
	DeleteUserApp(ctx context.Context, userID uint, appUuid string) error
	GetUserAllAppKeyList(ctx context.Context, userID uint) ([]*AppKeyOP, error)
	GetUserAppKeyList(ctx context.Context, userID uint, appUuid string) ([]*AppKeyOP, error)
	CreateUserAppKey(ctx context.Context, userID uint, appUuid string, keyName string) (*AppKeyOP, error)
	DeleteUserAppKey(ctx context.Context, userID uint, appUuid string, keyName string) error
	GetAppByApiKey(ctx context.Context, apiKey string) (*AppKeyOP, error)
}
