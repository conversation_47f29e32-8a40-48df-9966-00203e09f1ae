---
description: 
globs: 
alwaysApply: false
---
---
description: Security Implementation Guidelines
globs: **/security/**/*.go, **/auth/**/*.go
alwaysApply: false
---

When implementing security:

1. Authentication
- Implement JWT handling
- Support multiple auth methods
- Secure token storage
- Handle token refresh

Required auth implementation:
```go
type AuthService struct {
    secretKey    []byte
    tokenTTL     time.Duration
    refreshTTL   time.Duration
    blacklist    TokenBlacklist
}

func (a *AuthService) GenerateTokenPair(claims Claims) (*TokenPair, error) {
    accessToken, err := a.generateAccessToken(claims)
    if err != nil {
        return nil, fmt.Errorf("generate access token: %w", err)
    }
    
    refreshToken, err := a.generateRefreshToken(claims)
    if err != nil {
        return nil, fmt.Errorf("generate refresh token: %w", err)
    }
    
    return &TokenPair{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
    }, nil
}
```

2. Authorization
- Implement RBAC
- Support policy-based access control
- Handle permission inheritance
- Cache authorization decisions

Required RBAC structure:
```go
type RBACManager struct {
    roles       map[string]Role
    permissions map[string]Permission
    cache       Cache
}

func (r *RBACManager) CheckPermission(ctx context.Context, userID string, resource string, action string) (bool, error) {
    // Check cache first
    if allowed, found := r.checkCache(userID, resource, action); found {
        return allowed, nil
    }
    
    roles, err := r.getUserRoles(ctx, userID)
    if err != nil {
        return false, fmt.Errorf("get user roles: %w", err)
    }
    
    for _, role := range roles {
        if role.HasPermission(resource, action) {
            r.cacheDecision(userID, resource, action, true)
            return true, nil
        }
    }
    
    r.cacheDecision(userID, resource, action, false)
    return false, nil
}
```

3. Data Protection
- Implement encryption at rest
- Use TLS for transport
- Handle sensitive data
- Implement secure deletion

4. Security Headers
- Set CORS policies
- Implement CSP
- Enable HSTS
- Configure XSS protection

5. Input Validation
- Sanitize all inputs
- Validate content types
- Handle file uploads securely
- Prevent injection attacks

6. Audit Logging
- Log security events
- Include context information
- Implement log retention
- Support log analysis
