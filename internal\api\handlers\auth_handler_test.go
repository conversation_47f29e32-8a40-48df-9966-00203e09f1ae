package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestMain 设置测试环境
func TestMain(m *testing.M) {
	gin.SetMode(gin.TestMode)
	m.Run()
}

// MockServiceAPI Mock服务接口
type MockServiceAPI struct {
	mock.Mock
}

// AuthHandlerTestEnv 认证处理器测试环境
type AuthHandlerTestEnv struct {
	T       *testing.T
	Router  *gin.Engine
	Handler *AuthHandler
	Service *MockServiceAPI
}

// NewAuthHandlerTestEnv 创建认证处理器测试环境
func NewAuthHandlerTestEnv(t *testing.T) *AuthHandlerTestEnv {
	router := gin.New()
	svc := &MockServiceAPI{}
	handler := &AuthHandler{
		// 根据实际AuthHandler结构设置字段
		// service: svc,
	}

	env := &AuthHandlerTestEnv{
		T:       t,
		Router:  router,
		Handler: handler,
		Service: svc,
	}

	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *AuthHandlerTestEnv) Cleanup() {
	e.Service.AssertExpectations(e.T)
}

// TestAuthHandler_SignUp 测试用户注册
func TestAuthHandler_SignUp(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*AuthHandlerTestEnv)
		requestBody    interface{}
		expectedStatus int
		expectedBody   map[string]interface{}
		verify         func(*AuthHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功注册用户",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望用户注册成功
				// env.Service.On("SignUp", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SignUpRequest")).
				//     Return(SignUpResponse{UserID: 1, Token: "test_token"}, nil)
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"username": "testuser",
				"password": "password123",
			},
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"code": float64(0),
				"msg":  "success",
			},
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
				assert.Equal(t, "success", response["msg"])
			},
		},
		{
			name: "TC2-邮箱已存在",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望邮箱已存在错误
				// env.Service.On("SignUp", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SignUpRequest")).
				//     Return(nil, errors.New("email already exists"))
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"username": "testuser",
				"password": "password123",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.NotEqual(t, float64(0), response["code"])
			},
		},
		{
			name: "TC3-无效的请求体",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为请求体解析会失败
			},
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-缺少必需字段",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"email": "<EMAIL>",
				// 缺少username和password
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC5-密码太短",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"username": "testuser",
				"password": "123", // 太短
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC6-无效邮箱格式",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"email":    "invalid-email",
				"username": "testuser",
				"password": "password123",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewAuthHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/auth/signup", env.Handler.SignUp)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			var bodyReader *bytes.Reader
			if str, ok := tt.requestBody.(string); ok {
				bodyReader = bytes.NewReader([]byte(str))
			} else {
				jsonBody, _ := json.Marshal(tt.requestBody)
				bodyReader = bytes.NewReader(jsonBody)
			}

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/auth/signup", bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestAuthHandler_SignIn 测试用户登录
func TestAuthHandler_SignIn(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*AuthHandlerTestEnv)
		requestBody    interface{}
		expectedStatus int
		verify         func(*AuthHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功登录",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望登录成功
				// env.Service.On("SignIn", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SignInRequest")).
				//     Return(SignInResponse{Token: "test_token", UserID: 1}, nil)
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-用户不存在",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望用户不存在错误
				// env.Service.On("SignIn", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SignInRequest")).
				//     Return(nil, errors.New("user not found"))
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "password123",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC3-密码错误",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望密码错误
				// env.Service.On("SignIn", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("SignInRequest")).
				//     Return(nil, errors.New("invalid password"))
			},
			requestBody: map[string]interface{}{
				"email":    "<EMAIL>",
				"password": "wrongpassword",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC4-缺少邮箱",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"password": "password123",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC5-缺少密码",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"email": "<EMAIL>",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewAuthHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/auth/signin", env.Handler.SignIn)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/auth/signin", bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestAuthHandler_RefreshToken 测试刷新Token
func TestAuthHandler_RefreshToken(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*AuthHandlerTestEnv)
		requestBody    interface{}
		expectedStatus int
		verify         func(*AuthHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功刷新Token",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望刷新成功
				// env.Service.On("RefreshToken", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("string")).
				//     Return(RefreshTokenResponse{Token: "new_token"}, nil)
			},
			requestBody: map[string]interface{}{
				"refresh_token": "valid_refresh_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-无效的刷新Token",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望无效Token错误
				// env.Service.On("RefreshToken", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("string")).
				//     Return(nil, errors.New("invalid refresh token"))
			},
			requestBody: map[string]interface{}{
				"refresh_token": "invalid_refresh_token",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC3-过期的刷新Token",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望过期Token错误
				// env.Service.On("RefreshToken", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("string")).
				//     Return(nil, errors.New("refresh token expired"))
			},
			requestBody: map[string]interface{}{
				"refresh_token": "expired_refresh_token",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewAuthHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/auth/refresh", env.Handler.RefreshToken)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/auth/refresh", bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestAuthHandler_Logout 测试用户登出
func TestAuthHandler_Logout(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*AuthHandlerTestEnv)
		headers        map[string]string
		expectedStatus int
		verify         func(*AuthHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功登出",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望登出成功
				// env.Service.On("Logout", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("string")).
				//     Return(nil)
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-无效Token",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望无效Token错误
				// env.Service.On("Logout", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("string")).
				//     Return(errors.New("invalid token"))
			},
			headers: map[string]string{
				"Authorization": "Bearer invalid_token",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC3-缺少Authorization头",
			setup: func(env *AuthHandlerTestEnv) {
				// 不需要设置Mock，因为中间件会处理
			},
			headers:        map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewAuthHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/auth/logout", env.Handler.Logout)

			// 执行setup
			tt.setup(env)

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/auth/logout", nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestAuthHandler_GetProfile 测试获取用户档案
func TestAuthHandler_GetProfile(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*AuthHandlerTestEnv)
		headers        map[string]string
		expectedStatus int
		verify         func(*AuthHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取用户档案",
			setup: func(env *AuthHandlerTestEnv) {
				// Mock期望获取档案成功
				// env.Service.On("GetProfile", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("uint")).
				//     Return(UserProfile{ID: 1, Email: "<EMAIL>"}, nil)
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-无效Token",
			setup: func(env *AuthHandlerTestEnv) {
				// 中间件会处理无效Token
			},
			headers: map[string]string{
				"Authorization": "Bearer invalid_token",
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *AuthHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewAuthHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/auth/profile", env.Handler.GetProfile)

			// 执行setup
			tt.setup(env)

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, "/auth/profile", nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// BenchmarkAuthHandler_SignUp 注册性能基准测试
func BenchmarkAuthHandler_SignUp(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewAuthHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.POST("/auth/signup", env.Handler.SignUp)

	requestBody := map[string]interface{}{
		"email":    "<EMAIL>",
		"username": "benchuser",
		"password": "password123",
	}

	jsonBody, _ := json.Marshal(requestBody)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		bodyReader := bytes.NewReader(jsonBody)
		req := httptest.NewRequest(http.MethodPost, "/auth/signup", bodyReader)
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}

// BenchmarkAuthHandler_SignIn 登录性能基准测试
func BenchmarkAuthHandler_SignIn(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewAuthHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.POST("/auth/signin", env.Handler.SignIn)

	requestBody := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "password123",
	}

	jsonBody, _ := json.Marshal(requestBody)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		bodyReader := bytes.NewReader(jsonBody)
		req := httptest.NewRequest(http.MethodPost, "/auth/signin", bodyReader)
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}
