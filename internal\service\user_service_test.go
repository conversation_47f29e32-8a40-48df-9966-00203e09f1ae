package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestUserService_NewUserService 测试创建用户服务
func TestUserService_NewUserService(t *testing.T) {
	db := &repository.DB{}
	service := NewUserService(context.Background(), db)

	assert.NotNil(t, service)
	assert.Equal(t, db, service.repo)
}

// TestUserService_GetUser 测试获取用户
func TestUserService_GetUser(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		setup   func(*testutil.MockDependencies)
		want    *UserBaseOP
		wantErr bool
	}{
		{
			name:   "TC1-成功获取用户",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				userToken := &repository.UserToken{
					ID:     1,
					UserID: 1,
					Token:  "test-token",
				}
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
				}
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(userToken, nil)
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).Return(user, nil)
			},
			want: &UserBaseOP{
				ID:         1,
				IdentityID: "user-1",
				Name:       "testuser",
				Email:      "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name:   "TC2-用户不存在",
			userID: 999,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "TC3-数据库错误",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(nil, errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewUserService(ctx, &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			})

			// 调用实际存在的GetUserByToken方法进行测试
			got, err := service.GetUserByToken(ctx, "test-token")

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.IdentityID, got.IdentityID)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_GetUserByIdentityID 测试通过身份ID获取用户
func TestUserService_GetUserByIdentityID(t *testing.T) {
	tests := []struct {
		name       string
		identityID string
		setup      func(*testutil.MockDependencies)
		want       *UserBaseOP
		wantErr    bool
	}{
		{
			name:       "TC1-成功获取用户",
			identityID: "user-1",
			setup: func(mocks *testutil.MockDependencies) {
				userToken := &repository.UserToken{
					ID:     1,
					UserID: 1,
					Token:  "test-token",
				}
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
				}
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(userToken, nil)
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).Return(user, nil)
			},
			want: &UserBaseOP{
				ID:         1,
				IdentityID: "user-1",
				Name:       "testuser",
				Email:      "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name:       "TC2-用户不存在",
			identityID: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:       "TC3-数据库错误",
			identityID: "user-1",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "test-token").Return(nil, errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewUserService(ctx, &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			})

			// 调用实际存在的GetUserByToken方法进行测试
			got, err := service.GetUserByToken(ctx, "test-token")

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.IdentityID, got.IdentityID)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_CreateUser 测试创建用户
func TestUserService_CreateUser(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		password string
		userName string
		role     string
		setup    func(*testutil.MockDependencies)
		want     *UserBaseOP
		wantErr  bool
	}{
		{
			name:     "TC1-成功创建用户",
			email:    "<EMAIL>",
			password: "password123",
			userName: "testuser",
			role:     "user",
			setup: func(mocks *testutil.MockDependencies) {
				// 模拟邮箱不存在
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, nil)
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).
					Run(func(args mock.Arguments) {
						arg := args.Get(1).(*repository.User)
						arg.ID = 1
					}).Return(nil)
			},
			want: &UserBaseOP{
				ID:         1,
				IdentityID: "", // Will be set by the service, so we don't check it
				Name:       "testuser",
				Email:      "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name:     "TC2-创建失败",
			email:    "<EMAIL>",
			password: "password123",
			userName: "testuser",
			role:     "user",
			setup: func(mocks *testutil.MockDependencies) {
				// 模拟邮箱不存在
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, nil)
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).Return(errors.New("create failed"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewUserService(ctx, &repository.DB{
				User: mocks.DB.User,
			})

			// UserService没有CreateUser方法，使用AdminCreateUser方法
			got, err := service.AdminCreateUser(ctx, tt.email, tt.password, tt.userName, tt.role)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.NotEmpty(t, got.IdentityID) // Check that IdentityID is not empty but don't check specific value
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_UpdateUser 测试更新用户
func TestUserService_UpdateUser(t *testing.T) {
	tests := []struct {
		name       string
		identityID string
		email      string
		userName   string
		password   string
		profileURL string
		setup      func(*testutil.MockDependencies)
		want       *UserAdminUser
		wantErr    bool
	}{
		{
			name:       "TC1-邮箱已被其他用户使用",
			identityID: "user-1",
			email:      "<EMAIL>",
			userName:   "updateduser",
			password:   "newpassword",
			profileURL: "http://example.com/avatar.jpg",
			setup: func(mocks *testutil.MockDependencies) {
				now := time.Now()
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
					CreatedAt:  &now,
					UpdatedAt:  &now,
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-1").Return(user, nil)
				// 模拟邮箱检查，返回一个不同IdentityID的用户，表示邮箱已被其他用户使用
				existingUser := &repository.User{
					ID:         2,
					IdentityID: "user-2",
					Email:      "<EMAIL>",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").Return(existingUser, nil)
				// 由于邮箱已被其他用户使用，Update 不会被调用
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:       "TC2-成功更新用户",
			identityID: "user-1",
			email:      "<EMAIL>",
			userName:   "updateduser",
			password:   "newpassword",
			profileURL: "http://example.com/avatar.jpg",
			setup: func(mocks *testutil.MockDependencies) {
				now := time.Now()
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
					CreatedAt:  &now,
					UpdatedAt:  &now,
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-1").Return(user, nil)
				// 模拟邮箱检查，返回nil表示邮箱不存在，可以安全使用
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, nil)
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).Return(nil)
			},
			want: &UserAdminUser{
				UserBaseOP: UserBaseOP{
					ID:         1,
					IdentityID: "user-1",
					Name:       "updateduser",
					Email:      "<EMAIL>",
				},
			},
			wantErr: false,
		},
		{
			name:       "TC3-用户不存在",
			identityID: "user-999",
			email:      "<EMAIL>",
			userName:   "updateduser",
			password:   "newpassword",
			profileURL: "http://example.com/avatar.jpg",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-999").Return(nil, nil)
				// 由于用户不存在，GetByEmail 不会被调用
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:       "TC4-更新失败",
			identityID: "user-1",
			email:      "<EMAIL>",
			userName:   "updateduser",
			password:   "newpassword",
			profileURL: "http://example.com/avatar.jpg",
			setup: func(mocks *testutil.MockDependencies) {
				now := time.Now()
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
					CreatedAt:  &now,
					UpdatedAt:  &now,
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-1").Return(user, nil)
				// 模拟邮箱检查，返回nil表示邮箱可用
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, nil)
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).Return(errors.New("update failed"))
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewUserService(ctx, &repository.DB{
				User: mocks.DB.User,
			})

			got, err := service.AdminUpdateUser(ctx, tt.identityID, tt.email, tt.userName, tt.password, tt.profileURL)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.UserBaseOP.ID, got.UserBaseOP.ID)
				assert.Equal(t, tt.want.UserBaseOP.Name, got.UserBaseOP.Name)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_DeleteUser 测试删除用户
func TestUserService_DeleteUser(t *testing.T) {
	tests := []struct {
		name       string
		identityID string
		setup      func(*testutil.MockDependencies)
		wantErr    bool
	}{
		{
			name:       "TC1-成功删除用户",
			identityID: "user-1",
			setup: func(mocks *testutil.MockDependencies) {
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-1").Return(user, nil)
				mocks.DB.User.On("Delete", mock.Anything, uint(1)).Return(nil)
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).Return(nil)
			},
			wantErr: false,
		},
		{
			name:       "TC2-用户不存在",
			identityID: "user-999",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-999").Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name:       "TC3-删除失败",
			identityID: "user-1",
			setup: func(mocks *testutil.MockDependencies) {
				user := &repository.User{
					ID:         1,
					IdentityID: "user-1",
					Username:   "testuser",
					Email:      "<EMAIL>",
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-1").Return(user, nil)
				mocks.DB.User.On("Delete", mock.Anything, uint(1)).Return(errors.New("delete failed"))
				// 由于删除失败，ClearUserToken 不会被调用
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewUserService(ctx, &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			})

			err := service.AdminDeleteUser(ctx, tt.identityID)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_UpdateUserBalance 测试更新用户余额
// 注意：UserService没有UpdateUserBalance方法，此测试已移除
// 用户余额更新功能在UserBalanceService中实现

// TestUserService_RegisterAndGenerateToken 测试用户注册和生成Token
func TestUserService_RegisterAndGenerateToken(t *testing.T) {
	tests := []struct {
		name     string
		username string
		email    string
		password string
		setup    func(*testutil.MockDependencies)
		wantErr  bool
		errType  string
	}{
		{
			name:     "TC1-注册禁用时返回错误",
			username: "testuser",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").
					Return(&repository.SystemConfig{Value: "false"}, nil)
			},
			wantErr: true,
			errType: "register is not allowed",
		},
		{
			name:     "TC2-系统配置获取失败",
			username: "testuser",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").
					Return(nil, errors.New("database error"))
			},
			wantErr: true,
			errType: "database error",
		},
		{
			name:     "TC3-用户创建失败",
			username: "testuser",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").
					Return(&repository.SystemConfig{Value: "true"}, nil)
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "DEFAULT_USER_ROLE").
					Return(&repository.SystemConfig{Value: "user"}, nil)
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, nil)
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(errors.New("user creation failed"))
			},
			wantErr: true,
			errType: "user creation failed",
		},
		{
			name:     "TC4-Token生成失败",
			username: "testuser",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").
					Return(&repository.SystemConfig{Value: "true"}, nil)
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "DEFAULT_USER_ROLE").
					Return(&repository.SystemConfig{Value: "user"}, nil)
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, nil)

				user := &repository.User{
					ID:           1,
					Username:     "testuser",
					Email:        "<EMAIL>",
					PasswordHash: "$2a$10$hash",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
					CreatedAt:    &time.Time{},
					UpdatedAt:    &time.Time{},
				}
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).
					Run(func(args mock.Arguments) {
						arg := args.Get(1).(*repository.User)
						*arg = *user
					}).Return(nil)

				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
				mocks.DB.UserToken.On("GetByToken", mock.Anything, mock.AnythingOfType("string")).
					Return(nil, nil)
				mocks.DB.UserToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserToken")).
					Return(errors.New("token creation failed"))
			},
			wantErr: true,
			errType: "token creation failed",
		},
		{
			name:     "TC5-成功注册用户",
			username: "testuser",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").
					Return(&repository.SystemConfig{Value: "true"}, nil)
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "DEFAULT_USER_ROLE").
					Return(&repository.SystemConfig{Value: "user"}, nil)
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, nil)

				user := &repository.User{
					ID:           1,
					Username:     "testuser",
					Email:        "<EMAIL>",
					PasswordHash: "$2a$10$hash",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
					CreatedAt:    &time.Time{},
					UpdatedAt:    &time.Time{},
				}
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).
					Run(func(args mock.Arguments) {
						arg := args.Get(1).(*repository.User)
						*arg = *user
					}).Return(nil)

				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
				mocks.DB.UserToken.On("GetByToken", mock.Anything, mock.AnythingOfType("string")).
					Return(nil, nil)
				mocks.DB.UserToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserToken")).
					Return(nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 使用包含mock的DB结构
			db := &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
				User:         mocks.DB.User,
				UserToken:    mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			result, err := service.RegisterAndGenerateToken(ctx, tt.username, tt.email, tt.password)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.email, result.Email)
				assert.Equal(t, tt.username, result.Name)
				assert.Equal(t, "user", result.Role)
				assert.NotEmpty(t, result.Token)
				assert.Equal(t, "Bearer", result.TokenType)
			}
		})
	}
}

// TestUserService_Login 测试用户登录
func TestUserService_Login(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		password string
		setup    func(*testutil.MockDependencies)
		wantErr  bool
		errType  string
	}{
		{
			name:     "TC1-用户不存在",
			email:    "<EMAIL>",
			password: "password123",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, nil)
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:     "TC2-密码错误",
			email:    "<EMAIL>",
			password: "wrongpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					Email:        "<EMAIL>",
					PasswordHash: string(hashedPassword),
					Username:     "testuser",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(user, nil)
			},
			wantErr: true,
			errType: "invalid password",
		},
		{
			name:     "TC3-清除Token失败",
			email:    "<EMAIL>",
			password: "correctpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					Email:        "<EMAIL>",
					PasswordHash: string(hashedPassword),
					Username:     "testuser",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(user, nil)
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(errors.New("token deletion failed"))
			},
			wantErr: true,
			errType: "token deletion failed",
		},
		{
			name:     "TC4-Token生成失败",
			email:    "<EMAIL>",
			password: "correctpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					Email:        "<EMAIL>",
					PasswordHash: string(hashedPassword),
					Username:     "testuser",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(user, nil)
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
				mocks.DB.UserToken.On("GetByToken", mock.Anything, mock.AnythingOfType("string")).
					Return(nil, nil)
				mocks.DB.UserToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserToken")).
					Return(errors.New("token creation failed"))
			},
			wantErr: true,
			errType: "token creation failed",
		},
		{
			name:     "TC5-成功登录",
			email:    "<EMAIL>",
			password: "correctpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					Email:        "<EMAIL>",
					PasswordHash: string(hashedPassword),
					Username:     "testuser",
					Role:         repository.UserRoleUser,
					IdentityID:   "identity-123",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(user, nil)
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
				mocks.DB.UserToken.On("GetByToken", mock.Anything, mock.AnythingOfType("string")).
					Return(nil, nil)
				mocks.DB.UserToken.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserToken")).
					Return(nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 使用包含mock的DB结构
			db := &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			result, err := service.Login(ctx, tt.email, tt.password)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.email, result.Email)
				assert.Equal(t, "testuser", result.Name)
				assert.Equal(t, "user", result.Role)
				assert.NotEmpty(t, result.Token)
				assert.Equal(t, "Bearer", result.TokenType)
			}
		})
	}
}

// TestUserService_UpdatePassword 测试更新密码
func TestUserService_UpdatePassword(t *testing.T) {
	tests := []struct {
		name        string
		oldPassword string
		newPassword string
		setup       func(*testutil.MockDependencies)
		wantErr     bool
		errType     string
	}{
		{
			name:        "TC1-获取用户失败",
			oldPassword: "oldpassword",
			newPassword: "newpassword",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(nil, errors.New("user not found"))
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:        "TC2-旧密码错误",
			oldPassword: "wrongpassword",
			newPassword: "newpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					PasswordHash: string(hashedPassword),
				}
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(user, nil)
			},
			wantErr: true,
			errType: "invalid password",
		},
		{
			name:        "TC3-更新密码失败",
			oldPassword: "correctpassword",
			newPassword: "newpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					PasswordHash: string(hashedPassword),
				}
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(user, nil)
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(errors.New("update failed"))
			},
			wantErr: true,
			errType: "update failed",
		},
		{
			name:        "TC4-成功更新密码",
			oldPassword: "correctpassword",
			newPassword: "newpassword",
			setup: func(mocks *testutil.MockDependencies) {
				hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("correctpassword"), bcrypt.DefaultCost)
				user := &repository.User{
					ID:           1,
					PasswordHash: string(hashedPassword),
				}
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(user, nil)
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(nil)
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 使用包含mock的DB结构
			db := &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			userBase := &UserBaseOP{
				ID: 1,
			}

			err := service.UpdatePassword(ctx, userBase, tt.oldPassword, tt.newPassword)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestUserService_GetUserByToken 测试根据Token获取用户
func TestUserService_GetUserByToken(t *testing.T) {
	tests := []struct {
		name    string
		token   string
		setup   func(*testutil.MockDependencies)
		wantErr bool
		errType string
	}{
		{
			name:  "TC1-Token不存在",
			token: "invalid-token",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "invalid-token").
					Return(nil, nil)
			},
			wantErr: true,
			errType: "user token not found",
		},
		{
			name:  "TC2-用户不存在",
			token: "valid-token",
			setup: func(mocks *testutil.MockDependencies) {
				token := &repository.UserToken{
					ID:        1,
					UserID:    1,
					Token:     "valid-token",
					ExpiresAt: time.Now().Add(time.Hour),
				}
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "valid-token").
					Return(token, nil)
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(nil, errors.New("user not found"))
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:  "TC3-成功获取用户",
			token: "valid-token",
			setup: func(mocks *testutil.MockDependencies) {
				token := &repository.UserToken{
					ID:        1,
					UserID:    1,
					Token:     "valid-token",
					ExpiresAt: time.Now().Add(time.Hour),
				}
				user := &repository.User{
					ID:         1,
					Username:   "testuser",
					Email:      "<EMAIL>",
					Role:       repository.UserRoleUser,
					IdentityID: "identity-123",
				}
				mocks.DB.UserToken.On("GetByToken", mock.Anything, "valid-token").
					Return(token, nil)
				mocks.DB.User.On("GetByID", mock.Anything, uint(1)).
					Return(user, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 使用包含mock的DB结构
			db := &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			result, err := service.GetUserByToken(ctx, tt.token)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "testuser", result.Name)
				assert.Equal(t, "<EMAIL>", result.Email)
				assert.Equal(t, "user", result.Role)
				assert.Equal(t, "identity-123", result.IdentityID)
			}
		})
	}
}

// TestUserService_Logout 测试用户登出
func TestUserService_Logout(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		setup   func(*testutil.MockDependencies)
		wantErr bool
		errType string
	}{
		{
			name:   "TC1-清除Token失败",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(errors.New("delete failed"))
			},
			wantErr: true,
			errType: "delete failed",
		},
		{
			name:   "TC2-成功登出",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 使用包含mock的DB结构
			db := &repository.DB{
				UserToken: mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			err := service.Logout(ctx, tt.userID)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// BenchmarkUserService_NewService 创建用户服务性能基准测试
func BenchmarkUserService_NewService(b *testing.B) {
	ctx := context.Background()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewUserService(ctx, &repository.DB{})
		_ = service
	}
}

// TestUserService_GetUserSettings 测试获取用户设置
func TestUserService_GetUserSettings(t *testing.T) {
	t.Parallel()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	ctx := context.Background()
	userID := uint(1)

	// 设置mock
	mocks.DB.UserSetting.On("GetByUserIDAndKey", mock.Anything, userID, "system").Return(nil, nil)

	// 使用包含mock的DB结构
	db := &repository.DB{
		User:        mocks.DB.User,
		UserSetting: mocks.DB.UserSetting,
	}
	service := NewUserService(ctx, db)

	// 测试获取用户设置
	settings, err := service.GetUserSettings(ctx, userID)
	require.NoError(t, err)
	require.NotNil(t, settings)
}

// TestUserService_UpdateUserSettings 测试更新用户设置
func TestUserService_UpdateUserSettings(t *testing.T) {
	t.Parallel()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	ctx := context.Background()
	userID := uint(1)
	settings := &UserSettings{SyStem: "test system"}

	// 设置mock
	mocks.DB.UserSetting.On("GetByUserIDAndKey", mock.Anything, userID, "system").Return(nil, nil)
	mocks.DB.UserSetting.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserSetting")).Return(nil)

	// 使用包含mock的DB结构
	db := &repository.DB{
		User:        mocks.DB.User,
		UserSetting: mocks.DB.UserSetting,
	}
	service := NewUserService(ctx, db)

	// 测试更新用户设置
	err := service.UpdateUserSettings(ctx, userID, settings)
	require.NoError(t, err)
}

// TestUserService_UpdateUserChatRequest 测试更新用户聊天请求
func TestUserService_UpdateUserChatRequest(t *testing.T) {
	t.Parallel()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	ctx := context.Background()
	user := &UserBaseOP{ID: 1, IdentityID: "uid-1"}
	ip := "127.0.0.1"
	req := &UserChatRequest{
		UserID:                  1,
		IP:                      "127.0.0.1",
		ModelType:               "llm",
		AssociatedUUID:          "chat-123",
		ConversationMessageUUID: "msg-123",
		ModelID:                 "gpt-3.5-turbo",
	}

	// 设置mock
	mocks.DB.UserChatRequest.On("Create", mock.Anything, mock.AnythingOfType("*repository.UserChatRequest")).Return(nil)

	// 使用包含mock的DB结构
	db := &repository.DB{
		User:            mocks.DB.User,
		UserChatRequest: mocks.DB.UserChatRequest,
	}
	service := NewUserService(ctx, db)

	// 测试更新用户聊天请求
	err := service.UpdateUserChatRequest(ctx, user, ip, req)
	require.NoError(t, err)
}

// TestUserService_GetDayChatRequest 测试获取每日聊天请求
func TestUserService_GetDayChatRequest(t *testing.T) {
	t.Parallel()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	ctx := context.Background()
	day := "2024-01-01"

	// 设置mock
	requests := []*repository.UserChatRequest{
		{
			UserID:                  1,
			IP:                      "127.0.0.1",
			AssociatedUUID:          "chat-123",
			ConversationMessageUUID: "msg-123",
			ModelID:                 "gpt-3.5-turbo",
		},
	}
	mocks.DB.UserChatRequest.On("GetByCreatedAtPeriod", mock.Anything, "2024-01-01 00:00:00", "2024-01-01 23:59:59").Return(requests, nil)

	// 使用包含mock的DB结构
	db := &repository.DB{
		User:            mocks.DB.User,
		UserChatRequest: mocks.DB.UserChatRequest,
	}
	service := NewUserService(ctx, db)

	// 测试获取每日聊天请求
	result, err := service.GetDayChatRequest(ctx, day)
	require.NoError(t, err)
	require.NotNil(t, result)
	require.Len(t, result, 1)
}

// TestUserService_AdminCreateUser 测试管理员创建用户
func TestUserService_AdminCreateUser(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		password string
		userName string
		role     string
		setup    func(*testutil.MockDependencies)
		wantErr  bool
		errType  string
	}{
		{
			name:     "TC1-管理员创建用户成功",
			email:    "<EMAIL>",
			password: "password123",
			userName: "Admin User",
			role:     "admin",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 检查邮箱不存在
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, nil)
				// Mock 创建用户成功
				mocks.DB.User.On("Create", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(nil)
			},
			wantErr: false,
		},
		{
			name:     "TC2-邮箱已存在",
			email:    "<EMAIL>",
			password: "password123",
			userName: "Existing User",
			role:     "user",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 邮箱已存在
				existingUser := &repository.User{
					ID:         1,
					Email:      "<EMAIL>",
					Username:   "Existing User",
					IdentityID: "existing-id",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(existingUser, nil)
			},
			wantErr: true,
			errType: "email already exists",
		},
		{
			name:     "TC3-数据库错误",
			email:    "<EMAIL>",
			password: "password123",
			userName: "Error User",
			role:     "user",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 数据库错误
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(nil, errors.New("database error"))
			},
			wantErr: true,
			errType: "database error",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			// 使用包含mock的DB结构
			db := &repository.DB{
				User: mocks.DB.User,
			}
			service := NewUserService(ctx, db)

			user, err := service.AdminCreateUser(ctx, tt.email, tt.password, tt.userName, tt.role)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
				assert.Equal(t, tt.email, user.Email)
				assert.Equal(t, tt.userName, user.Name)
				assert.Equal(t, tt.role, user.Role)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_AdminUpdateUser 测试管理员更新用户
func TestUserService_AdminUpdateUser(t *testing.T) {
	tests := []struct {
		name            string
		identityID      string
		email           string
		userName        string
		password        string
		profileImageURL string
		setup           func(*testutil.MockDependencies)
		wantErr         bool
		errType         string
	}{
		{
			name:       "TC1-管理员更新用户成功",
			identityID: "user-123",
			email:      "<EMAIL>",
			userName:   "Updated User",
			password:   "newpassword123",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:           1,
					IdentityID:   "user-123",
					Email:        "<EMAIL>",
					Username:     "Old User",
					PasswordHash: "oldhash",
					CreatedAt:    &time.Time{},
					UpdatedAt:    &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 检查邮箱可用 - 返回当前用户表示邮箱可用（同一个用户）
				updatedUser := &repository.User{
					ID:           1,
					IdentityID:   "user-123",
					Email:        "<EMAIL>",
					Username:     "Old User",
					PasswordHash: "oldhash",
					CreatedAt:    &time.Time{},
					UpdatedAt:    &time.Time{},
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(updatedUser, nil)
				// Mock 更新用户成功
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(nil)
			},
			wantErr: false,
		},
		{
			name:       "TC2-用户不存在",
			identityID: "nonexistent",
			email:      "<EMAIL>",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 用户不存在
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "nonexistent").
					Return(nil, errors.New("user not found"))
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:       "TC3-邮箱已被其他用户使用",
			identityID: "user-123",
			email:      "<EMAIL>",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Old User",
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 邮箱已被其他用户使用
				otherUser := &repository.User{
					ID:         2,
					IdentityID: "other-user",
					Email:      "<EMAIL>",
				}
				mocks.DB.User.On("GetByEmail", mock.Anything, "<EMAIL>").
					Return(otherUser, nil)
			},
			wantErr: true,
			errType: "email already exists",
		},
		{
			name:       "TC4-无更新内容",
			identityID: "user-123",
			email:      "",
			userName:   "",
			password:   "",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Test User",
					CreatedAt:  &time.Time{},
					UpdatedAt:  &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
			},
			wantErr: true,
			errType: "no update",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			// 使用包含mock的DB结构
			db := &repository.DB{
				User: mocks.DB.User,
			}
			service := NewUserService(ctx, db)

			user, err := service.AdminUpdateUser(ctx, tt.identityID, tt.email, tt.userName, tt.password, tt.profileImageURL)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_AdminUpdateUserRole 测试管理员更新用户角色
func TestUserService_AdminUpdateUserRole(t *testing.T) {
	tests := []struct {
		name       string
		identityID string
		role       string
		setup      func(*testutil.MockDependencies)
		wantErr    bool
		errType    string
	}{
		{
			name:       "TC1-管理员更新用户角色成功",
			identityID: "user-123",
			role:       "admin",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Test User",
					Role:       repository.UserRoleUser,
					CreatedAt:  &time.Time{},
					UpdatedAt:  &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 更新用户成功
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(nil)
			},
			wantErr: false,
		},
		{
			name:       "TC2-用户不存在",
			identityID: "nonexistent",
			role:       "admin",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 用户不存在
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "nonexistent").
					Return(nil, errors.New("user not found"))
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:       "TC3-更新失败",
			identityID: "user-123",
			role:       "admin",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Test User",
					Role:       repository.UserRoleUser,
					CreatedAt:  &time.Time{},
					UpdatedAt:  &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 更新失败
				mocks.DB.User.On("Update", mock.Anything, mock.AnythingOfType("*repository.User")).
					Return(errors.New("update failed"))
			},
			wantErr: true,
			errType: "update failed",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			// 使用包含mock的DB结构
			db := &repository.DB{
				User: mocks.DB.User,
			}
			service := NewUserService(ctx, db)

			user, err := service.AdminUpdateUserRole(ctx, tt.identityID, tt.role)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
				// 注意：AdminUpdateUserRole 返回的 UserAdminUser 结构体中 Role 字段可能为空
				// 因为返回的结构体只设置了 ID 字段
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_AdminDeleteUser 测试管理员删除用户
func TestUserService_AdminDeleteUser(t *testing.T) {
	tests := []struct {
		name       string
		identityID string
		setup      func(*testutil.MockDependencies)
		wantErr    bool
		errType    string
	}{
		{
			name:       "TC1-管理员删除用户成功",
			identityID: "user-123",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Test User",
					CreatedAt:  &time.Time{},
					UpdatedAt:  &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 删除用户成功
				mocks.DB.User.On("Delete", mock.Anything, uint(1)).
					Return(nil)
				// Mock 清除用户Token成功
				mocks.DB.UserToken.On("DeleteByUserID", mock.Anything, uint(1)).
					Return(nil)
			},
			wantErr: false,
		},
		{
			name:       "TC2-用户不存在",
			identityID: "nonexistent",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 用户不存在
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "nonexistent").
					Return(nil, errors.New("user not found"))
			},
			wantErr: true,
			errType: "user not found",
		},
		{
			name:       "TC3-删除用户失败",
			identityID: "user-123",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户
				user := &repository.User{
					ID:         1,
					IdentityID: "user-123",
					Email:      "<EMAIL>",
					Username:   "Test User",
					CreatedAt:  &time.Time{},
					UpdatedAt:  &time.Time{},
				}
				mocks.DB.User.On("GetByIdentityID", mock.Anything, "user-123").
					Return(user, nil)
				// Mock 删除用户失败
				mocks.DB.User.On("Delete", mock.Anything, uint(1)).
					Return(errors.New("delete failed"))
			},
			wantErr: true,
			errType: "delete failed",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			// 使用包含mock的DB结构
			db := &repository.DB{
				User:      mocks.DB.User,
				UserToken: mocks.DB.UserToken,
			}
			service := NewUserService(ctx, db)

			err := service.AdminDeleteUser(ctx, tt.identityID)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserService_AdminGetAllUsers 测试管理员获取所有用户
func TestUserService_AdminGetAllUsers(t *testing.T) {
	tests := []struct {
		name      string
		page      int
		pageSize  int
		order     string
		direction string
		setup     func(*testutil.MockDependencies)
		wantErr   bool
		errType   string
	}{
		{
			name:      "TC1-管理员获取所有用户成功",
			page:      1,
			pageSize:  10,
			order:     "created_at",
			direction: "desc",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取用户列表
				now := time.Now()
				users := []*repository.User{
					{
						ID:         1,
						IdentityID: "user-1",
						Email:      "<EMAIL>",
						Username:   "User 1",
						Role:       repository.UserRoleUser,
						CreatedAt:  &now,
						UpdatedAt:  &now,
					},
					{
						ID:         2,
						IdentityID: "user-2",
						Email:      "<EMAIL>",
						Username:   "User 2",
						Role:       repository.UserRoleAdmin,
						CreatedAt:  &now,
						UpdatedAt:  &now,
					},
				}
				mocks.DB.User.On("List", mock.Anything, 0, 10, "created_at", "desc").
					Return(users, nil)
				// Mock 获取总数
				mocks.DB.User.On("Count", mock.Anything).
					Return(int64(2), nil)
			},
			wantErr: false,
		},
		{
			name:      "TC2-获取用户列表失败",
			page:      1,
			pageSize:  10,
			order:     "created_at",
			direction: "desc",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取总数成功
				mocks.DB.User.On("Count", mock.Anything).
					Return(int64(10), nil)
				// Mock 获取用户列表失败
				mocks.DB.User.On("List", mock.Anything, 0, 10, "created_at", "desc").
					Return(nil, errors.New("database error"))
			},
			wantErr: true,
			errType: "database error",
		},
		{
			name:      "TC3-获取总数失败",
			page:      1,
			pageSize:  10,
			order:     "created_at",
			direction: "desc",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock 获取总数失败
				mocks.DB.User.On("Count", mock.Anything).
					Return(int64(0), errors.New("count error"))
			},
			wantErr: true,
			errType: "count error",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			// 使用包含mock的DB结构
			db := &repository.DB{
				User: mocks.DB.User,
			}
			service := NewUserService(ctx, db)

			result, err := service.AdminGetAllUsers(ctx, tt.page, tt.pageSize, tt.order, tt.direction)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != "" {
					assert.Contains(t, err.Error(), tt.errType)
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotNil(t, result.Users)
			}

			mocks.AssertExpectations(t)
		})
	}
}
