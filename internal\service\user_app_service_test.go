package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestUserAppService_NewUserAppService 测试创建用户应用服务
func TestUserAppService_NewUserAppService(t *testing.T) {
	tests := []struct {
		name       string
		wantNotNil bool
	}{
		{
			name:       "TC1-创建用户应用服务",
			wantNotNil: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			if tt.wantNotNil {
				assert.NotNil(t, service)
			} else {
				assert.Nil(t, service)
			}
		})
	}
}

// TestUserAppService_GetUserAppList 测试获取用户应用列表
func TestUserAppService_GetUserAppList(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:   "TC1-获取用户应用列表成功",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				apps := []*repository.DevAppInfo{
					{
						Id:          1,
						UserId:      1,
						Uuid:        "test-uuid-1",
						Name:        "test-app-1",
						AccountId:   "account-1",
						Logo:        "logo-uuid-1",
						Description: "test description",
						Website:     "https://test.com",
						Status:      1,
						CreatedAt:   &time.Time{},
					},
				}
				file := &repository.File{
					ID:     1,
					UUID:   "logo-uuid-1",
					UserID: 1,
				}
				mocks.DB.DevAppInfo.On("GetAllByUserID", mock.Anything, uint(1)).Return(apps, nil)
				mocks.DB.File.On("GetByUUID", mock.Anything, "logo-uuid-1").Return(file, nil)
			},
			wantErr: false,
		},
		{
			name:   "TC2-数据库错误",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetAllByUserID", mock.Anything, uint(1)).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			result, err := service.GetUserAppList(ctx, tt.userID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
				assert.Equal(t, "test-app-1", result[0].AppName)
			}
		})
	}
}

// TestUserAppService_GetUserAppDetail 测试获取用户应用详情
func TestUserAppService_GetUserAppDetail(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appUuid string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:    "TC1-获取用户应用详情成功",
			userID:  1,
			appUuid: "test-uuid-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:          1,
					UserId:      1,
					Uuid:        "test-uuid-1",
					Name:        "test-app-1",
					AccountId:   "account-1",
					Logo:        "logo-uuid-1",
					Description: "test description",
					Website:     "https://test.com",
					Status:      1,
					CreatedAt:   &time.Time{},
				}
				file := &repository.File{
					ID:     1,
					UUID:   "logo-uuid-1",
					UserID: 1,
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.File.On("GetByUUID", mock.Anything, "logo-uuid-1").Return(file, nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-应用不存在",
			userID:  1,
			appUuid: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "non-existent").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
		{
			name:    "TC3-应用不属于用户",
			userID:  1,
			appUuid: "test-uuid-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 2, // 不同的用户ID
					Uuid:   "test-uuid-1",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			result, err := service.GetUserAppDetail(ctx, tt.userID, tt.appUuid)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "test-app-1", result.AppName)
			}
		})
	}
}

// TestUserAppService_CreateUserApp 测试创建用户应用
func TestUserAppService_CreateUserApp(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appInfo *UserAppInfoOP
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:   "TC1-创建用户应用成功",
			userID: 1,
			appInfo: &UserAppInfoOP{
				AppName:        "test-app",
				AppAccountId:   "account-1",
				AppLogo:        "https://example.com/files/image/logo-uuid/content",
				AppDescription: "test description",
				AppWebsite:     "https://test.com",
			},
			setup: func(mocks *testutil.MockDependencies) {
				file := &repository.File{
					ID:     1,
					UUID:   "logo-uuid",
					UserID: 1,
				}
				mocks.DB.File.On("GetByUUID", mock.Anything, "logo-uuid").Return(file, nil)
				mocks.DB.DevAppInfo.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppInfo")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:   "TC2-创建失败",
			userID: 1,
			appInfo: &UserAppInfoOP{
				AppName: "test-app",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppInfo")).Return(assert.AnError)
			},
			wantErr: true,
		},
		{
			name:   "TC3-无logo创建成功",
			userID: 1,
			appInfo: &UserAppInfoOP{
				AppName:        "test-app-no-logo",
				AppAccountId:   "account-1",
				AppLogo:        "",
				AppDescription: "test description",
				AppWebsite:     "https://test.com",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppInfo")).Return(nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			err := service.CreateUserApp(ctx, tt.userID, tt.appInfo)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestUserAppService_UpdateUserApp 测试更新用户应用
func TestUserAppService_UpdateUserApp(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appInfo *UserAppInfoOP
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:   "TC1-更新用户应用成功",
			userID: 1,
			appInfo: &UserAppInfoOP{
				AppUuid:        "test-uuid-1",
				AppName:        "updated-app",
				AppAccountId:   "account-1",
				AppLogo:        "https://example.com/files/image/logo-uuid/content",
				AppDescription: "updated description",
				AppWebsite:     "https://updated.com",
			},
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Logo:   "old-logo",
				}
				file := &repository.File{
					ID:     1,
					UUID:   "logo-uuid",
					UserID: 1,
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.File.On("GetByUUID", mock.Anything, "logo-uuid").Return(file, nil)
				mocks.DB.DevAppInfo.On("Update", mock.Anything, mock.AnythingOfType("*repository.DevAppInfo")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:   "TC2-应用不存在",
			userID: 1,
			appInfo: &UserAppInfoOP{
				AppUuid: "non-existent",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "non-existent").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			err := service.UpdateUserApp(ctx, tt.userID, tt.appInfo)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestUserAppService_DeleteUserApp 测试删除用户应用
func TestUserAppService_DeleteUserApp(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appUuid string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:    "TC1-删除用户应用成功",
			userID:  1,
			appUuid: "test-uuid-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.DevAppInfo.On("Delete", mock.Anything, uint(1)).Return(nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-应用不存在",
			userID:  1,
			appUuid: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "non-existent").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			err := service.DeleteUserApp(ctx, tt.userID, tt.appUuid)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestUserAppService_GetUserAllAppKeyList 测试获取用户所有应用密钥列表
func TestUserAppService_GetUserAllAppKeyList(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:   "TC1-获取用户所有应用密钥列表成功",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				apps := []*repository.DevAppInfo{
					{
						Id:   1,
						Uuid: "test-uuid-1",
						Name: "test-app-1",
					},
				}
				keys := []*repository.DevAppKey{
					{
						Id:        1,
						DevAppId:  1,
						Key:       "test-key-12345",
						Name:      "test-key",
						CreatedAt: &time.Time{},
					},
				}
				mocks.DB.DevAppInfo.On("GetAllByUserID", mock.Anything, uint(1)).Return(apps, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIds", mock.Anything, []uint{1}).Return(keys, nil)
			},
			wantErr: false,
		},
		{
			name:   "TC2-数据库错误",
			userID: 1,
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetAllByUserID", mock.Anything, uint(1)).Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			result, err := service.GetUserAllAppKeyList(ctx, tt.userID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
				assert.Equal(t, "test-****", result[0].Key)
			}
		})
	}
}

// TestUserAppService_GetUserAppKeyList 测试获取用户应用密钥列表
func TestUserAppService_GetUserAppKeyList(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appUuid string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:    "TC1-获取用户应用密钥列表成功",
			userID:  1,
			appUuid: "test-uuid-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Name:   "test-app-1",
				}
				keys := []*repository.DevAppKey{
					{
						Id:        1,
						DevAppId:  1,
						Key:       "test-key-12345",
						Name:      "test-key",
						CreatedAt: &time.Time{},
					},
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppId", mock.Anything, uint(1)).Return(keys, nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-应用不存在",
			userID:  1,
			appUuid: "non-existent",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "non-existent").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
				File:       mocks.DB.File,
			}
			service := NewUserAppService(db)

			result, err := service.GetUserAppKeyList(ctx, tt.userID, tt.appUuid)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
				assert.Equal(t, "test-****", result[0].Key)
			}
		})
	}
}

// BenchmarkUserAppService_NewService 创建用户应用服务性能基准测试
func BenchmarkUserAppService_NewService(b *testing.B) {
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	db := &repository.DB{
		DevAppInfo: mocks.DB.DevAppInfo,
		DevAppKey:  mocks.DB.DevAppKey,
		File:       mocks.DB.File,
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewUserAppService(db)
		_ = service
	}
}

// BenchmarkUserAppService_GetUserAppList 获取用户应用列表性能基准测试
func BenchmarkUserAppService_GetUserAppList(b *testing.B) {
	ctx := context.Background()
	mocks := testutil.NewMockDependencies()
	defer mocks.Cleanup()

	apps := []*repository.DevAppInfo{
		{
			Id:          1,
			UserId:      1,
			Uuid:        "test-uuid-1",
			Name:        "test-app-1",
			AccountId:   "account-1",
			Logo:        "logo-uuid-1",
			Description: "test description",
			Website:     "https://test.com",
			Status:      1,
			CreatedAt:   &time.Time{},
		},
	}
	file := &repository.File{
		ID:     1,
		UUID:   "logo-uuid-1",
		UserID: 1,
	}

	mocks.DB.DevAppInfo.On("GetAllByUserID", mock.Anything, uint(1)).Return(apps, nil)
	mocks.DB.File.On("GetByUUID", mock.Anything, "logo-uuid-1").Return(file, nil)

	db := &repository.DB{
		DevAppInfo: mocks.DB.DevAppInfo,
		DevAppKey:  mocks.DB.DevAppKey,
		File:       mocks.DB.File,
	}
	service := NewUserAppService(db)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetUserAppList(ctx, 1)
	}
}

// TestUserAppService_CreateUserAppKey 测试创建用户应用密钥
func TestUserAppService_CreateUserAppKey(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appUuid string
		keyName string
		setup   func(*testutil.MockDependencies)
		wantErr bool
		wantKey bool
	}{
		{
			name:    "TC1-创建用户应用密钥成功",
			userID:  1,
			appUuid: "test-uuid-1",
			keyName: "test-key-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Name:   "test-app-1",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndKey", mock.Anything, uint(1), "test-key-1").Return(nil, nil)
				mocks.DB.DevAppKey.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppKey")).
					Run(func(args mock.Arguments) {
						arg := args.Get(1).(*repository.DevAppKey)
						arg.Id = 1
						arg.CreatedAt = &time.Time{}
					}).Return(nil)
			},
			wantErr: false,
			wantKey: true,
		},
		{
			name:    "TC2-应用不存在",
			userID:  1,
			appUuid: "test-uuid-2",
			keyName: "test-key-2",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-2").Return(nil, assert.AnError)
			},
			wantErr: true,
			wantKey: false,
		},
		{
			name:    "TC3-应用不属于用户",
			userID:  1,
			appUuid: "test-uuid-3",
			keyName: "test-key-3",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     2,
					UserId: 2, // 不同的用户ID
					Uuid:   "test-uuid-3",
					Name:   "test-app-3",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-3").Return(app, nil)
			},
			wantErr: true,
			wantKey: false,
		},
		{
			name:    "TC4-密钥名称已存在",
			userID:  1,
			appUuid: "test-uuid-4",
			keyName: "test-key-4",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-4",
					Name:   "test-app-4",
				}
				existingKey := &repository.DevAppKey{
					Id:       1,
					DevAppId: 1,
					UserId:   1,
					Key:      "existing-key",
					Name:     "test-key-4",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-4").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndKey", mock.Anything, uint(1), "test-key-4").Return(existingKey, nil)
			},
			wantErr: true,
			wantKey: false,
		},
		{
			name:    "TC5-创建密钥失败",
			userID:  1,
			appUuid: "test-uuid-5",
			keyName: "test-key-5",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-5",
					Name:   "test-app-5",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-5").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndKey", mock.Anything, uint(1), "test-key-5").Return(nil, nil)
				mocks.DB.DevAppKey.On("Create", mock.Anything, mock.AnythingOfType("*repository.DevAppKey")).Return(assert.AnError)
			},
			wantErr: true,
			wantKey: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
			}
			service := NewUserAppService(db)

			result, err := service.CreateUserAppKey(context.Background(), tt.userID, tt.appUuid, tt.keyName)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.wantKey {
					assert.Equal(t, tt.userID, result.UserId)
					assert.Equal(t, tt.appUuid, result.AppUuid)
					assert.Equal(t, tt.keyName, result.KeyName)
					assert.NotEmpty(t, result.Key)
				}
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserAppService_DeleteUserAppKey 测试删除用户应用密钥
func TestUserAppService_DeleteUserAppKey(t *testing.T) {
	tests := []struct {
		name    string
		userID  uint
		appUuid string
		keyName string
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name:    "TC1-删除用户应用密钥成功",
			userID:  1,
			appUuid: "test-uuid-1",
			keyName: "test-key-1",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Name:   "test-app-1",
				}
				appKey := &repository.DevAppKey{
					Id:       1,
					DevAppId: 1,
					UserId:   1,
					Key:      "test-key-value",
					Name:     "test-key-1",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-1").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndName", mock.Anything, uint(1), "test-key-1").Return(appKey, nil)
				mocks.DB.DevAppKey.On("Delete", mock.Anything, uint(1)).Return(nil)
			},
			wantErr: false,
		},
		{
			name:    "TC2-应用不存在",
			userID:  1,
			appUuid: "test-uuid-2",
			keyName: "test-key-2",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-2").Return(nil, assert.AnError)
			},
			wantErr: true,
		},
		{
			name:    "TC3-应用不属于用户",
			userID:  1,
			appUuid: "test-uuid-3",
			keyName: "test-key-3",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     2,
					UserId: 2, // 不同的用户ID
					Uuid:   "test-uuid-3",
					Name:   "test-app-3",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-3").Return(app, nil)
			},
			wantErr: true,
		},
		{
			name:    "TC4-密钥不存在",
			userID:  1,
			appUuid: "test-uuid-4",
			keyName: "test-key-4",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-4",
					Name:   "test-app-4",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-4").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndName", mock.Anything, uint(1), "test-key-4").Return(nil, nil)
			},
			wantErr: true,
		},
		{
			name:    "TC5-密钥不属于用户",
			userID:  1,
			appUuid: "test-uuid-5",
			keyName: "test-key-5",
			setup: func(mocks *testutil.MockDependencies) {
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-5",
					Name:   "test-app-5",
				}
				appKey := &repository.DevAppKey{
					Id:       1,
					DevAppId: 1,
					UserId:   2, // 不同的用户ID
					Key:      "test-key-value",
					Name:     "test-key-5",
				}
				mocks.DB.DevAppInfo.On("GetByUuid", mock.Anything, "test-uuid-5").Return(app, nil)
				mocks.DB.DevAppKey.On("GetByDevAppIdAndName", mock.Anything, uint(1), "test-key-5").Return(appKey, nil)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
			}
			service := NewUserAppService(db)

			err := service.DeleteUserAppKey(context.Background(), tt.userID, tt.appUuid, tt.keyName)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestUserAppService_GetAppByApiKey 测试通过API密钥获取应用
func TestUserAppService_GetAppByApiKey(t *testing.T) {
	tests := []struct {
		name    string
		apiKey  string
		setup   func(*testutil.MockDependencies)
		wantErr bool
		wantApp bool
	}{
		{
			name:   "TC1-通过API密钥获取应用成功",
			apiKey: "valid-api-key",
			setup: func(mocks *testutil.MockDependencies) {
				appKey := &repository.DevAppKey{
					Id:        1,
					DevAppId:  1,
					UserId:    1,
					Key:       "valid-api-key",
					Name:      "test-key",
					IsDeleted: 0,
					CreatedAt: &time.Time{},
				}
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Name:   "test-app-1",
					Status: repository.DevAppStatusNormal,
				}
				mocks.DB.DevAppKey.On("GetByKey", mock.Anything, "valid-api-key").Return(appKey, nil)
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(app, nil)
			},
			wantErr: false,
			wantApp: true,
		},
		{
			name:   "TC2-API密钥不存在",
			apiKey: "invalid-api-key",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.DevAppKey.On("GetByKey", mock.Anything, "invalid-api-key").Return(nil, nil)
			},
			wantErr: true,
			wantApp: false,
		},
		{
			name:   "TC3-API密钥已删除",
			apiKey: "deleted-api-key",
			setup: func(mocks *testutil.MockDependencies) {
				appKey := &repository.DevAppKey{
					Id:        1,
					DevAppId:  1,
					UserId:    1,
					Key:       "deleted-api-key",
					Name:      "test-key",
					IsDeleted: 1, // 已删除
					CreatedAt: &time.Time{},
				}
				mocks.DB.DevAppKey.On("GetByKey", mock.Anything, "deleted-api-key").Return(appKey, nil)
			},
			wantErr: true,
			wantApp: false,
		},
		{
			name:   "TC4-应用不存在",
			apiKey: "app-not-found-key",
			setup: func(mocks *testutil.MockDependencies) {
				appKey := &repository.DevAppKey{
					Id:        1,
					DevAppId:  1,
					UserId:    1,
					Key:       "app-not-found-key",
					Name:      "test-key",
					IsDeleted: 0,
					CreatedAt: &time.Time{},
				}
				mocks.DB.DevAppKey.On("GetByKey", mock.Anything, "app-not-found-key").Return(appKey, nil)
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(nil, nil)
			},
			wantErr: true,
			wantApp: false,
		},
		{
			name:   "TC5-应用状态异常",
			apiKey: "app-abnormal-key",
			setup: func(mocks *testutil.MockDependencies) {
				appKey := &repository.DevAppKey{
					Id:        1,
					DevAppId:  1,
					UserId:    1,
					Key:       "app-abnormal-key",
					Name:      "test-key",
					IsDeleted: 0,
					CreatedAt: &time.Time{},
				}
				app := &repository.DevAppInfo{
					Id:     1,
					UserId: 1,
					Uuid:   "test-uuid-1",
					Name:   "test-app-1",
					Status: repository.DevAppStatusAbnormal, // 应用状态异常
				}
				mocks.DB.DevAppKey.On("GetByKey", mock.Anything, "app-abnormal-key").Return(appKey, nil)
				mocks.DB.DevAppInfo.On("GetByID", mock.Anything, uint(1)).Return(app, nil)
			},
			wantErr: true,
			wantApp: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			db := &repository.DB{
				DevAppInfo: mocks.DB.DevAppInfo,
				DevAppKey:  mocks.DB.DevAppKey,
			}
			service := NewUserAppService(db)

			result, err := service.GetAppByApiKey(context.Background(), tt.apiKey)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.wantApp {
					assert.Equal(t, tt.apiKey, result.Key)
					assert.NotEmpty(t, result.AppName)
				}
			}

			mocks.AssertExpectations(t)
		})
	}
}
