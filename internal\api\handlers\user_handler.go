package handlers

import (
	"errors"
	"math/big"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
	"topnetwork.ai/topai/chat-webserver/utils"
)

type UserHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewUserHandler(service *service.Api) *UserHandler {
	log := logger.GetLogger("user_handler")
	return &UserHandler{
		service: service,
		log:     log,
	}
}

// @Summary      获取用户列表
// @Description  获取所有用户信息
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Success      200           {object}  []service.UserAdminOP
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users [get]
func (h *UserHandler) AdminGetAllUsers(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	order := c.DefaultQuery("order", "created_at")
	direction := c.DefaultQuery("direction", "asc")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page"))
		return
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page size"))
		return
	}

	order = strings.TrimSpace(order)
	direction = strings.TrimSpace(direction)

	if order != "created_at" && order != "last_active_at" && order != "name" && order != "email" && order != "role" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid order"))
		return
	}

	if direction != "asc" && direction != "desc" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid direction"))
		return
	}

	users, err := h.service.User.AdminGetAllUsers(c.Request.Context(), page, pageSize, order, direction)
	if err != nil {
		h.log.Error("failed to get users", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get users", err)
		return
	}
	api.Success(c, users)
}

type CreateUserRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Name     string `json:"name" binding:"required"`
	Role     string `json:"role" binding:"required"`
}

// @Summary      创建用户
// @Description  创建新用户
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        request       body      CreateUserRequest  true  "创建用户请求"
// @Success      200           {object}  service.UserAdminOP
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users [post]
func (h *UserHandler) AdminCreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	// 检查角色是否合法
	if !repository.UserRole(req.Role).Check() {
		api.Fail(c, api.CodeClientError, "invalid role", errors.New("invalid role"))
		return
	}

	// 检查邮箱是否满足规则
	if !utils.IsEmailValid(req.Email) {
		api.Fail(c, api.CodeClientError, "invalid email", errors.New("invalid email"))
		return
	}

	// 检查用户名是否满足规则
	if !utils.IsUsernameValid(req.Name) {
		api.Fail(c, api.CodeClientError, "invalid username", errors.New("invalid username"))
		return
	}

	// 检查密码是否满足规则
	if !utils.IsPasswordValid(req.Password) {
		api.Fail(c, api.CodeClientError, "invalid password", errors.New("invalid password"))
		return
	}

	user, err := h.service.User.AdminCreateUser(c.Request.Context(), req.Email, req.Password, req.Name, req.Role)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to create user", err)
		return
	}
	api.Success(c, user)
}

type UpdateUserRequest struct {
	Email           string `json:"email" binding:"omitempty,email"`
	Name            string `json:"name" binding:"omitempty"`
	Password        string `json:"password" binding:"omitempty"`
	ProfileImageURL string `json:"profile_image_url" binding:"omitempty"`
}

// @Summary      更新用户
// @Description  更新用户信息
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        id            path      string  true  "用户ID"
// @Param        request       body      UpdateUserRequest  true  "更新用户请求"
// @Success      200           {object}  service.UserAdminOP
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/{id} [put]
func (h *UserHandler) AdminUpdateUser(c *gin.Context) {
	identityID := c.Param("id")
	if identityID == "" {
		api.Fail(c, api.CodeClientError, "invalid user id", errors.New("user id is required"))
		return
	}

	var req UpdateUserRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	if req.Email != "" {
		// 检查邮件是否满足规则
		if !utils.IsEmailValid(req.Email) {
			api.Fail(c, api.CodeClientError, "invalid email", errors.New("invalid email"))
			return
		}
	}
	if req.Name != "" {
		// 检查用户名是否满足规则
		if !utils.IsUsernameValid(req.Name) {
			api.Fail(c, api.CodeClientError, "invalid username", errors.New("invalid username"))
			return
		}
	}

	user, err := h.service.User.AdminUpdateUser(c.Request.Context(), identityID, req.Email, req.Name, req.Password, req.ProfileImageURL)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to update user", err)
		return
	}
	api.Success(c, user)
}

type UpdateUserRoleRequest struct {
	IdentityID string `json:"id" binding:"required"`
	Role       string `json:"role" binding:"required"`
}

// @Summary      更新用户角色
// @Description  更新用户角色
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        request       body      UpdateUserRoleRequest  true  "更新用户角色请求"
// @Success      200           {object}  service.UserAdminOP
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/update/role [post]
func (h *UserHandler) AdminUpdateUserRole(c *gin.Context) {
	req := UpdateUserRoleRequest{}

	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	if !repository.UserRole(req.Role).Check() {
		api.Fail(c, api.CodeClientError, "invalid role", errors.New("invalid role"))
		return
	}

	user, err := h.service.User.AdminUpdateUserRole(c.Request.Context(), req.IdentityID, req.Role)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to update user role", err)
		return
	}
	api.Success(c, user)
}

// @Summary      删除用户
// @Description  删除指定用户
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        id            path      string  true  "用户ID"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/{id} [delete]
func (h *UserHandler) AdminDeleteUser(c *gin.Context) {
	identityID := c.Param("id")
	if identityID == "" {
		api.Fail(c, api.CodeClientError, "invalid user id", errors.New("user id is required"))
		return
	}

	if err := h.service.User.AdminDeleteUser(c.Request.Context(), identityID); err != nil {
		api.Fail(c, api.CodeServerError, "failed to delete user", err)
		return
	}
	api.Success(c, true)
}

// @Summary      获取用户设置
// @Description  获取用户设置
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Success      200           {object}  service.UserSettings
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/user/settings [get]
func (h *UserHandler) GetUserSettings(c *gin.Context) {
	user, _ := c.Get("user")

	userOP, _ := user.(*service.UserBaseOP)

	settings, err := h.service.User.GetUserSettings(c.Request.Context(), userOP.ID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get user settings", err)
		return
	}
	api.Success(c, settings)
}

// @Summary      更新用户设置
// @Description  更新用户设置
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        request       body      service.UserSettings  true  "更新用户设置请求"
// @Success      200           {object}  service.UserSettings
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/user/settings/update [post]
func (h *UserHandler) UpdateUserSettings(c *gin.Context) {
	var req service.UserSettings
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	user, _ := c.Get("user")

	userOP, _ := user.(*service.UserBaseOP)

	err := h.service.User.UpdateUserSettings(c.Request.Context(), userOP.ID, &req)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to update user settings", err)
		return
	}
	api.Success(c, req)
}

type GiftBalanceRequest struct {
	ToUserID string `json:"to_user_id"`
	Amount   int    `json:"amount"`
}

// @Summary      管理员转赠余额
// @Description  管理员转赠余额
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        request       body      GiftBalanceRequest  true  "转赠余额请求"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/users/gift/balance [post]
func (h *UserHandler) AdminGiftBalance(c *gin.Context) {
	var req GiftBalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	if req.Amount <= 0 {
		api.Fail(c, api.CodeClientError, "invalid amount", errors.New("amount must be greater than 0"))
		return
	}
	amount := big.NewInt(int64(req.Amount))

	err := h.service.UserBalance.AdminGiftBalance(c.Request.Context(), req.ToUserID, amount, "admin gift balance")
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to gift balance", err)
		return
	}
	api.Success(c, true)
}

// @Summary      获取用户余额
// @Description  获取用户余额
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Success      200           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/user/balance [get]
// @Param        Authorization  header    string  true  "Bearer token"
func (h *UserHandler) GetUserBalance(c *gin.Context) {
	user, _ := c.Get("user")
	userOP, _ := user.(*service.UserBaseOP)

	balance, err := h.service.UserBalance.GetUserBalance(c.Request.Context(), userOP.ID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get user balance", err)
		return
	}

	// 保留小数点后两位
	balanceInt, _ := big.NewFloat(0).SetString(balance)
	balanceInt.Quo(balanceInt, big.NewFloat(1e18))
	balanceStr := utils.FormatPrice(balanceInt, 5)

	api.Success(c, balanceStr)
}

// @Summary      获取用户余额记录
// @Description  获取用户余额记录
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Success      200           {object}  service.UserBalanceRecordOP
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/user/balance/record [get]
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        page           query     int     false  "页码"
// @Param        page_size      query     int     false  "每页数量"
func (h *UserHandler) GetUserBalanceRecord(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page"))
		return
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page size"))
		return
	}

	user, _ := c.Get("user")
	userOP, _ := user.(*service.UserBaseOP)

	records, err := h.service.UserBalance.GetUserBalanceRecord(c.Request.Context(), userOP.ID, page, pageSize)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get user balance record", err)
		return
	}
	// 处理金额，保留小数点后5位
	for _, record := range records.Records {
		amount, _ := big.NewFloat(0).SetString(record.Amount)
		amount.Quo(amount, big.NewFloat(1e18))
		record.Amount = utils.FormatPrice(amount, 5)
	}
	api.Success(c, records)
}

// @Summary      获取用户充值地址
// @Description  获取用户充值地址
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Success      200           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/user/recharge_address [get]
// @Param        Authorization  header    string  true  "Bearer token"
func (h *UserHandler) GetUserShadowAddress(c *gin.Context) {
	user, _ := c.Get("user")
	userOP, _ := user.(*service.UserBaseOP)

	address, err := h.service.UserBalance.GetUserShadowAddress(c.Request.Context(), userOP.ID)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to get user shadow address", err)
		return
	}
	api.Success(c, address)
}

func (h *UserHandler) Close() {
	// 清理资源
}

// @Summary      管理员查看用户列表（带余额信息）
// @Description  获取所有用户信息，包含当前余额和累计充值
// @Tags         管理员-用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true   "Bearer token"
// @Param        page          query     int     false  "页码，默认1"
// @Param        page_size     query     int     false  "每页数量，默认10"
// @Param        order         query     string  false  "排序字段，默认created_at"
// @Param        direction     query     string  false  "排序方向，asc或desc，默认asc"
// @Success      200           {object}  service.AdminUsersListResponse
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/users/list [get]
func (h *UserHandler) AdminGetUsersWithBalance(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	order := c.DefaultQuery("order", "created_at")
	direction := c.DefaultQuery("direction", "asc")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page"))
		return
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid page_size"))
		return
	}

	// 验证排序字段
	allowedOrders := []string{"created_at", "updated_at", "email", "username"}
	validOrder := false
	for _, allowed := range allowedOrders {
		if order == allowed {
			validOrder = true
			break
		}
	}
	if !validOrder {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid order field"))
		return
	}

	if direction != "asc" && direction != "desc" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("invalid direction"))
		return
	}

	users, err := h.service.User.AdminGetUsersWithBalance(c.Request.Context(), page, pageSize, order, direction)
	if err != nil {
		h.log.Error("failed to get users with balance", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get users", err)
		return
	}
	api.Success(c, users)
}

// AdminGiftBalanceRequest 管理员赠送余额请求
type AdminGiftBalanceRequest struct {
	UserIdentityID string  `json:"user_identity_id" binding:"required"` // 用户身份ID
	Amount         float64 `json:"amount" binding:"required,gt=0"`      // 赠送金额
	Description    string  `json:"description"`                         // 描述
}

// @Summary      管理员赠送用户余额
// @Description  管理员向指定用户赠送余额
// @Tags         管理员-用户管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string                   true  "Bearer token"
// @Param        request       body      AdminGiftBalanceRequest  true  "赠送余额请求"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/users/gift-balance [post]
func (h *UserHandler) AdminGiftUserBalance(c *gin.Context) {
	var req AdminGiftBalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	if req.Amount <= 0 {
		api.Fail(c, api.CodeClientError, "invalid amount", errors.New("amount must be greater than 0"))
		return
	}

	// 转换金额为大整数（保留6位小数）
	amount := big.NewInt(int64(req.Amount * 1000000))

	err := h.service.UserBalance.AdminGiftBalance(c.Request.Context(), req.UserIdentityID, amount, req.Description)
	if err != nil {
		h.log.Error("failed to gift balance", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to gift balance", err)
		return
	}

	api.Success(c, gin.H{"message": "余额赠送成功"})
}
