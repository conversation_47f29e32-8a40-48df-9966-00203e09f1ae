package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestImageService_NewImageService 测试创建图像服务
func TestImageService_NewImageService(t *testing.T) {
	tests := []struct {
		name       string
		wantNotNil bool
	}{
		{
			name:       "TC1-创建图像服务成功",
			wantNotNil: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			service := NewImageService(ctx, &repository.DB{})

			if tt.wantNotNil {
				assert.NotNil(t, service)
			} else {
				assert.Nil(t, service)
			}
		})
	}
}

// TestImageService_GetImageConfig 测试获取图像配置
func TestImageService_GetImageConfig(t *testing.T) {
	tests := []struct {
		name        string
		setup       func(*testutil.MockDependencies)
		description string
		wantErr     bool
	}{
		{
			name: "TC1-获取图像配置成功",
			setup: func(mocks *testutil.MockDependencies) {
				configs := []*repository.SystemConfig{
					{
						ID:       1,
						Category: "image",
						Key:      "MODEL",
						Value:    "dall-e-3",
					},
					{
						ID:       2,
						Category: "image",
						Key:      "ENGINE",
						Value:    "model",
					},
					{
						ID:       3,
						Category: "image",
						Key:      "MAX_TEXT_LENGTH",
						Value:    "4000",
					},
					{
						ID:       4,
						Category: "image",
						Key:      "MAX_IMAGE_SIZE",
						Value:    "1024",
					},
					{
						ID:       5,
						Category: "image",
						Key:      "ENABLE",
						Value:    "true",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(configs, nil)
			},
			description: "测试获取图像配置成功",
			wantErr:     false,
		},
		{
			name: "TC2-数据库错误",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(nil, assert.AnError)
			},
			description: "测试数据库错误情况",
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			db := &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			}
			service := NewImageService(ctx, db)

			result, err := service.GetImageConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "dall-e-3", result.Model)
				assert.Equal(t, "model", result.Engine)
				assert.Equal(t, 4000, result.MaxTextLength)
				assert.Equal(t, 1024, result.MaxImageSize)
				assert.True(t, result.ENABLE)
			}
		})
	}
}

// TestImageService_GetImageBaseModels 测试获取图像基础模型
func TestImageService_GetImageBaseModels(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    []*ImageModel
		wantErr bool
	}{
		{
			name: "TC1-成功获取图片基础模型",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock model extensions
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "tti-model-1",
					},
					{
						Id:           2,
						TopaiModelId: 2,
						ModelId:      "tti-model-2",
					},
					{
						Id:           3,
						TopaiModelId: 3,
						ModelId:      "llm-model-1", // 非TTI模型
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)

				// Mock models
				models := []*repository.TopaiModel{
					{
						Id:        1,
						ModelType: repository.RemoterModelTypeTTI.String(),
					},
					{
						Id:        2,
						ModelType: repository.RemoterModelTypeTTI.String(),
					},
					{
						Id:        3,
						ModelType: repository.RemoterModelTypeLLM.String(), // 非TTI模型
					},
				}
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1, 2, 3}).Return(models, nil)
			},
			want: []*ImageModel{
				{
					ID:      1,
					ModelID: "tti-model-1",
					Engine:  "",
					Params:  nil,
				},
				{
					ID:      2,
					ModelID: "tti-model-2",
					Engine:  "",
					Params:  nil,
				},
			},
			wantErr: false,
		},
		{
			name: "TC2-获取模型扩展信息失败",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-获取模型信息失败",
			setup: func(mocks *testutil.MockDependencies) {
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "tti-model-1",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC4-没有TTI模型",
			setup: func(mocks *testutil.MockDependencies) {
				modelExts := []*repository.TopaiModelExt{
					{
						Id:           1,
						TopaiModelId: 1,
						ModelId:      "llm-model-1",
					},
				}
				mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)

				models := []*repository.TopaiModel{
					{
						Id:        1,
						ModelType: repository.RemoterModelTypeLLM.String(), // 非TTI模型
					},
				}
				mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(models, nil)
			},
			want:    []*ImageModel{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewImageService(ctx, &repository.DB{
				TopaiModelExt: mocks.DB.TopaiModelExt,
				TopaiModel:    mocks.DB.TopaiModel,
			})

			got, err := service.GetImageBaseModels(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestImageService_UpdateImageConfig 测试更新图像配置
func TestImageService_UpdateImageConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *ImageConfig
		setup       func(*testutil.MockDependencies)
		description string
		wantErr     bool
	}{
		{
			name: "TC1-更新图像配置成功",
			config: &ImageConfig{
				Model:         "dall-e-3",
				Engine:        "model",
				Config:        map[string]interface{}{"quality": "hd"},
				MaxTextLength: 4000,
				MaxImageSize:  1024,
				ENABLE:        true,
			},
			setup: func(mocks *testutil.MockDependencies) {
				configs := []*repository.SystemConfig{
					{
						ID:       1,
						Category: "image",
						Key:      "MODEL",
						Value:    "dall-e-2",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(configs, nil)
				mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
				mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			description: "测试更新图像配置成功",
			wantErr:     false,
		},
		{
			name: "TC2-数据库错误",
			config: &ImageConfig{
				Model:         "dall-e-3",
				Engine:        "model",
				MaxTextLength: 4000,
				MaxImageSize:  1024,
				ENABLE:        true,
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(nil, assert.AnError)
			},
			description: "测试数据库错误情况",
			wantErr:     true,
		},
		{
			name:   "TC3-空配置",
			config: &ImageConfig{},
			setup: func(mocks *testutil.MockDependencies) {
				configs := []*repository.SystemConfig{}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(configs, nil)
				mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
			},
			description: "测试更新空配置成功",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			tt.setup(mocks)

			// 创建带有RunWithTx mock的DB
			db := &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			}

			// 为RunWithTx提供mock实现
			originalRunWithTx := db.RunWithTx
			db.RunWithTx = func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
				// 在事务中执行函数，使用mock的DB
				return fn(ctx, &repository.DB{
					SystemConfig: mocks.DB.SystemConfig,
				})
			}
			defer func() {
				db.RunWithTx = originalRunWithTx
			}()

			service := NewImageService(ctx, db)

			err := service.UpdateImageConfig(ctx, tt.config)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestImageService_Integration 测试图像服务集成功能
func TestImageService_Integration(t *testing.T) {
	t.Run("TC1-完整工作流", func(t *testing.T) {
		t.Parallel()

		ctx := context.Background()
		mocks := testutil.NewMockDependencies()
		defer mocks.Cleanup()

		// 设置mock
		configs := []*repository.SystemConfig{
			{
				ID:       1,
				Category: "image",
				Key:      "MODEL",
				Value:    "dall-e-3",
			},
		}
		modelExts := []*repository.TopaiModelExt{
			{
				Id:           1,
				TopaiModelId: 1,
				ModelId:      "dall-e-3",
			},
		}
		models := []*repository.TopaiModel{
			{
				Id:        1,
				ModelType: "tti",
			},
		}

		mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "image").Return(configs, nil)
		mocks.DB.TopaiModelExt.On("GetOnlineAll", mock.Anything).Return(modelExts, nil)
		mocks.DB.TopaiModel.On("GetByIds", mock.Anything, []uint{1}).Return(models, nil)
		mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
		mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		db := &repository.DB{
			SystemConfig:  mocks.DB.SystemConfig,
			TopaiModelExt: mocks.DB.TopaiModelExt,
			TopaiModel:    mocks.DB.TopaiModel,
		}

		// 为RunWithTx提供mock实现
		originalRunWithTx := db.RunWithTx
		db.RunWithTx = func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
			// 在事务中执行函数，使用mock的DB
			return fn(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			})
		}
		defer func() {
			db.RunWithTx = originalRunWithTx
		}()

		service := NewImageService(ctx, db)

		// 测试获取配置
		config, err1 := service.GetImageConfig(ctx)
		assert.NoError(t, err1)
		assert.NotNil(t, config)

		// 测试获取基础模型
		imageModels, err2 := service.GetImageBaseModels(ctx)
		assert.NoError(t, err2)
		assert.NotNil(t, imageModels)

		// 测试更新配置
		updateConfig := &ImageConfig{
			Model:         "dall-e-3",
			Engine:        "model",
			Config:        map[string]interface{}{"quality": "standard"},
			MaxTextLength: 1000,
			MaxImageSize:  512,
			ENABLE:        true,
		}
		err3 := service.UpdateImageConfig(ctx, updateConfig)
		assert.NoError(t, err3)

		// 测试完成 - 所有方法调用都不应该panic
		assert.True(t, true, "All image service methods executed without panic")
	})
}

// BenchmarkImageService_NewService 创建图像服务性能基准测试
func BenchmarkImageService_NewService(b *testing.B) {
	ctx := context.Background()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewImageService(ctx, &repository.DB{})
		_ = service
	}
}

// BenchmarkImageService_GetImageConfig 获取图像配置性能基准测试
func BenchmarkImageService_GetImageConfig(b *testing.B) {
	ctx := context.Background()
	service := NewImageService(ctx, &repository.DB{})

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetImageConfig(ctx)
	}
}

// BenchmarkImageService_GetImageBaseModels 获取图像基础模型性能基准测试
func BenchmarkImageService_GetImageBaseModels(b *testing.B) {
	ctx := context.Background()
	service := NewImageService(ctx, &repository.DB{})

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetImageBaseModels(ctx)
	}
}

// BenchmarkImageService_UpdateImageConfig 更新图像配置性能基准测试
func BenchmarkImageService_UpdateImageConfig(b *testing.B) {
	ctx := context.Background()
	service := NewImageService(ctx, &repository.DB{})

	config := &ImageConfig{
		Model:         "dall-e-3",
		Engine:        "model",
		Config:        map[string]interface{}{"quality": "standard"},
		MaxTextLength: 1000,
		MaxImageSize:  512,
		ENABLE:        true,
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = service.UpdateImageConfig(ctx, config)
	}
}
