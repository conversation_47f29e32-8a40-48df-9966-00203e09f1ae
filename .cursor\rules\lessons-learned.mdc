---
description: 
globs: 
alwaysApply: false
---
---
description: Stores important patterns, preferences, and project intelligence, living document that grows smarter as progress happens
globs: 
alwaysApply: false
---

# 经验教训规则

## 1. 经验记录规范

### 1.1 经验分类
- 技术经验：技术实现、架构设计、性能优化等
- 项目管理：进度控制、资源分配、风险管理等
- 团队协作：沟通协作、知识分享、团队建设等
- 流程优化：开发流程、测试流程、部署流程等
- 业务理解：需求分析、业务规则、用户体验等

### 1.2 经验记录格式
```json
{
  "id": "LL-************",
  "title": "经验标题",
  "category": "technical|project|team|process|business",
  "description": "经验描述",
  "context": {
    "project": "项目名称",
    "phase": "项目阶段",
    "date": "2024-03-21",
    "author": "记录人"
  },
  "impact": {
    "positive": ["正面影响1", "正面影响2"],
    "negative": ["负面影响1", "负面影响2"]
  },
  "recommendations": ["建议1", "建议2"],
  "status": "draft|reviewed|published"
}
```

### 1.3 经验记录位置
- 经验库：`docs/lessons_learned/`
- 知识库：Confluence、Wiki 等
- 团队分享：定期会议、技术分享等

## 2. 经验总结流程

### 2.1 经验收集
1. 日常记录
2. 项目总结
3. 问题分析
4. 团队反馈

### 2.2 经验分析
1. 分类整理
2. 价值评估
3. 优先级排序
4. 行动计划

### 2.3 经验分享
1. 文档编写
2. 团队评审
3. 知识传播
4. 实践应用

### 2.4 经验跟踪
1. 效果评估
2. 持续改进
3. 定期更新
4. 团队反馈

## 3. 经验文档模板

### 3.1 技术经验模板
```markdown
# 技术经验：优化数据库查询性能

## 背景
- 项目：Open WebUI 后端
- 时间：2024-03-21
- 作者：张三

## 问题描述
- 现象：数据库查询响应慢
- 影响：用户体验下降
- 范围：所有用户查询

## 解决方案
- 优化方案：
  1. 添加索引
  2. 优化 SQL 语句
  3. 使用缓存
- 实施步骤：
  1. 分析慢查询
  2. 设计优化方案
  3. 实施优化
  4. 测试验证

## 效果评估
- 性能提升：50%
- 资源消耗：减少 30%
- 用户反馈：明显改善

## 经验总结
- 成功因素：
  1. 准确的性能分析
  2. 合理的优化方案
  3. 充分的测试验证
- 改进空间：
  1. 提前预防
  2. 持续监控
  3. 定期优化

## 建议
- 技术建议：
  1. 建立性能基准
  2. 定期性能分析
  3. 优化方案评审
- 流程建议：
  1. 性能测试流程
  2. 优化评审流程
  3. 监控告警流程
```

### 3.2 项目管理经验模板
```markdown
# 项目管理经验：风险管理

## 背景
- 项目：Open WebUI 后端
- 时间：2024-03-21
- 作者：李四

## 问题描述
- 现象：项目进度延迟
- 影响：交付延期
- 范围：整个项目

## 解决方案
- 优化方案：
  1. 风险识别
  2. 风险评估
  3. 风险应对
- 实施步骤：
  1. 建立风险清单
  2. 制定应对策略
  3. 监控风险状态
  4. 调整项目计划

## 效果评估
- 进度控制：明显改善
- 风险处理：及时有效
- 团队协作：更加顺畅

## 经验总结
- 成功因素：
  1. 早期风险识别
  2. 有效沟通机制
  3. 灵活应对策略
- 改进空间：
  1. 风险预警机制
  2. 资源调配策略
  3. 团队协作方式

## 建议
- 管理建议：
  1. 建立风险库
  2. 定期风险评估
  3. 团队风险意识
- 流程建议：
  1. 风险评审流程
  2. 应对策略流程
  3. 监控反馈流程
```

## 4. 经验管理工具

### 4.1 文档工具
- Confluence
- Wiki
- Notion
- Docsify

### 4.2 协作工具
- Jira
- Trello
- Asana
- Monday

### 4.3 知识管理
- GitBook
- Read the Docs
- Docusaurus
- VuePress

## 5. 经验管理最佳实践

### 5.1 经验收集
- 及时记录
- 分类整理
- 价值评估
- 优先级排序

### 5.2 经验分享
- 定期会议
- 技术分享
- 文档更新
- 团队培训

### 5.3 经验应用
- 实践验证
- 效果评估
- 持续改进
- 团队反馈

### 5.4 经验维护
- 定期更新
- 质量检查
- 版本控制
- 团队协作

## 6. 项目经验记录

### 6.1 API 接口设计与实现经验
```markdown
# 技术经验：API 接口规范更新与管理

## 背景
- 项目：Open WebUI 后端
- 时间：2024-03-22
- 作者：开发团队

## 问题描述
- 现象：API 接口规范从原来的 4 组扩展到 15 组
- 影响：增加了开发工作量和复杂度
- 范围：整个后端开发团队

## 解决方案
- 优化方案：
  1. 接口分组管理
  2. 优先级排序
  3. 分阶段实现
  4. 保持架构一致性
- 实施步骤：
  1. 分析新的 API 接口规范
  2. 按功能和依赖关系分组
  3. 设定优先级
  4. 调整开发计划
  5. 分阶段实现

## 效果评估
- 计划合理性：提高
- 开发效率：保持稳定
- 系统一致性：得到保障

## 经验总结
- 成功因素：
  1. 清晰的接口分组
  2. 合理的优先级设置
  3. 保持架构一致性
  4. 分阶段实现策略
- 改进空间：
  1. 提前规划 API 演进路径
  2. 增强前后端接口协议
  3. 构建更完善的测试机制

## 建议
- 技术建议：
  1. 建立 API 设计规范
  2. API 版本控制策略
  3. 接口文档即时更新
  4. 自动化接口测试
- 流程建议：
  1. API 变更审查流程
  2. 接口兼容性评估
  3. 接口废弃策略
  4. 前后端协作机制
```

### 6.2 模块划分与依赖管理经验
```markdown
# 项目管理经验：模块化设计与接口分组

## 背景
- 项目：Open WebUI 后端
- 时间：2024-03-22
- 作者：项目管理团队

## 问题描述
- 现象：API 接口数量大幅增加，功能模块扩展
- 影响：增加了项目复杂度和管理难度
- 范围：整个项目团队

## 解决方案
- 优化方案：
  1. 功能模块化设计
  2. API 接口分组
  3. 优先级管理
  4. 资源调配优化
- 实施步骤：
  1. 重新梳理系统功能边界
  2. 按领域划分模块
  3. 按功能相关性分组 API
  4. 制定分阶段实现计划
  5. 调整资源分配

## 效果评估
- 开发可管理性：显著提高
- 团队协作：更加高效
- 进度管理：更加精准

## 经验总结
- 成功因素：
  1. 清晰的功能边界
  2. 合理的模块划分
  3. 明确的责任分配
  4. 灵活的资源调配
- 改进空间：
  1. 模块间依赖管理
  2. 接口变更协调
  3. 测试覆盖策略

## 建议
- 管理建议：
  1. 构建模块责任矩阵
  2. 建立跨模块协调机制
  3. 灵活的资源调配策略
- 流程建议：
  1. 模块设计评审流程
  2. 接口变更协调流程
  3. 集成测试策略
  4. 渐进式交付方法
```