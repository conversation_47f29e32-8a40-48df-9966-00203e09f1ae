package middleware

import (
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
)

// CORS 跨域资源共享中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		referer := c.Request.Header.Get("Referer")
		// log.Info("origin", zap.String("origin", origin), zap.String("referer", referer))
		// log.Info("c.Request.Header", zap.Any("c.Request.Header", c.Request.Header))
		// log.Info("c.Request.URL", zap.String("c.Request.URL", c.Request.Host))

		// 优化 Origin 提取逻辑
		if origin == "" && referer != "" {
			u, err := url.Parse(referer)
			if err == nil {
				origin = u.Scheme + "://" + u.Host
			}
		}

		// 仅在跨域时设置 CORS 头
		if origin != "" {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
			c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			c.Header("Access-Control-Allow-Headers", strings.Join([]string{
				"Origin", "Content-Type", "Content-Length", "Accept-Encoding",
				"X-CSRF-Token", "Authorization", "X-API-Key", "Accept",
				"Cache-Control", "X-Requested-With", "Upgrade", "Connection",
				"Sec-WebSocket-Key", "Sec-WebSocket-Version", "Sec-WebSocket-Protocol", "Referer",
			}, ", "))
			c.Header("Access-Control-Allow-Credentials", "true")
			c.Header("Access-Control-Expose-Headers", strings.Join([]string{
				"Content-Length", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers",
				"Cache-Control", "Content-Language", "Content-Type",
			}, ", "))
			c.Header("Access-Control-Max-Age", "86400")

			// WebSocket 升级处理（避免覆盖已有头）
			if c.Request.Header.Get("Upgrade") == "websocket" {
				c.Header("Connection", "Upgrade")
				c.Header("Upgrade", "websocket")
				// 如果是socket.io ,删除Origin，以防验证失败
				c.Request.Header.Del("Origin")
			}
		}

		// 处理 OPTIONS 预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
