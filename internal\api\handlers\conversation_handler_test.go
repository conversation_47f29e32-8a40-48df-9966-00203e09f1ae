package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// ConversationHandlerTestEnv 对话处理器测试环境
type ConversationHandlerTestEnv struct {
	T       *testing.T
	Router  *gin.Engine
	Handler *ConversationHandler
	Service *MockServiceAPI
}

// NewConversationHandlerTestEnv 创建对话处理器测试环境
func NewConversationHandlerTestEnv(t *testing.T) *ConversationHandlerTestEnv {
	router := gin.New()
	svc := &MockServiceAPI{}
	handler := &ConversationHandler{
		// 根据实际ConversationHandler结构设置字段
		// service: svc,
	}

	env := &ConversationHandlerTestEnv{
		T:       t,
		Router:  router,
		Handler: handler,
		Service: svc,
	}

	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *ConversationHandlerTestEnv) Cleanup() {
	e.Service.AssertExpectations(e.T)
}

// TestConversationHandler_CreateConversation 测试创建对话
func TestConversationHandler_CreateConversation(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ConversationHandlerTestEnv)
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		verify         func(*ConversationHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功创建对话",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望创建对话成功
				// env.Service.On("CreateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("CreateConversationRequest")).
				//     Return(ConversationResponse{ID: "conv-123", Title: "New Conversation"}, nil)
			},
			requestBody: map[string]interface{}{
				"title":    "Test Conversation",
				"model_id": "gpt-3.5-turbo",
				"system":   "You are a helpful assistant.",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
				assert.NotNil(t, response["data"])
			},
		},
		{
			name: "TC2-缺少必需字段",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为验证会失败
			},
			requestBody: map[string]interface{}{
				"title": "Test Conversation",
				// 缺少model_id
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC3-无效的模型ID",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望模型不存在错误
				// env.Service.On("CreateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("CreateConversationRequest")).
				//     Return(nil, errors.New("model not found"))
			},
			requestBody: map[string]interface{}{
				"title":    "Test Conversation",
				"model_id": "invalid-model",
				"system":   "You are a helpful assistant.",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-未授权访问",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为中间件会处理
			},
			requestBody: map[string]interface{}{
				"title":    "Test Conversation",
				"model_id": "gpt-3.5-turbo",
			},
			headers:        map[string]string{},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC5-空标题使用默认值",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望使用默认标题创建
				// env.Service.On("CreateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("CreateConversationRequest")).
				//     Return(ConversationResponse{ID: "conv-124", Title: "New Conversation"}, nil)
			},
			requestBody: map[string]interface{}{
				"model_id": "gpt-3.5-turbo",
				"system":   "You are a helpful assistant.",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewConversationHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.POST("/conversations", env.Handler.CreateConversation)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			req := httptest.NewRequest(http.MethodPost, "/conversations", bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestConversationHandler_GetConversations 测试获取对话列表
func TestConversationHandler_GetConversations(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ConversationHandlerTestEnv)
		queryParams    map[string]string
		headers        map[string]string
		expectedStatus int
		verify         func(*ConversationHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取对话列表",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望获取对话列表成功
				// env.Service.On("GetUserConversations", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("GetConversationsRequest")).
				//     Return(ConversationsResponse{Conversations: []Conversation{...}}, nil)
			},
			queryParams: map[string]string{
				"page":     "1",
				"pageSize": "20",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
				assert.NotNil(t, response["data"])
			},
		},
		{
			name: "TC2-使用默认分页参数",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望使用默认参数获取
				// env.Service.On("GetUserConversations", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("GetConversationsRequest")).
				//     Return(ConversationsResponse{Conversations: []Conversation{}}, nil)
			},
			queryParams: map[string]string{},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC3-无效的分页参数",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为参数验证会失败
			},
			queryParams: map[string]string{
				"page":     "-1",
				"pageSize": "0",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
		{
			name: "TC4-分页大小超过限制",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为参数验证会失败
			},
			queryParams: map[string]string{
				"page":     "1",
				"pageSize": "1000", // 超过最大限制
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewConversationHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/conversations", env.Handler.GetConversations)

			// 执行setup
			tt.setup(env)

			// 构建URL和查询参数
			url := "/conversations"
			if len(tt.queryParams) > 0 {
				url += "?"
				first := true
				for key, value := range tt.queryParams {
					if !first {
						url += "&"
					}
					url += fmt.Sprintf("%s=%s", key, value)
					first = false
				}
			}

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestConversationHandler_GetConversation 测试获取单个对话
func TestConversationHandler_GetConversation(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ConversationHandlerTestEnv)
		conversationID string
		headers        map[string]string
		expectedStatus int
		verify         func(*ConversationHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功获取对话",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望获取对话成功
				// env.Service.On("GetConversationByID", mock.AnythingOfType("*gin.Context"), "conv-123").
				//     Return(ConversationResponse{ID: "conv-123", Title: "Test"}, nil)
			},
			conversationID: "conv-123",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
				assert.NotNil(t, response["data"])
			},
		},
		{
			name: "TC2-对话不存在",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望对话不存在错误
				// env.Service.On("GetConversationByID", mock.AnythingOfType("*gin.Context"), "conv-999").
				//     Return(nil, errors.New("conversation not found"))
			},
			conversationID: "conv-999",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusNotFound,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusNotFound, w.Code)
			},
		},
		{
			name: "TC3-无权限访问",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望权限错误
				// env.Service.On("GetConversationByID", mock.AnythingOfType("*gin.Context"), "conv-456").
				//     Return(nil, errors.New("permission denied"))
			},
			conversationID: "conv-456",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC4-空的对话ID",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为参数验证会失败
			},
			conversationID: "",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewConversationHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.GET("/conversations/:id", env.Handler.GetConversation)

			// 执行setup
			tt.setup(env)

			// 创建请求
			url := fmt.Sprintf("/conversations/%s", tt.conversationID)
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestConversationHandler_UpdateConversation 测试更新对话
func TestConversationHandler_UpdateConversation(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ConversationHandlerTestEnv)
		conversationID string
		requestBody    interface{}
		headers        map[string]string
		expectedStatus int
		verify         func(*ConversationHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功更新对话标题",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望更新成功
				// env.Service.On("UpdateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("UpdateConversationRequest")).
				//     Return(nil)
			},
			conversationID: "conv-123",
			requestBody: map[string]interface{}{
				"title": "Updated Title",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-更新系统提示",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望更新成功
				// env.Service.On("UpdateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("UpdateConversationRequest")).
				//     Return(nil)
			},
			conversationID: "conv-123",
			requestBody: map[string]interface{}{
				"system": "You are a coding assistant.",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC3-无权限更新",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望权限错误
				// env.Service.On("UpdateConversation", mock.AnythingOfType("*gin.Context"), mock.AnythingOfType("UpdateConversationRequest")).
				//     Return(errors.New("permission denied"))
			},
			conversationID: "conv-456",
			requestBody: map[string]interface{}{
				"title": "Hacked Title",
			},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name: "TC4-空的更新内容",
			setup: func(env *ConversationHandlerTestEnv) {
				// 不需要Mock，因为没有更新内容
			},
			conversationID: "conv-123",
			requestBody:    map[string]interface{}{},
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusBadRequest,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusBadRequest, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewConversationHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.PUT("/conversations/:id", env.Handler.UpdateConversation)

			// 执行setup
			tt.setup(env)

			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			bodyReader := bytes.NewReader(jsonBody)

			// 创建请求
			url := fmt.Sprintf("/conversations/%s", tt.conversationID)
			req := httptest.NewRequest(http.MethodPut, url, bodyReader)
			req.Header.Set("Content-Type", "application/json")

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestConversationHandler_DeleteConversation 测试删除对话
func TestConversationHandler_DeleteConversation(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*ConversationHandlerTestEnv)
		conversationID string
		headers        map[string]string
		expectedStatus int
		verify         func(*ConversationHandlerTestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-成功删除对话",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望删除成功
				// env.Service.On("DeleteConversation", mock.AnythingOfType("*gin.Context"), "conv-123").
				//     Return(nil)
			},
			conversationID: "conv-123",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusOK,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, float64(0), response["code"])
			},
		},
		{
			name: "TC2-删除不存在的对话",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望对话不存在错误
				// env.Service.On("DeleteConversation", mock.AnythingOfType("*gin.Context"), "conv-999").
				//     Return(errors.New("conversation not found"))
			},
			conversationID: "conv-999",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusNotFound,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusNotFound, w.Code)
			},
		},
		{
			name: "TC3-无权限删除",
			setup: func(env *ConversationHandlerTestEnv) {
				// Mock期望权限错误
				// env.Service.On("DeleteConversation", mock.AnythingOfType("*gin.Context"), "conv-456").
				//     Return(errors.New("permission denied"))
			},
			conversationID: "conv-456",
			headers: map[string]string{
				"Authorization": "Bearer valid_token",
			},
			expectedStatus: http.StatusForbidden,
			verify: func(env *ConversationHandlerTestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewConversationHandlerTestEnv(t)
			defer env.Cleanup()

			// 设置路由
			env.Router.DELETE("/conversations/:id", env.Handler.DeleteConversation)

			// 执行setup
			tt.setup(env)

			// 创建请求
			url := fmt.Sprintf("/conversations/%s", tt.conversationID)
			req := httptest.NewRequest(http.MethodDelete, url, nil)

			// 设置请求头
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// BenchmarkConversationHandler_CreateConversation 创建对话性能基准测试
func BenchmarkConversationHandler_CreateConversation(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewConversationHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.POST("/conversations", env.Handler.CreateConversation)

	requestBody := map[string]interface{}{
		"title":    "Benchmark Conversation",
		"model_id": "gpt-3.5-turbo",
		"system":   "You are a helpful assistant.",
	}

	jsonBody, _ := json.Marshal(requestBody)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		bodyReader := bytes.NewReader(jsonBody)
		req := httptest.NewRequest(http.MethodPost, "/conversations", bodyReader)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}

// BenchmarkConversationHandler_GetConversations 获取对话列表性能基准测试
func BenchmarkConversationHandler_GetConversations(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	env := NewConversationHandlerTestEnv(&testing.T{})
	defer env.Cleanup()

	env.Router.GET("/conversations", env.Handler.GetConversations)

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/conversations?page=1&pageSize=20", nil)
		req.Header.Set("Authorization", "Bearer test_token")

		w := httptest.NewRecorder()
		env.Router.ServeHTTP(w, req)
	}
}
