package middleware

import (
	"bytes"
	"fmt"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 自定义响应写入器，用于捕获响应
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write 重写Write方法
func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// Logger 日志中间件，记录HTTP请求和响应
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		start := time.Now()

		// 捕获请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 捕获响应体
		w := &responseWriter{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
		}
		c.Writer = w

		// 请求处理前的日志
		log.Info("请求开始",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("ip", c.ClientIP()),
			zap.String("user-agent", c.Request.UserAgent()),
		)

		// 请求体日志（可选，可能包含敏感信息）
		if len(requestBody) > 0 && shouldLogRequestBody(c) {
			// 限制请求体日志大小
			if len(requestBody) > 4096 {
				log.Debug("请求体(已截断)",
					zap.String("body", string(requestBody[:4096])+"..."),
				)
			} else {
				log.Debug("请求体", zap.String("body", string(requestBody)))
			}
		}

		// 处理请求
		c.Next()

		// 计算耗时
		elapsed := time.Since(start)

		// 获取响应体（可选）
		var responseBodyStr string
		if shouldLogResponseBody(c) {
			responseBodyStr = w.body.String()
			// 限制响应体日志大小
			if len(responseBodyStr) > 4096 {
				responseBodyStr = responseBodyStr[:4096] + "..."
			}
		}

		// 请求处理后的日志
		log.Info("请求完成",
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.Int("status", c.Writer.Status()),
			zap.String("elapsed", fmt.Sprintf("%v", elapsed)),
			zap.Int("size", c.Writer.Size()),
		)

		// 响应体日志（可选）
		if responseBodyStr != "" {
			log.Debug("响应体", zap.String("body", responseBodyStr))
		}
	}
}

// shouldLogRequestBody 判断是否应该记录请求体
func shouldLogRequestBody(c *gin.Context) bool {
	// 某些路径可能包含敏感信息，不应该记录
	sensitivePaths := map[string]bool{
		"/api/v1/auth/login":    true,
		"/api/v1/auth/register": true,
	}

	return !sensitivePaths[c.FullPath()]
}

// shouldLogResponseBody 判断是否应该记录响应体
func shouldLogResponseBody(c *gin.Context) bool {
	// 某些大型响应不适合记录，比如文件下载
	if c.Writer.Size() > 1024*1024 { // 大于1MB
		return false
	}

	// 某些路径可能包含敏感信息，不应该记录
	sensitivePaths := map[string]bool{
		"/api/v1/auth/me": true,
	}

	return !sensitivePaths[c.FullPath()]
}
