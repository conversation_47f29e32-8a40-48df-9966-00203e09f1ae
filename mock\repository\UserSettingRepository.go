// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserSettingRepository is an autogenerated mock type for the UserSettingRepository type
type UserSettingRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, setting
func (_m *UserSettingRepository) Create(ctx context.Context, setting *repository.UserSetting) error {
	ret := _m.Called(ctx, setting)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserSetting) error); ok {
		r0 = rf(ctx, setting)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByUserID provides a mock function with given fields: ctx, userID
func (_m *UserSettingRepository) FindByUserID(ctx context.Context, userID uint) ([]*repository.UserSetting, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 []*repository.UserSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) ([]*repository.UserSetting, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) []*repository.UserSetting); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUserIDAndKey provides a mock function with given fields: ctx, userID, key
func (_m *UserSettingRepository) GetByUserIDAndKey(ctx context.Context, userID uint, key string) (*repository.UserSetting, error) {
	ret := _m.Called(ctx, userID, key)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserIDAndKey")
	}

	var r0 *repository.UserSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) (*repository.UserSetting, error)); ok {
		return rf(ctx, userID, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint, string) *repository.UserSetting); ok {
		r0 = rf(ctx, userID, key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint, string) error); ok {
		r1 = rf(ctx, userID, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, setting
func (_m *UserSettingRepository) Update(ctx context.Context, setting *repository.UserSetting) error {
	ret := _m.Called(ctx, setting)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserSetting) error); ok {
		r0 = rf(ctx, setting)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserSettingRepository creates a new instance of UserSettingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserSettingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserSettingRepository {
	mock := &UserSettingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
