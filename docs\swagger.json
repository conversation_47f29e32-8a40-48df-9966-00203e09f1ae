{"swagger": "2.0", "info": {"description": "用户登陆，聊天系统", "title": "topai web服务api文档", "contact": {}, "version": "1.0"}, "host": "127.0.0.1:8080", "paths": {"/api/chat/completed": {"post": {"description": "聊天完成", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["websocket"], "summary": "聊天完成", "parameters": [{"description": "聊天完成请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ChatCompletedRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ChatCompletedRequest"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/chat/completions": {"post": {"description": "开始聊天", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["websocket"], "summary": "开始聊天", "parameters": [{"type": "string", "description": "聊天ID", "name": "chat_id", "in": "path", "required": true}, {"description": "聊天请求", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.CreateChatCompletionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/chat/tasks/:chat_id": {"get": {"description": "获取聊天任务", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["websocket"], "summary": "获取聊天任务", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/config": {"get": {"description": "获取配置", "produces": ["application/json"], "tags": ["配置"], "summary": "获取配置", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.SystemCommonConfig"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}}}}, "/api/models/base": {"get": {"description": "获取所有基础模型", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "获取所有基础模型", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.BaseModel"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/openai/audio/speech": {"post": {"description": "文本转语音", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["openai-api"], "summary": "文本转语音", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ChatTTSByApiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "integer"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/openai/audio/transcriptions": {"post": {"description": "语音转文本", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["openai-api"], "summary": "语音转文本", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "model", "name": "model", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ChatASRByApiResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/openai/chat/completions": {"post": {"description": "大语言模型聊天(openai标准流式和非流式)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["openai-api"], "summary": "大语言模型聊天", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ChatLLMByApiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/openai/images/generations": {"post": {"description": "文本转图像", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["openai-api"], "summary": "文本转图像", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ChatTTIByApiRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ChatTTIByApiResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/openai/usage/{request_id}": {"get": {"description": "获取使用情况", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["openai-api"], "summary": "获取使用情况", "parameters": [{"type": "string", "description": "Authorization", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.GetUsageResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1//topai/models/categories/all": {"get": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型管理"], "summary": "全部分类列表", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}}}}}, "/api/v1/admin/models/categories": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员新增分类", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "创建分类", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AdminCreateCategoryRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}}}}, "/api/v1/admin/models/central": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员新增中心化模型", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "创建中心化模型", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.AdminCreateCentralModelRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AdminModelDetailResponse"}}}}}, "/api/v1/admin/models/list": {"get": {"description": "获取所有模型信息，包含输入输出价格、token消耗量、上下架状态等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员查看模型列表", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认20", "name": "page_size", "in": "query"}, {"type": "integer", "description": "状态筛选：1上线，0下线，-1删除，不传则查询上线和下线，多个以逗号分隔", "name": "status", "in": "query"}, {"type": "integer", "description": "是否链上模型筛选，1是，0否，不传则查询所有", "name": "is_onchain", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AdminModelListResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/models/series": {"post": {"consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员新增系列", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "创建系列", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AdminCreateSeriesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.TopaiModelSeriesOP"}}}}}, "/api/v1/admin/models/{model_id}": {"get": {"description": "获取指定模型的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员查看模型详细信息", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "模型ID", "name": "model_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AdminModelDetailResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"description": "编辑模型详情，链上模型只允许编辑描述，非链上模型可以编辑输入输出价格等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员编辑模型详情", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "模型ID", "name": "model_id", "in": "path", "required": true}, {"description": "更新模型请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.AdminUpdateModelRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/models/{model_id}/status": {"post": {"description": "管理员对模型进行上线、下线或删除操作", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-模型管理"], "summary": "管理员模型上下架/删除", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "模型ID", "name": "model_id", "in": "path", "required": true}, {"description": "状态操作请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AdminModelStatusRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/providers/create": {"post": {"description": "创建新的中心化供应商（wallet_address为空）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-供应商管理"], "summary": "管理员新增中心化供应商", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "创建供应商请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AdminCreateProviderRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.AdminProviderListItem"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/providers/list": {"get": {"description": "获取所有供应商信息，区分中心化/链上供应商", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-供应商管理"], "summary": "管理员查看供应商列表", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认10", "name": "page_size", "in": "query"}, {"type": "boolean", "description": "是否中心化供应商筛选（wallet_address 以 self- 开头）", "name": "is_centralized", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.AdminProviderListResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/users/gift-balance": {"post": {"description": "管理员向指定用户赠送余额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "管理员赠送用户余额", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "赠送余额请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AdminGiftBalanceRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/admin/users/list": {"get": {"description": "获取所有用户信息，包含当前余额和累计充值", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["管理员-用户管理"], "summary": "管理员查看用户列表（带余额信息）", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量，默认10", "name": "page_size", "in": "query"}, {"type": "string", "description": "排序字段，默认created_at", "name": "order", "in": "query"}, {"type": "string", "description": "排序方向，asc或desc，默认asc", "name": "direction", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AdminUsersListResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/audio/config": {"get": {"description": "语音管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "获取音频配置", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AudioConfig"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"description": "语音管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "更新音频配置", "parameters": [{"description": "音频配置", "name": "config", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.AudioConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AudioConfig"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/audio/models": {"get": {"description": "语音管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "获取音频基础模型", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.AudioBaseModel"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/audio/speech/{chat_id}/{msg_id}": {"post": {"description": "语音管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "创建语音", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "integer"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/audio/voices": {"get": {"description": "语音管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "获取音频声音", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.AudioVoice"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats": {"get": {"description": "获取当前用户的所有聊天记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "获取聊天记录列表", "parameters": [{"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "每页条数", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.ConversationOP"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/:chat_id": {"delete": {"description": "删除指定的对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "删除对话", "parameters": [{"type": "string", "description": "对话ID", "name": "chat_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/asr": {"post": {"description": "开始ASR对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "开始ASR对话", "parameters": [{"type": "file", "description": "file", "name": "file", "in": "formData", "required": true}, {"type": "string", "description": "model_id", "name": "model_id", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.StartChatASRResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/new": {"post": {"description": "创建新的对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "创建新对话", "parameters": [{"description": "对话信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.CreateConversationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ConversationOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/tti": {"post": {"description": "开始TTI对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "开始TTI对话", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.StartChatTTIRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.StartChatTTIResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/tti/all_shared": {"get": {"description": "获取所有分享的tti对话(不需要认证)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "获取所有分享的tti对话", "parameters": [{"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页条数", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.TTIChatSharedListOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/tti/share": {"post": {"description": "分享对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "分享对话", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateChatShareRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/tti/shared": {"get": {"description": "获取用户分享的tti对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "获取用户分享的tti对话", "parameters": [{"type": "integer", "default": 1, "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "default": 20, "description": "每页条数", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.TTIChatSharedListOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/tts": {"post": {"description": "开始TTS对话", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "开始TTS对话", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.StartChatTTSRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.StartChatTTSResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/chats/{chat_id}": {"post": {"description": "更新指定ID的聊天", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "更新聊天", "parameters": [{"type": "string", "description": "聊天ID", "name": "id", "in": "path", "required": true}, {"description": "聊天信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UpdateConversationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.Conversation"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/conversations/{id}": {"get": {"description": "获取指定对话的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["conversation"], "summary": "获取对话详情", "parameters": [{"type": "integer", "description": "对话ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.Conversation"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/files/audio/{file_id}/content": {"get": {"description": "获取指定音频文件", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["文件管理"], "summary": "获取音频文件", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "文件ID", "name": "file_id", "in": "path", "required": true}], "responses": {"200": {"description": "音频文件", "schema": {"type": "file"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/files/image": {"post": {"description": "上传新图片文件", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["文件管理"], "summary": "上传图片文件", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "file", "description": "文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.FileInfoOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/files/image/{file_id}/content": {"get": {"description": "获取指定图片文件", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["文件管理"], "summary": "获取图片文件", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "目录", "name": "dir", "in": "path", "required": true}, {"type": "string", "description": "图片名称", "name": "image_name", "in": "path", "required": true}], "responses": {"200": {"description": "图片文件", "schema": {"type": "file"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/files/upload": {"post": {"description": "上传新文件", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["文件管理"], "summary": "上传文件", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "file", "description": "文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.FileInfoOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/files/{id}": {"delete": {"description": "删除指定文件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["文件管理"], "summary": "删除文件", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "文件ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/images/config": {"get": {"description": "图像管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "获取图像配置", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ImageConfig"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"description": "图像管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "更新图像配置", "parameters": [{"description": "图像配置", "name": "config", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ImageConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ImageConfig"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/images/models": {"get": {"description": "图像管理", "consumes": ["application/json"], "produces": ["application/json"], "summary": "获取图像基础模型", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.ImageModel"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/knowledge": {"get": {"description": "获取所有知识库列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["知识库管理"], "summary": "获取知识库列表", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/models": {"get": {"description": "获取可以使用的模型列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "获取可以使用的模型列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.AvailableModel"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/models/base": {"get": {"description": "获取所有基础模型配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "获取所有基础模型配置", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.ModelConfigIO"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/models/create": {"post": {"description": "创建新的模型配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "创建模型配置", "parameters": [{"description": "模型配置信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ModelConfigIO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ModelConfigIO"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/models/toggle": {"post": {"description": "修改指定模型的启用状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "修改模型启用状态", "parameters": [{"type": "string", "description": "模型ID", "name": "id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ModelConfigIO"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/provider/categories": {"get": {"security": [{"BearerAuth": []}], "description": "获取所有分类", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "获取所有分类", "responses": {"200": {"description": "成功", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/provider/info": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前登录模型提供者的基本信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "获取提供者信息", "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.ProviderInfo"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "更新当前登录模型提供者的基本信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "更新提供者信息", "parameters": [{"description": "更新请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UpdateProviderInfoRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"type": "object", "properties": {"message": {"type": "string"}}}}, "400": {"description": "请求参数错误", "schema": {"type": "string"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/provider/login": {"post": {"description": "模型提供者使用钱包签名进行登录验证", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "钱包签名登录", "parameters": [{"description": "登录请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ProviderAuthRequest"}}], "responses": {"200": {"description": "登录成功", "schema": {"$ref": "#/definitions/service.ProviderAuthResponse"}}, "400": {"description": "请求参数错误", "schema": {"type": "string"}}, "401": {"description": "登录失败", "schema": {"type": "string"}}}}}, "/api/v1/provider/models": {"get": {"security": [{"BearerAuth": []}], "description": "获取当前登录模型提供者上传的所有模型列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "获取提供者的模型列表", "parameters": [{"type": "integer", "description": "页码,默认1", "name": "page", "in": "query", "required": true}, {"type": "integer", "description": "每页数量,默认20", "name": "limit", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.ProviderModelList"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/provider/models/image": {"post": {"security": [{"BearerAuth": []}], "description": "上传模型图片", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "上传模型图片", "parameters": [{"type": "file", "description": "图片文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.FileInfoOP"}}, "400": {"description": "请求参数错误", "schema": {"type": "string"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/provider/models/{chain_model_id}": {"get": {"security": [{"BearerAuth": []}], "description": "获取指定模型的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "获取模型信息", "parameters": [{"type": "integer", "description": "链上模型ID", "name": "chain_model_id", "in": "path", "required": true}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.ProviderModelInfo"}}, "400": {"description": "请求参数错误", "schema": {"type": "string"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "更新指定模型的扩展描述信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "更新模型扩展信息", "parameters": [{"type": "integer", "description": "链上模型ID", "name": "chain_model_id", "in": "path", "required": true}, {"description": "更新请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UpdateModelExtRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"type": "object", "properties": {"message": {"type": "string"}}}}, "400": {"description": "请求参数错误", "schema": {"type": "string"}}, "401": {"description": "Token无效或已过期", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/provider/nonce": {"get": {"description": "为模型提供者生成用于钱包签名的随机字符串，需要提供钱包地址", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["模型提供者"], "summary": "生成登录随机字符串", "parameters": [{"type": "string", "description": "钱包地址", "name": "wallet_addr", "in": "query", "required": true}], "responses": {"200": {"description": "成功", "schema": {"type": "object", "properties": {"nonce": {"type": "string"}}}}, "400": {"description": "钱包地址缺失", "schema": {"type": "string"}}, "500": {"description": "服务器错误", "schema": {"type": "string"}}}}}, "/api/v1/public/files/audio/{file_id}/content": {"get": {"description": "获取指定公共音频文件", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["文件管理"], "summary": "获取公共音频文件", "parameters": [{"type": "string", "description": "文件ID", "name": "file_id", "in": "path", "required": true}], "responses": {"200": {"description": "音频文件", "schema": {"type": "file"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/public/files/image/{file_id}/content": {"get": {"description": "获取指定公共图片文件", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["文件管理"], "summary": "获取公共图片文件", "parameters": [{"type": "string", "description": "文件ID", "name": "file_id", "in": "path", "required": true}], "responses": {"200": {"description": "图片文件", "schema": {"type": "file"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/system/concurrency/stats": {"get": {"description": "获取当前并发限制器的统计信息", "produces": ["application/json"], "tags": ["系统"], "summary": "获取并发限制统计信息", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/tools": {"get": {"description": "获取工具列表", "produces": ["application/json"], "tags": ["工具"], "summary": "获取工具列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/topai/models": {"get": {"description": "获取模型列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型列表", "parameters": [{"description": "请求参数", "name": "json", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.GetTopaiModelListRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelsInfoOP"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/categories": {"get": {"description": "获取模型分类列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型分类列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/providers": {"get": {"description": "获取模型提供商列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型提供商列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelProviderOP"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/ranking": {"get": {"description": "获取模型列表（按token排序）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型列表（按token排序）", "parameters": [{"description": "请求参数", "name": "json", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.GetTopaiModelListRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelsInfoOP"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/series": {"get": {"description": "获取模型系列列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型系列列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelSeriesOP"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/support-params": {"get": {"description": "获取模型支持参数列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型支持参数列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/types": {"get": {"description": "获取模型类型列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型类型列表", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/topai/models/{model_id}": {"get": {"description": "获取指定ID的模型详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["topai"], "summary": "获取模型详情", "parameters": [{"type": "string", "description": "模型ID", "name": "model_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.TopaiModelsInfoDetailOP"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/api.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/api.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/api.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/api.Response"}}}}}, "/api/v1/user/app/avatar": {"post": {"description": "上传用户应用头像", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "上传用户应用头像", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "file", "description": "文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/app/create": {"post": {"description": "创建用户应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "创建用户应用", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"description": "应用信息", "name": "app_info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/app/delete/{app_id}": {"post": {"description": "删除用户应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "删除用户应用", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "string", "description": "应用ID", "name": "app_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "成功"}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/app/info/{app_id}": {"get": {"description": "获取用户应用详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "获取用户应用详情", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/user/app/key/list": {"get": {"description": "获取用户应用密钥列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "获取用户应用密钥列表", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "string", "description": "应用ID", "name": "app_uuid", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.AppKeyOP"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/user/app/key/{app_id}/create": {"post": {"description": "创建用户应用密钥", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "创建用户应用密钥", "parameters": [{"type": "string", "description": "应用ID", "name": "app_id", "in": "path", "required": true}, {"description": "密钥名称", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateUserAppKeyRequest"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.AppKeyOP"}}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/app/key/{app_id}/delete/{key_name}": {"post": {"description": "删除用户应用密钥", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "删除用户应用密钥", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "string", "description": "应用ID", "name": "app_uuid", "in": "path", "required": true}, {"description": "密钥名称", "name": "key_name", "in": "body", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功"}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/app/list": {"get": {"description": "获取用户应用列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "获取用户应用列表", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.UserAppInfoOP"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/user/app/update/{app_id}": {"post": {"description": "更新用户应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["UserApp"], "summary": "更新用户应用", "parameters": [{"type": "string", "description": "用户ID", "name": "user_id", "in": "query", "required": true}, {"type": "string", "description": "应用ID", "name": "app_uuid", "in": "path", "required": true}, {"description": "应用信息", "name": "app_info", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}], "responses": {"200": {"description": "成功", "schema": {"$ref": "#/definitions/service.UserAppInfoOP"}}, "400": {"description": "失败", "schema": {"type": "string"}}, "500": {"description": "失败", "schema": {"type": "string"}}}}}, "/api/v1/user/balance": {"get": {"description": "获取用户余额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "获取用户余额", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/user/balance/record": {"get": {"description": "获取用户余额记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "获取用户余额记录", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "page_size", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserBalanceRecordOP"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/user/recharge_address": {"get": {"description": "获取用户充值地址", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "获取用户充值地址", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users": {"get": {"description": "获取所有用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "获取用户列表", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/service.UserAdminOP"}}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"description": "创建新用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "创建用户", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "创建用户请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAdminOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users/gift/balance": {"post": {"description": "管理员转赠余额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "管理员转赠余额", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "转赠余额请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.GiftBalanceRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users/update/role": {"post": {"description": "更新用户角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "更新用户角色", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "更新用户角色请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRoleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAdminOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users/user/settings": {"get": {"description": "获取用户设置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "获取用户设置", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserSettings"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users/user/settings/update": {"post": {"description": "更新用户设置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "更新用户设置", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"description": "更新用户设置请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.UserSettings"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserSettings"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/v1/users/{id}": {"put": {"description": "更新用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "更新用户", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "更新用户请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAdminOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "delete": {"description": "删除指定用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户管理"], "summary": "删除用户", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}, {"type": "string", "description": "用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/api/version/updates": {"get": {"description": "获取版本差异", "produces": ["application/json"], "tags": ["版本"], "summary": "获取版本更新", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/": {"get": {"description": "获取当前登录用户的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "获取当前用户信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserBaseOP"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}}}}, "/auth/admin/config": {"get": {"description": "获取系统管理员配置(需要管理员权限)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "获取管理员配置", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.SystemCommonConfig"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}, "post": {"description": "更新系统管理员配置(需要管理员权限)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "更新管理员配置", "parameters": [{"description": "管理员配置", "name": "config", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.SystemCommonConfig"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.SystemCommonConfig"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/signin": {"post": {"description": "用户登录接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "用户登录", "parameters": [{"description": "登录信息", "name": "login", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.SigninRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAuthResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/signout": {"get": {"description": "用户登出接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "用户登出", "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/signup": {"post": {"description": "用户注册接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "用户注册", "parameters": [{"description": "注册信息", "name": "register", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.SignupRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserAuthResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/update/password": {"post": {"description": "更新当前登录用户的密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "更新用户密码", "parameters": [{"description": "密码信息", "name": "password", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdatePasswordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/auth/update/profile": {"post": {"description": "更新当前登录用户的资料", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "更新用户资料", "parameters": [{"description": "用户资料", "name": "profile", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateProfileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.UserBaseOP"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/model/{model_id}/config": {"post": {"description": "更新指定模型的配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["model"], "summary": "更新模型配置", "parameters": [{"type": "string", "description": "模型ID", "name": "model_id", "in": "path", "required": true}, {"description": "配置信息", "name": "config", "in": "body", "required": true, "schema": {"$ref": "#/definitions/service.ModelConfigIO"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/service.ModelConfigIO"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "401": {"description": "Unauthorized", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/ws/socket.io": {"get": {"description": "处理WebSocket连接", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["websocket"], "summary": "处理WebSocket连接", "responses": {"200": {"description": "OK"}}}}}, "definitions": {"api.Response": {"type": "object", "properties": {"code": {"description": "业务状态码", "type": "integer"}, "data": {"description": "响应数据"}, "error": {"description": "错误详情（仅在开发环境显示）", "type": "string"}, "msg": {"description": "用户消息", "type": "string"}}}, "handlers.AdminCreateCategoryRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "handlers.AdminCreateProviderRequest": {"type": "object", "required": ["name"], "properties": {"name": {"description": "供应商名称", "type": "string"}}}, "handlers.AdminCreateSeriesRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "handlers.AdminGiftBalanceRequest": {"type": "object", "required": ["amount", "user_identity_id"], "properties": {"amount": {"description": "赠送金额", "type": "number"}, "description": {"description": "描述", "type": "string"}, "user_identity_id": {"description": "用户身份ID", "type": "string"}}}, "handlers.AdminModelStatusRequest": {"type": "object", "required": ["action"], "properties": {"action": {"description": "操作类型：online（上线）、offline（下线）、delete（删除）", "type": "string"}}}, "handlers.AdminProviderListItem": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "integer"}, "id": {"description": "供应商ID", "type": "integer"}, "is_centralized": {"description": "是否中心化供应商（wallet_address为空）", "type": "boolean"}, "name": {"description": "供应商名称", "type": "string"}, "uuid": {"description": "供应商UUID", "type": "string"}, "wallet_address": {"description": "钱包地址", "type": "string"}}}, "handlers.AdminProviderListResponse": {"type": "object", "properties": {"providers": {"type": "array", "items": {"$ref": "#/definitions/handlers.AdminProviderListItem"}}, "total": {"type": "integer"}}}, "handlers.ChatCompletedRequest": {"type": "object", "properties": {"chat_id": {"type": "string"}, "id": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string"}, "role": {"type": "string"}, "timestamp": {"type": "integer"}}}}, "model": {"type": "string"}, "model_item": {"$ref": "#/definitions/service.AvailableModel"}, "session_id": {"type": "string"}}}, "handlers.Conversation": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "handlers.CreateUserAppKeyRequest": {"type": "object", "properties": {"key_name": {"type": "string"}}}, "handlers.CreateUserRequest": {"type": "object", "required": ["email", "name", "password", "role"], "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string", "minLength": 6}, "role": {"type": "string"}}}, "handlers.GiftBalanceRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "to_user_id": {"type": "string"}}}, "handlers.SigninRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 6}}}, "handlers.SignupRequest": {"type": "object", "required": ["email", "name", "password"], "properties": {"email": {"type": "string"}, "name": {"type": "string", "maxLength": 50, "minLength": 3}, "password": {"type": "string", "minLength": 6}, "profile_image_id": {"type": "string"}}}, "handlers.UpdateChatShareRequest": {"type": "object", "properties": {"id": {"type": "string"}, "is_share": {"type": "boolean"}}}, "handlers.UpdatePasswordRequest": {"type": "object", "required": ["new_password", "password"], "properties": {"new_password": {"type": "string", "minLength": 6}, "password": {"type": "string"}}}, "handlers.UpdateProfileRequest": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "profile_image_id": {"type": "string"}}}, "handlers.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "profile_image_url": {"type": "string"}}}, "handlers.UpdateUserRoleRequest": {"type": "object", "required": ["id", "role"], "properties": {"id": {"type": "string"}, "role": {"type": "string"}}}, "service.AdminCreateCentralModelRequest": {"type": "object", "properties": {"category_ids": {"type": "array", "items": {"type": "integer"}}, "context_length": {"type": "integer"}, "description": {"type": "string"}, "input_price": {"type": "string"}, "max_output": {"type": "integer"}, "model_name": {"type": "string"}, "model_type": {"type": "string"}, "model_version": {"type": "string"}, "output_price": {"type": "string"}, "provider_id": {"type": "integer"}, "series_id": {"type": "integer"}, "weight": {"type": "integer"}}}, "service.AdminModelDetailResponse": {"type": "object", "properties": {"context_length": {"description": "上下文长度", "type": "integer"}, "created_at": {"description": "创建时间", "type": "integer"}, "description": {"description": "描述", "type": "string"}, "id": {"description": "模型ID", "type": "integer"}, "input_price": {"description": "输入价格", "type": "string"}, "is_on_chain": {"description": "是否链上模型", "type": "boolean"}, "latency": {"description": "延迟", "type": "integer"}, "max_output": {"description": "最大输出", "type": "integer"}, "model_id": {"description": "模型标识符", "type": "string"}, "model_name": {"description": "模型名称", "type": "string"}, "model_version": {"description": "模型版本", "type": "string"}, "output_price": {"description": "输出价格", "type": "string"}, "provider_name": {"description": "提供商名称", "type": "string"}, "sample_code": {"description": "示例代码", "type": "string"}, "series_id": {"description": "系列ID", "type": "integer"}, "show_picture": {"description": "展示图片", "type": "string"}, "show_video": {"description": "展示视频", "type": "string"}, "status": {"description": "上下架状态：1上线，0下线，-1删除", "type": "integer"}, "throughput": {"description": "吞吐量", "type": "integer"}, "token_consumption": {"description": "token消耗量", "type": "string"}, "updated_at": {"description": "更新时间", "type": "integer"}, "weight": {"description": "权重", "type": "integer"}}}, "service.AdminModelListItem": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "integer"}, "description": {"description": "描述", "type": "string"}, "id": {"description": "模型ID", "type": "integer"}, "input_price": {"description": "输入价格", "type": "string"}, "is_on_chain": {"description": "是否链上模型", "type": "boolean"}, "model_id": {"description": "模型标识符", "type": "string"}, "model_name": {"description": "模型名称", "type": "string"}, "model_version": {"description": "模型版本", "type": "string"}, "output_price": {"description": "输出价格", "type": "string"}, "provider_name": {"description": "提供商名称", "type": "string"}, "status": {"description": "上下架状态：1上线，0下线，-1删除", "type": "integer"}, "token_consumption": {"description": "token消耗量", "type": "string"}, "updated_at": {"description": "更新时间", "type": "integer"}}}, "service.AdminModelListResponse": {"type": "object", "properties": {"models": {"type": "array", "items": {"$ref": "#/definitions/service.AdminModelListItem"}}, "total": {"type": "integer"}}}, "service.AdminUpdateModelRequest": {"type": "object", "properties": {"context_length": {"description": "上下文长度（仅非链上模型可编辑）", "type": "integer"}, "description": {"description": "描述（链上和非链上模型都可以编辑）", "type": "string"}, "input_price": {"description": "输入价格（仅非链上模型可编辑）", "type": "string"}, "max_output": {"description": "最大输出（仅非链上模型可编辑）", "type": "integer"}, "output_price": {"description": "输出价格（仅非链上模型可编辑）", "type": "string"}, "weight": {"description": "权重", "type": "integer"}}}, "service.AdminUserWithBalance": {"type": "object", "properties": {"accumulated_total": {"description": "累计充值", "type": "string"}, "created_at": {"type": "integer"}, "current_balance": {"description": "当前余额", "type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "identity_id": {"type": "string"}, "role": {"type": "string"}, "updated_at": {"type": "integer"}, "username": {"type": "string"}}}, "service.AdminUsersListResponse": {"type": "object", "properties": {"total": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/service.AdminUserWithBalance"}}}}, "service.AppKeyOP": {"type": "object", "properties": {"app_id": {"type": "string"}, "app_name": {"type": "string"}, "created_at": {"type": "integer"}, "key": {"type": "string"}, "key_name": {"type": "string"}}}, "service.AudioBaseModel": {"type": "object", "properties": {"asr": {"type": "array", "items": {"$ref": "#/definitions/service.AudioModel"}}, "tts": {"type": "array", "items": {"$ref": "#/definitions/service.AudioModel"}}}}, "service.AudioConfig": {"type": "object", "properties": {"asr": {"description": "语音转文本", "allOf": [{"$ref": "#/definitions/service.AudioConfigASR"}]}, "tts": {"description": "文本转语音", "allOf": [{"$ref": "#/definitions/service.AudioConfigTTS"}]}}}, "service.AudioConfigASR": {"type": "object", "properties": {"ENABLE": {"description": "是否启用语音转文本", "type": "boolean"}, "MODEL": {"description": "模型，为空表示使用默认模型", "type": "string"}}}, "service.AudioConfigTTS": {"type": "object", "properties": {"ENABLE": {"description": "是否启用图片生成", "type": "boolean"}, "MODEL": {"description": "模型，为空表示使用默认模型", "type": "string"}, "VOICE": {"description": "音色，为空表示使用默认音色", "type": "string"}}}, "service.AudioModel": {"type": "object", "properties": {"id": {"type": "string"}, "voices": {"type": "array", "items": {"$ref": "#/definitions/service.AudioVoice"}}}}, "service.AudioVoice": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "service.AvailableModel": {"type": "object", "properties": {"actions": {"type": "array", "items": {}}, "active": {"type": "boolean"}, "context_window": {"type": "integer"}, "created": {"type": "integer"}, "id": {"type": "string"}, "info": {"$ref": "#/definitions/service.ModelConfigIO"}, "max_completion_tokens": {"type": "integer"}, "model_type": {"type": "string"}, "name": {"type": "string"}, "object": {"type": "string"}, "owned_by": {"type": "string"}, "public_apps": {}, "remote": {"$ref": "#/definitions/service.RemoteModel"}, "tags": {"type": "array", "items": {"type": "string"}}, "total_tokens": {"type": "string"}, "urlIdx": {"type": "integer"}}}, "service.BaseModel": {"type": "object", "properties": {"active": {"type": "boolean"}, "context_window": {"type": "integer"}, "created_at": {"type": "integer"}, "id": {"type": "string"}, "max_completion_tokens": {"type": "integer"}, "model_type": {"type": "string"}, "name": {"type": "string"}, "object": {"type": "string"}, "owned_by": {"type": "string"}, "public_apps": {}, "remote": {"$ref": "#/definitions/service.RemoteModel"}, "urlIdx": {"type": "integer"}}}, "service.ChatASRByApiResponse": {"type": "object", "properties": {"text": {"type": "string"}}}, "service.ChatCompletionMessage": {"type": "object", "properties": {"content": {"type": "string"}, "file_uuid": {"type": "string"}, "role": {"type": "string"}}}, "service.ChatLLMByApiMessage": {"type": "object", "properties": {"content": {"type": "string"}, "role": {"type": "string"}}}, "service.ChatLLMByApiRequest": {"type": "object", "properties": {"frequency_penalty": {"type": "number"}, "max_tokens": {"type": "integer"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/service.ChatLLMByApiMessage"}}, "model": {"type": "string"}, "presence_penalty": {"type": "number"}, "response_format": {"$ref": "#/definitions/service.ChatLLMByApiResponseFormat"}, "seed": {"type": "integer"}, "stop": {"type": "array", "items": {"type": "string"}}, "stream": {"type": "boolean"}, "temperature": {"type": "number"}, "top_p": {"type": "number"}, "user": {"type": "string"}}}, "service.ChatLLMByApiResponseFormat": {"type": "object", "properties": {"type": {"type": "string"}}}, "service.ChatTTIByApiRequest": {"type": "object", "properties": {"model": {"type": "string"}, "prompt": {"type": "string"}, "response_format": {"type": "string"}, "size": {"type": "string"}, "user": {"type": "string"}}}, "service.ChatTTIByApiResponse": {"type": "object", "properties": {"created": {"type": "integer"}, "data": {"type": "object", "properties": {"b64_json": {"type": "string"}, "revised_prompt": {"type": "string"}}}}}, "service.ChatTTSByApiRequest": {"type": "object", "properties": {"input": {"type": "string"}, "model": {"type": "string"}, "response_format": {"type": "string"}, "speed": {"type": "number"}, "voice": {"type": "string"}}}, "service.Conversation": {"type": "object", "properties": {"files": {"type": "array", "items": {"$ref": "#/definitions/service.ConversationMessageFile"}}, "history": {"$ref": "#/definitions/service.ConversationHistory"}, "id": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/service.ConversationMessage"}}, "models": {"type": "array", "items": {"type": "string"}}, "params": {}, "system": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "timestamp": {"description": "注意，此处是毫秒", "type": "integer"}, "title": {"type": "string"}}}, "service.ConversationHistory": {"type": "object", "properties": {"currentId": {"type": "string"}, "messages": {"type": "object", "additionalProperties": {"$ref": "#/definitions/service.ConversationMessage"}}}}, "service.ConversationMessage": {"type": "object", "properties": {"childrenIds": {"type": "array", "items": {"type": "string"}}, "content": {"type": "string"}, "done": {"type": "boolean"}, "files": {"type": "array", "items": {"$ref": "#/definitions/service.ConversationMessageFile"}}, "id": {"type": "string"}, "lastSentence": {"type": "string"}, "model": {"type": "string"}, "models": {"type": "array", "items": {"type": "string"}}, "parentId": {"type": "string"}, "role": {"type": "string"}, "timestamp": {"description": "注意，此处是秒", "type": "integer"}, "userContext": {}}}, "service.ConversationMessageFile": {"type": "object", "properties": {"collection_name": {"type": "string"}, "error": {"type": "string"}, "file": {"$ref": "#/definitions/service.FileInfoOP"}, "id": {"type": "string"}, "item_id": {"type": "string"}, "name": {"type": "string"}, "size": {"type": "integer"}, "status": {"type": "string"}, "type": {"type": "string"}, "url": {"type": "string"}}}, "service.ConversationOP": {"type": "object", "properties": {"archived": {"type": "boolean"}, "chat": {"$ref": "#/definitions/service.Conversation"}, "created_at": {"type": "integer"}, "folder_id": {}, "id": {"type": "string"}, "meta": {}, "pinned": {"type": "boolean"}, "share_id": {}, "title": {"type": "string"}, "updated_at": {"type": "integer"}, "user_id": {"type": "string"}}}, "service.CreateChatCompletionRequest": {"type": "object", "properties": {"chat_id": {"type": "string"}, "features": {"type": "object", "properties": {"code_interpreter": {"type": "boolean"}, "image_generation": {"type": "boolean"}, "web_search": {"type": "boolean"}}}, "files": {"type": "array", "items": {"$ref": "#/definitions/service.ConversationMessageFile"}}, "id": {"type": "string"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/service.ChatCompletionMessage"}}, "model": {"type": "string"}, "model_item": {"$ref": "#/definitions/service.AvailableModel"}, "params": {}, "session_id": {"type": "string"}, "stream": {"type": "boolean"}, "tool_servers": {"type": "array", "items": {}}, "variables": {"type": "object", "additionalProperties": true}}}, "service.CreateConversationRequest": {"type": "object", "properties": {"chat": {"$ref": "#/definitions/service.Conversation"}}}, "service.FileInfoData": {"type": "object", "properties": {"content": {"type": "string"}}}, "service.FileInfoMeta": {"type": "object", "properties": {"collection_name": {"type": "string"}, "content_type": {"type": "string"}, "data": {}, "name": {"type": "string"}, "size": {"type": "integer"}}}, "service.FileInfoOP": {"type": "object", "properties": {"created_at": {"type": "integer"}, "data": {"$ref": "#/definitions/service.FileInfoData"}, "file_name": {"type": "string"}, "hash": {"type": "string"}, "id": {"type": "string"}, "meta": {"$ref": "#/definitions/service.FileInfoMeta"}, "updated_at": {"type": "integer"}, "url": {"type": "string"}, "user_id": {"type": "string"}}}, "service.GetTopaiModelListRequest": {"type": "object", "properties": {"category_ids": {"type": "array", "items": {"type": "integer"}}, "model_ids": {"type": "array", "items": {"type": "integer"}}, "model_types": {"type": "array", "items": {"type": "string"}}, "order_by": {"type": "string"}, "order_type": {"type": "string"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "price_free": {"type": "boolean"}, "providers": {"type": "array", "items": {"type": "string"}}, "search": {"type": "string"}, "series_ids": {"type": "array", "items": {"type": "integer"}}, "support_params": {"type": "array", "items": {"type": "string"}}}}, "service.GetUsageResponse": {"type": "object", "properties": {"id": {"type": "string"}, "model": {"type": "string"}, "tokens_completion": {"type": "string"}, "tokens_prompt": {"type": "string"}, "total_cost": {"type": "string"}}}, "service.ImageConfig": {"type": "object", "properties": {"CONFIG": {"description": "模型配置"}, "ENABLE": {"description": "是否启用图片生成", "type": "boolean"}, "ENGINE": {"description": "模型分类，model/comfuui", "type": "string"}, "MODEL": {"description": "选择的模型id", "type": "string"}, "max_image_size": {"description": "最大图片大小", "type": "integer"}, "max_text_length": {"description": "最大文本长度", "type": "integer"}}}, "service.ImageModel": {"type": "object", "properties": {"engine": {"description": "模型分类，model/comfuui", "type": "string"}, "model_id": {"description": "模型id", "type": "string"}, "params": {"description": "模型配置参数"}}}, "service.ModelConfigAccessControl": {"type": "object", "properties": {"read": {"type": "object", "properties": {"group_ids": {"type": "array", "items": {"type": "string"}}, "user_ids": {"type": "array", "items": {"type": "string"}}}}, "write": {"type": "object", "properties": {"group_ids": {"type": "array", "items": {"type": "string"}}, "user_ids": {"type": "array", "items": {"type": "string"}}}}}}, "service.ModelConfigIO": {"type": "object", "properties": {"access_control": {"$ref": "#/definitions/service.ModelConfigAccessControl"}, "base_model_id": {"type": "string"}, "created_at": {"type": "integer"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "meta": {"$ref": "#/definitions/service.ModelConfigMeta"}, "name": {"type": "string"}, "params": {}, "updated_at": {"type": "integer"}}}, "service.ModelConfigMeta": {"type": "object", "properties": {"capabilities": {"type": "object", "properties": {"citations": {"type": "boolean"}, "vision": {"type": "boolean"}}}, "description": {"type": "string"}, "profile_image_url": {"type": "string"}, "suggestion_prompts": {}, "tags": {"type": "array", "items": {"type": "string"}}}}, "service.ProviderAuthRequest": {"description": "模型提供者登录请求参数", "type": "object", "required": ["nonce", "signature", "timestamp", "wallet_addr"], "properties": {"nonce": {"description": "随机字符串", "type": "string", "example": "a1b2c3d4e5f6"}, "signature": {"description": "钱包签名", "type": "string", "example": "0xabcdef1234567890"}, "timestamp": {"description": "时间戳", "type": "integer", "example": **********}, "wallet_addr": {"description": "钱包地址", "type": "string", "example": "0x1234567890abcdef"}}}, "service.ProviderAuthResponse": {"description": "模型提供者登录响应", "type": "object", "properties": {"expires_at": {"description": "过期时间戳", "type": "integer", "example": **********}, "token": {"description": "访问令牌", "type": "string", "example": "abc123def456"}}}, "service.ProviderInfo": {"description": "模型提供者基本信息", "type": "object", "properties": {"created_at": {"description": "创建时间戳", "type": "integer", "example": **********}, "id": {"description": "提供者ID", "type": "integer", "example": 1}, "name": {"description": "提供者名称", "type": "string", "example": "AI模型提供者"}, "token": {"description": "访问令牌", "type": "string"}, "uuid": {"description": "提供者UUID", "type": "string", "example": "550e8400-e29b-41d4-a716-************"}, "wallet_address": {"description": "钱包地址", "type": "string", "example": "0x1234567890abcdef"}}}, "service.ProviderModelInfo": {"description": "提供者的模型详细信息", "type": "object", "properties": {"category_ids": {"description": "分类", "type": "array", "items": {"type": "integer"}}, "chain_model_id": {"description": "链上模型ID", "type": "integer", "example": 100}, "description": {"description": "扩展信息", "type": "string", "example": "强大的语言模型"}, "input_price": {"description": "输入价格", "type": "string", "example": "0.001"}, "model_id": {"description": "模型ID", "type": "string", "example": "1"}, "model_name": {"description": "模型名称", "type": "string", "example": "GPT-4"}, "model_type": {"description": "模型类型", "type": "string", "example": "text-generation"}, "model_version": {"description": "模型版本", "type": "string", "example": "1.0"}, "output_price": {"description": "输出价格", "type": "string", "example": "0.002"}, "owner_address": {"description": "所有者地址", "type": "string", "example": "0x1234567890abcdef"}, "series_id": {"description": "系列ID", "type": "integer", "example": 1}, "show_picture": {"description": "展示图片", "type": "string", "example": "https://example.com/pic.jpg"}, "show_video": {"description": "展示视频", "type": "string", "example": "https://example.com/video.mp4"}, "status": {"description": "状态", "type": "integer", "example": 1}, "tts_voices": {"description": "TTS音色列表，只有模型为TTS时才会被使用", "type": "array", "items": {"type": "string"}}}}, "service.ProviderModelList": {"type": "object", "properties": {"count": {"type": "integer"}, "models": {"type": "array", "items": {"$ref": "#/definitions/service.ProviderModelInfo"}}}}, "service.RemoteModel": {"type": "object", "properties": {"active": {"type": "boolean"}, "context_window": {"type": "integer"}, "created": {"type": "integer"}, "id": {"type": "string"}, "max_completion_tokens": {"type": "integer"}, "object": {"type": "string"}, "owned_by": {"type": "string"}, "public_apps": {}}}, "service.StartChatASRResponse": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string"}}}, "service.StartChatTTIRequest": {"type": "object", "properties": {"content": {"type": "string"}, "model_id": {"type": "string"}, "response_format": {"type": "string"}, "size": {"type": "string"}}}, "service.StartChatTTIResponse": {"type": "object", "properties": {"id": {"type": "string"}, "url": {"type": "string"}}}, "service.StartChatTTSRequest": {"type": "object", "properties": {"content": {"type": "string"}, "model_id": {"type": "string"}, "speed": {"type": "number"}, "voice": {"type": "string"}}}, "service.StartChatTTSResponse": {"type": "object", "properties": {"id": {"type": "string"}, "url": {"type": "string"}}}, "service.SystemCommonConfig": {"type": "object", "properties": {"API_KEY_ALLOWED_ENDPOINTS": {"description": "api key 允许的路由", "type": "string"}, "DEFAULT_USER_ROLE": {"description": "默认用户角色", "type": "string"}, "ENABLE_API_KEY": {"description": "是否启用 api key", "type": "boolean"}, "ENABLE_API_KEY_ENDPOINT_RESTRICTIONS": {"description": "是否启用 api key 限制路由", "type": "boolean"}, "ENABLE_CHANNELS": {"description": "是否启用频道", "type": "boolean"}, "ENABLE_COMMUNITY_SHARING": {"description": "是否启用社区分享", "type": "boolean"}, "ENABLE_MESSAGE_RATING": {"description": "是否启用消息评分", "type": "boolean"}, "ENABLE_SIGNUP": {"description": "是否启用注册", "type": "boolean"}, "JWT_EXPIRES_IN": {"description": "jwt 过期时间", "type": "string"}, "SHOW_ADMIN_DETAILS": {"description": "是否显示管理员详情", "type": "boolean"}, "WEBUI_URL": {"description": "前端 url", "type": "string"}}}, "service.TTIChatSharedListOP": {"type": "object", "properties": {"count": {"type": "integer"}, "list": {"type": "array", "items": {"$ref": "#/definitions/service.TTIChatSharedOP"}}}}, "service.TTIChatSharedOP": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "integer"}, "file_urls": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "is_shared": {"type": "boolean"}, "model_id": {"type": "string"}, "model_type": {"type": "string"}}}, "service.TopaiModelAppUsedOP": {"type": "object", "properties": {"app_avatar": {"type": "string"}, "app_id": {"type": "string"}, "app_name": {"type": "string"}, "description": {"type": "string"}, "total_input_tokens": {"type": "string"}, "total_output_tokens": {"type": "string"}, "total_tokens": {"type": "string"}, "website": {"type": "string"}}}, "service.TopaiModelCategoryOP": {"type": "object", "properties": {"belong_to": {"type": "string"}, "category_id": {"type": "integer"}, "category_name": {"type": "string"}}}, "service.TopaiModelDayPutDataOP": {"type": "object", "properties": {"count": {"type": "integer"}, "day": {"type": "string"}, "total_input_tokens": {"type": "string"}, "total_output_tokens": {"type": "string"}, "total_tokens": {"type": "string"}}}, "service.TopaiModelProviderOP": {"type": "object", "properties": {"provider_id": {"type": "string"}, "provider_name": {"type": "string"}}}, "service.TopaiModelSeriesOP": {"type": "object", "properties": {"series_id": {"type": "integer"}, "series_name": {"type": "string"}}}, "service.TopaiModelsInfoDetailOP": {"type": "object", "properties": {"app_used": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelAppUsedOP"}}, "category_list": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}, "context_length": {"type": "string"}, "day_put_data": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelDayPutDataOP"}}, "description": {"type": "string"}, "input_price": {"type": "string"}, "is_free": {"type": "boolean"}, "max_output": {"type": "string"}, "model_id": {"type": "string"}, "model_name": {"type": "string"}, "model_type": {"type": "string"}, "output_price": {"type": "string"}, "provider": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelsInfoOP"}}, "provider_name": {"type": "string"}, "sample_code": {"type": "object", "additionalProperties": {"type": "string"}}, "series_id": {"type": "integer"}, "series_name": {"type": "string"}, "show_picture": {"type": "string"}, "throughput": {"type": "array", "items": {}}, "total_tokens": {"type": "string"}}}, "service.TopaiModelsInfoOP": {"type": "object", "properties": {"category_list": {"type": "array", "items": {"$ref": "#/definitions/service.TopaiModelCategoryOP"}}, "context_length": {"type": "string"}, "description": {"type": "string"}, "input_price": {"type": "string"}, "is_free": {"type": "boolean"}, "max_output": {"type": "string"}, "model_id": {"type": "string"}, "model_name": {"type": "string"}, "model_type": {"type": "string"}, "output_price": {"type": "string"}, "provider_name": {"type": "string"}, "show_picture": {"type": "string"}, "total_tokens": {"type": "string"}}}, "service.UpdateConversationRequest": {"type": "object", "properties": {"chat": {"$ref": "#/definitions/service.Conversation"}}}, "service.UpdateModelExtRequest": {"type": "object"}, "service.UpdateProviderInfoRequest": {"description": "更新提供者信息请求参数", "type": "object", "required": ["name"], "properties": {"name": {"description": "提供者名称", "type": "string", "example": "AI模型提供者"}}}, "service.UserAdminOP": {"type": "object", "properties": {"total": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/service.UserAdminUser"}}}}, "service.UserAdminUser": {"type": "object", "properties": {"api_key": {"type": "string"}, "created_at": {"type": "integer"}, "email": {"type": "string"}, "expires_at": {"type": "integer"}, "id": {"type": "string"}, "info": {}, "last_active_at": {"type": "integer"}, "name": {"type": "string"}, "oauth_sub": {}, "profile_image_url": {"type": "string"}, "role": {"type": "string"}, "settings": {}, "token": {"type": "string"}, "token_type": {"type": "string"}, "updated_at": {"type": "integer"}}}, "service.UserAppInfoOP": {"type": "object", "properties": {"app_account_id": {"type": "string"}, "app_created_at": {"type": "integer"}, "app_description": {"type": "string"}, "app_id": {"type": "string"}, "app_logo": {"type": "string"}, "app_name": {"type": "string"}, "app_status": {"type": "integer"}, "app_website": {"type": "string"}}}, "service.UserAuthResponse": {"type": "object", "properties": {"expires_at": {"type": "integer"}, "token": {"type": "string"}, "user": {"$ref": "#/definitions/service.UserBaseOP"}}}, "service.UserBalanceRecord": {"type": "object", "properties": {"amount": {"type": "string"}, "created_at": {"type": "integer"}, "description": {"type": "string"}, "id": {"type": "integer"}, "is_in": {"type": "boolean"}, "type": {"type": "string"}}}, "service.UserBalanceRecordOP": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/service.UserBalanceRecord"}}, "total": {"type": "integer"}}}, "service.UserBaseOP": {"type": "object", "properties": {"email": {"type": "string"}, "expires_at": {"type": "integer"}, "id": {"type": "string"}, "name": {"type": "string"}, "profile_image_url": {"type": "string"}, "role": {"type": "string"}, "token": {"type": "string"}, "token_type": {"type": "string"}}}, "service.UserSettings": {"type": "object", "properties": {"system": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}