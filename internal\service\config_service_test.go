package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/testutil"
)

// TestConfigService_NewConfigService 测试创建配置服务
func TestConfigService_NewConfigService(t *testing.T) {
	tests := []struct {
		name string
		want *ConfigService
	}{
		{
			name: "TC1-成功创建配置服务",
			want: &ConfigService{},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			service := NewConfigService(ctx, &repository.DB{})

			assert.NotNil(t, service)
			assert.NotNil(t, service.log)
			assert.NotNil(t, service.db)
		})
	}
}

// TestConfigService_GetSystemCommonConfig 测试获取系统通用配置
func TestConfigService_GetSystemCommonConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    *SystemCommonConfig
		wantErr bool
	}{
		{
			name: "TC1-成功获取系统通用配置",
			setup: func(mocks *testutil.MockDependencies) {
				configList := []*repository.SystemConfig{
					{
						Category: "system",
						Key:      "API_KEY_ALLOWED_ENDPOINTS",
						Value:    "/api/v1/chat,/api/v1/models",
					},
					{
						Category: "system",
						Key:      "DEFAULT_USER_ROLE",
						Value:    "user",
					},
					{
						Category: "system",
						Key:      "ENABLE_API_KEY",
						Value:    "true",
					},
					{
						Category: "system",
						Key:      "ENABLE_SIGNUP",
						Value:    "false",
					},
					{
						Category: "system",
						Key:      "JWT_EXPIRES_IN",
						Value:    "24h",
					},
					{
						Category: "system",
						Key:      "WEBUI_URL",
						Value:    "http://localhost:3000",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configList, nil)
			},
			want: &SystemCommonConfig{
				API_KEY_ALLOWED_ENDPOINTS: "/api/v1/chat,/api/v1/models",
				DEFAULT_USER_ROLE:         "user",
				ENABLE_API_KEY:            true,
				ENABLE_SIGNUP:             false,
				JWT_EXPIRES_IN:            "24h",
				WEBUI_URL:                 "http://localhost:3000",
			},
			wantErr: false,
		},
		{
			name: "TC2-获取配置失败",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(nil, errors.New("db error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "TC3-空配置列表",
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return([]*repository.SystemConfig{}, nil)
			},
			want:    &SystemCommonConfig{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConfigService(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
				RunWithTx:    mocks.DB.RunWithTx,
			})

			got, err := service.GetSystemCommonConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConfigService_UpdateSystemCommonConfig 测试更新系统通用配置
func TestConfigService_UpdateSystemCommonConfig(t *testing.T) {
	tests := []struct {
		name    string
		config  *SystemCommonConfig
		setup   func(*testutil.MockDependencies)
		wantErr bool
	}{
		{
			name: "TC1-成功更新系统通用配置",
			config: &SystemCommonConfig{
				API_KEY_ALLOWED_ENDPOINTS: "/api/v1/chat,/api/v1/models",
				DEFAULT_USER_ROLE:         "user",
				ENABLE_API_KEY:            true,
				ENABLE_SIGNUP:             false,
				JWT_EXPIRES_IN:            "24h",
				WEBUI_URL:                 "http://localhost:3000",
			},
			setup: func(mocks *testutil.MockDependencies) {
				// Mock existing configs
				configList := []*repository.SystemConfig{
					{
						ID:       1,
						Category: "system",
						Key:      "API_KEY_ALLOWED_ENDPOINTS",
						Value:    "old_value",
					},
					{
						ID:       2,
						Category: "system",
						Key:      "DEFAULT_USER_ROLE",
						Value:    "admin",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configList, nil)

				// Mock transaction - RunWithTx 会自动调用传入的函数

				// Mock update methods
				mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(nil)
				mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "TC2-获取配置失败",
			config: &SystemCommonConfig{
				API_KEY_ALLOWED_ENDPOINTS: "/api/v1/chat",
			},
			setup: func(mocks *testutil.MockDependencies) {
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(nil, errors.New("db error"))
			},
			wantErr: true,
		},
		{
			name: "TC3-更新配置失败",
			config: &SystemCommonConfig{
				API_KEY_ALLOWED_ENDPOINTS: "/api/v1/chat",
			},
			setup: func(mocks *testutil.MockDependencies) {
				configList := []*repository.SystemConfig{
					{
						ID:       1,
						Category: "system",
						Key:      "API_KEY_ALLOWED_ENDPOINTS",
						Value:    "old_value",
					},
				}
				mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configList, nil)
				mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return(errors.New("update failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConfigService(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
				RunWithTx:    mocks.DB.RunWithTx,
			})

			err := service.UpdateSystemCommonConfig(ctx, tt.config)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mocks.AssertExpectations(t)
		})
	}
}

// TestConfigService_GetBaseConfig 测试获取基础配置
func TestConfigService_GetBaseConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func(*testutil.MockDependencies)
		want    *BaseConfig
		wantErr bool
	}{
		{
			name: "TC1-成功获取基础配置",
			setup: func(mocks *testutil.MockDependencies) {
				// Mock SystemConfig calls
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").Return(nil, nil)
				mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "image", "ENABLE").Return(nil, nil)
			},
			want: &BaseConfig{
				Status:        true,
				Name:          "TopAI",
				Version:       "0.0.1",
				DefaultLocale: "",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			mocks := testutil.NewMockDependencies()
			defer mocks.Cleanup()

			if tt.setup != nil {
				tt.setup(mocks)
			}

			service := NewConfigService(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			})

			got, err := service.GetBaseConfig(ctx)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.Status, got.Status)
				assert.Equal(t, tt.want.Name, got.Name)
				assert.Equal(t, tt.want.Version, got.Version)
				assert.Equal(t, tt.want.DefaultLocale, got.DefaultLocale)
			}
		})
	}
}

// TestConfigService_Integration 测试配置服务集成功能
func TestConfigService_Integration(t *testing.T) {
	t.Run("TC1-完整工作流", func(t *testing.T) {
		t.Parallel()

		ctx := context.Background()
		mocks := testutil.NewMockDependencies()
		defer mocks.Cleanup()

		// 设置mock
		configs := []*repository.SystemConfig{
			{
				ID:       1,
				Category: "system",
				Key:      "ENABLE_SIGNUP",
				Value:    "true",
			},
		}
		config1 := &repository.SystemConfig{
			ID:       1,
			Category: "system",
			Key:      "ENABLE_SIGNUP",
			Value:    "true",
		}
		config2 := &repository.SystemConfig{
			ID:       2,
			Category: "image",
			Key:      "ENABLE",
			Value:    "true",
		}

		mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configs, nil)
		mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").Return(config1, nil)
		mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "image", "ENABLE").Return(config2, nil)
		mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
		mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		db := &repository.DB{
			SystemConfig: mocks.DB.SystemConfig,
		}

		// 为RunWithTx提供mock实现
		originalRunWithTx := db.RunWithTx
		db.RunWithTx = func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
			// 在事务中执行函数，使用mock的DB
			return fn(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			})
		}
		defer func() {
			db.RunWithTx = originalRunWithTx
		}()

		service := NewConfigService(ctx, db)

		// 测试获取系统通用配置
		commonConfig, err1 := service.GetSystemCommonConfig(ctx)
		assert.NoError(t, err1)
		assert.NotNil(t, commonConfig)

		// 测试获取基础配置
		baseConfig, err2 := service.GetBaseConfig(ctx)
		assert.NoError(t, err2)
		assert.NotNil(t, baseConfig)

		// 测试更新系统通用配置
		updateConfig := &SystemCommonConfig{
			ENABLE_SIGNUP:            true,
			DEFAULT_USER_ROLE:        "user",
			ENABLE_API_KEY:           false,
			ENABLE_CHANNELS:          true,
			ENABLE_COMMUNITY_SHARING: false,
			ENABLE_MESSAGE_RATING:    true,
			JWT_EXPIRES_IN:           "7d",
			SHOW_ADMIN_DETAILS:       true,
			WEBUI_URL:                "http://localhost:3000",
		}
		err3 := service.UpdateSystemCommonConfig(ctx, updateConfig)
		assert.NoError(t, err3)

		// 测试完成 - 所有方法调用都不应该panic
		assert.True(t, true, "All config service methods executed without panic")
	})
}

// TestConfigService_EdgeCases 测试配置服务边界情况
func TestConfigService_EdgeCases(t *testing.T) {
	t.Run("TC1-并发访问", func(t *testing.T) {
		t.Parallel()

		ctx := context.Background()
		mocks := testutil.NewMockDependencies()
		defer mocks.Cleanup()

		// 设置mock
		configs := []*repository.SystemConfig{
			{
				ID:       1,
				Category: "system",
				Key:      "ENABLE_SIGNUP",
				Value:    "true",
			},
		}
		config1 := &repository.SystemConfig{
			ID:       1,
			Category: "system",
			Key:      "ENABLE_SIGNUP",
			Value:    "true",
		}
		config2 := &repository.SystemConfig{
			ID:       2,
			Category: "image",
			Key:      "ENABLE",
			Value:    "true",
		}

		mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configs, nil)
		mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "system", "ENABLE_SIGNUP").Return(config1, nil)
		mocks.DB.SystemConfig.On("GetByCategoryAndKey", mock.Anything, "image", "ENABLE").Return(config2, nil)
		mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
		mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		db := &repository.DB{
			SystemConfig: mocks.DB.SystemConfig,
		}

		// 为RunWithTx提供mock实现
		originalRunWithTx := db.RunWithTx
		db.RunWithTx = func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
			// 在事务中执行函数，使用mock的DB
			return fn(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			})
		}
		defer func() {
			db.RunWithTx = originalRunWithTx
		}()

		service := NewConfigService(ctx, db)

		// 测试并发访问不会panic
		done := make(chan bool, 3)

		go func() {
			_, _ = service.GetSystemCommonConfig(ctx)
			done <- true
		}()

		go func() {
			_, _ = service.GetBaseConfig(ctx)
			done <- true
		}()

		go func() {
			config := &SystemCommonConfig{
				ENABLE_SIGNUP:     true,
				DEFAULT_USER_ROLE: "user",
			}
			_ = service.UpdateSystemCommonConfig(ctx, config)
			done <- true
		}()

		// 等待所有goroutine完成
		for i := 0; i < 3; i++ {
			<-done
		}

		assert.True(t, true, "Concurrent access to config service completed without panic")
	})

	t.Run("TC2-极端配置值", func(t *testing.T) {
		t.Parallel()

		ctx := context.Background()
		mocks := testutil.NewMockDependencies()
		defer mocks.Cleanup()

		// 设置mock
		configs := []*repository.SystemConfig{
			{
				ID:       1,
				Category: "system",
				Key:      "ENABLE_SIGNUP",
				Value:    "true",
			},
		}

		mocks.DB.SystemConfig.On("GetByCategory", mock.Anything, "system").Return(configs, nil)

		db := &repository.DB{
			SystemConfig: mocks.DB.SystemConfig,
		}

		// 为RunWithTx提供mock实现
		originalRunWithTx := db.RunWithTx
		db.RunWithTx = func(ctx context.Context, fn func(context.Context, *repository.DB) error) error {
			// 在事务中执行函数，使用mock的DB
			return fn(ctx, &repository.DB{
				SystemConfig: mocks.DB.SystemConfig,
			})
		}
		defer func() {
			db.RunWithTx = originalRunWithTx
		}()

		service := NewConfigService(ctx, db)

		// 测试极端配置值
		config := &SystemCommonConfig{
			ENABLE_SIGNUP:     true,
			DEFAULT_USER_ROLE: "admin",
			JWT_EXPIRES_IN:    "365d",
			WEBUI_URL:         "https://very-long-url-that-might-cause-issues.example.com/very-long-path",
		}

		// 添加必要的mock
		mocks.DB.SystemConfig.On("Create", mock.Anything, mock.AnythingOfType("*repository.SystemConfig")).Return(nil)
		mocks.DB.SystemConfig.On("UpdateByCategoryAndKey", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		err := service.UpdateSystemCommonConfig(ctx, config)
		assert.NoError(t, err)

		assert.True(t, true, "Extreme config values handled without panic")
	})
}

// BenchmarkConfigService_NewService 创建配置服务性能基准测试
func BenchmarkConfigService_NewService(b *testing.B) {
	ctx := context.Background()

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		service := NewConfigService(ctx, &repository.DB{})
		_ = service
	}
}

// BenchmarkConfigService_GetSystemCommonConfig 获取系统通用配置性能基准测试
func BenchmarkConfigService_GetSystemCommonConfig(b *testing.B) {
	ctx := context.Background()
	service := NewConfigService(ctx, &repository.DB{})

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetSystemCommonConfig(ctx)
	}
}

// BenchmarkConfigService_GetBaseConfig 获取基础配置性能基准测试
func BenchmarkConfigService_GetBaseConfig(b *testing.B) {
	ctx := context.Background()
	service := NewConfigService(ctx, &repository.DB{})

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = service.GetBaseConfig(ctx)
	}
}

// BenchmarkConfigService_UpdateSystemCommonConfig 更新系统通用配置性能基准测试
func BenchmarkConfigService_UpdateSystemCommonConfig(b *testing.B) {
	ctx := context.Background()
	service := NewConfigService(ctx, &repository.DB{})

	config := &SystemCommonConfig{
		ENABLE_SIGNUP:            true,
		DEFAULT_USER_ROLE:        "user",
		ENABLE_API_KEY:           false,
		ENABLE_CHANNELS:          true,
		ENABLE_COMMUNITY_SHARING: false,
		ENABLE_MESSAGE_RATING:    true,
		JWT_EXPIRES_IN:           "7d",
		SHOW_ADMIN_DETAILS:       true,
		WEBUI_URL:                "http://localhost:3000",
	}

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = service.UpdateSystemCommonConfig(ctx, config)
	}
}
