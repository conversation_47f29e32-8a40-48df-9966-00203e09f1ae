{"_format": "hh-sol-artifact-1", "contractName": "ERC20Sample", "sourceName": "contracts/Test/ERC20Sample.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}