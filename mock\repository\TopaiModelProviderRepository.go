// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// TopaiModelProviderRepository is an autogenerated mock type for the TopaiModelProviderRepository type
type TopaiModelProviderRepository struct {
	mock.Mock
}

// GetAll provides a mock function with given fields: ctx
func (_m *TopaiModelProviderRepository) GetAll(ctx context.Context) ([]*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []*repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*repository.TopaiModelProvider, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*repository.TopaiModelProvider); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByID provides a mock function with given fields: ctx, id
func (_m *TopaiModelProviderRepository) GetByID(ctx context.Context, id uint) (*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.TopaiModelProvider, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.TopaiModelProvider); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ctx, ids
func (_m *TopaiModelProviderRepository) GetByIds(ctx context.Context, ids []uint) ([]*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for GetByIds")
	}

	var r0 []*repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []uint) ([]*repository.TopaiModelProvider, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []uint) []*repository.TopaiModelProvider); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []uint) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByNames provides a mock function with given fields: ctx, names
func (_m *TopaiModelProviderRepository) GetByNames(ctx context.Context, names []string) ([]*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx, names)

	if len(ret) == 0 {
		panic("no return value specified for GetByNames")
	}

	var r0 []*repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModelProvider, error)); ok {
		return rf(ctx, names)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModelProvider); ok {
		r0 = rf(ctx, names)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, names)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUuids provides a mock function with given fields: ctx, uuids
func (_m *TopaiModelProviderRepository) GetByUuids(ctx context.Context, uuids []string) ([]*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx, uuids)

	if len(ret) == 0 {
		panic("no return value specified for GetByUuids")
	}

	var r0 []*repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*repository.TopaiModelProvider, error)); ok {
		return rf(ctx, uuids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*repository.TopaiModelProvider); ok {
		r0 = rf(ctx, uuids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, uuids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByWalletAddress provides a mock function with given fields: ctx, walletAddress
func (_m *TopaiModelProviderRepository) GetByWalletAddress(ctx context.Context, walletAddress string) (*repository.TopaiModelProvider, error) {
	ret := _m.Called(ctx, walletAddress)

	if len(ret) == 0 {
		panic("no return value specified for GetByWalletAddress")
	}

	var r0 *repository.TopaiModelProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.TopaiModelProvider, error)); ok {
		return rf(ctx, walletAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.TopaiModelProvider); ok {
		r0 = rf(ctx, walletAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TopaiModelProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, walletAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTopaiModelProviderRepository creates a new instance of TopaiModelProviderRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTopaiModelProviderRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TopaiModelProviderRepository {
	mock := &TopaiModelProviderRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
