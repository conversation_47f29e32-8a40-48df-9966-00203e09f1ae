package handlers

import (
	"errors"
	"regexp"

	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type UserAppHandler struct {
	service *service.Api
	log     *logger.ModuleLogger
}

func NewUserAppHandler(service *service.Api) *UserAppHandler {
	return &UserAppHandler{
		service: service,
		log:     logger.GetLogger("user_app_handler"),
	}
}

// GetUserAppList 获取用户应用列表
// @Summary 获取用户应用列表
// @Description 获取用户应用列表
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Success 200 {object} []service.UserAppInfoOP
// @Failure 400 {object} string
// @Failure 500 {object} string
// @Router /api/v1/user/app/list [get]
func (h *UserAppHandler) GetUserAppList(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")

	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appList, err := h.service.UserApp.GetUserAppList(ctx, userOP.ID)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, appList)
}

// GetUserAppDetail 获取用户应用详情
// @Summary 获取用户应用详情
// @Description 获取用户应用详情
// @Tags UserApp
// @Accept json
// @Produce json
// @Success 200 {object} service.UserAppInfoOP
// @Failure 400 {object} string
// @Failure 500 {object} string
// @Router /api/v1/user/app/info/{app_id} [get]
func (h *UserAppHandler) GetUserAppDetail(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appUuid := c.Param("app_id")
	appDetail, err := h.service.UserApp.GetUserAppDetail(ctx, userOP.ID, appUuid)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}

	api.Success(c, appDetail)
}

// CreateUserApp 创建用户应用
// @Summary 创建用户应用
// @Description 创建用户应用
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param app_info body service.UserAppInfoOP true "应用信息"
// @Success 200 {object} service.UserAppInfoOP "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/create [post]
func (h *UserAppHandler) CreateUserApp(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appInfo := &service.UserAppInfoOP{}
	if err := c.ShouldBindJSON(appInfo); err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	// 应用名称不能为空, 长度不超过32个字符, 只支持英文、数字、下划线， 横线， 斜杠
	if appInfo.AppName == "" || len(appInfo.AppName) > 32 || !regexp.MustCompile(`^[a-zA-Z0-9_\-/]{4,32}$`).MatchString(appInfo.AppName) {
		api.Fail(c, api.CodeClientError, "app name is invalid, only support english, number, underline, hyphen, slash, length 4-32", errors.New("app name is invalid"))
		return
	}
	err := h.service.UserApp.CreateUserApp(ctx, userOP.ID, appInfo)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, appInfo)
}

// UpdateUserApp 更新用户应用
// @Summary 更新用户应用
// @Description 更新用户应用
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param app_uuid path string true "应用ID"
// @Param app_info body service.UserAppInfoOP true "应用信息"
// @Success 200 {object} service.UserAppInfoOP "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/update/{app_id} [post]
func (h *UserAppHandler) UpdateUserApp(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appUuid := c.Param("app_id")
	appInfo := &service.UserAppInfoOP{
		AppUuid: appUuid,
	}
	if err := c.ShouldBindJSON(appInfo); err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	err := h.service.UserApp.UpdateUserApp(ctx, userOP.ID, appInfo)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, appInfo)
}

// DeleteUserApp 删除用户应用
// @Summary 删除用户应用
// @Description 删除用户应用
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param app_uuid path string true "应用ID"
// @Success 200 {object} nil "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/delete/{app_id} [post]
func (h *UserAppHandler) DeleteUserApp(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appUuid := c.Param("app_id")
	err := h.service.UserApp.DeleteUserApp(ctx, userOP.ID, appUuid)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, nil)
}

// GetUserAppKeyList 获取用户应用密钥列表
// @Summary 获取用户应用密钥列表
// @Description 获取用户应用密钥列表
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param app_uuid path string true "应用ID"
// @Success 200 {object} []service.AppKeyOP
// @Failure 400 {object} string
// @Failure 500 {object} string
// @Router /api/v1/user/app/key/list [get]
func (h *UserAppHandler) GetUserAppKeyList(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}

	keyList, err := h.service.UserApp.GetUserAllAppKeyList(ctx, userOP.ID)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, keyList)
}

type CreateUserAppKeyRequest struct {
	KeyName string `json:"key_name" form:"key_name"`
}

// CreateUserAppKey 创建用户应用密钥
// @Summary 创建用户应用密钥
// @Description 创建用户应用密钥
// @Tags UserApp
// @Accept json
// @Produce json
// @Param app_id path string true "应用ID"
// @Param request body CreateUserAppKeyRequest true "密钥名称"
// @Success 200 {object} service.AppKeyOP "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/key/{app_id}/create [post]
func (h *UserAppHandler) CreateUserAppKey(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appUuid := c.Param("app_id")
	request := &CreateUserAppKeyRequest{}
	if err := c.ShouldBindJSON(request); err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	keyName := request.KeyName
	// keyName 不能为空，只支持英文、数字、下划线， 横线， 斜杠，长度不超过32个字符
	if keyName == "" || !regexp.MustCompile(`^[a-zA-Z0-9_\-/]{4,32}$`).MatchString(keyName) {
		api.Fail(c, api.CodeClientError, "key name is invalid, only support english, number, underline, hyphen, slash, length 4-32", errors.New("key name is invalid"))
		return
	}
	key, err := h.service.UserApp.CreateUserAppKey(ctx, userOP.ID, appUuid, keyName)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, key)
}

// DeleteUserAppKey 删除用户应用密钥
// @Summary 删除用户应用密钥
// @Description 删除用户应用密钥
// @Tags UserApp
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param app_uuid path string true "应用ID"
// @Param key_name body string true "密钥名称"
// @Success 200 {object} nil "成功"
// @Failure 400 {object} string "失败"
// @Failure 500 {object} string "失败"
// @Router /api/v1/user/app/key/{app_id}/delete/{key_name} [post]
func (h *UserAppHandler) DeleteUserAppKey(c *gin.Context) {
	ctx := c.Request.Context()
	user, _ := c.Get("user")
	userOP, ok := user.(*service.UserBaseOP)
	if !ok {
		api.Fail(c, api.CodeUnauthorized, "user not found", errors.New("user not found"))
		return
	}
	appUuid := c.Param("app_id")
	keyName := c.Param("key_name")
	err := h.service.UserApp.DeleteUserAppKey(ctx, userOP.ID, appUuid, keyName)
	if err != nil {
		api.Fail(c, api.CodeClientError, err.Error(), err)
		return
	}
	api.Success(c, nil)
}

func (h *UserAppHandler) Close() {

}
