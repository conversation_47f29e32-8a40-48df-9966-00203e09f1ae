package handlers

import (
	"context"
	"errors"
	"math/big"
	"math/rand"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
	"topnetwork.ai/topai/chat-webserver/internal/service"
	"topnetwork.ai/topai/chat-webserver/logger"
)

type topaiModelHandler struct {
	mainCtx context.Context
	service *service.Api
	logger  *logger.ModuleLogger
}

func NewTopaiModelHandler(ctx context.Context, service *service.Api) *topaiModelHandler {
	return &topaiModelHandler{
		mainCtx: ctx,
		service: service,
		logger:  logger.GetLogger("topai_model_handler"),
	}
}

func (h *topaiModelHandler) Start(ctx context.Context) {
	// 模拟用户调用api
	go h.startTask(ctx)
	go h.startReport(ctx)
}

func (h *topaiModelHandler) startTask(ctx context.Context) {
	h.logger.Info("start task")

	// 模拟用户通过api调用
	uuidList := []string{
		"87de60d2-4113-11f0-a5b0-52e60342603b",
		"87de66bf-4113-11f0-a5b0-52e60342603b",
		"87de6880-4113-11f0-a5b0-52e60342603b",
		"87de697f-4113-11f0-a5b0-52e60342603b",
		"87de6a46-4113-11f0-a5b0-52e60342603b",
		"87de6b18-4113-11f0-a5b0-52e60342603b",
		"87de6be9-4113-11f0-a5b0-52e60342603b",
		"87de6cc2-4113-11f0-a5b0-52e60342603b",
		"87de793d-4113-11f0-a5b0-52e60342603b",
		"87de7a78-4113-11f0-a5b0-52e60342603b",
		"87de7b47-4113-11f0-a5b0-52e60342603b",
		"87de7c09-4113-11f0-a5b0-52e60342603b",
		"87de7cde-4113-11f0-a5b0-52e60342603b",
		"87de7d9c-4113-11f0-a5b0-52e60342603b",
		"87de7e1c-4113-11f0-a5b0-52e60342603b",
		"87de7e95-4113-11f0-a5b0-52e60342603b",
		"87de7f4b-4113-11f0-a5b0-52e60342603b",
		"87de8016-4113-11f0-a5b0-52e60342603b",
		"87de80b9-4113-11f0-a5b0-52e60342603b",
		"87de8139-4113-11f0-a5b0-52e60342603b",
		"87de81d5-4113-11f0-a5b0-52e60342603b",
		"87de82a3-4113-11f0-a5b0-52e60342603b",
		"87de831f-4113-11f0-a5b0-52e60342603b",
		"87de8398-4113-11f0-a5b0-52e60342603b",
		"87de8410-4113-11f0-a5b0-52e60342603b",
		"87de8489-4113-11f0-a5b0-52e60342603b",
		"87de850a-4113-11f0-a5b0-52e60342603b",
		"87de85d4-4113-11f0-a5b0-52e60342603b",
	}
	// 1. 获取所有的appKey
	appKeys, err := h.service.Topai.GetAppKeyList(ctx)
	if err != nil {
		h.logger.Error("get app key list failed", zap.Error(err))
		return
	}
	newAppKeys := make([]*service.AppKeyOP, 0)
	for _, appKey := range appKeys {
		if slices.Contains(uuidList, appKey.AppUuid) {
			newAppKeys = append(newAppKeys, appKey)
		}
	}
	appKeys = newAppKeys
	// 2. 获取所有的模型
	models, err := h.service.Topai.GetTopaiModelList(ctx, &service.GetTopaiModelListRequest{
		Page:      1,
		PageSize:  10000,
		OrderBy:   "id",
		OrderType: "asc",
	})
	if err != nil {
		h.logger.Error("get topai model list failed", zap.Error(err))
		return
	}
	modelIds := make([]string, len(models))
	modelMap := make(map[string]*service.TopaiModelsInfoOP)
	for i, model := range models {
		modelIds[i] = model.ModelId
		modelMap[model.ModelId] = model
	}

	// 最多并发100个任务
	maxChan := make(chan struct{}, 100)
	defer close(maxChan)
	// 3. 随机选择一个appkey，然后随机选择一个模型
	for {
		select {
		case <-ctx.Done():
			return
		case maxChan <- struct{}{}:
		}
		// 每次任务开始前，随机等待 10～100s
		time.Sleep(time.Second * time.Duration(rand.Intn(90)+10))
		go func() {
			defer func() {
				<-maxChan
			}()
			// 随机选择一个appkey
			appKey := appKeys[rand.Intn(len(appKeys))]
			// 随机选择一个模型
			selectedModel := h.selectAppModel(ctx, appKey, models)
			if selectedModel == nil {
				h.logger.Debug("select app model failed", zap.String("app_uuid", appKey.AppUuid))
				return
			}

			// 当前是测试环境，TOP的单价是1U，限制每笔价格在 0.0001～0.01U之间
			totalCost := big.NewInt(0).Mul(big.NewInt(1e14), big.NewInt(rand.Int63n(100)+1))
			var inputTokens, outputTokens, ttft, duration *big.Int
			inputPrice, _ := big.NewInt(0).SetString(selectedModel.InputPrice, 10)
			outputPrice, _ := big.NewInt(0).SetString(selectedModel.OutputPrice, 10)

			switch selectedModel.ModelType {
			case repository.RemoterModelTypeLLM.String():
				inputTokens = big.NewInt(rand.Int63n(100) + 20)
				outputTokens = big.NewInt(rand.Int63n(100) + 20)
				ttft = big.NewInt(rand.Int63n(5000) + 100)      // 100～5000ms
				duration = big.NewInt(rand.Int63n(20000) + 100) // 1000～20000ms
			case repository.RemoterModelTypeASR.String():
				inputTokens = big.NewInt(rand.Int63n(100) + 20)
				outputTokens = big.NewInt(rand.Int63n(100) + 20)
				ttft = big.NewInt(rand.Int63n(5000) + 1000)  // 1000～5000ms
				duration = big.NewInt(rand.Int63n(200) + 10) // 10～200ms
			case repository.RemoterModelTypeTTS.String():
				inputTokens = big.NewInt(rand.Int63n(100) + 20)
				outputTokens = big.NewInt(rand.Int63n(100) + 20)
				ttft = big.NewInt(rand.Int63n(5000) + 100)   // 100～5000ms
				duration = big.NewInt(rand.Int63n(200) + 10) // 10～200ms
			case repository.RemoterModelTypeTTI.String():
				inputTokens = big.NewInt(rand.Int63n(100) + 20)
				outputTokens = big.NewInt(125)
				ttft = big.NewInt(rand.Int63n(5000) + 1000)  // 1000～5000ms
				duration = big.NewInt(rand.Int63n(200) + 10) // 10～200ms
			default:
				return
			}

			if inputPrice.Cmp(big.NewInt(0)) > 0 && outputPrice.Cmp(big.NewInt(0)) > 0 {
				// 价格都不为0，根据价格计算token，随机产生一个总金额分配比例
				ratio := big.NewInt(rand.Int63n(60) + 20)
				inputTokens = big.NewInt(0).Div(big.NewInt(0).Mul(totalCost, ratio), inputPrice)
				inputTokens = inputTokens.Div(inputTokens, big.NewInt(100))
				outputTokens = big.NewInt(0).Div(big.NewInt(0).Mul(totalCost, big.NewInt(0).Sub(big.NewInt(100), ratio)), outputPrice)
				outputTokens = outputTokens.Div(outputTokens, big.NewInt(100))
			} else if outputPrice.Cmp(big.NewInt(0)) > 0 {
				// 输入价格为0，根据输出价格计算token
				outputTokens = big.NewInt(0).Div(totalCost, outputPrice)
			} else if inputPrice.Cmp(big.NewInt(0)) > 0 {
				// 输出价格为0，根据输入价格计算token
				inputTokens = big.NewInt(0).Div(totalCost, inputPrice)
			}

			// 模拟用户调用api
			err = h.service.Model.MockUserChatUsage(ctx, appKey, selectedModel, inputTokens, outputTokens, ttft, duration)
			if err != nil {
				h.logger.Error("mock user chat usage failed", zap.Error(err))
			}
		}()
	}
}

// 配置app调用模型
type appModelConfig struct {
	ModelType map[repository.RemoterModelType]int      // 模型类型，key是模型类型，value是模型%比例，总加起来为 100
	ModelIds  map[repository.RemoterModelType][]string // 模型ID，key是模型类型，value是模型ID列表，为空表示不限制
}

func (h *topaiModelHandler) selectAppModel(ctx context.Context, appKey *service.AppKeyOP, models []*service.TopaiModelsInfoOP) *service.TopaiModelsInfoOP {
	// 配置app调用模型, appKey.AppUuid 为key
	configs := map[string]*appModelConfig{
		//CodeGenX
		"87de60d2-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"claude-sonnet-4@v3",
					"gemini-2.5-flash-preview-05-20@v5",
					"gpt-4o-mini@v3",
					"claude-3.7-sonnet@v5",
					"deepseek-chat@v6",
					"unslopnemo-12b@v3",
					"gemini-pro-1.5@v2",
					"devstral-small@v5",
					"codestral-2501@v2",
				},
			},
		},
		//VoiceCraft
		"87de66bf-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 30,
				repository.RemoterModelTypeTTS: 70,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"gemini-flash-1.5@v2",
					"GLM-4-32B@v3",
					"mistral-small-24b-instruct-2501@v4",
					"claude-3.7-sonnet@v7",
					"llama-3.1-70b-instruct@v3",
					"grok-3-mini-beta@v4",
				},
				repository.RemoterModelTypeTTS: []string{
					"Zonos-v0.1-transformer@v2",
					"Kokoro-82M@v2",
					"Dia-1.6B@v2",
					"orpheus-3b-0.1-ft@v2",
				},
			},
		},
		//PolyGlot
		"87de6880-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 40,
				repository.RemoterModelTypeASR: 60,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large-v3-turbo@v2",
				},
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"claude-sonnet-4@v3",
					"gemini-2.5-flash-preview-05-20@v5",
					"gpt-4o-mini@v3",
					"claude-3.7-sonnet@v5",
					"deepseek-chat@v6",
					"mixtral-8x7b-instruct@v3",
					"mistral-small-3.1-24b-instruct@v5",
					"o4-mini-high@v4",
				},
			},
		},
		//StoryForge
		"87de697f-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"gemini-2.5-flash-preview-05-20@v6",
					"DeepSeek-R1-NSFW@v2",
					"GLM-4-32B@v3",
					"gemini-2.0-flash-001@v2",
				},
			},
		},
		//MathWhiz
		"87de6a46-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 90,
				repository.RemoterModelTypeASR: 10,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large-v3-turbo@v2",
				},
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"gemma-3-27b-it@v4",
					"deepseek-chat@v5",
					"deepseek-r1@v5",
					"claude-sonnet-4@v3",
					"gemini-2.5-flash-preview-05-20@v5",
					"gpt-4o-mini@v3",
					"claude-3.7-sonnet@v5",
					"claude-3-haiku@v2",
					"chatgpt-4o-latest@v4",
					"hermes-2-pro-llama-3-8b@v4",
					"l3.1-euryale-70b@v3",
					"qwen-2.5-coder-32b-instruct@v2",
				},
			},
		},
		//ArtPrompt
		"87de6b18-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 50,
				repository.RemoterModelTypeTTI: 50,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"gemma-3-27b-it@v4",
					"deepseek-r1@v5",
					"gemini-2.5-pro-preview@v4",
					"gpt-4.1-mini@v3",
					"deepseek-chat@v6",
					"deepseek-r1-distill-llama-70b@v2",
					"claude-3.5-haiku@v3",
					"gemma-3-4b-it@v3",
					"mistral-tiny@v2",
					"qwen3-14b@v5",
				},
				repository.RemoterModelTypeTTI: []string{
					"FLUX-1-dev@v2",
					"FLUX-pro@v2",
					"Juggernaut-Flux@v1",
					"sd3.5@v2",
					"sdxl-turbo@v3",
				},
			},
		},
		//LegalEase
		"87de6be9-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"gemma-3-27b-it@v4",
					"deepseek-chat@v5",
					"deepseek-r1@v5",
					"gemini-2.5-pro-preview@v4",
					"gpt-4.1-mini@v3",
					"claude-3.7-sonnet@v7",
					"llama-3.1-70b-instruct@v3",
					"grok-3-mini-beta@v4",
				},
			},
		},
		//janitorai
		"87de6cc2-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"gemma-3-27b-it@v4",
					"deepseek-chat@v5",
					"deepseek-r1@v5",
					"gemini-2.5-pro-preview@v4",
					"gpt-4.1-mini@v3",
					"deepseek-chat@v6",
					"qwen3-30b-a3b@v2",
					"minimax-01@v2",
					"lfm-7b@v4",
					"rocinante-12b@v3",
				},
			},
		},
		//TutorAI
		"87de793d-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 70,
				repository.RemoterModelTypeTTI: 30,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gpt-4.1@v4",
					"mistral-nemo@v4",
					"deepseek-r1@v4",
					"gemini-2.0-flash-lite-001@v4",
					"mixtral-8x7b-instruct@v3",
					"mistral-small-3.1-24b-instruct@v5",
					"o4-mini-high@v4",
				},
				repository.RemoterModelTypeTTI: []string{
					"FLUX-1.1-pro@v3",
					"FLUX-pro@v2",
					"Juggernaut-Flux@v1",
					"sd3.5-medium@v1",
				},
			},
		},
		//SalesBot
		"87de7a78-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gpt-4.1@v4",
					"mistral-nemo@v4",
					"deepseek-r1@v4",
					"gemini-2.0-flash-lite-001@v4",
					"unslopnemo-12b@v3",
					"gemini-pro-1.5@v2",
					"devstral-small@v5",
					"codestral-2501@v2",
				},
			},
		},
		//HealthGuard
		"87de7b47-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 70,
				repository.RemoterModelTypeTTS: 20,
				repository.RemoterModelTypeASR: 10,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large-v3-turbo@v2",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gpt-4.1@v4",
					"mistral-nemo@v4",
					"deepseek-r1@v4",
					"gemini-2.0-flash-lite-001@v4",
					"deepseek-r1-distill-llama-70b@v2",
					"claude-3.5-haiku@v3",
					"gemma-3-4b-it@v3",
					"mistral-tiny@v2",
					"qwen3-14b@v5",
				},
				repository.RemoterModelTypeTTS: []string{
					"Zonos-v0.1-transformer@v2",
					"CosyVoice2-0.5B@v2",
				},
			},
		},
		//MusicLyric
		"87de7c09-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 30,
				repository.RemoterModelTypeTTS: 20,
				repository.RemoterModelTypeASR: 50,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large@v3",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-flash-1.5-8b@v4",
					"GLM-4-32B@v3",
					"qwen3-30b-a3b@v2",
					"minimax-01@v2",
					"lfm-7b@v4",
					"rocinante-12b@v3",
				},
				repository.RemoterModelTypeTTS: []string{
					"orpheus-3b-0.1-ft@v2",
					"csm-1b@v3",
					"Zonos@v0.1-hybrid@v4",
					"CosyVoice2-0.5B@v2",
				},
			},
		},
		//DebateMaster
		"87de7cde-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 60,
				repository.RemoterModelTypeTTS: 20,
				repository.RemoterModelTypeASR: 20,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large-v3-turbo@v2",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-flash-1.5-8b@v4",
					"GLM-4-32B@v3",
					"llama-3.3-70b-instruct@v4",
					"gemma-3-27b-it@v2",
					"gpt-4o-2024-11-20@v3",
					"wizardlm-2-8x22b@v4",
					"qwen3-235b-a22b@v4",
					"gpt-4o@v3",
				},
				repository.RemoterModelTypeTTS: []string{
					"Zonos-v0.1-transformer@v2",
					"Kokoro-82M@v2",
					"Dia-1.6B@v2",
					"csm-1b@v3",
					"Zonos@v0.1-hybrid@v4",
				},
			},
		},
		//CareerPath
		"87de7d9c-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-flash-1.5-8b@v4",
					"GLM-4-32B@v3",
					"hermes-3-llama-3.1-70b@v4",
					"mythomax-l2-13b@v3",
					"gpt-4o-mini-2024-07-18@v4",
				},
			},
		},
		//RecipeGen
		"87de7e1c-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-chat@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-2.5-flash-preview-05-20@v6",
					"DeepSeek-R1-NSFW@v2",
					"GLM-4-32B@v3",
					"gemini-2.0-flash-001@v2",
					"claude-3-haiku@v2",
					"chatgpt-4o-latest@v4",
					"hermes-2-pro-llama-3-8b@v4",
					"l3.1-euryale-70b@v3",
					"qwen-2.5-coder-32b-instruct@v2",
				},
			},
		},
		//Nanobrowser
		"87de7e95-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-2.5-flash-preview-05-20@v6",
					"gemini-2.0-flash-001@v2",
					"hermes-3-llama-3.1-70b@v4",
					"mythomax-l2-13b@v3",
					"gpt-4o-mini-2024-07-18@v4",
				},
			},
		},
		//ChatAdvisor
		"87de7f4b-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 60,
				repository.RemoterModelTypeTTS: 20,
				repository.RemoterModelTypeASR: 20,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large-v3-turbo@v2",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-2.5-flash-preview-05-20@v6",
					"DeepSeek-R1-NSFW@v2",
					"GLM-4-32B@v3",
					"gemini-2.0-flash-001@v2",
					"llama-3.3-70b-instruct@v4",
					"gemma-3-27b-it@v2",
					"gpt-4o-2024-11-20@v3",
					"wizardlm-2-8x22b@v4",
					"qwen3-235b-a22b@v4",
					"gpt-4o@v3",
				},
				repository.RemoterModelTypeTTS: []string{
					"Dia-1.6B@v2",
					"orpheus-3b-0.1-ft@v2",
					"csm-1b@v3",
				},
			},
		},
		//GamePlot
		"87de8016-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 80,
				repository.RemoterModelTypeASR: 20,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large@v3",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-flash-1.5@v2",
					"GLM-4-32B@v3",
					"mistral-small-24b-instruct-2501@v4",
					"llama-4-scout@v2",
					"qwen3-32b@v3",
					"hermes-3-llama-3.1-405b@v4",
					"gemini-2.0-flash-001@v2",
					"gemma-3-27b-it@v2",
					"gpt-4o-2024-11-20@v3",
					"wizardlm-2-8x22b@v4",
					"qwen3-235b-a22b@v4",
					"gpt-4o@v3",
				},
			},
		},
		//ChatWise
		"87de80b9-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-flash-1.5@v2",
					"GLM-4-32B@v3",
					"mistral-small-24b-instruct-2501@v4",
					"llama-3.2-1b-instruct@v2",
					"claude-3.5-sonnet@v3",
					"grok-3-beta@v3",
				},
			},
		},
		//FitnessAI
		"87de8139-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 80,
				repository.RemoterModelTypeTTI: 20,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-flash-1.5@v2",
					"GLM-4-32B@v3",
					"mistral-small-24b-instruct-2501@v4",
					"lfm-3b@v2",
					"rocinante-12b@v3",
					"gemma-3-12b-it@v4",
					"l3-lunaris-8b@v2",
					"ministral-8b@v4",
				},
				repository.RemoterModelTypeTTI: []string{
					"FLUX-1-dev@v2",
					"FLUX-1.1-pro@v3",
					"FLUX-pro@v2",
					"Juggernaut-Flux@v1",
				},
			},
		},
		//TravelGenie
		"87de81d5-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-2.5-flash-preview@v2",
					"llama-3.2-1b-instruct@v2",
					"claude-3.5-sonnet@v3",
					"grok-3-beta@v3",
				},
			},
		},
		//Kilo Code
		"87de82a3-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"gemma-3-27b-it@v4",
					"gemini-2.5-flash-preview@v2",
					"claude-opus-4@v2",
					"gpt-4.1-nano@v4",
					"lfm-3b@v2",
					"rocinante-12b@v3",
					"gemma-3-12b-it@v4",
					"l3-lunaris-8b@v2",
					"ministral-8b@v4",
				},
			},
		},
		//PoemCraft
		"87de831f-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"gemini-2.0-flash-exp@v3",
					"deepseek-r1-0528@v3",
					"qwen3-32b@v5",
					"llama-4-maverick@v4",
					"gemini-2.5-flash-preview@v2",
					"llama-4-scout@v2",
					"qwen3-32b@v3",
					"hermes-3-llama-3.1-405b@v4",
				},
			},
		},
		//CryptoInsight
		"87de8398-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 70,
				repository.RemoterModelTypeTTI: 30,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"devstral-small@v4",
					"deepseek-chat@v5",
					"deepseek-r1@v5",
					"gemini-2.0-flash-exp@v3",
					"claude-3.5-sonnet@v5",
					"llama-4-maverick@v5",
					"gpt-4.1-nano@v4",
				},
				repository.RemoterModelTypeTTI: []string{
					"FLUX-1-dev@v2",
					"Juggernaut-Lightning-Flux@v2",
					"sd3.5-medium@v1",
					"sdxl-turbo@v3",
				},
			},
		},
		//StudyBuddy
		"87de8410-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"llama-3.1-nemotron-ultra-253b@v2",
					"mistral-small-3.1-24b-instruct@v3",
					"qwq-32b@v3",
					"devstral-small@v4",
					"deepseek-chat@v5",
					"deepseek-r1@v5",
					"claude-3.5-sonnet@v5",
					"llama-4-maverick@v5",
					"gpt-4.1-nano@v4",
					"llama-3.2-3b-instruct@v3",
					"grok-3-beta@v3",
					"rocinante-12b@v3",
				},
			},
		},
		//TypingMind
		"87de8489-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 100,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-2.0-flash-exp@v3",
					"llama-3.1-lumimaid-8b@v3",
					"codestral-2501@v2",
					"qwen-turbo@v2",
				},
			},
		},
		//EcoTrack
		"87de850a-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 70,
				repository.RemoterModelTypeTTI: 30,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeLLM: []string{
					"deepseek-prover@v2",
					"qwen3-14b@v3",
					"devstral-small@v4",
					"deepseek-r1t-chimera@v3",
					"gemini-2.0-flash-exp@v3",
					"claude-3.5-sonnet@v5",
					"llama-4-maverick@v5",
					"gpt-4.1-nano@v4",
					"llama-3.1-lumimaid-8b@v3",
					"codestral-2501@v2",
					"qwen-turbo@v2",
				},
				repository.RemoterModelTypeTTI: []string{
					"FLUX-pro@v2",
					"Juggernaut-Lightning-Flux@v2",
					"sd3.5-medium@v1",
					"sd3.5@v2",
					"sdxl-turbo@v3",
				},
			},
		},
		//VoiceClone
		"87de85d4-4113-11f0-a5b0-52e60342603b": &appModelConfig{
			ModelType: map[repository.RemoterModelType]int{
				repository.RemoterModelTypeLLM: 10,
				repository.RemoterModelTypeTTS: 60,
				repository.RemoterModelTypeASR: 30,
			},
			ModelIds: map[repository.RemoterModelType][]string{
				repository.RemoterModelTypeASR: []string{
					"whisper-large@v3",
				},
				repository.RemoterModelTypeLLM: []string{
					"deepseek-r1-0528-qwen3-8b@v3",
					"qwen3-235b-a22b@v5",
					"mai-ds-r1@v4",
					"gemini-2.0-flash-exp@v3",
					"gemini-2.5-flash-preview@v2",
					"claude-opus-4@v2",
					"gpt-4.1-nano@v4",
					"llama-3.2-3b-instruct@v3",
					"grok-3-beta@v3",
					"rocinante-12b@v3",
				},
				repository.RemoterModelTypeTTS: []string{
					"Dia-1.6B@v2",
					"orpheus-3b-0.1-ft@v2",
				},
			},
		},
	}
	conf, ok := configs[appKey.AppUuid]
	if !ok {
		h.logger.Info("app model config not found", zap.String("app_uuid", appKey.AppUuid))
		return nil
	}
	selectModels := make([]*service.TopaiModelsInfoOP, 0)
	totalWeight := 0
	for _, model := range models {
		if conf.ModelType[repository.RemoterModelType(model.ModelType)] > 0 {
			if len(conf.ModelIds) > 0 {
				if slices.Contains(conf.ModelIds[repository.RemoterModelType(model.ModelType)], model.ModelId) {
					totalWeight += model.Weight
					selectModels = append(selectModels, model)
				}
			} else {
				totalWeight += model.Weight
				selectModels = append(selectModels, model)
			}

		}
	}
	if len(selectModels) == 0 {
		h.logger.Info("no model selected", zap.String("app_uuid", appKey.AppUuid))
		return nil
	}

	if totalWeight == 0 {
		// 随机选择一个模型
		h.logger.Info("total weight is 0, random select a model", zap.String("app_uuid", appKey.AppUuid))
		return selectModels[rand.Intn(len(selectModels))]
	}

	randWeight := rand.Intn(totalWeight)
	for _, model := range selectModels {
		if randWeight <= model.Weight {
			return model
		}
		randWeight -= model.Weight
	}
	return nil
}

func (h *topaiModelHandler) startReport(ctx context.Context) {
	h.logger.Info("start report")
	aiModelsAddress := "0x4d3aec3d99d5B1Edf2C375657d0765D960175a3b"
	chainUrl := "https://toptest.crosschain.topnetwork.org"

	// 每5分钟同步一次
	for {
		err := h.service.Topai.SyncOnchainModel(ctx, chainUrl, aiModelsAddress)
		if err != nil {
			h.logger.Error("sync onchain model failed", zap.Error(err))
		}
		select {
		case <-ctx.Done():
			return
		case <-time.After(5 * time.Minute):
		}
	}
}

// GetTopaiModelDetail 获取模型详情
// @Summary 获取模型详情
// @Description 获取指定ID的模型详情
// @Tags topai
// @Accept json
// @Produce json
// @Param model_id path string true "模型ID"
// @Success 200 {object} service.TopaiModelsInfoDetailOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/{model_id} [get]
func (h *topaiModelHandler) GetTopaiModelDetail(c *gin.Context) {
	topaiModelId := c.Param("model_id")
	if topaiModelId == "" {
		h.logger.Error("invalid model id")
		api.Fail(c, api.CodeClientError, "invalid model id", errors.New("invalid model id"))
		return
	}
	detail, err := h.service.Topai.GetTopaiModelDetail(c, topaiModelId)
	if err != nil {
		h.logger.Error("get topai model detail failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model detail failed", err)
		return
	}
	api.Success(c, detail)
}

// GetTopaiModelList 获取模型列表
// @Summary 获取模型列表
// @Description 获取模型列表
// @Tags topai
// @Accept json
// @Produce json
// @Param json body service.GetTopaiModelListRequest true "请求参数"
// @Success 200 {object} []service.TopaiModelsInfoOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models [get]
func (h *topaiModelHandler) GetTopaiModelList(c *gin.Context) {
	req := &service.GetTopaiModelListRequest{}
	if err := c.BindJSON(req); err != nil {
		h.logger.Error("invalid request", zap.Error(err))
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	if req.OrderBy == "" {
		req.OrderBy = "id"
	}
	if req.OrderType == "" {
		req.OrderType = "desc"
	}
	if req.OrderType != "asc" && req.OrderType != "desc" {
		req.OrderType = "desc"
	}
	if req.OrderBy != "id" && req.OrderBy != "price" && req.OrderBy != "throughput" && req.OrderBy != "latency" && req.OrderBy != "context_length" {
		req.OrderBy = "id"
	}
	models, err := h.service.Topai.GetTopaiModelList(c, req)
	if err != nil {
		h.logger.Error("get topai model list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model list failed", err)
		return
	}
	api.Success(c, models)
}

// GetTopaiModelCategoryList 获取模型分类列表
// @Summary 获取模型分类列表
// @Description 获取模型分类列表
// @Tags topai
// @Accept json
// @Produce json
// @Success 200 {object} []service.TopaiModelCategoryOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/categories [get]
func (h *topaiModelHandler) GetUsedTopaiModelCategoryList(c *gin.Context) {
	categoryList, err := h.service.Topai.GetUsedCategoryList(c.Request.Context())
	if err != nil {
		h.logger.Error("get topai model category list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model category list failed", err)
		return
	}
	api.Success(c, categoryList)
}

// GetTopaiModelSeriesList 获取模型系列列表
// @Summary 获取模型系列列表
// @Description 获取模型系列列表
// @Tags topai
// @Accept json
// @Produce json
// @Success 200 {object} []service.TopaiModelSeriesOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/series [get]
func (h *topaiModelHandler) GetTopaiModelSeriesList(c *gin.Context) {
	seriesList, err := h.service.Topai.GetSeriesList(c.Request.Context())
	if err != nil {
		h.logger.Error("get topai model series list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model series list failed", err)
		return
	}
	api.Success(c, seriesList)
}

// GetTopaiModelProviderList 获取模型提供商列表
// @Summary 获取模型提供商列表
// @Description 获取模型提供商列表
// @Tags topai
// @Accept json
// @Produce json
// @Success 200 {object} []service.TopaiModelProviderOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/providers [get]
func (h *topaiModelHandler) GetTopaiModelProviderList(c *gin.Context) {
	providerList, err := h.service.Topai.GetProviderList(c.Request.Context())
	if err != nil {
		h.logger.Error("get topai model provider list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model provider list failed", err)
		return
	}
	api.Success(c, providerList)
}

// GetTopaiModelSupportParamList 获取模型支持参数列表
// @Summary 获取模型支持参数列表
// @Description 获取模型支持参数列表
// @Tags topai
// @Accept json
// @Produce json
// @Success 200 {object} []string
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/support-params [get]
func (h *topaiModelHandler) GetTopaiModelSupportParamList(c *gin.Context) {
	supportParamList, err := h.service.Topai.GetSupportParamList(c.Request.Context())
	if err != nil {
		h.logger.Error("get topai model support param list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model support param list failed", err)
		return
	}
	api.Success(c, supportParamList)
}

// GetTopaiModelTypeList 获取模型类型列表
// @Summary 获取模型类型列表
// @Description 获取模型类型列表
// @Tags topai
// @Accept json
// @Produce json
// @Success 200 {object} []string
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/types [get]
func (h *topaiModelHandler) GetTopaiModelTypeList(c *gin.Context) {
	modelTypeList, err := h.service.Topai.GetModelTypeList(c.Request.Context())
	if err != nil {
		h.logger.Error("get topai model model type list failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model model type list failed", err)
		return
	}
	api.Success(c, modelTypeList)
}

// GetTopaiModelListByTokenSort 获取模型列表（按token排序）
// @Summary 获取模型列表（按token排序）
// @Description 获取模型列表（按token排序）
// @Tags topai
// @Accept json
// @Produce json
// @Param json body service.GetTopaiModelListRequest true "请求参数"
// @Success 200 {object} []service.TopaiModelsInfoOP
// @Failure 400 {object} api.Response
// @Failure 401 {object} api.Response
// @Failure 404 {object} api.Response
// @Failure 500 {object} api.Response
// @Router /api/v1/topai/models/ranking [get]
func (h *topaiModelHandler) GetTopaiModelListByTokenSort(c *gin.Context) {
	req := &service.GetTopaiModelListRequest{}
	if err := c.BindJSON(req); err != nil {
		h.logger.Error("invalid request", zap.Error(err))
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	models, err := h.service.Topai.GetTopaiModelListByTokenSort(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("get topai model list by token sort failed", zap.Error(err))
		api.Fail(c, api.CodeServerError, "get topai model list by token sort failed", err)
		return
	}
	api.Success(c, models)
}

// @Summary      管理员查看模型列表
// @Description  获取所有模型信息，包含输入输出价格、token消耗量、上下架状态等
// @Tags         管理员-模型管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true   "Bearer token"
// @Param        page          query     int     false  "页码，默认1"
// @Param        page_size     query     int     false  "每页数量，默认20"
// @Param        status        query     int     false  "状态筛选：1上线，0下线，-1删除，不传则查询上线和下线，多个以逗号分隔"
// @Param        is_onchain   query     int    false  "是否链上模型筛选，1是，0否，不传则查询所有"
// @Success      200           {object}  service.AdminModelListResponse
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/models/list [get]
func (h *topaiModelHandler) AdminGetModelList(c *gin.Context) {
	page := c.DefaultQuery("page", "1")
	pageSize := c.DefaultQuery("page_size", "20")
	status := c.Query("status")
	isOnChain := c.Query("is_onchain")

	pageInt, err := strconv.Atoi(page)
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid page", err)
		return
	}
	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil {
		api.Fail(c, api.CodeClientError, "invalid page_size", err)
		return
	}
	statusIntArr := []int{}
	if status != "" {
		statusArr := strings.Split(status, ",")
		for _, statusStr := range statusArr {
			if statusStr == "" {
				continue
			}
			statusInt, err := strconv.Atoi(statusStr)
			if err != nil {
				api.Fail(c, api.CodeClientError, "invalid status", err)
				return
			}
			if statusInt == 1 || statusInt == 0 || statusInt == -1 {
				statusIntArr = append(statusIntArr, statusInt)
			}
		}
	}
	isOnChainInt := -1
	if isOnChain != "" {
		if isOnChain == "1" {
			isOnChainInt = 1
		} else if isOnChain == "0" {
			isOnChainInt = 0
		}
	}

	// 调用服务层方法
	models, err := h.service.Topai.AdminGetModelList(c.Request.Context(), pageInt, pageSizeInt, statusIntArr, isOnChainInt)
	if err != nil {
		h.logger.Error("failed to get admin model list", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get model list", err)
		return
	}

	api.Success(c, models)
}

// @Summary      管理员查看模型详细信息
// @Description  获取指定模型的详细信息
// @Tags         管理员-模型管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true  "Bearer token"
// @Param        model_id      path      string  true  "模型ID"
// @Success      200           {object}  service.AdminModelDetailResponse
// @Failure      401           {object}  string
// @Failure      404           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/models/{model_id} [get]
func (h *topaiModelHandler) AdminGetModelDetail(c *gin.Context) {
	modelID := c.Param("model_id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("model_id is required"))
		return
	}

	model, err := h.service.Topai.AdminGetModelDetail(c.Request.Context(), modelID)
	if err != nil {
		h.logger.Error("failed to get admin model detail", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get model detail", err)
		return
	}

	api.Success(c, model)
}

// @Summary      管理员编辑模型详情
// @Description  编辑模型详情，链上模型只允许编辑描述，非链上模型可以编辑输入输出价格等
// @Tags         管理员-模型管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string                   true  "Bearer token"
// @Param        model_id      path      string                   true  "模型ID"
// @Param        request       body      service.AdminUpdateModelRequest  true  "更新模型请求"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      404           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/models/{model_id} [post]
func (h *topaiModelHandler) AdminUpdateModel(c *gin.Context) {
	modelID := c.Param("model_id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("model_id is required"))
		return
	}

	var req service.AdminUpdateModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	serviceReq := &service.AdminUpdateModelRequest{
		Description:   req.Description,
		InputPrice:    req.InputPrice,
		OutputPrice:   req.OutputPrice,
		ContextLength: req.ContextLength,
		MaxOutput:     req.MaxOutput,
		Weight:        req.Weight,
	}
	err := h.service.Topai.AdminUpdateModel(c.Request.Context(), modelID, serviceReq)
	if err != nil {
		h.logger.Error("failed to update model", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to update model", err)
		return
	}

	api.Success(c, gin.H{"message": "模型更新成功"})
}

// AdminModelStatusRequest 管理员模型状态操作请求
type AdminModelStatusRequest struct {
	Action string `json:"action" binding:"required"` // 操作类型：online（上线）、offline（下线）、delete（删除）
}

// @Summary      管理员模型上下架/删除
// @Description  管理员对模型进行上线、下线或删除操作
// @Tags         管理员-模型管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string                   true  "Bearer token"
// @Param        model_id      path      string                   true  "模型ID"
// @Param        request       body      AdminModelStatusRequest  true  "状态操作请求"
// @Success      200           {object}  string
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      404           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/models/{model_id}/status [post]
func (h *topaiModelHandler) AdminUpdateModelStatus(c *gin.Context) {
	modelID := c.Param("model_id")
	if modelID == "" {
		api.Fail(c, api.CodeClientError, "invalid request", errors.New("model_id is required"))
		return
	}

	var req AdminModelStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	// 验证操作类型
	validActions := []string{"online", "offline", "delete"}
	if !slices.Contains(validActions, req.Action) {
		api.Fail(c, api.CodeClientError, "invalid action", errors.New("action must be one of: online, offline, delete"))
		return
	}

	err := h.service.Topai.AdminUpdateModelStatus(c.Request.Context(), modelID, req.Action)
	if err != nil {
		h.logger.Error("failed to update model status", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to update model status", err)
		return
	}

	api.Success(c, nil)
}

// AdminProviderListItem 管理员供应商列表项
type AdminProviderListItem struct {
	ID            uint   `json:"id"`             // 供应商ID
	UUID          string `json:"uuid"`           // 供应商UUID
	Name          string `json:"name"`           // 供应商名称
	WalletAddress string `json:"wallet_address"` // 钱包地址
	IsCentralized bool   `json:"is_centralized"` // 是否中心化供应商（wallet_address为空）
	CreatedAt     int64  `json:"created_at"`     // 创建时间
}

// AdminProviderListResponse 管理员供应商列表响应
type AdminProviderListResponse struct {
	Total     int64                    `json:"total"`
	Providers []*AdminProviderListItem `json:"providers"`
}

// @Summary      管理员查看供应商列表
// @Description  获取所有供应商信息，区分中心化/链上供应商
// @Tags         管理员-供应商管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string  true   "Bearer token"
// @Param        page          query     int     false  "页码，默认1"
// @Param        page_size     query     int     false  "每页数量，默认10"
// @Param        is_centralized query    bool    false  "是否中心化供应商筛选（wallet_address 以 self- 开头）"
// @Success      200           {object}  AdminProviderListResponse
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/providers/list [get]
func (h *topaiModelHandler) AdminGetProviderList(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	isCentralizedStr := c.Query("is_centralized")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	var isCentralized *bool
	if isCentralizedStr != "" {
		if b, err := strconv.ParseBool(isCentralizedStr); err == nil {
			isCentralized = &b
		}
	}

	providers, err := h.service.Provider.AdminGetProviderList(c.Request.Context(), page, pageSize, isCentralized)
	if err != nil {
		h.logger.Error("failed to get admin provider list", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to get provider list", err)
		return
	}

	api.Success(c, providers)
}

// AdminCreateProviderRequest 管理员创建中心化供应商请求
type AdminCreateProviderRequest struct {
	Name string `json:"name" binding:"required"` // 供应商名称
}

// @Summary      管理员新增中心化供应商
// @Description  创建新的中心化供应商（wallet_address为空）
// @Tags         管理员-供应商管理
// @Accept       json
// @Produce      json
// @Param        Authorization  header    string                      true  "Bearer token"
// @Param        request       body      AdminCreateProviderRequest  true  "创建供应商请求"
// @Success      200           {object}  AdminProviderListItem
// @Failure      400           {object}  string
// @Failure      401           {object}  string
// @Failure      500           {object}  string
// @Router       /api/v1/admin/providers/create [post]
func (h *topaiModelHandler) AdminCreateProvider(c *gin.Context) {
	var req AdminCreateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}

	provider, err := h.service.Provider.AdminCreateProvider(c.Request.Context(), req.Name)
	if err != nil {
		h.logger.Error("failed to create provider", zap.Error(err))
		api.Fail(c, api.CodeServerError, "failed to create provider", err)
		return
	}

	api.Success(c, provider)
}

// AdminCreateCategoryRequest 管理员创建分类请求
type AdminCreateCategoryRequest struct {
	Name string `json:"name" binding:"required"`
}

// @Summary 管理员新增分类
// @Tags 管理员-模型管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body AdminCreateCategoryRequest true "创建分类"
// @Success 200 {object} service.TopaiModelCategoryOP
// @Router /api/v1/admin/models/categories [post]
func (h *topaiModelHandler) AdminCreateCategory(c *gin.Context) {
	var req AdminCreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	cat, err := h.service.Provider.AdminCreateCategory(c.Request.Context(), req.Name, "")
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to create category", err)
		return
	}
	api.Success(c, cat)
}

// @Summary 全部分类列表
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 200 {array} service.TopaiModelCategoryOP
// @Router /api/v1//topai/models/categories/all [get]
func (h *topaiModelHandler) AllListCategories(c *gin.Context) {
	cats, err := h.service.Provider.GetAllCategories(c.Request.Context())
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to list categories", err)
		return
	}
	api.Success(c, cats)
}

// AdminCreateSeriesRequest 管理员创建系列请求
type AdminCreateSeriesRequest struct {
	Name string `json:"name" binding:"required"`
}

// @Summary 管理员新增系列
// @Tags 管理员-模型管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body AdminCreateSeriesRequest true "创建系列"
// @Success 200 {object} service.TopaiModelSeriesOP
// @Router /api/v1/admin/models/series [post]
func (h *topaiModelHandler) AdminCreateSeries(c *gin.Context) {
	var req AdminCreateSeriesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	item, err := h.service.Provider.AdminCreateSeries(c.Request.Context(), req.Name)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to create series", err)
		return
	}
	api.Success(c, item)
}

// @Summary 管理员新增中心化模型
// @Tags 管理员-模型管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param request body service.AdminCreateCentralModelRequest true "创建中心化模型"
// @Success 200 {object} service.AdminModelDetailResponse
// @Router /api/v1/admin/models/central [post]
func (h *topaiModelHandler) AdminCreateCentralModel(c *gin.Context) {
	var req service.AdminCreateCentralModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		api.Fail(c, api.CodeClientError, "invalid request", err)
		return
	}
	detail, err := h.service.Provider.AdminCreateCentralModel(c.Request.Context(), &req)
	if err != nil {
		api.Fail(c, api.CodeServerError, "failed to create model", err)
		return
	}
	api.Success(c, detail)
}
