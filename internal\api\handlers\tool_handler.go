package handlers

import (
	"github.com/gin-gonic/gin"
	"topnetwork.ai/topai/chat-webserver/internal/api"
	"topnetwork.ai/topai/chat-webserver/internal/service"
)

type ToolHandler struct {
	service *service.Api
}

func NewToolHandler(service *service.Api) *ToolHandler {
	return &ToolHandler{
		service: service,
	}
}

// GetTools 获取工具列表
// @Summary 获取工具列表
// @Description 获取工具列表
// @Tags 工具
// @Produce json
// @Success 200 {object} []interface{}
// @Failure 400 {object} string
// @Failure 401 {object} string
// @Failure 500 {object} string
// @Router /api/v1/tools [get]
func (h *ToolHandler) GetTools(c *gin.Context) {
	// todo 直接返回空，需要的时候再实现
	api.Success(c, []interface{}{})
}

func (h *ToolHandler) Close() {
	// 清理资源
}
