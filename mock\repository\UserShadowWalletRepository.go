// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserShadowWalletRepository is an autogenerated mock type for the UserShadowWalletRepository type
type UserShadowWalletRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: ctx, wallet
func (_m *UserShadowWalletRepository) Create(ctx context.Context, wallet *repository.UserShadowWallet) error {
	ret := _m.Called(ctx, wallet)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.UserShadowWallet) error); ok {
		r0 = rf(ctx, wallet)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByPublicAddress provides a mock function with given fields: ctx, publicAddress
func (_m *UserShadowWalletRepository) GetByPublicAddress(ctx context.Context, publicAddress string) (*repository.UserShadowWallet, error) {
	ret := _m.Called(ctx, publicAddress)

	if len(ret) == 0 {
		panic("no return value specified for GetByPublicAddress")
	}

	var r0 *repository.UserShadowWallet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.UserShadowWallet, error)); ok {
		return rf(ctx, publicAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.UserShadowWallet); ok {
		r0 = rf(ctx, publicAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserShadowWallet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, publicAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByUserID provides a mock function with given fields: ctx, userID
func (_m *UserShadowWalletRepository) GetByUserID(ctx context.Context, userID uint) (*repository.UserShadowWallet, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserID")
	}

	var r0 *repository.UserShadowWallet
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint) (*repository.UserShadowWallet, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint) *repository.UserShadowWallet); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserShadowWallet)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserShadowWalletRepository creates a new instance of UserShadowWalletRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserShadowWalletRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserShadowWalletRepository {
	mock := &UserShadowWalletRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
