package middleware

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestMain 设置测试环境
func TestMain(m *testing.M) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)
	m.Run()
}

// MockServiceAPI Mock服务接口
type MockServiceAPI struct {
	mock.Mock
}

// TestEnv 中间件测试环境
type TestEnv struct {
	T      *testing.T
	Router *gin.Engine
	Svc    *MockServiceAPI
}

// NewTestEnv 创建中间件测试环境
func NewTestEnv(t *testing.T) *TestEnv {
	router := gin.New()
	svc := &MockServiceAPI{}

	env := &TestEnv{
		T:      t,
		Router: router,
		Svc:    svc,
	}

	t.Cleanup(func() {
		env.Cleanup()
	})

	return env
}

// Cleanup 清理测试环境
func (e *TestEnv) Cleanup() {
	// 验证所有Mock期望
	e.Svc.AssertExpectations(e.T)
}

// TestAuth 测试JWT认证中间件
func TestAuth(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*TestEnv) string
		token          string
		expectedStatus int
		expectedBody   string
		verify         func(*TestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-有效Token认证成功",
			setup: func(env *TestEnv) string {
				// 这里应该生成一个有效的JWT token
				// 简化处理，返回固定token
				return "Bearer valid_jwt_token"
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusOK, w.Code)
			},
		},
		{
			name: "TC2-缺少Authorization头",
			setup: func(env *TestEnv) string {
				return ""
			},
			token:          "",
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
				assert.Contains(t, w.Body.String(), "Authorization header is required")
			},
		},
		{
			name: "TC3-无效的Bearer格式",
			setup: func(env *TestEnv) string {
				return "InvalidFormat token"
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
				assert.Contains(t, w.Body.String(), "Invalid authorization header format")
			},
		},
		{
			name: "TC4-空Token",
			setup: func(env *TestEnv) string {
				return "Bearer "
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
				assert.Contains(t, w.Body.String(), "Token is required")
			},
		},
		{
			name: "TC5-过期Token",
			setup: func(env *TestEnv) string {
				// 模拟过期token
				return "Bearer expired_jwt_token"
			},
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewTestEnv(t)
			defer env.Cleanup()

			// 设置测试路由
			env.Router.Use(Auth(env.Svc))
			env.Router.GET("/protected", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// 获取Authorization头
			authHeader := tt.setup(env)
			if tt.token != "" {
				authHeader = tt.token
			}

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, "/protected", nil)
			if authHeader != "" {
				req.Header.Set("Authorization", authHeader)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.verify != nil {
				tt.verify(env, w)
			}
		})
	}
}

// TestAuthAdmin 测试管理员认证中间件
func TestAuthAdmin(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*TestEnv)
		token          string
		expectedStatus int
		verify         func(*TestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-有效管理员Token",
			setup: func(env *TestEnv) {
				// Mock管理员用户验证
				// 这里需要根据实际的用户验证逻辑来设置Mock
			},
			token:          "Bearer admin_jwt_token",
			expectedStatus: http.StatusOK,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusOK, w.Code)
			},
		},
		{
			name: "TC2-普通用户Token访问管理员接口",
			setup: func(env *TestEnv) {
				// Mock普通用户验证
			},
			token:          "Bearer user_jwt_token",
			expectedStatus: http.StatusForbidden,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusForbidden, w.Code)
			},
		},
		{
			name:           "TC3-无Token访问管理员接口",
			setup:          func(env *TestEnv) {},
			token:          "",
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewTestEnv(t)
			defer env.Cleanup()

			// 设置测试路由
			env.Router.Use(AuthAdmin(env.Svc))
			env.Router.GET("/admin", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "admin success"})
			})

			// 执行setup
			tt.setup(env)

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, "/admin", nil)
			if tt.token != "" {
				req.Header.Set("Authorization", tt.token)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			tt.verify(env, w)
		})
	}
}

// TestAuthApiKey 测试API Key认证中间件
func TestAuthApiKey(t *testing.T) {
	tests := []struct {
		name           string
		setup          func(*TestEnv)
		apiKey         string
		expectedStatus int
		verify         func(*TestEnv, *httptest.ResponseRecorder)
	}{
		{
			name: "TC1-有效API Key",
			setup: func(env *TestEnv) {
				// Mock API Key验证
			},
			apiKey:         "valid_api_key",
			expectedStatus: http.StatusOK,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusOK, w.Code)
			},
		},
		{
			name: "TC2-无效API Key",
			setup: func(env *TestEnv) {
				// Mock无效API Key
			},
			apiKey:         "invalid_api_key",
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name:           "TC3-缺少API Key",
			setup:          func(env *TestEnv) {},
			apiKey:         "",
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
		{
			name: "TC4-过期API Key",
			setup: func(env *TestEnv) {
				// Mock过期API Key
			},
			apiKey:         "expired_api_key",
			expectedStatus: http.StatusUnauthorized,
			verify: func(env *TestEnv, w *httptest.ResponseRecorder) {
				assert.Equal(t, http.StatusUnauthorized, w.Code)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			env := NewTestEnv(t)
			defer env.Cleanup()

			// 设置测试路由
			env.Router.Use(AuthApiKey(env.Svc))
			env.Router.GET("/api", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "api success"})
			})

			// 执行setup
			tt.setup(env)

			// 创建请求
			req := httptest.NewRequest(http.MethodGet, "/api", nil)
			if tt.apiKey != "" {
				req.Header.Set("X-API-Key", tt.apiKey)
			}

			// 执行请求
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			// 验证响应
			tt.verify(env, w)
		})
	}
}

// TestAuthMiddleware_MultipleMethods 测试多种HTTP方法的认证
func TestAuthMiddleware_MultipleMethods(t *testing.T) {
	methods := []string{
		http.MethodGet,
		http.MethodPost,
		http.MethodPut,
		http.MethodDelete,
		http.MethodPatch,
	}

	for _, method := range methods {
		method := method
		t.Run(fmt.Sprintf("Method_%s", method), func(t *testing.T) {
			t.Parallel()

			env := NewTestEnv(t)
			defer env.Cleanup()

			// 设置测试路由
			env.Router.Use(Auth(env.Svc))
			switch method {
			case http.MethodGet:
				env.Router.GET("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"method": method})
				})
			case http.MethodPost:
				env.Router.POST("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"method": method})
				})
			case http.MethodPut:
				env.Router.PUT("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"method": method})
				})
			case http.MethodDelete:
				env.Router.DELETE("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"method": method})
				})
			case http.MethodPatch:
				env.Router.PATCH("/test", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{"method": method})
				})
			}

			// 测试无Token的情况
			req := httptest.NewRequest(method, "/test", nil)
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusUnauthorized, w.Code)
		})
	}
}

// TestAuthMiddleware_ConcurrentRequests 测试并发请求认证
func TestAuthMiddleware_ConcurrentRequests(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 设置测试路由
	env.Router.Use(Auth(env.Svc))
	env.Router.GET("/concurrent", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	const numRequests = 100
	results := make(chan int, numRequests)

	// 并发发送请求
	for i := 0; i < numRequests; i++ {
		go func() {
			req := httptest.NewRequest(http.MethodGet, "/concurrent", nil)
			// 不设置Token，应该都返回401
			w := httptest.NewRecorder()
			env.Router.ServeHTTP(w, req)
			results <- w.Code
		}()
	}

	// 收集结果
	for i := 0; i < numRequests; i++ {
		status := <-results
		assert.Equal(t, http.StatusUnauthorized, status)
	}
}

// TestAuthMiddleware_ContextValues 测试认证后的上下文值
func TestAuthMiddleware_ContextValues(t *testing.T) {
	env := NewTestEnv(t)
	defer env.Cleanup()

	// 设置测试路由（这里需要Mock一个有效的认证流程）
	env.Router.Use(func(c *gin.Context) {
		// 模拟认证成功，设置用户信息到上下文
		c.Set("user_id", "test_user_123")
		c.Set("user_role", "user")
		c.Next()
	})

	env.Router.GET("/context", func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		assert.True(t, exists)
		assert.Equal(t, "test_user_123", userID)

		userRole, exists := c.Get("user_role")
		assert.True(t, exists)
		assert.Equal(t, "user", userRole)

		c.JSON(http.StatusOK, gin.H{
			"user_id":   userID,
			"user_role": userRole,
		})
	})

	// 创建请求
	req := httptest.NewRequest(http.MethodGet, "/context", nil)
	w := httptest.NewRecorder()
	env.Router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "test_user_123")
	assert.Contains(t, w.Body.String(), "user")
}

// BenchmarkAuth 认证中间件性能基准测试
func BenchmarkAuth(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	svc := &MockServiceAPI{}

	router.Use(func(c *gin.Context) {
		// 简化的认证逻辑，直接通过
		c.Next()
	})

	router.GET("/benchmark", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req := httptest.NewRequest(http.MethodGet, "/benchmark", nil)
	req.Header.Set("Authorization", "Bearer test_token")

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

// BenchmarkAuthApiKey API Key认证性能基准测试
func BenchmarkAuthApiKey(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	svc := &MockServiceAPI{}

	router.Use(func(c *gin.Context) {
		// 简化的API Key认证逻辑
		c.Next()
	})

	router.GET("/benchmark", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req := httptest.NewRequest(http.MethodGet, "/benchmark", nil)
	req.Header.Set("X-API-Key", "test_api_key")

	b.ReportAllocs()
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}
