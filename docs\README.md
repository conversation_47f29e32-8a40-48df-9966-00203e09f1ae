# Open WebUI 后端文档中心

## 1. 核心文档

### 1.1 产品文档
- [产品需求文档](./product_requirement_docs.md)
  - 项目背景
  - 问题陈述
  - 项目目标
  - 核心需求
  - 项目范围
  - 项目约束
  - 项目风险
  - 项目里程碑
  - 项目依赖
  - 项目验收标准

### 1.2 技术文档
- [技术文档](./technical.md)
  - 开发环境
  - 技术栈
  - 技术决策
  - 设计模式
  - 代码规范
  - 测试规范
  - 部署规范

### 1.3 任务文档
- [任务计划](../tasks/tasks_plan.md)
  - 项目阶段划分
  - 任务优先级
  - 时间安排
  - 资源分配
  - 风险管理
  - 验收标准

- [活动上下文](../tasks/active_context.md)
  - 当前任务状态
  - 当前上下文
  - 重要决策
  - 待解决问题
  - 近期计划
  - 注意事项
  - 更新记录

## 2. 规则文档

### 2.1 错误文档规则
- [错误文档规则](../.cursor/rules/error-documentation.mdc)
  - 错误记录规范
  - 错误处理流程
  - 错误文档模板
  - 错误管理工具
  - 错误处理最佳实践

### 2.2 经验教训规则
- [经验教训规则](../.cursor/rules/lessons-learned.mdc)
  - 经验记录规范
  - 经验总结流程
  - 经验文档模板
  - 经验管理工具
  - 经验管理最佳实践

## 3. 文档更新记录

### 3.1 2024-03-21
- 创建产品需求文档
- 创建技术文档
- 创建任务计划
- 创建活动上下文
- 创建错误文档规则
- 创建经验教训规则
- 创建文档索引

## 4. 文档维护指南

### 4.1 文档更新流程
1. 确定更新内容
2. 编写更新内容
3. 团队评审
4. 更新文档
5. 更新记录

### 4.2 文档版本控制
- 使用 Git 进行版本控制
- 遵循语义化版本
- 保持文档一致性
- 定期备份文档

### 4.3 文档访问权限
- 开发团队：所有文档
- 测试团队：技术文档、任务文档
- 运维团队：技术文档、部署文档
- 产品团队：产品文档、任务文档

## 5. 文档模板

### 5.1 新增文档模板
```markdown
# 文档标题

## 1. 文档信息
- 创建时间：YYYY-MM-DD
- 最后更新：YYYY-MM-DD
- 作者：XXX
- 版本：v1.0.0

## 2. 文档内容
[文档内容]

## 3. 更新记录
- YYYY-MM-DD：初始版本
```

### 5.2 文档更新模板
```markdown
## 更新内容
- 更新项1
- 更新项2
- 更新项3

## 更新原因
- 原因1
- 原因2
- 原因3

## 影响范围
- 影响1
- 影响2
- 影响3
```

## 6. 文档工具

### 6.1 文档编写工具
- Markdown 编辑器
- 文档生成工具
- 版本控制工具
- 协作平台

### 6.2 文档管理工具
- 文档管理系统
- 知识库平台
- 团队协作工具
- 版本控制系统

## 7. 文档规范

### 7.1 格式规范
- 使用 Markdown 格式
- 遵循统一的标题层级
- 保持一致的代码风格
- 使用规范的图片引用

### 7.2 内容规范
- 语言简洁明了
- 结构清晰合理
- 内容准确完整
- 示例具体实用

### 7.3 维护规范
- 定期更新文档
- 及时修正错误
- 保持版本同步
- 记录更新历史 