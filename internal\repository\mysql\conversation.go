package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// conversationRepository 实现对话相关的数据库操作
type conversationRepository struct {
	db *gorm.DB
}

// NewConversationRepository 创建对话仓库实例
func NewConversationRepository(db *gorm.DB) repository.ConversationRepository {
	return &conversationRepository{
		db: db,
	}
}

// GetByID 根据ID获取对话
func (r *conversationRepository) GetByID(ctx context.Context, id uint) (*repository.Conversation, error) {
	var conv repository.Conversation
	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = 0", id).First(&conv).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &conv, nil
}

// GetByUUID 根据UUID获取对话
func (r *conversationRepository) GetByUUID(ctx context.Context, uuid string) (*repository.Conversation, error) {
	var conv repository.Conversation
	if err := r.db.WithContext(ctx).Where("uuid = ? AND is_deleted = 0", uuid).First(&conv).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &conv, nil
}

// GetByUserID 获取用户的所有对话
func (r *conversationRepository) GetByUserID(ctx context.Context, userID uint) ([]*repository.Conversation, error) {
	var convs []*repository.Conversation
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_deleted = 0", userID).Find(&convs).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return convs, nil
}

func (r *conversationRepository) UpdateTitleByUUID(ctx context.Context, uuid string, title string) error {
	return r.db.WithContext(ctx).Model(&repository.Conversation{}).Where("uuid = ?", uuid).Update("title", title).Error
}

func (r *conversationRepository) UpdateCurrentMsgUUIDByUUID(ctx context.Context, uuid string, currentMsgUUID string) error {
	return r.db.WithContext(ctx).Model(&repository.Conversation{}).Where("uuid = ?", uuid).Update("current_msg_uuid", currentMsgUUID).Error
}

// Create 创建对话
func (r *conversationRepository) Create(ctx context.Context, conv *repository.Conversation) error {
	return r.db.WithContext(ctx).Create(conv).Error
}

// Delete 删除对话（软删除）
func (r *conversationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.Conversation{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// FindByUserIDOrderByUpdatedAtDesc 根据用户ID获取对话，并按更新时间降序排序
func (r *conversationRepository) FindByUserIDOrderByUpdatedAtDesc(ctx context.Context, userID uint, limit int, offset int) ([]*repository.Conversation, error) {
	var convs []*repository.Conversation
	err := r.db.WithContext(ctx).Where("user_id = ? AND is_deleted = 0", userID).Order("updated_at DESC").Limit(limit).Offset(offset).Find(&convs).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return convs, nil
}

type conversationMessageRepository struct {
	db *gorm.DB
}

func NewConversationMessageRepository(db *gorm.DB) repository.ConversationMessageRepository {
	return &conversationMessageRepository{
		db: db,
	}
}

func (r *conversationMessageRepository) GetByID(ctx context.Context, id uint) (*repository.ConversationMessage, error) {
	var conv repository.ConversationMessage
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&conv).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &conv, nil
}

func (r *conversationMessageRepository) Create(ctx context.Context, conv *repository.ConversationMessage) error {
	return r.db.WithContext(ctx).Create(conv).Error
}

func (r *conversationMessageRepository) Update(ctx context.Context, conv *repository.ConversationMessage) error {
	return r.db.WithContext(ctx).Omit("created_at", "updated_at").Save(conv).Error
}

func (r *conversationMessageRepository) GetByConversationID(ctx context.Context, conversationID uint) ([]*repository.ConversationMessage, error) {
	var convs []*repository.ConversationMessage
	if err := r.db.WithContext(ctx).Where("conversation_id = ?", conversationID).Find(&convs).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return convs, nil
}

func (r *conversationMessageRepository) GetByConversationIDAndUUID(ctx context.Context, conversationID uint, uuid string) (*repository.ConversationMessage, error) {
	var conv repository.ConversationMessage
	if err := r.db.WithContext(ctx).Where("conversation_id = ? AND uuid = ?", conversationID, uuid).First(&conv).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &conv, nil
}

func (r *conversationMessageRepository) UpdateContentAndDoneById(ctx context.Context, id uint, content, fileUUIDs string) error {
	return r.db.WithContext(ctx).Model(&repository.ConversationMessage{}).Where("id = ?", id).UpdateColumns(map[string]interface{}{
		"content":    content,
		"is_done":    1,
		"file_uuids": fileUUIDs,
	}).Error
}

type userChatASRRecordRepository struct {
	db *gorm.DB
}

func NewUserChatASRRecordRepository(db *gorm.DB) repository.UserChatASRRecordRepository {
	return &userChatASRRecordRepository{
		db: db,
	}
}

func (r *userChatASRRecordRepository) Create(ctx context.Context, record *repository.UserChatASRRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

type userChatTTSRecordRepository struct {
	db *gorm.DB
}

func NewUserChatTTSRecordRepository(db *gorm.DB) repository.UserChatTTSRecordRepository {
	return &userChatTTSRecordRepository{
		db: db,
	}
}

func (r *userChatTTSRecordRepository) Create(ctx context.Context, record *repository.UserChatTTSRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *userChatTTSRecordRepository) GetByUserChatUsageID(ctx context.Context, userChatUsageID uint) (*repository.UserChatTTSRecord, error) {
	var record repository.UserChatTTSRecord
	if err := r.db.WithContext(ctx).Where("user_chat_usage_id = ?", userChatUsageID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

type userChatTTIRecordRepository struct {
	db *gorm.DB
}

func NewUserChatTTIRecordRepository(db *gorm.DB) repository.UserChatTTIRecordRepository {
	return &userChatTTIRecordRepository{
		db: db,
	}
}

func (r *userChatTTIRecordRepository) Create(ctx context.Context, record *repository.UserChatTTIRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

func (r *userChatTTIRecordRepository) GetByUUID(ctx context.Context, uuid string) (*repository.UserChatTTIRecord, error) {
	var record repository.UserChatTTIRecord
	if err := r.db.WithContext(ctx).Where("uuid = ?", uuid).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

func (r *userChatTTIRecordRepository) UpdateIsSharedByUUID(ctx context.Context, uuid string, isShared uint8) error {
	return r.db.WithContext(ctx).Model(&repository.UserChatTTIRecord{}).Where("uuid = ?", uuid).Update("is_shared", isShared).Error
}

func (r *userChatTTIRecordRepository) GetSharedByUserID(ctx context.Context, userID uint, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	var records []*repository.UserChatTTIRecord
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_shared = 1", userID).Order(order).Limit(limit).Offset(offset).Find(&records).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}

func (r *userChatTTIRecordRepository) GetSharedCountByUserID(ctx context.Context, userID uint) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&repository.UserChatTTIRecord{}).Where("user_id = ? AND is_shared = 1", userID).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *userChatTTIRecordRepository) GetSharedCount(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&repository.UserChatTTIRecord{}).Where("is_shared = 1").Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *userChatTTIRecordRepository) GetShared(ctx context.Context, limit int, offset int, order string) ([]*repository.UserChatTTIRecord, error) {
	var records []*repository.UserChatTTIRecord
	if err := r.db.WithContext(ctx).Where("is_shared = 1").Order(order).Limit(limit).Offset(offset).Find(&records).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return records, nil
}
