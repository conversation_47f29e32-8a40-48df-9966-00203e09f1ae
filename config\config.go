package config

import (
	"fmt"
	"os"
	"path"
	"sync"
	"time"

	"gopkg.in/yaml.v3"
)

var (
	conf      *Config
	notifiers []ConfigChangeNotifier
	mu        sync.RWMutex
)

// ConfigVersion 表示配置版本
type ConfigVersion struct {
	Version string    `yaml:"version"`
	Time    time.Time `yaml:"time"`
	Config  *Config   `yaml:"config"`
}

// ConfigChangeNotifier 定义配置变更通知接口
type ConfigChangeNotifier interface {
	NotifyChange(old, new *Config)
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port    int           `yaml:"port"`
	Timeout time.Duration `yaml:"timeout"`
	TLS     struct {
		Enabled bool   `yaml:"enabled"`
		Cert    string `yaml:"cert"`
		Key     string `yaml:"key"`
	} `yaml:"tls"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	Pool     struct {
		MaxOpenConns    int           `yaml:"max_open_conns"`
		MaxIdleConns    int           `yaml:"max_idle_conns"`
		ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
		ConnMaxIdleTime time.Duration `yaml:"conn_max_idletime"`
	} `yaml:"pool"`
	Timeouts struct {
		Connect     time.Duration `yaml:"connect"`
		Query       time.Duration `yaml:"query"`
		Transaction time.Duration `yaml:"transaction"`
	} `yaml:"timeouts"`
	ParseTime bool   `yaml:"parse_time"`
	Collation string `yaml:"collation"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	Secret   string        `yaml:"secret"`
	TokenTTL time.Duration `yaml:"tokenTTL"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `yaml:"level"`
	Path       string `yaml:"path"`
	MaxSize    int    `yaml:"maxSize"`    // 单个日志文件最大大小（MB）
	MaxBackups int    `yaml:"maxBackups"` // 保留的旧日志文件最大数量
	MaxAge     int    `yaml:"maxAge"`     // 保留的旧日志文件最大天数
	Compress   bool   `yaml:"compress"`   // 是否压缩旧日志文件
	Console    bool   `yaml:"console"`    // 是否同时输出到控制台
}

type ServiceConfig struct {
	File             FileServiceConfig      `yaml:"file"`
	Openai           map[string]Openai      `yaml:"openai"`
	TopAi            TopAi                  `yaml:"topai"`
	ShadowAccount    ShadowAccountConfig    `yaml:"shadow_account"`
	TopChain         TopChain               `yaml:"top_chain"`
	CompanyUserID    uint                   `yaml:"company_user_id"`
	ConcurrencyLimit ConcurrencyLimitConfig `yaml:"concurrency_limit"`
}

// ConcurrencyLimitConfig 并发限制配置
type ConcurrencyLimitConfig struct {
	GlobalLimit    int `yaml:"global_limit"`     // 全局并发限制，0表示无限制
	UserLimit      int `yaml:"user_limit"`       // 单用户并发限制，0表示无限制
	UserModelLimit int `yaml:"user_model_limit"` // 单用户单模型并发限制，0表示无限制
}

type ShadowAccountConfig struct {
	Version        string `yaml:"version"`
	Algorithm      string `yaml:"algorithm"`        // 加密算法
	KdfAlgorithm   string `yaml:"kdf_algorithm"`    // 密钥派生算法
	Base64Key      string `yaml:"base64_key"`       // 密钥
	PoolAccountPub string `yaml:"pool_account_pub"` // 池子账户公钥
	PoolAccountPri string `yaml:"pool_account_pri"` // 池子账户私钥
}

// TopChain 链上配置
type TopChain struct {
	ChainUrl string `yaml:"chain_url"`
	ChainID  int64  `yaml:"chain_id"`

	// imo 合约地址
	IMOEntryAddress        string `yaml:"imo_entry_address"`
	TokenVaultAddress      string `yaml:"token_vault_address"`
	ProxyAdminAddress      string `yaml:"proxy_admin_address"`
	InternalFactoryAddress string `yaml:"internal_factory_address"`
	InternalRouterAddress  string `yaml:"internal_router_address"`
	ModelTokenAddress      string `yaml:"model_token_address"`
	ModelLockTokenAddress  string `yaml:"model_lock_token_address"`
	ModelFactoryAddress    string `yaml:"model_factory_address"`

	// 结算合约地址
	BankAddress       string `yaml:"bank_address"`
	SettlementAddress string `yaml:"settlement_address"`
	DepositAddress    string `yaml:"deposit_address"`

	// models 合约地址
	AssetManagementAddress string `yaml:"asset_management_address"`
	NodeRegisterAddress    string `yaml:"node_register_address"`
	AiModelsAddress        string `yaml:"ai_models_address"`
	AiWorkerAddress        string `yaml:"ai_worker_address"`

	// uniswap 合约地址
	WETH9Address             string `yaml:"weth9_address"`
	UniswapV2FactoryAddress  string `yaml:"uniswap_v2_factory_address"`
	UniswapV2Router02Address string `yaml:"uniswap_v2_router02_address"`

	// erc20 合约地址
	TopErc20WrapperAddress  string `yaml:"top_erc20_wrapper_address"`
	UsdcErc20WrapperAddress string `yaml:"usdc_erc20_wrapper_address"`
	UsdtErc20WrapperAddress string `yaml:"usdt_erc20_wrapper_address"`
}

type FileServiceConfig struct {
	MaxSize int64  `yaml:"max_size"`
	Path    string `yaml:"path"`
}

// Config 定义了应用程序的配置结构
type Config struct {
	Version  string         `yaml:"version"`
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Auth     AuthConfig     `yaml:"auth"`
	Log      LogConfig      `yaml:"log"`
	Service  ServiceConfig  `yaml:"service"`
}

// Openai 第三方平台，兼容OpenAI的api
type Openai struct {
	BaseUrl string `json:"base_url"`
	ApiKey  string `json:"api_key"`
}

// TopAi 自有的去中心化ai平台
type TopAi struct {
	WalletAddr string `json:"wallet_addr"`
	IpMapPath  string `json:"ip_map_path"`
}

// defaultConfig 返回默认配置
func defaultConfig() *Config {
	conf := &Config{
		Version: "1.0.0",
		Server: ServerConfig{
			Port:    8080,
			Timeout: 30 * time.Second,
			TLS: struct {
				Enabled bool   `yaml:"enabled"`
				Cert    string `yaml:"cert"`
				Key     string `yaml:"key"`
			}{
				Enabled: false,
				Cert:    "",
				Key:     "",
			},
		},
		Database: DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "",
			Database: "chat_server",
			Pool: struct {
				MaxOpenConns    int           `yaml:"max_open_conns"`
				MaxIdleConns    int           `yaml:"max_idle_conns"`
				ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
				ConnMaxIdleTime time.Duration `yaml:"conn_max_idletime"`
			}{
				MaxOpenConns:    100,
				MaxIdleConns:    10,
				ConnMaxLifetime: time.Hour,
				ConnMaxIdleTime: 30 * time.Minute,
			},
			Timeouts: struct {
				Connect     time.Duration `yaml:"connect"`
				Query       time.Duration `yaml:"query"`
				Transaction time.Duration `yaml:"transaction"`
			}{
				Connect:     5 * time.Second,
				Query:       30 * time.Second,
				Transaction: 30 * time.Second,
			},
			ParseTime: true,
			Collation: "utf8mb4_unicode_ci",
		},
		Auth: AuthConfig{
			Secret:   "default-secret-key-for-jwt-token-generation",
			TokenTTL: 24 * time.Hour,
		},
		Log: LogConfig{
			Level:      "info",
			Path:       "logs/app.log",
			MaxSize:    100,
			MaxBackups: 3,
			MaxAge:     7,
			Compress:   true,
			Console:    true,
		},
		Service: ServiceConfig{
			File: FileServiceConfig{
				MaxSize: 1024 * 1024 * 10, // 10MB
				Path:    "uploads",
			},
			Openai: map[string]Openai{
				"openai": {
					BaseUrl: "https://api.openai.com/v1",
					ApiKey:  "sk-...",
				},
			},
			TopAi: TopAi{
				WalletAddr: "******************************************",
				IpMapPath:  "ip_map.json",
			},
			ShadowAccount: ShadowAccountConfig{
				Version:        "1",
				Algorithm:      "aes-256-gcm",
				KdfAlgorithm:   "HKDF-SHA256",
				Base64Key:      "1234567890123456789012345678901234567890",
				PoolAccountPub: "******************************************",
				PoolAccountPri: "******************************************",
			},
			TopChain: TopChain{
				ChainUrl:                 "https://api.topai.com/v1",
				ChainID:                  1023,
				TopErc20WrapperAddress:   "******************************************",
				UsdcErc20WrapperAddress:  "******************************************",
				UsdtErc20WrapperAddress:  "******************************************",
				BankAddress:              "******************************************",
				SettlementAddress:        "******************************************",
				DepositAddress:           "******************************************",
				AssetManagementAddress:   "******************************************",
				NodeRegisterAddress:      "******************************************",
				AiModelsAddress:          "******************************************",
				AiWorkerAddress:          "******************************************",
				WETH9Address:             "******************************************",
				UniswapV2FactoryAddress:  "******************************************",
				UniswapV2Router02Address: "******************************************",
				IMOEntryAddress:          "******************************************",
				TokenVaultAddress:        "******************************************",
				ProxyAdminAddress:        "******************************************",
				InternalFactoryAddress:   "******************************************",
				InternalRouterAddress:    "******************************************",
				ModelTokenAddress:        "******************************************",
				ModelLockTokenAddress:    "******************************************",
				ModelFactoryAddress:      "******************************************",
			},
			ConcurrencyLimit: ConcurrencyLimitConfig{
				GlobalLimit:    100, // 全局最多100个并发请求
				UserLimit:      10,  // 单用户最多10个并发请求
				UserModelLimit: 3,   // 单用户单模型最多3个并发请求
			},
		},
	}

	return conf
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) error {
	// 解析配置
	newConfig := defaultConfig()

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil && os.IsNotExist(err) {
		dir := path.Dir(configPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("create config directory: %w", err)
		}
		file, err := os.Create(configPath)
		if err != nil {
			return fmt.Errorf("create config file: %w", err)
		}
		defer file.Close()
		data, err = yaml.Marshal(newConfig)
		if err != nil {
			return fmt.Errorf("marshal config: %w", err)
		}
		if _, err := file.Write(data); err != nil {
			return fmt.Errorf("write config file: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("read config file: %w", err)
	} else {
		// 解析配置
		if err := yaml.Unmarshal(data, newConfig); err != nil {
			return fmt.Errorf("parse config: %w", err)
		}
	}

	// 验证配置
	if err := newConfig.Validate(); err != nil {
		return fmt.Errorf("validate config: %w", err)
	}

	// 更新配置
	mu.Lock()
	oldConfig := conf
	conf = newConfig
	mu.Unlock()

	// 通知配置变更
	if oldConfig != nil {
		// 创建旧配置的深拷贝
		oldConfigCopy := *oldConfig
		notifyConfigChange(&oldConfigCopy, newConfig)
	}

	return nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Server.Port <= 0 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}
	if c.Server.Timeout <= 0 {
		return fmt.Errorf("invalid server timeout: %v", c.Server.Timeout)
	}
	if c.Database.Port <= 0 {
		return fmt.Errorf("invalid database port: %d", c.Database.Port)
	}
	if c.Auth.TokenTTL <= 0 {
		return fmt.Errorf("invalid auth token TTL: %v", c.Auth.TokenTTL)
	}
	return nil
}

// RegisterNotifier 注册配置变更通知器
func RegisterNotifier(notifier ConfigChangeNotifier) {
	mu.Lock()
	defer mu.Unlock()
	notifiers = append(notifiers, notifier)
}

// notifyConfigChange 通知所有注册的通知器
func notifyConfigChange(old, new *Config) {
	mu.RLock()
	defer mu.RUnlock()
	for _, notifier := range notifiers {
		notifier.NotifyChange(old, new)
	}
}

// GetConfig 获取当前配置
func GetConfig() *Config {
	mu.RLock()
	defer mu.RUnlock()
	return conf
}

// GetLogConfig 获取日志配置
func GetLogConfig() LogConfig {
	mu.RLock()
	defer mu.RUnlock()
	return conf.Log
}
