// Code generated by mockery v2.53.4. DO NOT EDIT.

package repository

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	repository "topnetwork.ai/topai/chat-webserver/internal/repository"
)

// UserUsageDayRecordRepository is an autogenerated mock type for the UserUsageDayRecordRepository type
type UserUsageDayRecordRepository struct {
	mock.Mock
}

// CreateBatch provides a mock function with given fields: ctx, records
func (_m *UserUsageDayRecordRepository) CreateBatch(ctx context.Context, records []*repository.UserUsageDayRecord) error {
	ret := _m.Called(ctx, records)

	if len(ret) == 0 {
		panic("no return value specified for CreateBatch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*repository.UserUsageDayRecord) error); ok {
		r0 = rf(ctx, records)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetByModelId provides a mock function with given fields: ctx, modelId
func (_m *UserUsageDayRecordRepository) GetByModelId(ctx context.Context, modelId string) ([]*repository.UserUsageDayRecord, error) {
	ret := _m.Called(ctx, modelId)

	if len(ret) == 0 {
		panic("no return value specified for GetByModelId")
	}

	var r0 []*repository.UserUsageDayRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*repository.UserUsageDayRecord, error)); ok {
		return rf(ctx, modelId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*repository.UserUsageDayRecord); ok {
		r0 = rf(ctx, modelId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.UserUsageDayRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, modelId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLast provides a mock function with given fields: ctx
func (_m *UserUsageDayRecordRepository) GetLast(ctx context.Context) (*repository.UserUsageDayRecord, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetLast")
	}

	var r0 *repository.UserUsageDayRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*repository.UserUsageDayRecord, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *repository.UserUsageDayRecord); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.UserUsageDayRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserUsageDayRecordRepository creates a new instance of UserUsageDayRecordRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserUsageDayRecordRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserUsageDayRecordRepository {
	mock := &UserUsageDayRecordRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
