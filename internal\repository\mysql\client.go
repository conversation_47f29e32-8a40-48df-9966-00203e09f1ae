package mysql

import (
	"context"
	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"topnetwork.ai/topai/chat-webserver/config"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// NewClient 创建一个新的 MySQL 客户端
func NewClient(cfg *config.DatabaseConfig) (*repository.DB, error) {
	// 构建 DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=%v&collation=%s",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.ParseTime,
		cfg.Collation,
	)

	// 配置 GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 打开数据库连接
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.Pool.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Pool.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.Pool.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(cfg.Pool.ConnMaxIdleTime)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Timeouts.Connect)
	defer cancel()
	if err := sqlDB.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 创建数据库访问层实例
	opDB := repository.DB{
		User:                          NewUserRepository(db),
		Conversation:                  NewConversationRepository(db),
		ConversationMessage:           NewConversationMessageRepository(db),
		LLMModelConfig:                NewLLMModelConfigRepository(db),
		RemoterModelInfo:              NewRemoterModelInfoRepository(db),
		UserToken:                     NewUserTokenRepository(db),
		File:                          NewFileRepository(db),
		UserSetting:                   NewUserSettingRepository(db),
		SystemConfig:                  NewSystemConfigRepository(db),
		TTSModelVoice:                 NewTTSModelVoiceRepository(db),
		TopaiModel:                    NewTopaiModelRepository(db),
		TopaiModelExt:                 NewTopaiModelExtRepository(db),
		TopaiModelCategory:            NewTopaiModelCategoryRepository(db),
		TopaiModelCategoryRelation:    NewTopaiModelCategoryRelationRepository(db),
		TopaiModelSeries:              NewTopaiModelSeriesRepository(db),
		TopaiModelSeriesRelation:      NewTopaiModelSeriesRelationRepository(db),
		TopaiModelProvider:            NewTopaiModelProviderRepository(db),
		TopaiModelSupportParam:        NewTopaiModelSupportParamRepository(db),
		DevAppInfo:                    NewDevAppInfoRepository(db),
		DevAppKey:                     NewDevAppKeyRepository(db),
		TopaiModelCost:                NewTopaiModelCostRepository(db),
		UserChatRequest:               NewUserChatRequestRepository(db),
		UserChatUsage:                 NewUserChatUsageRepository(db),
		UserBalance:                   NewUserBalanceRepository(db),
		UserBalanceRecord:             NewUserBalanceRecordRepository(db),
		UserShadowWallet:              NewUserShadowWalletRepository(db),
		UserShadowWalletBalance:       NewUserShadowWalletBalanceRepository(db),
		UserShadowWalletBalanceRecord: NewUserShadowWalletBalanceRecordRepository(db),
		WalletTransferRecord:          NewWalletTransferRecordRepository(db),
		WalletReportCostRecord:        NewWalletReportCostRecordRepository(db),
		WalletDepositRecord:           NewWalletDepositRecordRepository(db),
		UserRechargeRecord:            NewUserRechargeRecordRepository(db),
		UserChatASRRecord:             NewUserChatASRRecordRepository(db),
		UserChatTTSRecord:             NewUserChatTTSRecordRepository(db),
		UserChatTTIRecord:             NewUserChatTTIRecordRepository(db),
		UserUsageDayRecord:            NewUserUsageDayRecordRepository(db),
		ProviderToken:                 NewProviderTokenRepository(db),

		Close: func() error { return sqlDB.Close() },
	}

	// 设置事务处理
	opDB.RunWithTx = func(ctx context.Context, fn func(ctx context.Context, db *repository.DB) error) error {
		return db.Transaction(func(tx *gorm.DB) error {
			txDB := &repository.DB{
				User:                          NewUserRepository(tx),
				Conversation:                  NewConversationRepository(tx),
				ConversationMessage:           NewConversationMessageRepository(tx),
				LLMModelConfig:                NewLLMModelConfigRepository(tx),
				RemoterModelInfo:              NewRemoterModelInfoRepository(tx),
				UserToken:                     NewUserTokenRepository(tx),
				File:                          NewFileRepository(tx),
				UserSetting:                   NewUserSettingRepository(tx),
				SystemConfig:                  NewSystemConfigRepository(tx),
				TTSModelVoice:                 NewTTSModelVoiceRepository(tx),
				TopaiModel:                    NewTopaiModelRepository(tx),
				TopaiModelExt:                 NewTopaiModelExtRepository(tx),
				TopaiModelCategory:            NewTopaiModelCategoryRepository(tx),
				TopaiModelCategoryRelation:    NewTopaiModelCategoryRelationRepository(tx),
				TopaiModelSeries:              NewTopaiModelSeriesRepository(tx),
				TopaiModelSeriesRelation:      NewTopaiModelSeriesRelationRepository(tx),
				TopaiModelProvider:            NewTopaiModelProviderRepository(tx),
				TopaiModelSupportParam:        NewTopaiModelSupportParamRepository(tx),
				DevAppInfo:                    NewDevAppInfoRepository(tx),
				DevAppKey:                     NewDevAppKeyRepository(tx),
				TopaiModelCost:                NewTopaiModelCostRepository(tx),
				UserChatRequest:               NewUserChatRequestRepository(tx),
				UserChatUsage:                 NewUserChatUsageRepository(tx),
				UserBalance:                   NewUserBalanceRepository(tx),
				UserBalanceRecord:             NewUserBalanceRecordRepository(tx),
				UserShadowWallet:              NewUserShadowWalletRepository(tx),
				UserShadowWalletBalance:       NewUserShadowWalletBalanceRepository(tx),
				UserShadowWalletBalanceRecord: NewUserShadowWalletBalanceRecordRepository(tx),
				WalletTransferRecord:          NewWalletTransferRecordRepository(tx),
				WalletReportCostRecord:        NewWalletReportCostRecordRepository(tx),
				WalletDepositRecord:           NewWalletDepositRecordRepository(tx),
				UserRechargeRecord:            NewUserRechargeRecordRepository(tx),
				UserChatASRRecord:             NewUserChatASRRecordRepository(tx),
				UserChatTTSRecord:             NewUserChatTTSRecordRepository(tx),
				UserChatTTIRecord:             NewUserChatTTIRecordRepository(tx),
				UserUsageDayRecord:            NewUserUsageDayRecordRepository(tx),
				ProviderToken:                 NewProviderTokenRepository(tx),
			}
			return fn(ctx, txDB)
		})
	}

	return &opDB, nil
}
