package chain

import (
	"context"
	"crypto/ecdsa"
	_ "embed"
	"encoding/json"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

var (
	//go:embed abi/aimodels.json
	aiModelsJsoin string
	aiModelsAbi   = mustParseContractABI(aiModelsJsoin)
	//go:embed abi/settlement.json
	settlementJsoin string
	settlementAbi   = mustParseContractABI(settlementJsoin)
	//go:embed abi/aiworkload.json
	aiWorkloadJsoin string
	aiWorkloadAbi   = mustParseContractABI(aiWorkloadJsoin)
	//go:embed abi/nodesregistry.json
	nodesRegistryJsoin string
	nodesRegistryAbi   = mustParseContractABI(nodesRegistryJsoin)
	//go:embed abi/nodesgovernance.json
	nodesGovernanceJsoin string
	nodesGovernanceAbi   = mustParseContractABI(nodesGovernanceJsoin)
	//go:embed abi/ERC20Sample.json
	erc20Jsoin string
	erc20Abi   = mustParseContractABI(erc20Jsoin)
	//go:embed abi/Deposit.json
	depositJsoin string
	depositAbi   = mustParseContractABI(depositJsoin)
)

// 合约 JSON 文件结构
type ContractJSON struct {
	ABI json.RawMessage `json:"abi"`
}

// AIModel 链上模型信息
type AIModelInfo struct {
	ModelId          *big.Int
	ModelName        string
	ModelVersion     string
	Uploader         common.Address
	ExtendInfo       string
	ExtendInfoExtend *AIModelInfoExtend `json:"-"`
	Timestamp        *big.Int
	InTokenPrice     *big.Int
	OutTokenPrice    *big.Int
}

// {"type": "image", "framework": "TensorRT", "resources": {"cpu": {"request": 1.0, "limit": 2.0}, "memory": {"request": 2000, "limit": 8000}, "gpu": {"count": 1, "memory": 10000, "core_count": 0, "models": ["NVIDIA GeForce RTX 3090", "NVIDIA GeForce RTX 3080"]}}, "load_timeout": 900, "idle_timeout": 1800}
type AIModelInfoExtend struct {
	Type      string `json:"type"`
	Framework string `json:"framework"`
	Resources struct {
		CPU struct {
			Request float64 `json:"request"`
			Limit   float64 `json:"limit"`
		} `json:"cpu"`
		Memory struct {
			Request int `json:"request"`
			Limit   int `json:"limit"`
		} `json:"memory"`
		GPU struct {
			Count     int      `json:"count"`
			Memory    int      `json:"memory"`
			CoreCount int      `json:"core_count"`
			Models    []string `json:"models"`
		} `json:"gpu"`
	} `json:"resources"`
	LoadTimeout int `json:"load_timeout"`
	IdleTimeout int `json:"idle_timeout"`
}

// mustParseContractABI 从完整的合约 JSON 中解析出 ABI
func mustParseContractABI(contractJSON string) abi.ABI {
	var contract ContractJSON
	if err := json.Unmarshal([]byte(contractJSON), &contract); err != nil {
		panic("failed to parse contract JSON: " + err.Error())
	}

	parsed, err := abi.JSON(strings.NewReader(string(contract.ABI)))
	if err != nil {
		panic("invalid ABI: " + err.Error())
	}
	return parsed
}

func GetAiModelsList(ctx context.Context, chainUrl, aiModelsAddress string) ([]*AIModelInfo, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}
	// 先获取下一个modelId
	contractAddress := common.HexToAddress(aiModelsAddress)
	data, err := aiModelsAbi.Pack("nextModelId")
	if err != nil {
		return nil, err
	}
	resp, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, err
	}
	var s interface{}

	err = aiModelsAbi.UnpackIntoInterface(&s, "nextModelId", resp)
	if err != nil {
		return nil, err
	}
	nextModelId := s.(*big.Int)
	fmt.Println("nextModelId", nextModelId)

	resList := make([]*AIModelInfo, 0)

	for {
		nextModelId = nextModelId.Sub(nextModelId, big.NewInt(1))
		data, err = aiModelsAbi.Pack("uploadModels", nextModelId)
		if err != nil {
			return nil, err
		}
		resp, err = client.CallContract(ctx, ethereum.CallMsg{
			To:   &contractAddress,
			Data: data,
		}, nil)
		if err != nil {
			return nil, err
		}
		modelInfo := new(AIModelInfo)
		err = aiModelsAbi.UnpackIntoInterface(modelInfo, "uploadModels", resp)
		if err != nil {
			return nil, err
		}
		if modelInfo.ExtendInfo != "" {
			modelInfo.ExtendInfoExtend = &AIModelInfoExtend{}
			err = json.Unmarshal([]byte(modelInfo.ExtendInfo), modelInfo.ExtendInfoExtend)
			if err != nil {
				modelInfo.ExtendInfoExtend = nil
			}
		}
		resList = append(resList, modelInfo)
		if nextModelId.Cmp(big.NewInt(1)) <= 0 {
			break
		}
	}
	return resList, nil
}

func mustParsePrivateKey(privateKey string) *ecdsa.PrivateKey {
	key, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		panic(err)
	}
	return key
}

var PrivateKeys = []*ecdsa.PrivateKey{
	mustParsePrivateKey("5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a"), //******************************************
	//mustParsePrivateKey("7de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365b"), //******************************************
	mustParsePrivateKey("9de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365c"), //******************************************
	mustParsePrivateKey("7de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365d"), //******************************************
}

var addressKeyMap = map[string]int{
	"******************************************": 0,
	"******************************************": 1,
	"******************************************": 2,
}

func ReportModelCost(ctx context.Context, chainUrl, aiWorkloadAddress, userAddress string, modelId, sessionId, epochId uint, inputTokens, outputTokens *big.Int) (string, error) {
	fmt.Println("ReportModelCost", chainUrl, aiWorkloadAddress, userAddress, modelId, sessionId, epochId, inputTokens, outputTokens)
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contractAddress := common.HexToAddress(aiWorkloadAddress)
	modelIdBig := big.NewInt(int64(modelId))
	sessionIdBig := big.NewInt(int64(sessionId))
	epochIdBig := big.NewInt(int64(epochId))

	//根据epochId + sessionId ,得到workerAddress
	index := (int(sessionId) + int(epochId)) % len(PrivateKeys)
	workerAddress := crypto.PubkeyToAddress(PrivateKeys[index].PublicKey)

	signatures, err := SignText(
		workerAddress.Hex(),
		userAddress,
		outputTokens, // workload
		modelIdBig,
		sessionIdBig,
		epochIdBig,
		inputTokens, // inputWorkload
		PrivateKeys,
	)
	if err != nil {
		return "", err
	}

	// TestAllSignaturesLocally(
	// 	workerAddress.Hex(),
	// 	userAddress,
	// 	outputTokens,
	// 	modelIdBig,
	// 	sessionIdBig,
	// 	epochIdBig,
	// 	inputTokens,
	// 	signatures,
	// 	PrivateKeys,
	// )

	auth, err := createLatestAuth(client, PrivateKeys[0], aiWorkloadAddress)
	if err != nil {
		return "", fmt.Errorf("create latest auth error: %v", err)
	}

	contract := bind.NewBoundContract(contractAddress, aiWorkloadAbi, client, client, client)
	if err != nil {
		return "", fmt.Errorf("create contract error: %v", err)
	}

	tx, err := contract.Transact(auth, "reportWorkload",
		workerAddress,
		common.HexToAddress(userAddress),
		outputTokens,
		big.NewInt(int64(modelId)),
		big.NewInt(int64(sessionId)),
		big.NewInt(int64(epochId)),
		inputTokens,
		signatures,
	)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}

	// 等待交易被打包
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("wait mined error: %v", err)
	}

	// 检查交易状态
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}

	return tx.Hash().Hex(), nil
}

type Signature struct {
	R [32]byte
	S [32]byte
	V uint8
}

func SignText(
	workerAddress string,
	userAddress string,
	workload *big.Int,
	modelId *big.Int,
	sessionId *big.Int,
	epochId *big.Int,
	inputWorkload *big.Int,
	privKeys []*ecdsa.PrivateKey,
) ([]Signature, error) {
	// 准备要签名的数据
	arguments := abi.Arguments{
		{Type: getAddressType()},
		{Type: getAddressType()},
		{Type: getUint256Type()},
		{Type: getUint256Type()},
		{Type: getUint256Type()},
		{Type: getUint256Type()},
		{Type: getUint256Type()},
	}

	// 编码数据
	encoded, err := arguments.Pack(
		common.HexToAddress(workerAddress),
		common.HexToAddress(userAddress),
		workload,
		modelId,
		sessionId,
		epochId,
		inputWorkload,
	)
	if err != nil {
		return []Signature{}, fmt.Errorf("failed to encode data: %v", err)
	}
	//fmt.Printf("encoded: %x\n", encoded)
	// 0x0000000000000000000000003c44cdddb6a900fa2b585dd299e03d12fa4293bc00000000000000000000000057990b470a4fb08c26cf73e51c1b6b888dd0547900000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000064
	// 0000000000000000000000003c44cdddb6a900fa2b585dd299e03d12fa4293bc00000000000000000000000057990b470a4fb08c26cf73e51c1b6b888dd0547900000000000000000000000000000000000000000000000000000000000000c80000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000064

	// 添加前缀并计算哈希
	prefix := fmt.Sprintf("\x19Ethereum Signed Message:\n%d", len(encoded))
	prefixedHash := crypto.Keccak256(append([]byte(prefix), encoded...))

	// 收集所有签名
	// signatures := make([]struct {
	// 	R, S string
	// 	V    uint8
	// }, len(privKeys))
	signatures := make([]Signature, len(privKeys))
	// 遍历所有私钥进行签名
	for i, privateKey := range privKeys {
		// signature, err := crypto.Sign(prefixedHash, privateKey)
		// if err != nil {
		// 	return []Signature{}, fmt.Errorf("failed to sign with key %d: %v", i, err)
		// }

		// fmt.Printf("signature: %x\n", signature)
		// // signatures[i] = AIWorkload.Signature{
		// // 	R: toBytes32(signature[:32]),
		// // 	S: toBytes32(signature[32:64]),
		// // 	V: signature[64] + 27,
		// // }
		// // 将签名转换为十六进制字符串以便验证
		// fullSig := hexutil.Encode(signature) // 0x + r + s + v

		// // 解析各部分 - 模拟 JS 的处理方式
		// var r, s [32]byte
		// rBytes, err := hexutil.Decode(fullSig[:66]) // 0x + 前32字节
		// if err != nil {
		// 	return []Signature{}, fmt.Errorf("failed to decode R: %v", err)
		// }
		// sBytes, err := hexutil.Decode("0x" + fullSig[66:130]) // 0x + 中间32字节
		// if err != nil {
		// 	return []Signature{}, fmt.Errorf("failed to decode S: %v", err)
		// }
		// v := signature[64] + 27 // 最后一个字节 + 27

		// // fmt.Println("r:", r)
		// // fmt.Println("s:", r)
		// // fmt.Println("v:", r)
		// // 复制到固定大小的数组
		// copy(r[:], rBytes)
		// copy(s[:], sBytes)

		// signatures[i] = Signature{
		// 	R: r,
		// 	S: s,
		// 	V: v,
		// }
		// fmt.Printf("+++++signature: %+v\n", signatures[i])
		// 生成原始签名（65字节）
		sigBytes, err := crypto.Sign(prefixedHash, privateKey)
		if err != nil {
			return nil, fmt.Errorf("sign error: %v", err)
		}

		// 调试输出：原始签名
		//fmt.Printf("Raw signature[%d]: 0x%x\n", i, sigBytes)

		// 5. 直接提取 R, S, V 不经过 hex 转换
		var r [32]byte
		var s [32]byte
		copy(r[:], sigBytes[:32])
		copy(s[:], sigBytes[32:64])

		// 6. V 值处理（保持与合约兼容）
		v := sigBytes[64]
		if v < 27 { // 确保 V 值为 27 或 28
			v += 27
		}

		signatures[i] = Signature{R: r, S: s, V: v}
		//fmt.Printf("Parsed signature[%d]: V=%d R=0x%x S=0x%x\n",
		//	i, v, r, s)
		// 在 Go 端验证签名恢复的地址
		// pubKey, err := crypto.SigToPub(prefixedHash, sigBytes)
		// if err == nil {
		// 	recoveredAddr := crypto.PubkeyToAddress(*pubKey)
		// 	fmt.Printf("Recovered address: %s, original address: %s\n", recoveredAddr.Hex(), crypto.PubkeyToAddress(privateKey.PublicKey).Hex())
		// }
	}

	// fmt.Println("signatures: ", signatures)

	return signatures, nil
}

// 辅助函数：获取 ABI 类型
func getAddressType() abi.Type {
	t, _ := abi.NewType("address", "", nil)
	return t
}

func getUint256Type() abi.Type {
	t, _ := abi.NewType("uint256", "", nil)
	return t
}

func createLatestAuth(client *ethclient.Client, privateKey *ecdsa.PrivateKey, contract string) (*bind.TransactOpts, error) {
	ctx := context.Background()

	// 1. 获取链ID
	chainID, err := client.ChainID(ctx)
	if err != nil {
		return nil, fmt.Errorf("get chain id error: %v", err)
	}

	// 2. 创建基础auth对象
	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, chainID)
	if err != nil {
		return nil, fmt.Errorf("create auth error: %v", err)
	}

	return auth, nil
}

func RegisterNode(ctx context.Context, chainUrl, nodesRegistryAddress string, privateKey *ecdsa.PrivateKey, aliasIdentifier string) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(nodesRegistryAddress), nodesRegistryAbi, client, client, client)
	if err != nil {
		return "", err
	}
	auth, err := createLatestAuth(client, privateKey, nodesRegistryAddress)
	if err != nil {
		return "", err
	}

	tx, err := contract.Transact(auth, "registerNode",
		crypto.PubkeyToAddress(privateKey.PublicKey),
		aliasIdentifier,
		[]string{"3080", "3090"},
		[]*big.Int{big.NewInt(2), big.NewInt(2)},
		true,
	)
	if err != nil {
		fmt.Println("registerNode err", err)
		return "", err
	}
	return tx.Hash().Hex(), nil
}

func UploadModel(ctx context.Context, chainUrl, aiModelsAddress string, privateKey *ecdsa.PrivateKey, modelName, modelVersion, extendInfo string, inTokenPrice, outTokenPrice *big.Int) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(aiModelsAddress), aiModelsAbi, client, client, client)
	if err != nil {
		return "", err
	}
	auth, err := createLatestAuth(client, privateKey, aiModelsAddress)
	if err != nil {
		return "", err
	}
	tx, err := contract.Transact(auth, "recordModelUpload", modelName, modelVersion, extendInfo, inTokenPrice, outTokenPrice)
	if err != nil {
		return "", err
	}

	// 等待交易被打包
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}

	// 检查交易状态
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}

	return tx.Hash().Hex(), nil
}

func CheckRegisteNode(ctx context.Context, chainUrl, nodesRegistryAddress string, identifier string) (bool, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return false, err
	}
	contractAddress := common.HexToAddress(nodesRegistryAddress)
	data, err := nodesRegistryAbi.Pack("check", common.HexToAddress(identifier))
	if err != nil {
		return false, err
	}
	resp, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddress,
		Data: data,
	}, nil)
	if err != nil {
		return false, err
	}
	var s interface{}
	err = nodesRegistryAbi.UnpackIntoInterface(&s, "check", resp)
	if err != nil {
		return false, err
	}
	return s.(bool), nil
}

func NodeVote(ctx context.Context, chainUrl, nodesGovernanceAddress string, result bool) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(nodesGovernanceAddress), nodesGovernanceAbi, client, client, client)
	if err != nil {
		return "", err
	}
	roundId, err := GetRoundId(ctx, chainUrl, nodesGovernanceAddress)
	if err != nil {
		return "", err
	}
	candidates, err := GetRoundCandidates(ctx, chainUrl, nodesGovernanceAddress, roundId)
	if err != nil {
		return "", err
	}
	validators, err := GetValidatorsOfCandidate(ctx, chainUrl, nodesGovernanceAddress, roundId, candidates[0])
	if err != nil {
		return "", err
	}
	fmt.Println("validators", validators)
	txs := make([]string, 0)
	for _, validAddr := range validators {
		privateKey := PrivateKeys[addressKeyMap[validAddr.Hex()]]
		fmt.Println("privateKey", crypto.PubkeyToAddress(privateKey.PublicKey).Hex())
		auth, err := createLatestAuth(client, privateKey, nodesGovernanceAddress)
		if err != nil {
			return "", err
		}

		tx, err := contract.Transact(auth, "vote",
			big.NewInt(int64(roundId)),
			candidates[0],
			result,
		)
		if err != nil {
			return "", err
		}
		receipt, err := bind.WaitMined(ctx, client, tx)
		if err != nil {
			return "", fmt.Errorf("pack tx error: %v", err)
		}
		if receipt.Status != types.ReceiptStatusSuccessful {
			return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
		}
		txs = append(txs, tx.Hash().Hex())
	}
	return strings.Join(txs, ","), nil
}

func GetRoundId(ctx context.Context, chainUrl, nodesGovernanceAddress string) (uint, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return 0, err
	}
	contractAddress := common.HexToAddress(nodesGovernanceAddress)
	data, err := nodesGovernanceAbi.Pack("currentRoundId")
	if err != nil {
		return 0, err
	}
	resp, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddress,
		Data: data,
	}, nil)
	if err != nil {
		return 0, err
	}
	var s interface{}
	err = nodesGovernanceAbi.UnpackIntoInterface(&s, "currentRoundId", resp)
	if err != nil {
		return 0, err
	}
	return uint(s.(*big.Int).Uint64()), nil
}

func GetRoundCandidates(ctx context.Context, chainUrl, nodesGovernanceAddress string, roundId uint) ([]common.Address, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}
	contractAddress := common.HexToAddress(nodesGovernanceAddress)
	data, err := nodesGovernanceAbi.Pack("getRoundCandidates", big.NewInt(int64(roundId)))
	if err != nil {
		return nil, err
	}
	resp, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, err
	}
	var s struct {
		Candidates []common.Address `json:"candidates"`
	}
	err = nodesGovernanceAbi.UnpackIntoInterface(&s, "getRoundCandidates", resp)
	if err != nil {
		return nil, err
	}
	return s.Candidates, nil
}

func GetValidatorsOfCandidate(ctx context.Context, chainUrl, nodesGovernanceAddress string, roundId uint, candidate common.Address) ([]common.Address, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return nil, err
	}
	contractAddress := common.HexToAddress(nodesGovernanceAddress)
	data, err := nodesGovernanceAbi.Pack("getValidatorsOfCandidate", big.NewInt(int64(roundId)), candidate)
	if err != nil {
		return nil, err
	}
	resp, err := client.CallContract(ctx, ethereum.CallMsg{
		To:   &contractAddress,
		Data: data,
	}, nil)
	if err != nil {
		return nil, err
	}
	var s struct {
		Validators []common.Address `json:"validators"`
	}
	err = nodesGovernanceAbi.UnpackIntoInterface(&s, "getValidatorsOfCandidate", resp)
	if err != nil {
		return nil, err
	}
	return s.Validators, nil
}

func DeregisterNode(ctx context.Context, chainUrl, nodesRegistryAddress string, privateKey *ecdsa.PrivateKey) (string, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return "", err
	}
	contract := bind.NewBoundContract(common.HexToAddress(nodesRegistryAddress), nodesRegistryAbi, client, client, client)
	if err != nil {
		return "", err
	}
	auth, err := createLatestAuth(client, privateKey, nodesRegistryAddress)
	if err != nil {
		return "", err
	}
	tx, err := contract.Transact(auth, "deregisterNode", crypto.PubkeyToAddress(privateKey.PublicKey))
	if err != nil {
		return "", err
	}
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return "", fmt.Errorf("pack tx error: %v", err)
	}
	if receipt.Status != types.ReceiptStatusSuccessful {
		return "", fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}
	return tx.Hash().Hex(), nil
}

// 验证单个签名是否有效
func verifySignatureLocally(
	workerAddress string,
	userAddress string,
	workload *big.Int,
	modelId *big.Int,
	sessionId *big.Int,
	epochId *big.Int,
	inputWorkload *big.Int,
	signature Signature, // 要验证的签名
	expectedAddress common.Address, // 签名对应的预期地址
) (bool, error) {
	// 1. 编码参数（与合约中的 abi.encode 相同）
	addressType, _ := abi.NewType("address", "", nil)
	uint256Type, _ := abi.NewType("uint256", "", nil)

	arguments := abi.Arguments{
		{Type: addressType},
		{Type: addressType},
		{Type: uint256Type},
		{Type: uint256Type},
		{Type: uint256Type},
		{Type: uint256Type},
		{Type: uint256Type},
	}

	workerAddr := common.HexToAddress(workerAddress)
	userAddr := common.HexToAddress(userAddress)

	encoded, err := arguments.Pack(
		workerAddr,
		userAddr,
		workload,
		modelId,
		sessionId,
		epochId,
		inputWorkload,
	)
	if err != nil {
		return false, fmt.Errorf("encoding failed: %v", err)
	}

	// 2. 计算内容哈希（keccak256(content)）
	contentHash := crypto.Keccak256(encoded)

	// 3. 添加以太坊签名前缀
	prefix := fmt.Sprintf("\x19Ethereum Signed Message:\n%d", len(contentHash))
	prefixedHash := crypto.Keccak256(append([]byte(prefix), contentHash...))

	// 4. 构建签名字节（模拟合约的 abi.encodePacked(r, s, v)）
	signatureBytes := make([]byte, 65)
	copy(signatureBytes[0:32], signature.R[:])
	copy(signatureBytes[32:64], signature.S[:])

	// 关键修改：调整 V 值为 0/1 格式
	v := signature.V
	if v >= 27 {
		v -= 27 // 转换为 0/1 格式
	}
	signatureBytes[64] = v

	// 5. 调试输出签名字节
	fmt.Printf("Signature bytes (65 bytes): 0x%x\n", signatureBytes)
	fmt.Printf("V value: %d (original) -> %d (adjusted)\n", signature.V, v)

	// 6. 尝试恢复地址
	recoveredPub, err := crypto.SigToPub(prefixedHash, signatureBytes)
	if err != nil {
		// 添加更多调试信息
		fmt.Printf("Prefixed hash: 0x%x\n", prefixedHash)
		fmt.Printf("Signature length: %d bytes\n", len(signatureBytes))
		fmt.Printf("Signature R: 0x%x\n", signature.R[:])
		fmt.Printf("Signature S: 0x%x\n", signature.S[:])
		return false, fmt.Errorf("signature recovery failed: %v", err)
	}

	fmt.Printf("Recovered address: %s\n", crypto.PubkeyToAddress(*recoveredPub).Hex())

	return true, nil
}

// 批量验证所有签名
func TestAllSignaturesLocally(
	workerAddress string,
	userAddress string,
	workload *big.Int,
	modelId *big.Int,
	sessionId *big.Int,
	epochId *big.Int,
	inputWorkload *big.Int,
	signatures []Signature,
	privKeys []*ecdsa.PrivateKey,
) {
	// 获取所有预期地址
	expectedAddresses := make([]common.Address, len(privKeys))
	for i, privKey := range privKeys {
		publicKey := privKey.Public()
		publicKeyECDSA, _ := publicKey.(*ecdsa.PublicKey)
		expectedAddresses[i] = crypto.PubkeyToAddress(*publicKeyECDSA)
	}

	// 验证每个签名
	for i, sig := range signatures {
		valid, err := verifySignatureLocally(
			workerAddress,
			userAddress,
			workload,
			modelId,
			sessionId,
			epochId,
			inputWorkload,
			sig,
			expectedAddresses[i],
		)

		if valid {
			fmt.Printf("✅ Signature %d VALID for address %s\n",
				i, expectedAddresses[i].Hex())
		} else {
			fmt.Printf("❌ Signature %d INVALID: %v\n", i, err)

			// 调试输出
			fmt.Printf("Signature %d: V=%d R=%x S=%x\n",
				i, sig.V, sig.R, sig.S)
		}
	}
}

type TransactionStatus struct {
	Pending     bool
	Included    bool
	Success     bool
	BlockNumber uint64
	EventLogs   []*types.Log
	Error       error
}

func GetTransactionByHash(ctx context.Context, chainUrl, txHash string) (*TransactionStatus, error) {
	status := &TransactionStatus{}
	client, err := ethclient.Dial(chainUrl)

	if err != nil {
		return nil, fmt.Errorf("dial chain url error: %v", err)
	}
	// 检查是否在交易池
	var rawTx map[string]interface{}
	err = client.Client().CallContext(ctx, &rawTx, "eth_getTransactionByHash", txHash)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, fmt.Errorf("transaction by hash error: %v", err)
	}
	if rawTx["blockHash"] == nil {
		status.Pending = true
		return status, nil
	}

	// 检查上链结果
	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		return nil, fmt.Errorf("transaction receipt error: %v", err)
	}
	status.Included = true
	if receipt.Status != types.ReceiptStatusSuccessful {
		return nil, fmt.Errorf("tx execution failed, status code: %v", receipt.Status)
	}

	status.Success = true
	status.BlockNumber = receipt.BlockNumber.Uint64()
	status.EventLogs = receipt.Logs
	return status, nil
}

func GetLatestBlockNumber(ctx context.Context, chainUrl string) (uint64, error) {
	client, err := ethclient.Dial(chainUrl)
	if err != nil {
		return 0, err
	}
	return client.BlockNumber(ctx)
}
