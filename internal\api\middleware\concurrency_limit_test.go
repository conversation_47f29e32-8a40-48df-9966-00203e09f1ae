package middleware

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"topnetwork.ai/topai/chat-webserver/config"
)

func TestConcurrencyLimiter(t *testing.T) {
	t.Run("全局并发限制测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    2,
			UserLimit:      10,
			UserModelLimit: 5,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		// 第一个请求应该成功
		assert.True(t, limiter.AcquireGlobal())
		// 第二个请求应该成功
		assert.True(t, limiter.AcquireGlobal())
		// 第三个请求应该失败（超过全局限制）
		assert.False(t, limiter.AcquireGlobal())

		// 释放一个许可
		limiter.ReleaseGlobal()
		// 现在应该可以获取新的许可
		assert.True(t, limiter.AcquireGlobal())
	})

	t.Run("用户级别并发限制测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    100,
			UserLimit:      2,
			UserModelLimit: 5,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID := uint(123)

		// 第一个请求应该成功
		assert.True(t, limiter.AcquireUser(userID))
		// 第二个请求应该成功
		assert.True(t, limiter.AcquireUser(userID))
		// 第三个请求应该失败（超过用户限制）
		assert.False(t, limiter.AcquireUser(userID))

		// 不同用户应该不受影响
		otherUserID := uint(456)
		assert.True(t, limiter.AcquireUser(otherUserID))

		// 释放原用户的一个许可
		limiter.ReleaseUser(userID)
		// 现在原用户应该可以获取新的许可
		assert.True(t, limiter.AcquireUser(userID))
	})

	t.Run("用户+模型级别并发限制测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    100,
			UserLimit:      100,
			UserModelLimit: 2,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID := uint(123)
		modelID := "gpt-4"

		// 第一个请求应该成功
		assert.True(t, limiter.AcquireUserModel(userID, modelID))
		// 第二个请求应该成功
		assert.True(t, limiter.AcquireUserModel(userID, modelID))
		// 第三个请求应该失败（超过用户+模型限制）
		assert.False(t, limiter.AcquireUserModel(userID, modelID))

		// 同一用户不同模型应该不受影响
		otherModelID := "gpt-3.5-turbo"
		assert.True(t, limiter.AcquireUserModel(userID, otherModelID))

		// 不同用户同一模型应该不受影响
		otherUserID := uint(456)
		assert.True(t, limiter.AcquireUserModel(otherUserID, modelID))
	})

	t.Run("组合限制测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    3,
			UserLimit:      2,
			UserModelLimit: 1,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID := uint(123)
		modelID := "gpt-4"

		// 应该成功获取第一个许可
		acquired, reason := limiter.AcquireAll(userID, modelID)
		assert.True(t, acquired)
		assert.Empty(t, reason)

		// 尝试获取同一用户同一模型的第二个许可，应该失败
		acquired, reason = limiter.AcquireAll(userID, modelID)
		assert.False(t, acquired)
		assert.Equal(t, "user model concurrency limit exceeded", reason)

		// 同一用户不同模型应该成功
		otherModelID := "gpt-3.5-turbo"
		acquired, reason = limiter.AcquireAll(userID, otherModelID)
		assert.True(t, acquired)
		assert.Empty(t, reason)

		// 尝试获取同一用户第三个许可，应该失败（用户限制）
		thirdModelID := "claude-3"
		acquired, reason = limiter.AcquireAll(userID, thirdModelID)
		assert.False(t, acquired)
		assert.Equal(t, "user concurrency limit exceeded", reason)
	})

	t.Run("并发安全测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    10,
			UserLimit:      5,
			UserModelLimit: 3,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		var wg sync.WaitGroup
		successCount := 0
		var mu sync.Mutex

		// 启动多个goroutine并发获取许可
		for i := 0; i < 20; i++ {
			wg.Add(1)
			go func(i int) {
				defer wg.Done()
				userID := uint(i % 3) // 3个不同用户
				modelID := "gpt-4"

				acquired, _ := limiter.AcquireAll(userID, modelID)
				if acquired {
					mu.Lock()
					successCount++
					mu.Unlock()

					// 模拟处理时间
					time.Sleep(10 * time.Millisecond)

					limiter.ReleaseAll(userID, modelID)
				}
			}(i)
		}

		wg.Wait()

		// 应该有一些请求成功，但不能超过限制
		assert.Greater(t, successCount, 0)
		assert.LessOrEqual(t, successCount, 20)
	})

	t.Run("统计信息测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    5,
			UserLimit:      3,
			UserModelLimit: 2,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID1 := uint(123)
		userID2 := uint(456)
		modelID := "gpt-4"

		// 获取一些许可
		limiter.AcquireAll(userID1, modelID)
		limiter.AcquireAll(userID2, modelID)

		stats := limiter.GetStats()

		// 检查统计信息结构
		assert.Contains(t, stats, "global")
		assert.Contains(t, stats, "user")
		assert.Contains(t, stats, "user_model")

		globalStats := stats["global"].(map[string]interface{})
		assert.Equal(t, 5, globalStats["limit"])
		assert.Equal(t, 2, globalStats["current"])

		userStats := stats["user"].(map[string]interface{})
		assert.Equal(t, 3, userStats["limit"])
		assert.Equal(t, 2, userStats["active_users"])

		userModelStats := stats["user_model"].(map[string]interface{})
		assert.Equal(t, 2, userModelStats["limit"])
		assert.Equal(t, 2, userModelStats["active_user_models"])
	})

	t.Run("清理功能测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    100,
			UserLimit:      10,
			UserModelLimit: 5,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID := uint(123)
		modelID := "gpt-4"

		// 获取然后立即释放许可
		limiter.AcquireAll(userID, modelID)
		limiter.ReleaseAll(userID, modelID)

		// 手动触发清理
		limiter.cleanup()

		// 检查清理后的状态
		stats := limiter.GetStats()
		userStats := stats["user"].(map[string]interface{})
		userModelStats := stats["user_model"].(map[string]interface{})

		assert.Equal(t, 0, userStats["active_users"])
		assert.Equal(t, 0, userModelStats["active_user_models"])
	})

	t.Run("无限制配置测试", func(t *testing.T) {
		config := config.ConcurrencyLimitConfig{
			GlobalLimit:    0, // 0表示无限制
			UserLimit:      0,
			UserModelLimit: 0,
		}
		limiter := NewConcurrencyLimiter(config)
		defer limiter.Close()

		userID := uint(123)
		modelID := "gpt-4"

		// 无限制情况下，应该总是能获取许可
		for i := 0; i < 100; i++ {
			acquired, reason := limiter.AcquireAll(userID, modelID)
			assert.True(t, acquired)
			assert.Empty(t, reason)
		}
	})
}
