package mysql

import (
	"context"

	"gorm.io/gorm"
	"topnetwork.ai/topai/chat-webserver/internal/repository"
)

// fileRepository 实现文件相关的数据库操作
type fileRepository struct {
	db *gorm.DB
}

// NewFileRepository 创建文件仓库实例
func NewFileRepository(db *gorm.DB) repository.FileRepository {
	return &fileRepository{
		db: db,
	}
}

// Create 创建文件
func (r *fileRepository) Create(ctx context.Context, file *repository.File) error {
	return r.db.WithContext(ctx).Create(file).Error
}

// GetByID 根据ID获取文件
func (r *fileRepository) GetByID(ctx context.Context, id uint) (*repository.File, error) {
	var file repository.File
	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = 0", id).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &file, nil
}

// GetByFileID 根据FileID获取文件
func (r *fileRepository) GetByFileID(ctx context.Context, fileID string) (*repository.File, error) {
	var file repository.File
	if err := r.db.WithContext(ctx).Where("file_id = ? AND is_deleted = 0", fileID).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &file, nil
}

// Delete 删除文件（软删除）
func (r *fileRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&repository.File{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// UpdateContentById 更新文件内容
func (r *fileRepository) UpdateContentById(ctx context.Context, id uint, content string) error {
	return r.db.WithContext(ctx).Model(&repository.File{}).Where("id = ?", id).Update("content", content).Error
}

// FindByUserIDAndUUIDs 根据用户ID和UUIDs获取文件
func (r *fileRepository) FindByUserIDAndUUIDs(ctx context.Context, userID uint, uuids []string) ([]*repository.File, error) {
	var files []*repository.File
	if err := r.db.WithContext(ctx).Where("user_id = ? AND uuid IN ? AND is_deleted = 0", userID, uuids).Find(&files).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return files, nil
}

// GetByUUID 根据UUID获取文件
func (r *fileRepository) GetByUUID(ctx context.Context, uuid string) (*repository.File, error) {
	var file repository.File
	if err := r.db.WithContext(ctx).Where("uuid = ? AND is_deleted = 0", uuid).First(&file).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &file, nil
}
